'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { AlertTriangle, Database, RefreshCw, Wifi, WifiOff } from '@/components/ui/icons'
import Link from 'next/link'
import { useSyncManager } from '@/hooks/use-local-database'

/**
 * Componente para mostrar un resumen del estado de sincronización en el dashboard principal
 */
export function SyncStatusCard() {
  const { lastSyncTime, syncStats, syncNow, isSyncing } = useSyncManager();
  const [isOnline, setIsOnline] = useState<boolean>(true);

  // Detectar estado de conexión
  useEffect(() => {
    const updateOnlineStatus = () => {
      setIsOnline(navigator.onLine);
    };

    // Configurar listeners
    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);

    // Estado inicial
    updateOnlineStatus();

    // Limpiar al desmontar
    return () => {
      window.removeEventListener('online', updateOnlineStatus);
      window.removeEventListener('offline', updateOnlineStatus);
    };
  }, []);

  // Formatear tiempo desde última sincronización
  const getLastSyncText = () => {
    if (!lastSyncTime) return 'Nunca';

    const now = Date.now();
    const diff = now - lastSyncTime;

    if (diff < 60000) {
      return 'Hace menos de un minuto';
    } else if (diff < 3600000) {
      return `Hace ${Math.floor(diff / 60000)} minutos`;
    } else if (diff < 86400000) {
      return `Hace ${Math.floor(diff / 3600000)} horas`;
    } else {
      return `Hace ${Math.floor(diff / 86400000)} días`;
    }
  };

  // Si no hay estadísticas, no mostrar nada
  if (!syncStats) {
    return null;
  }

  const hasPendingOperations = syncStats.totalPendingOperations > 0;
  const hasErrors = syncStats.errorLogsLast24h > 0;

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg">Sincronización Local</CardTitle>
          {isOnline ? (
            <Badge variant="outline" className="bg-green-50">
              <Wifi className="h-3 w-3 mr-1" /> En línea
            </Badge>
          ) : (
            <Badge variant="outline" className="bg-red-50">
              <WifiOff className="h-3 w-3 mr-1" /> Sin conexión
            </Badge>
          )}
        </div>
        <CardDescription>
          Estado de la sincronización con Supabase
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {!isOnline && (
          <div className="bg-amber-50 p-2 rounded-md flex items-center text-sm mb-4">
            <AlertTriangle className="h-4 w-4 mr-2 text-amber-500" />
            <span className="text-amber-700">Trabajando sin conexión</span>
          </div>
        )}

        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Última sincronización</span>
            <span>{getLastSyncText()}</span>
          </div>

          <div className="flex justify-between text-sm">
            <span>Operaciones pendientes</span>
            <span className={hasPendingOperations ? 'font-medium text-amber-600' : ''}>
              {syncStats.totalPendingOperations}
            </span>
          </div>

          <div className="flex justify-between text-sm">
            <span>Errores (24h)</span>
            <span className={hasErrors ? 'font-medium text-destructive' : ''}>
              {syncStats.errorLogsLast24h}
            </span>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          size="sm"
          onClick={() => syncNow()}
          disabled={isSyncing || !isOnline}
        >
          {isSyncing ? (
            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <RefreshCw className="mr-2 h-4 w-4" />
          )}
          {isSyncing ? 'Sincronizando...' : 'Sincronizar'}
        </Button>

        <Button variant="ghost" size="sm" asChild>
          <Link href="/dashboard/admin/sync">
            <Database className="mr-2 h-4 w-4" />
            Gestionar
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
}
