-- Este script debe ejecutarse desde el Editor SQL en el panel de administración de Supabase
-- Configura los buckets de almacenamiento y las políticas de seguridad necesarias

-- 1. Crear buckets si no existen
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types, owner, created_at, updated_at)
VALUES 
  ('documents', 'documents', false, 10485760, ARRAY['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/msword', 'text/plain', 'image/jpeg', 'image/png']::text[], NULL, NOW(), NOW()),
  ('projects', 'projects', false, 10485760, ARRAY['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/msword', 'text/plain']::text[], NULL, NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- 2. Habilitar RLS en la tabla de objetos
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- 3. Eliminar políticas existentes para evitar conflictos
DROP POLICY IF EXISTS "Allow authenticated users to read from buckets" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to upload to buckets" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to update own objects" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to delete own objects" ON storage.objects;
DROP POLICY IF EXISTS "Allow admins full access" ON storage.objects;

-- 4. Crear políticas para permitir a usuarios autenticados leer de los buckets
CREATE POLICY "Allow authenticated users to read from buckets"
  ON storage.objects FOR SELECT
  USING (auth.role() = 'authenticated');

-- 5. Crear política para permitir a usuarios autenticados subir a los buckets
CREATE POLICY "Allow authenticated users to upload to buckets"
  ON storage.objects FOR INSERT
  WITH CHECK (auth.role() = 'authenticated');

-- 6. Crear política para permitir a usuarios autenticados actualizar sus propios objetos
CREATE POLICY "Allow authenticated users to update own objects"
  ON storage.objects FOR UPDATE
  USING (
    auth.role() = 'authenticated' AND
    (owner = auth.uid() OR EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid() AND role = 'admin'
    ))
  );

-- 7. Crear política para permitir a usuarios autenticados eliminar sus propios objetos
CREATE POLICY "Allow authenticated users to delete own objects"
  ON storage.objects FOR DELETE
  USING (
    auth.role() = 'authenticated' AND
    (owner = auth.uid() OR EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid() AND role = 'admin'
    ))
  );

-- 8. Crear política para permitir a administradores acceso completo
CREATE POLICY "Allow admins full access"
  ON storage.objects
  USING (
    auth.role() = 'authenticated' AND
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- 9. Configurar la tabla de documentos
CREATE TABLE IF NOT EXISTS public.documents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  filename TEXT NOT NULL,
  file_path TEXT,
  file_url TEXT,
  file_size BIGINT,
  file_type TEXT,
  description TEXT,
  category TEXT,
  project_id UUID REFERENCES public.projects(id) ON DELETE SET NULL,
  work_order_id UUID REFERENCES public.work_orders(id) ON DELETE SET NULL,
  public_url TEXT,
  uploaded_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 10. Habilitar RLS en la tabla de documentos
ALTER TABLE public.documents ENABLE ROW LEVEL SECURITY;

-- 11. Crear políticas para la tabla de documentos
DROP POLICY IF EXISTS "Allow authenticated users to read documents" ON public.documents;
DROP POLICY IF EXISTS "Allow authenticated users to insert documents" ON public.documents;
DROP POLICY IF EXISTS "Allow users to update own documents" ON public.documents;
DROP POLICY IF EXISTS "Allow users to delete own documents" ON public.documents;
DROP POLICY IF EXISTS "Allow admins full access to documents" ON public.documents;

CREATE POLICY "Allow authenticated users to read documents"
  ON public.documents FOR SELECT
  USING (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated users to insert documents"
  ON public.documents FOR INSERT
  WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Allow users to update own documents"
  ON public.documents FOR UPDATE
  USING (
    auth.role() = 'authenticated' AND
    (uploaded_by = auth.uid() OR EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid() AND role = 'admin'
    ))
  );

CREATE POLICY "Allow users to delete own documents"
  ON public.documents FOR DELETE
  USING (
    auth.role() = 'authenticated' AND
    (uploaded_by = auth.uid() OR EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid() AND role = 'admin'
    ))
  );

CREATE POLICY "Allow admins full access to documents"
  ON public.documents
  USING (
    auth.role() = 'authenticated' AND
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );
