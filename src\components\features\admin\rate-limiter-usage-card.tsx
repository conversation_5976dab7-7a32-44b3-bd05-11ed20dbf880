'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { AlertTriangle, Gauge } from 'lucide-react'
import Link from 'next/link'
import { rateLimiterService, OperationType, UsageStats } from '@/lib/services/rate-limiter-service'

/**
 * Componente para mostrar un resumen del uso de la API en el dashboard principal
 */
export function RateLimiterUsageCard() {
  const [stats, setStats] = useState<UsageStats | null>(null)
  
  // Cargar estadísticas iniciales y suscribirse a actualizaciones
  useEffect(() => {
    // Obtener estadísticas iniciales
    setStats(rateLimiterService.getUsageStats())
    
    // Suscribirse a actualizaciones
    const unsubscribe = rateLimiterService.addStatsListener((newStats) => {
      setStats(newStats)
    })
    
    // Limpiar suscripción al desmontar
    return () => {
      unsubscribe()
    }
  }, [])

  // Si no hay estadísticas, mostrar carga
  if (!stats) {
    return null
  }

  // Calcular estadísticas generales
  const operationTypes = Object.keys(stats.counts) as OperationType[]
  
  // Calcular el total de operaciones en la última hora
  const totalOperationsLastHour = operationTypes.reduce(
    (total, type) => total + stats.counts[type].lastHour, 
    0
  )
  
  // Calcular el total de errores
  const totalErrors = operationTypes.reduce(
    (total, type) => total + stats.errors[type], 
    0
  )
  
  // Determinar si hay algún tipo de operación cerca del límite
  const hasHighUsage = operationTypes.some(type => {
    const config = rateLimiterService.getConfig(type)
    if (!config) return false
    
    const hourlyUsage = stats.counts[type].lastHour / config.operationsPerHour
    return hourlyUsage > 0.8 // 80% del límite
  })

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg">API Usage</CardTitle>
          <Badge variant={hasHighUsage ? 'destructive' : (totalErrors > 0 ? 'secondary' : 'outline')}>
            {hasHighUsage ? 'High Usage' : (totalErrors > 0 ? `${totalErrors} errors` : 'Normal')}
          </Badge>
        </div>
        <CardDescription>
          Supabase API usage in the last hour
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {hasHighUsage && (
          <div className="bg-destructive/10 p-2 rounded-md flex items-center text-sm mb-4">
            <AlertTriangle className="h-4 w-4 mr-2 text-destructive" />
            <span className="text-destructive">Approaching rate limits</span>
          </div>
        )}
        
        <div className="space-y-4">
          {operationTypes.map(type => {
            const config = rateLimiterService.getConfig(type)
            if (!config) return null
            
            const hourlyUsage = (stats.counts[type].lastHour / config.operationsPerHour) * 100
            const isHigh = hourlyUsage > 80
            const isWarning = hourlyUsage > 60 && hourlyUsage <= 80
            
            return (
              <div key={type} className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span className="capitalize">{type}</span>
                  <span className={isHigh ? 'text-destructive font-medium' : ''}>
                    {stats.counts[type].lastHour} / {config.operationsPerHour}
                  </span>
                </div>
                <Progress 
                  value={hourlyUsage} 
                  className={
                    isHigh 
                      ? 'bg-destructive/20' 
                      : isWarning 
                        ? 'bg-warning/20' 
                        : ''
                  }
                />
              </div>
            )
          })}
        </div>
      </CardContent>
      <CardFooter>
        <Button variant="outline" className="w-full" asChild>
          <Link href="/dashboard/admin/rate-limiter">
            <Gauge className="mr-2 h-4 w-4" />
            View Rate Limiter Dashboard
          </Link>
        </Button>
      </CardFooter>
    </Card>
  )
}
