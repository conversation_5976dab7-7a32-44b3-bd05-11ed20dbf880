/**
 * Middleware de autenticación para proteger rutas
 */
import { NextResponse, type NextRequest } from 'next/server'
import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { type Database } from '@/lib/supabase/types'

// Lista de rutas públicas que no requieren autenticación
const PUBLIC_ROUTES = [
  '/',
  '/auth/login',
  '/auth/register',
  '/auth/forgot-password',
  '/auth/callback',
  '/api',
  '/favicon.ico',
  '/_next',
]

// Función para verificar si una ruta es pública
function isPublicRoute(pathname: string): boolean {
  return PUBLIC_ROUTES.some(route =>
    pathname === route ||
    (route.endsWith('/') ? pathname.startsWith(route) : pathname.startsWith(route + '/'))
  )
}

/**
 * Middleware de autenticación
 */
export async function authMiddleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  const response = NextResponse.next()

  // Permitir acceso a rutas públicas sin verificación
  if (isPublicRoute(pathname)) {
    return response
  }

  // Verificar si hay una sesión en las cookies antes de crear el cliente Supabase
  const hasSessionCookie = request.cookies.has('sb-access-token') ||
                          request.cookies.has('sb-refresh-token')

  // Si no hay cookies de sesión y estamos en una ruta protegida, redirigir inmediatamente
  if (!hasSessionCookie && pathname.startsWith('/dashboard')) {
    const redirectUrl = new URL('/auth/login', request.url)
    redirectUrl.searchParams.set('from', pathname)
    redirectUrl.searchParams.set('session_error', 'no_session')
    return NextResponse.redirect(redirectUrl)
  }

  // Para rutas protegidas con cookies de sesión, verificar la validez de la sesión
  try {
    // Crear cliente de Supabase con manejo optimizado de cookies
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return request.cookies.get(name)?.value
          },
          set(name: string, value: string, options: CookieOptions) {
            const cookieOptions = {
              ...options,
              maxAge: options.maxAge || 60 * 60 * 24 * 30, // 30 días por defecto
              path: options.path || '/',
            }

            request.cookies.set({
              name,
              value,
              ...cookieOptions,
            })
            response.cookies.set({
              name,
              value,
              ...cookieOptions,
            })
          },
          remove(name: string, options: CookieOptions) {
            request.cookies.delete(name)
            response.cookies.delete(name)
          },
        },
        global: {
          headers: {
            'Cache-Control': 'no-store, max-age=0',
          },
        },
      }
    )

    // Verificar la sesión solo para rutas protegidas
    if (pathname.startsWith('/dashboard')) {
      const { data, error } = await supabase.auth.getSession()

      if (error) {
        const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

        console.error('Error al verificar sesión en middleware:', errorMessage)
        const redirectUrl = new URL('/auth/login', request.url)
        redirectUrl.searchParams.set('from', pathname)
        redirectUrl.searchParams.set('session_error', 'invalid_session')
        return NextResponse.redirect(redirectUrl)
      }

      // Si no hay sesión válida, redirigir al login
      if (!data.session) {
        const redirectUrl = new URL('/auth/login', request.url)
        redirectUrl.searchParams.set('from', pathname)
        redirectUrl.searchParams.set('session_error', 'expired_session')
        return NextResponse.redirect(redirectUrl)
      }

      // Verificar si la sesión está a punto de expirar (menos de 5 minutos)
      const expiresAt = data.session.expires_at
      const expiresInMs = expiresAt ? (expiresAt * 1000) - Date.now() : 0

      if (expiresInMs < 5 * 60 * 1000 && expiresInMs > 0) {
        try {
          // Intentar refrescar la sesión
          await supabase.auth.refreshSession()
        } catch (refreshError) {
          console.warn('No se pudo refrescar la sesión en middleware:', refreshError)
        }
      }
    }

    return response
  } catch (error) {
    console.error('Error inesperado en middleware:', error)
    return response
  }
}
