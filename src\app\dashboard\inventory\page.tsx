import { <PERSON>ada<PERSON> } from "next"
import { createClient } from "@/lib/supabase/client"
import { InventoryTable } from "@/components/features/inventory/inventory-table"
import { Button } from "@/components/ui/button"
import Link from "next/link"
import { Plus } from "lucide-react"

export const metadata: Metadata = {
  title: "Inventario | AdminCore ",
  description: "Gestión de inventario y materiales",
}

async function getInventoryItems() {
  const supabase: unknown = createClient()
  let items = [];
  let error = null;
  // Try to use .order if available, fallback to select only (for mock clients)
  if (typeof supabase.from('inventory_items').select('*').order === 'function') {
    const result = await supabase
      .from('inventory_items')
      .select('*')
      .order('name');
    items = result.data;
    error = result.error;
  } else {
    const result = await supabase
      .from('inventory_items')
      .select('*');
    items = result.data;
    error = result.error;
  }

  if (error) {
    console.error("Error al cargar inventario:", error)
    return []
  }

  return items || []
}

export default async function InventoryPage() {
  const inventoryItems = await getInventoryItems()

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Inventario</h2>
          <p className="text-muted-foreground mt-2">
            Gestiona los materiales e insumos disponibles para tus proyectos y órdenes de trabajo.
          </p>
        </div>
        <Button asChild>
          <Link href="/dashboard/inventory/new">
            <Plus className="mr-2 h-4 w-4" /> Nuevo Item
          </Link>
        </Button>
      </div>

      <InventoryTable data={inventoryItems} />

      {inventoryItems.length === 0 && (
        <div className="text-center py-10 border rounded-md bg-muted/20">
          <h3 className="text-lg font-medium mb-2">No hay items en el inventario</h3>
          <p className="text-muted-foreground mb-4">
            Comienza agregando materiales e insumos a tu inventario.
          </p>
          <Button asChild>
            <Link href="/dashboard/inventory/new">
              <Plus className="mr-2 h-4 w-4" /> Agregar Primer Item
            </Link>
          </Button>
        </div>
      )}
    </div>
  )
}
