'use client'

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { ServiceContractsTable } from '@/components/service-contracts/service-contracts-table';
import { ContractMetricsDashboard } from '@/components/service-contracts/contract-metrics-dashboard';
import { PageHeader } from '@/components/page-header';
import { Plus, Search, Filter, BarChart } from 'lucide-react';
import { useRouter } from 'next/navigation';

export default function ServiceContractsPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');
  const [clientFilter, setClientFilter] = useState('all');
  const router = useRouter();

  const handleCreateNew = () => {
    router.push('/dashboard/service-contracts/new');
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <PageHeader
        title="Contratos de Servicio"
        description="Gestione los contratos de servicio para sus clientes"
        actions={
          <Button onClick={handleCreateNew}>
            <Plus className="mr-2 h-4 w-4" />
            Nuevo Contrato
          </Button>
        }
      />

      {/* Métricas */}
      <ContractMetricsDashboard />

      {/* Filtros */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Buscar contratos..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex gap-2">
          <div className="w-40">
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Tipo" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos</SelectItem>
                <SelectItem value="basic">Básico</SelectItem>
                <SelectItem value="standard">Estándar</SelectItem>
                <SelectItem value="premium">Premium</SelectItem>
                <SelectItem value="custom">Personalizado</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="w-40">
            <Select value={clientFilter} onValueChange={setClientFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Cliente" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos</SelectItem>
                {/* Aquí se cargarían dinámicamente los clientes */}
                <SelectItem value="client1">Cliente 1</SelectItem>
                <SelectItem value="client2">Cliente 2</SelectItem>
                <SelectItem value="client3">Cliente 3</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <Button variant="outline" size="icon">
            <Filter className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Pestañas */}
      <Tabs defaultValue="active" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="active">Activos</TabsTrigger>
          <TabsTrigger value="all">Todos</TabsTrigger>
          <TabsTrigger value="expiring">Por vencer</TabsTrigger>
          <TabsTrigger value="expired">Vencidos</TabsTrigger>
          <TabsTrigger value="analytics">
            <BarChart className="h-4 w-4 mr-2" />
            Análisis
          </TabsTrigger>
        </TabsList>
        <TabsContent value="active" className="mt-0">
          <ServiceContractsTable includeExpired={false} limit={20} />
        </TabsContent>
        <TabsContent value="all" className="mt-0">
          <ServiceContractsTable includeExpired={true} limit={20} />
        </TabsContent>
        <TabsContent value="expiring" className="mt-0">
          {/* Aquí iría una tabla filtrada para mostrar solo los contratos por vencer */}
          <ServiceContractsTable includeExpired={false} limit={20} />
        </TabsContent>
        <TabsContent value="expired" className="mt-0">
          {/* Aquí iría una tabla filtrada para mostrar solo los contratos vencidos */}
          <ServiceContractsTable includeExpired={true} limit={20} />
        </TabsContent>
        <TabsContent value="analytics" className="mt-0">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-4">
              <div className="bg-muted/50 p-6 rounded-lg">
                <h3 className="text-lg font-medium mb-2">Análisis de Renovación</h3>
                <p className="text-muted-foreground mb-4">
                  Análisis de tasas de renovación de contratos y factores que influyen en la renovación.
                </p>
                <div className="h-40 bg-muted/70 rounded-md flex items-center justify-center">
                  <p className="text-muted-foreground">Gráfico de renovación (próximamente)</p>
                </div>
              </div>
              <div className="bg-muted/50 p-6 rounded-lg">
                <h3 className="text-lg font-medium mb-2">Análisis de Rentabilidad</h3>
                <p className="text-muted-foreground mb-4">
                  Análisis de rentabilidad por tipo de contrato y cliente.
                </p>
                <div className="h-40 bg-muted/70 rounded-md flex items-center justify-center">
                  <p className="text-muted-foreground">Gráfico de rentabilidad (próximamente)</p>
                </div>
              </div>
            </div>
            <div className="space-y-4">
              <div className="bg-muted/50 p-6 rounded-lg">
                <h3 className="text-lg font-medium mb-2">Análisis de Servicios</h3>
                <p className="text-muted-foreground mb-4">
                  Análisis de servicios realizados bajo contratos y su impacto en la satisfacción del cliente.
                </p>
                <div className="h-40 bg-muted/70 rounded-md flex items-center justify-center">
                  <p className="text-muted-foreground">Gráfico de servicios (próximamente)</p>
                </div>
              </div>
              <div className="bg-muted/50 p-6 rounded-lg">
                <h3 className="text-lg font-medium mb-2">Proyección de Ingresos</h3>
                <p className="text-muted-foreground mb-4">
                  Proyección de ingresos futuros basados en contratos activos y tasas de renovación.
                </p>
                <div className="h-40 bg-muted/70 rounded-md flex items-center justify-center">
                  <p className="text-muted-foreground">Gráfico de proyección (próximamente)</p>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
