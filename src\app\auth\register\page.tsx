import { Metadata } from "next"
import Image from "next/image"
import Link from "next/link"
import { RegisterForm } from "@/components/features/auth/register-form"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"

export const metadata: Metadata = {
  title: "Registro | AdminCore ",
  description: "Crea una nueva cuenta en AdminCore ",
}

export default function RegisterPage() {
  return (
    <div className="container flex h-screen w-screen flex-col items-center justify-center">
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px] md:w-[450px]">
        <div className="flex flex-col space-y-2 text-center">
          <Image
            src="/logo.svg"
            width={80}
            height={80}
            alt="AdminCore  Logo"
            className="mx-auto"
          />
          <h1 className="text-2xl font-semibold tracking-tight">
            Crear una cuenta
          </h1>
          <p className="text-sm text-muted-foreground">
            Ingresa tus datos para crear una nueva cuenta
          </p>
        </div>

        <Card>
          <CardHeader className="space-y-1">
            <CardTitle className="text-xl">Registro</CardTitle>
            <CardDescription>
              Completa el formulario para crear tu cuenta
            </CardDescription>
          </CardHeader>
          <CardContent className="p-6">
            <RegisterForm />
          </CardContent>
          <CardFooter className="flex flex-col space-y-4 border-t px-6 py-4">
            <div className="text-sm text-muted-foreground text-center">
              Al registrarte, aceptas nuestros{" "}
              <Link href="/terms" className="underline underline-offset-4 hover:text-primary">
                Términos de servicio
              </Link>{" "}
              y{" "}
              <Link href="/privacy" className="underline underline-offset-4 hover:text-primary">
                Política de privacidad
              </Link>
              .
            </div>
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}
