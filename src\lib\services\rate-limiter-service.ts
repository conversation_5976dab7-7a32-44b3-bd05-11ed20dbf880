/**
 * @file Servicio para gestionar límites de tasa en peticiones a Supabase
 * @description Este servicio implementa un sistema de limitación de tasa para evitar exceder los límites del plan gratuito de Supabase
 */

// Tipos de operaciones que pueden ser limitadas
export enum OperationType {
  AUTH = 'auth',
  DATABASE = 'database',
  STORAGE = 'storage',
  FUNCTIONS = 'functions',
  REALTIME = 'realtime',
}

// Prioridades para las operaciones
export enum OperationPriority {
  LOW = 0,
  MEDIUM = 1,
  HIGH = 2,
  CRITICAL = 3,
}

// Interfaz para una operación en cola
export interface QueuedOperation {
  id: string;
  type: OperationType;
  priority: OperationPriority;
  timestamp: number;
  execute: () => Promise<any>;
  retryCount: number;
  maxRetries: number;
}

// Configuración de límites para cada tipo de operación
export interface RateLimitConfig {
  // Número máximo de operaciones por minuto
  operationsPerMinute: number;
  // Número máximo de operaciones por hora
  operationsPerHour: number;
  // Número máximo de operaciones por día
  operationsPerDay: number;
  // Tamaño máximo de la cola
  maxQueueSize: number;
  // Tiempo de espera entre operaciones (ms)
  throttleMs: number;
}

// Estadísticas de uso
export interface UsageStats {
  // Contador de operaciones por tipo y periodo
  counts: {
    [key in OperationType]: {
      lastMinute: number;
      lastHour: number;
      lastDay: number;
      total: number;
    };
  };
  // Timestamp del último reseteo de contadores
  lastReset: {
    minute: number;
    hour: number;
    day: number;
  };
  // Errores por tipo
  errors: {
    [key in OperationType]: number;
  };
  // Operaciones en cola actualmente
  queueSize: number;
  // Tiempo promedio de espera en cola (ms)
  averageWaitTime: number;
}

/**
 * Clase que implementa un servicio de limitación de tasa para peticiones a Supabase
 */
export class RateLimiterService {
  private static instance: RateLimiterService;
  private config: Map<OperationType, RateLimitConfig>;
  private usage: UsageStats;
  private queue: QueuedOperation[];
  private isProcessingQueue: boolean;
  private storage: Storage | null = null;
  private listeners: Array<(stats: UsageStats) => void> = [];

  // Configuración predeterminada para el plan gratuito de Supabase
  private defaultConfig: Record<OperationType, RateLimitConfig> = {
    [OperationType.AUTH]: {
      operationsPerMinute: 30,
      operationsPerHour: 300,
      operationsPerDay: 2000,
      maxQueueSize: 100,
      throttleMs: 2000, // 2 segundos entre operaciones de autenticación
    },
    [OperationType.DATABASE]: {
      operationsPerMinute: 60,
      operationsPerHour: 1000,
      operationsPerDay: 10000,
      maxQueueSize: 200,
      throttleMs: 500, // 0.5 segundos entre operaciones de base de datos
    },
    [OperationType.STORAGE]: {
      operationsPerMinute: 20,
      operationsPerHour: 200,
      operationsPerDay: 1000,
      maxQueueSize: 50,
      throttleMs: 3000, // 3 segundos entre operaciones de almacenamiento
    },
    [OperationType.FUNCTIONS]: {
      operationsPerMinute: 10,
      operationsPerHour: 100,
      operationsPerDay: 500,
      maxQueueSize: 30,
      throttleMs: 5000, // 5 segundos entre invocaciones de funciones
    },
    [OperationType.REALTIME]: {
      operationsPerMinute: 30,
      operationsPerHour: 300,
      operationsPerDay: 2000,
      maxQueueSize: 50,
      throttleMs: 1000, // 1 segundo entre operaciones de tiempo real
    },
  };

  /**
   * Constructor privado para implementar el patrón Singleton
   */
  private constructor() {
    this.config = new Map();
    this.queue = [];
    this.isProcessingQueue = false;

    // Inicializar configuración con valores predeterminados
    Object.entries(this.defaultConfig).forEach(([type, config]) => {
      this.config.set(type as OperationType, { ...config });
    });

    // Inicializar estadísticas de uso
    this.usage = {
      counts: {
        [OperationType.AUTH]: { lastMinute: 0, lastHour: 0, lastDay: 0, total: 0 },
        [OperationType.DATABASE]: { lastMinute: 0, lastHour: 0, lastDay: 0, total: 0 },
        [OperationType.STORAGE]: { lastMinute: 0, lastHour: 0, lastDay: 0, total: 0 },
        [OperationType.FUNCTIONS]: { lastMinute: 0, lastHour: 0, lastDay: 0, total: 0 },
        [OperationType.REALTIME]: { lastMinute: 0, lastHour: 0, lastDay: 0, total: 0 },
      },
      lastReset: {
        minute: Date.now(),
        hour: Date.now(),
        day: Date.now(),
      },
      errors: {
        [OperationType.AUTH]: 0,
        [OperationType.DATABASE]: 0,
        [OperationType.STORAGE]: 0,
        [OperationType.FUNCTIONS]: 0,
        [OperationType.REALTIME]: 0,
      },
      queueSize: 0,
      averageWaitTime: 0,
    };

    // Inicializar almacenamiento si estamos en el navegador
    if (typeof window !== 'undefined') {
      this.storage = window.localStorage;
      this.loadState();
    }

    // Configurar intervalos para resetear contadores
    this.setupResetIntervals();
  }

  /**
   * Obtiene la instancia única del servicio (patrón Singleton)
   */
  public static getInstance(): RateLimiterService {
    // Solo crear la instancia en el cliente
    if (typeof window === 'undefined') {
      // Devolver un proxy que no hace nada en el servidor
      return new Proxy({} as RateLimiterService, {
        get: (target, prop) => {
          // Proporcionar implementaciones de no-op para métodos comunes
          if (prop === 'then') return undefined;
          if (prop === 'enqueue') {
            return (type: unknown, operation: () => Promise<any>) => operation();
          }
          return () => {};
        }
      });
    }

    // En el cliente, usar el patrón singleton normal
    if (!RateLimiterService.instance) {
      RateLimiterService.instance = new RateLimiterService();
    }
    return RateLimiterService.instance;
  }

  /**
   * Configura los intervalos para resetear los contadores de uso
   */
  private setupResetIntervals(): void {
    // Resetear contadores por minuto
    setInterval(() => {
      const now = Date.now();
      if (now - this.usage.lastReset.minute >= 60000) {
        Object.keys(this.usage.counts).forEach((type) => {
          this.usage.counts[type as OperationType].lastMinute = 0;
        });
        this.usage.lastReset.minute = now;
        this.saveState();
        this.notifyListeners();
      }
    }, 10000); // Verificar cada 10 segundos

    // Resetear contadores por hora
    setInterval(() => {
      const now = Date.now();
      if (now - this.usage.lastReset.hour >= 3600000) {
        Object.keys(this.usage.counts).forEach((type) => {
          this.usage.counts[type as OperationType].lastHour = 0;
        });
        this.usage.lastReset.hour = now;
        this.saveState();
        this.notifyListeners();
      }
    }, 60000); // Verificar cada minuto

    // Resetear contadores por día
    setInterval(() => {
      const now = Date.now();
      if (now - this.usage.lastReset.day >= 86400000) {
        Object.keys(this.usage.counts).forEach((type) => {
          this.usage.counts[type as OperationType].lastDay = 0;
        });
        this.usage.lastReset.day = now;
        this.saveState();
        this.notifyListeners();
      }
    }, 3600000); // Verificar cada hora
  }

  /**
   * Guarda el estado actual en el almacenamiento local
   */
  private saveState(): void {
    if (this.storage) {
      try {
        this.storage.setItem('rateLimiter_config', JSON.stringify(Array.from(this.config.entries())));
        this.storage.setItem('rateLimiter_usage', JSON.stringify(this.usage));
      } catch (error) {
        console.error('Error al guardar estado del limitador de tasa:', error);
      }
    }
  }

  /**
   * Carga el estado desde el almacenamiento local
   */
  private loadState(): void {
    if (this.storage) {
      try {
        const configStr = this.storage.getItem('rateLimiter_config');
        const usageStr = this.storage.getItem('rateLimiter_usage');

        if (configStr) {
          const configEntries = JSON.parse(configStr);
          this.config = new Map(configEntries);
        }

        if (usageStr) {
          const savedUsage = JSON.parse(usageStr);
          // Fusionar con la estructura actual para manejar cambios en la estructura
          this.usage = {
            ...this.usage,
            ...savedUsage,
            // Asegurar que todos los tipos de operación estén presentes
            counts: {
              ...this.usage.counts,
              ...(savedUsage.counts || {}),
            },
            errors: {
              ...this.usage.errors,
              ...(savedUsage.errors || {}),
            },
          };
        }
      } catch (error) {
        console.error('Error al cargar estado del limitador de tasa:', error);
      }
    }
  }

  /**
   * Notifica a los listeners sobre cambios en las estadísticas
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener({ ...this.usage });
      } catch (error) {
        console.error('Error en listener de estadísticas:', error);
      }
    });
  }

  /**
   * Registra una operación en las estadísticas
   */
  private recordOperation(type: OperationType, isError: boolean = false): void {
    // Incrementar contadores
    this.usage.counts[type].lastMinute++;
    this.usage.counts[type].lastHour++;
    this.usage.counts[type].lastDay++;
    this.usage.counts[type].total++;

    // Registrar error si corresponde
    if (isError) {
      this.usage.errors[type]++;
    }

    // Actualizar estadísticas de cola
    this.usage.queueSize = this.queue.length;

    // Guardar estado y notificar
    this.saveState();
    this.notifyListeners();
  }

  /**
   * Verifica si se ha alcanzado el límite para un tipo de operación
   */
  private isRateLimited(type: OperationType): boolean {
    const config = this.config.get(type);
    const counts = this.usage.counts[type];

    if (!config) return false;

    return (
      counts.lastMinute >= config.operationsPerMinute ||
      counts.lastHour >= config.operationsPerHour ||
      counts.lastDay >= config.operationsPerDay
    );
  }

  /**
   * Procesa la cola de operaciones
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessingQueue || this.queue.length === 0) return;

    this.isProcessingQueue = true;

    try {
      // Ordenar la cola por prioridad (mayor primero)
      this.queue.sort((a, b) => b.priority - a.priority);

      // Procesar la primera operación
      const operation = this.queue.shift()!;
      const type = operation.type;
      const config = this.config.get(type);

      if (!config) {
        console.error(`Configuración no encontrada para el tipo de operación: ${type}`);
        this.isProcessingQueue = false;
        return;
      }

      // Verificar si estamos limitados por tasa
      if (this.isRateLimited(type)) {
        // Si aún podemos reintentar, volver a poner en la cola
        if (operation.retryCount < operation.maxRetries) {
          operation.retryCount++;
          this.queue.push(operation);
        } else {
          console.warn(`Operación descartada por límite de tasa: ${operation.id} (tipo: ${type})`);
          this.recordOperation(type, true);
        }
      } else {
        try {
          // Ejecutar la operación
          await operation.execute();
          this.recordOperation(type);
        } catch (error) {
          console.error(`Error al ejecutar operación ${operation.id}:`, error);
          this.recordOperation(type, true);

          // Reintentar si es posible
          if (operation.retryCount < operation.maxRetries) {
            operation.retryCount++;
            this.queue.push(operation);
          }
        }

        // Esperar el tiempo de throttling antes de procesar la siguiente operación
        await new Promise(resolve => setTimeout(resolve, config.throttleMs));
      }
    } finally {
      this.isProcessingQueue = false;

      // Continuar procesando la cola si hay más operaciones
      if (this.queue.length > 0) {
        this.processQueue();
      }
    }
  }

  /**
   * Encola una operación para su ejecución
   */
  public async enqueue<T>(
    type: OperationType,
    operation: () => Promise<T>,
    options: {
      priority?: OperationPriority;
      maxRetries?: number;
    } = {}
  ): Promise<T> {
    const {
      priority = OperationPriority.MEDIUM,
      maxRetries = 3,
    } = options;

    const config = this.config.get(type);
    if (!config) {
      throw new Error(`Tipo de operación no configurado: ${type}`);
    }

    // Si no estamos limitados por tasa y la cola está vacía, ejecutar inmediatamente
    if (!this.isRateLimited(type) && this.queue.length === 0 && !this.isProcessingQueue) {
      try {
        const result = await operation();
        this.recordOperation(type);
        return result;
      } catch (error) {
        this.recordOperation(type, true);
        throw error;
      }
    }

    // Verificar si la cola está llena
    if (this.queue.length >= config.maxQueueSize) {
      // Si la prioridad es alta, eliminar una operación de baja prioridad
      if (priority >= OperationPriority.HIGH) {
        const lowPriorityIndex = this.queue.findIndex(op => op.priority === OperationPriority.LOW);
        if (lowPriorityIndex >= 0) {
          this.queue.splice(lowPriorityIndex, 1);
        } else {
          throw new Error(`Cola llena para operaciones de tipo: ${type}`);
        }
      } else {
        throw new Error(`Cola llena para operaciones de tipo: ${type}`);
      }
    }

    // Crear una promesa que se resolverá cuando la operación se complete
    return new Promise<T>((resolve, reject) => {
      const id = `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Encolar la operación
      this.queue.push({
        id,
        type,
        priority,
        timestamp: Date.now(),
        retryCount: 0,
        maxRetries,
        execute: async () => {
          try {
            const result = await operation();
            resolve(result);
            return result;
          } catch (error) {
            reject(error);
            throw error;
          }
        },
      });

      // Actualizar estadísticas
      this.usage.queueSize = this.queue.length;
      this.notifyListeners();

      // Iniciar procesamiento de la cola si no está en curso
      if (!this.isProcessingQueue) {
        this.processQueue();
      }
    });
  }

  /**
   * Obtiene la configuración actual para un tipo de operación
   */
  public getConfig(type: OperationType): RateLimitConfig | undefined {
    return this.config.get(type);
  }

  /**
   * Actualiza la configuración para un tipo de operación
   */
  public updateConfig(type: OperationType, config: Partial<RateLimitConfig>): void {
    const currentConfig = this.config.get(type);
    if (currentConfig) {
      this.config.set(type, { ...currentConfig, ...config });
      this.saveState();
    }
  }

  /**
   * Restablece la configuración a los valores predeterminados
   */
  public resetConfig(): void {
    Object.entries(this.defaultConfig).forEach(([type, config]) => {
      this.config.set(type as OperationType, { ...config });
    });
    this.saveState();
  }

  /**
   * Obtiene las estadísticas de uso actuales
   */
  public getUsageStats(): UsageStats {
    return { ...this.usage };
  }

  /**
   * Limpia las estadísticas de uso
   */
  public clearStats(): void {
    Object.keys(this.usage.counts).forEach((type) => {
      const opType = type as OperationType;
      this.usage.counts[opType] = { lastMinute: 0, lastHour: 0, lastDay: 0, total: 0 };
      this.usage.errors[opType] = 0;
    });

    this.usage.lastReset = {
      minute: Date.now(),
      hour: Date.now(),
      day: Date.now(),
    };

    this.saveState();
    this.notifyListeners();
  }

  /**
   * Registra un listener para recibir actualizaciones de estadísticas
   */
  public addStatsListener(listener: (stats: UsageStats) => void): () => void {
    this.listeners.push(listener);

    // Notificar inmediatamente con las estadísticas actuales
    listener({ ...this.usage });

    // Devolver función para eliminar el listener
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index >= 0) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * Vacía la cola de operaciones
   */
  public clearQueue(): void {
    this.queue = [];
    this.usage.queueSize = 0;
    this.saveState();
    this.notifyListeners();
  }
}

// Exportar una instancia única del servicio
// Asegurarse de que solo se inicializa en el cliente
export const rateLimiterService = RateLimiterService.getInstance();
