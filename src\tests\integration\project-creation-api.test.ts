/**
 * @file Integration tests for project creation API
 * @description Tests the complete project creation flow through the API
 */

import { NextRequest } from 'next/server';
import { POST } from '@/app/api/projects/route';

// Mock Supabase client
import { vi } from 'vitest';
const mockSupabaseInsert = vi.fn();
const mockSupabaseSelect = vi.fn();
const mockSupabaseSingle = vi.fn();

vi.mock('@/lib/supabase/server-client', () => ({
  createClient: vi.fn(() => ({
    from: vi.fn(() => ({
      insert: mockSupabaseInsert.mockReturnValue({
        select: mockSupabaseSelect.mockReturnValue({
          single: mockSupabaseSingle
        })
      })
    }))
  }))
}));

vi.mock('next/headers', () => ({
  cookies: vi.fn(() => ({
    get: vi.fn((name: string) => ({ value: `mock-${name}-cookie` })),
    set: vi.fn(),
    remove: vi.fn(),
  })),
}));

describe('Project Creation API', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('POST /api/projects', () => {
    it('should create project with valid data', async () => {
      const validProjectData = {
        name: 'Test Project',
        description: 'Test Description',
        status: 'pending',
        start_date: '2024-01-01',
        end_date: '2024-12-31',
        client_id: null,
        budget: '10000',
        currency: 'CLP'
      };

      const mockCreatedProject = {
        id: 'test-uuid',
        ...validProjectData,
        root_path: '/',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      };

      mockSupabaseSingle.mockResolvedValue({
        data: mockCreatedProject,
        error: null
      });

      const request = new NextRequest('http://localhost:3000/api/projects', {
        method: 'POST',
        body: JSON.stringify(validProjectData),
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const response = await POST(request);
      const responseData = await response.json();

      expect(response.status).toBe(201);
      expect(responseData).toMatchObject({
        id: 'test-uuid',
        name: 'Test Project',
        status: 'pending'
      });
      expect(mockSupabaseInsert).toHaveBeenCalledWith([
        expect.objectContaining({
          name: 'Test Project',
          status: 'pending',
          root_path: '/'
        })
      ]);
    });

    it('should reject invalid status values', async () => {
      const invalidProjectData = {
        name: 'Test Project',
        status: 'invalid_status', // Invalid status
        currency: 'CLP'
      };

      const request = new NextRequest('http://localhost:3000/api/projects', {
        method: 'POST',
        body: JSON.stringify(invalidProjectData),
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const response = await POST(request);
      const responseData = await response.json();

      expect(response.status).toBe(400);
      expect(responseData.success).toBe(false);
      expect(responseData.errors).toBeDefined();
    });

    it('should handle all valid status values', async () => {
      const validStatuses = ['pending', 'planning', 'in_progress', 'completed', 'cancelled', 'on_hold'];

      for (const status of validStatuses) {
        const projectData = {
          name: `Test Project ${status}`,
          status,
          currency: 'CLP'
        };

        const mockCreatedProject = {
          id: `test-uuid-${status}`,
          ...projectData,
          root_path: '/',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z'
        };

        mockSupabaseSingle.mockResolvedValue({
          data: mockCreatedProject,
          error: null
        });

        const request = new NextRequest('http://localhost:3000/api/projects', {
          method: 'POST',
          body: JSON.stringify(projectData),
          headers: {
            'Content-Type': 'application/json'
          }
        });

        const response = await POST(request);
        const responseData = await response.json();

        expect(response.status).toBe(201);
        expect(responseData.status).toBe(status);
      }
    });

    it('should handle database constraint violations', async () => {
      const projectData = {
        name: 'Test Project',
        status: 'pending',
        currency: 'CLP'
      };

      // Mock constraint violation error
      mockSupabaseSingle.mockResolvedValue({
        data: null,
        error: {
          message: 'new row for relation "projects" violates check constraint "projects_status_check"',
          code: '23514'
        }
      });

      const request = new NextRequest('http://localhost:3000/api/projects', {
        method: 'POST',
        body: JSON.stringify(projectData),
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const response = await POST(request);
      const responseData = await response.json();

      expect(response.status).toBe(500);
      expect(responseData.error).toBeDefined();
    });

    it('should require name field', async () => {
      const projectDataWithoutName = {
        status: 'pending',
        currency: 'CLP'
      };

      const request = new NextRequest('http://localhost:3000/api/projects', {
        method: 'POST',
        body: JSON.stringify(projectDataWithoutName),
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const response = await POST(request);
      const responseData = await response.json();

      expect(response.status).toBe(400);
      expect(responseData.success).toBe(false);
      expect(responseData.errors).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            field: 'name',
            message: expect.stringContaining('obligatorio')
          })
        ])
      );
    });
  });
});
