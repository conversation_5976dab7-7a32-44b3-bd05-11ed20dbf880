/**
 * @ai-file-description: "Utility for extracting text from different document types"
 * @ai-related-files: ["document-analyzer.ts", "pdf-worker.ts", "canvas-fallback.ts"]
 * @ai-owner: "File-Based Projects"
 */

import { extractTextFromPdf } from './pdf-worker';
import { initCanvasFallback } from './canvas-fallback';
import mammoth from 'mammoth';

// Initialize canvas fallback if needed
if (typeof window !== 'undefined') {
  initCanvasFallback();
}

/**
 * Extracts text from a document based on its file type
 *
 * @param fileUrl URL of the document
 * @param fileType Type of the document (pdf, docx, txt, etc.)
 * @returns Extracted text content
 */
export async function extractTextFromDocument(fileUrl: string, fileType: string): Promise<string> {
  console.log('Extracting text from document:', { fileUrl, fileType });

  try {
    // Normalize file type to lowercase and remove dot if present
    const normalizedFileType = fileType.toLowerCase().replace('.', '');

    // Check if file URL is valid
    let validatedUrl: string;
    try {
      validatedUrl = fileUrl.startsWith('http') ? fileUrl : fileUrl.replace('file://', '');
    } catch (urlError) {
      console.error('Invalid URL format:', urlError);
      return `Unable to process document due to invalid URL format. Please try uploading the document again.`;
    }

    console.log(`Extracting text from ${normalizedFileType} document at URL: ${validatedUrl}`);

    // Extract text based on file type
    let extractedText = '';
    switch (normalizedFileType) {
      case 'pdf':
      case 'application/pdf':
        extractedText = await extractTextFromPdf(validatedUrl);
        break;
      case 'docx':
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        extractedText = await extractTextFromDocx(validatedUrl);
        break;
      case 'txt':
      case 'text/plain':
        extractedText = await extractTextFromTxt(validatedUrl);
        break;
      default:
        console.warn(`Unsupported file type: ${fileType}, using fallback text extraction`);
        extractedText = await extractTextFromTxt(validatedUrl);
    }

    if (!extractedText || extractedText.trim() === '') {
      console.warn('Extracted text is empty');
      return `El documento parece estar vacío o no se pudo procesar. Por favor, verifique el documento e intente de nuevo.`;
    }

    console.log(`Successfully extracted ${extractedText.length} characters of text`);
    return extractedText;
  } catch (error) {
    console.error('Error extracting text from document:', error);
    return `Error al extraer texto del documento: ${error instanceof Error ? error.message : 'Error desconocido'}. Por favor, intente con otro documento o contacte al soporte.`;
  }
}

/**
 * Extracts text from a DOCX document
 */
async function extractTextFromDocx(fileUrl: string): Promise<string> {
  try {
    const response = await fetch(fileUrl);
    const buffer = await response.arrayBuffer();
    const result = await mammoth.extractRawText({ arrayBuffer: buffer });
    return result.value;
  } catch (error) {
    console.error('Error extracting text from DOCX:', error);
    throw error;
  }
}

/**
 * Extracts text from a TXT document
 */
async function extractTextFromTxt(fileUrl: string): Promise<string> {
  try {
    const response = await fetch(fileUrl);
    const text = await response.text();
    return text;
  } catch (error) {
    console.error('Error extracting text from TXT:', error);
    throw error;
  }
}
