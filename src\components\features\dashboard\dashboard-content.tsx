"use client"

import { useState, useEffect, Suspense, lazy } from "react"
import { DateRange } from "react-day-picker"
import { Skeleton } from "@/components/ui/skeleton"
import { Folder<PERSON>pen, ClipboardList, FileText, Users, Wrench, Calendar } from "lucide-react"
import { useSupabaseSession } from "@/hooks/use-supabase-session"
import { MetricCard } from "./metric-card"
import { DashboardFilters } from "./dashboard-filters"
import { useSupabase } from "@/hooks/use-supabase"
import { ErrorNotification } from "./error-notification"
import { DashboardFixButton } from "@/components/dashboard/dashboard-fix-button"
import { getDashboardData, DashboardData } from "@/lib/actions/dashboard-actions"
import { ActivityList, type Activity } from "./activity-list"
import { TaskList, type Task } from "./task-list"
import {
  ProjectRow,
  UserRow,
  ServiceRequestRow,
  User,
  MonthlyStats,
  MetricsState,
  mapUserRowToUser,
  mapNotificationToActivity,
  mapWorkOrderTaskToTask
} from "@/lib/types/dashboard"

// Lazy loading para componentes pesados
const LineChart = lazy(() => import("@/components/features/dashboard/line-chart").then(mod => ({ default: mod.LineChart })))
const AreaChart = lazy(() => import("@/components/features/dashboard/area-chart").then(mod => ({ default: mod.AreaChart })))
const BarChart = lazy(() => import("@/components/features/dashboard/bar-chart").then(mod => ({ default: mod.BarChart })))
const RadarChart = lazy(() => import("@/components/features/dashboard/radar-chart").then(mod => ({ default: mod.RadarChart })))
const PieChart = lazy(() => import("@/components/features/dashboard/pie-chart").then(mod => ({ default: mod.PieChart })))

// Componente de carga para usar con Suspense
const ChartSkeleton = ({ height = 300 }) => {
  // Usar un estilo en línea para evitar problemas con la interpolación de cadenas en className
  const skeletonStyle = {
    height: `${height}px`,
    width: '100%'
  };

  return (
    <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
      <div className="p-6 flex flex-col space-y-4">
        <Skeleton className="h-6 w-1/3" />
        <Skeleton className="h-4 w-1/4" />
        <Skeleton style={skeletonStyle} />
      </div>
    </div>
  );
}

export function DashboardContent() {
  const { supabase } = useSupabase();
  const [error, setError] = useState<string | null>(null);
  const [metrics, setMetrics] = useState<MetricsState>({
    projectsCount: 0,
    activeProjectsCount: 0,
    ordersCount: 0,
    pendingOrdersCount: 0,
    documentsCount: 0,
    usersCount: 0,
    serviceRequestsCount: 0,
    pendingServiceRequestsCount: 0,
    customerEquipmentCount: 0,
    maintenanceSchedulesCount: 0,
    overdueMaintenanceCount: 0,
    projectsTrend: 0,
    ordersTrend: 0,
    documentsTrend: 0,
    usersTrend: 0,
    serviceRequestsTrend: 0,
    maintenanceTrend: 0
  });
  const [projectDistribution, setProjectDistribution] = useState<DashboardData['projectDistribution']>([]);
  const [performanceData, setPerformanceData] = useState<DashboardData['performanceMetrics']>([]);
  const [resourceAllocation, setResourceAllocation] = useState<DashboardData['resourceAllocation']>([]);
  const [monthlyData, setMonthlyData] = useState<MonthlyStats[]>([]);
  const [recentActivities, setRecentActivities] = useState<Activity[]>([]);
  const [pendingTasks, setPendingTasks] = useState<Task[]>([]);
  const [projects, setProjects] = useState<ProjectRow[]>([]);
  const [users, setUsers] = useState<User[]>([]);

  // Estado para los filtros
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(new Date().setDate(1)), // Primer día del mes actual
    to: new Date(),
  })

  // Cargar proyectos y usuarios reales al iniciar
  useEffect(() => {
    const loadProjectsAndUsers = async () => {
      try {
        // Cargar proyectos reales desde Supabase
        const { data: projectsData, error: projectsError } = await supabase
          .from('projects')
          .select('*')
          .order('name')

        if (!projectsError && projectsData) {
          setProjects(projectsData as ProjectRow[])
        }

        // Cargar usuarios reales desde Supabase
        const { data: usersData, error: usersError } = await supabase
          .from('users')
          .select('*')
          .order('first_name')

        if (!usersError && usersData) {
          setUsers(usersData.map(mapUserRowToUser))
        }
      } catch (error) {
        console.error('Error al cargar proyectos y usuarios:', error)
      }
    }

    loadProjectsAndUsers()
  }, [supabase])

  // Cargar datos al montar el componente y cuando cambien los filtros
  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        console.log('🔄 Cargando datos del dashboard...');
        const dashboardData = await getDashboardData();

        console.log('✅ Datos del dashboard cargados:', dashboardData);

        setMetrics((prev: MetricsState) => ({
          ...prev,
          projectsCount: dashboardData.projectDistribution.reduce((acc: number, curr: DashboardData['projectDistribution'][0]) => acc + (curr.value || 0), 0),
          activeProjectsCount: dashboardData.projectDistribution.find((p: DashboardData['projectDistribution'][0]) => p.name === 'Activo' || p.name === 'Activos')?.value || 0,
          // Keep other metrics as they are for now
          ordersCount: prev.ordersCount,
          pendingOrdersCount: prev.pendingOrdersCount,
          documentsCount: prev.documentsCount,
          usersCount: prev.usersCount,
          serviceRequestsCount: prev.serviceRequestsCount,
          pendingServiceRequestsCount: prev.pendingServiceRequestsCount,
          customerEquipmentCount: prev.customerEquipmentCount,
          maintenanceSchedulesCount: prev.maintenanceSchedulesCount,
          overdueMaintenanceCount: prev.overdueMaintenanceCount,
          projectsTrend: prev.projectsTrend,
          ordersTrend: prev.ordersTrend,
          documentsTrend: prev.documentsTrend,
          usersTrend: prev.usersTrend,
          serviceRequestsTrend: prev.serviceRequestsTrend,
          maintenanceTrend: prev.maintenanceTrend,
        }));

        setProjectDistribution(dashboardData.projectDistribution);
        setPerformanceData(dashboardData.performanceMetrics);
        setResourceAllocation(dashboardData.resourceAllocation);

        // 3. Cargar datos secundarios
        const [monthlyDataResult, recentActivitiesResult, pendingTasksResult] = await Promise.all([
          getMonthlyData(),
          getRecentActivities(),
          getPendingTasks()
        ]);
        setMonthlyData(monthlyDataResult || []);
        setRecentActivities(recentActivitiesResult || []);
        setPendingTasks(pendingTasksResult || []);

        console.log('✅ Todos los datos del dashboard cargados exitosamente');
      } catch (error: unknown) {
        console.error('❌ Error cargando datos del dashboard:', error);
        setError(error instanceof Error ? error.message : 'Error inesperado al cargar el dashboard');

        // Usar datos mock en caso de error
        setProjectDistribution([
          { name: 'Activos', value: 5, color: '#10b981' },
          { name: 'En Progreso', value: 3, color: '#f59e0b' },
          { name: 'Completados', value: 8, color: '#3b82f6' },
          { name: 'Pausados', value: 1, color: '#ef4444' }
        ]);
        setPerformanceData([
          { name: 'Eficiencia', value: 85, target: 85 },
          { name: 'Tasa de Completitud', value: 90, target: 90 },
          { name: 'Calidad', value: 95, target: 95 }
        ]);
        setResourceAllocation([
          { name: 'Personal', value: 75, capacity: 100 },
          { name: 'Equipos', value: 60, capacity: 100 },
          { name: 'Tiempo', value: 80, capacity: 100 }
        ]);
      }
    };

    loadDashboardData();
  }, []);

  // Estado para controlar la carga progresiva
  const [isInitialLoadComplete, setIsInitialLoadComplete] = useState(false)

  // Marcar la carga inicial como completa después de que se carguen las métricas
  useEffect(() => {
    if (metrics.projectsCount > 0 || metrics.activeProjectsCount > 0) {
      // Esperar un poco para asegurar que la UI se ha renderizado
      const timer = setTimeout(() => {
        setIsInitialLoadComplete(true)
      }, 500)
      return () => clearTimeout(timer)
    }
  }, [metrics])

  // --- Mapeo de datos para los gráficos ---
  // Adapta monthlyData a la estructura que espera cada gráfico
  const lineChartData = monthlyData.map(d => ({ name: d.name, value: d.projects })); // Para LineChart
  const areaChartData = monthlyData.map(d => ({ name: d.name, value1: d.documents, value2: d.users })); // Para AreaChart si espera value1/value2
  const barChartData = monthlyData.map(d => ({ name: d.name, value1: d.projects, value2: d.orders })); // Para BarChart
  // ... adapta según la estructura esperada por cada gráfico

  const aggregateData = (data: ProjectRow[]) => {
    return data.reduce((acc: Record<string, number>, curr: ProjectRow) => {
      const status = curr.status || 'Unknown';
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {});
  }

  // Update getMonthlyData with proper typing
  async function getMonthlyData() {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select('created_at, status')
        .order('created_at', { ascending: true });

      if (error) throw error;

      // Group data by month and calculate metrics
      const monthlyStats = (data || []).reduce((acc: Record<string, MonthlyStats>, project: Pick<ProjectRow, 'created_at' | 'status'>) => {
        const month = new Date(project.created_at || '').toLocaleString('default', { month: 'short' });
        if (!acc[month]) {
          acc[month] = { name: month, projects: 0, orders: 0, documents: 0, users: 0 };
        }
        acc[month].projects++;
        return acc;
      }, {});

      return Object.values(monthlyStats);
    } catch (error) {
      console.error('Error fetching monthly data:', error);
      return [];
    }
  }

  async function getRecentActivities() {
    try {
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(5);

      if (error) throw error;
      return (data || []).map(mapNotificationToActivity);
    } catch (error) {
      console.error('Error fetching recent activities:', error);
      return [];
    }
  }

  async function getPendingTasks() {
    try {
      const { data, error } = await supabase
        .from('work_order_tasks')
        .select('*')
        .eq('is_completed', false)
        .order('created_at', { ascending: true })
        .limit(5);

      if (error) throw error;
      return (data || []).map(mapWorkOrderTaskToTask);
    } catch (error) {
      console.error('Error fetching pending tasks:', error);
      return [];
    }
  }

  return (
    <div className="space-y-8">
      <div className="flex flex-col md:flex-row md:items-center justify-between space-y-4 md:space-y-0">
        <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
      </div>

      {/* Mostrar notificación de errores si hay alguno */}
      {error && (
        <ErrorNotification
          errors={[error]}
          onDismiss={() => setError(null)}
        />
      )}

      {/* Botón para corregir funciones del dashboard */}
      <DashboardFixButton
        onFixed={() => {
          // Limpiar errores y recargar datos
          setError(null);
          // Forzar recarga de datos después de la corrección
          setTimeout(() => {
            window.location.reload();
          }, 1000);
        }}
      />

      <DashboardFilters
        onDateRangeChange={setDateRange}
        onProjectsChange={() => {}}
        onUsersChange={() => {}}
        onStatusesChange={() => {}}
        projects={projects}
        users={users}
        initialDateRange={dateRange}
      />

      {/* Métricas principales - Siempre se cargan primero */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <MetricCard
          title="Proyectos Activos"
          value={metrics.activeProjectsCount}
          icon={FolderOpen}
          iconColor="text-violet-500"
          trend={metrics.projectsTrend}
          description={`${metrics.projectsCount} proyectos totales`}
          href="/dashboard/projects"
        />
        <MetricCard
          title="Órdenes de Trabajo"
          value={metrics.pendingOrdersCount}
          icon={ClipboardList}
          iconColor="text-pink-700"
          trend={metrics.ordersTrend}
          description={`${metrics.ordersCount} órdenes totales`}
          href="/dashboard/work-orders"
        />
        <MetricCard
          title="Documentos"
          value={metrics.documentsCount}
          icon={FileText}
          iconColor="text-emerald-500"
          trend={metrics.documentsTrend}
          href="/dashboard/documents"
        />
        <MetricCard
          title="Usuarios"
          value={metrics.usersCount}
          icon={Users}
          iconColor="text-blue-500"
          trend={metrics.usersTrend}
          href="/dashboard/users"
        />
      </div>

      {/* Métricas de Servicio Técnico */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <MetricCard
          title="Solicitudes de Servicio"
          value={metrics.pendingServiceRequestsCount}
          icon={Wrench}
          iconColor="text-orange-500"
          trend={metrics.serviceRequestsTrend}
          description={`${metrics.serviceRequestsCount} solicitudes totales`}
          href="/dashboard/service-requests"
        />
        <MetricCard
          title="Equipos de Clientes"
          value={metrics.customerEquipmentCount}
          icon={Wrench}
          iconColor="text-blue-600"
          href="/dashboard/customer-equipment"
        />
        <MetricCard
          title="Mantenimientos Pendientes"
          value={metrics.overdueMaintenanceCount}
          icon={Calendar}
          iconColor="text-red-500"
          trend={metrics.maintenanceTrend}
          description={`${metrics.maintenanceSchedulesCount} programados`}
          href="/dashboard/maintenance"
        />
      </div>

      {/* Gráficos con carga diferida */}
      {isInitialLoadComplete ? (
        <>
          <div className="grid gap-4 md:grid-cols-2">
            <Suspense fallback={<ChartSkeleton height={300} />}>
              <LineChart
                title="Actividad Mensual"
                data={lineChartData}
                dataKeys={["value"]}
                colors={["#8884d8"]}
                height={300}
              />
            </Suspense>
            <Suspense fallback={<ChartSkeleton height={300} />}>
              <AreaChart
                title="Tendencia de Documentos y Usuarios"
                data={areaChartData}
                dataKeys={["value1", "value2"]}
                colors={["#ffc658", "#ff8042"]}
                height={300}
              />
            </Suspense>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <Suspense fallback={<ChartSkeleton height={300} />}>
              <PieChart
                title="Distribución de Proyectos"
                data={projectDistribution}
                height={300}
                innerRadius={60}
                outerRadius={90}
              />
            </Suspense>
            <Suspense fallback={<ChartSkeleton height={300} />}>
              <PieChart
                title="Distribución de Recursos"
                data={resourceAllocation}
                height={300}
                innerRadius={60}
                outerRadius={90}
              />
            </Suspense>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <Suspense fallback={<ChartSkeleton height={300} />}>
              <BarChart
                title="Comparativa de Actividades"
                description="Comparación de proyectos y órdenes por mes"
                data={barChartData}
                dataKeys={["value1", "value2"]}
                colors={["#8884d8", "#82ca9d"]}
                height={300}
                layout="vertical"
              />
            </Suspense>
            <Suspense fallback={<ChartSkeleton height={300} />}>
              <RadarChart
                title="Rendimiento por Área"
                description="Evaluación de rendimiento en diferentes áreas"
                data={performanceData}
                dataKeys={["efficiency", "completion", "quality"]}
                colors={["#8884d8", "#82ca9d", "#ffc658"]}
                height={300}
              />
            </Suspense>
          </div>

          {/* Actividades y Tareas */}
          <div className="grid gap-4 md:grid-cols-2">
            <Suspense fallback={<ChartSkeleton height={300} />}>
              <ActivityList
                activities={recentActivities}
                title="Actividades Recientes"
              />
            </Suspense>
            <Suspense fallback={<ChartSkeleton height={300} />}>
              <TaskList
                tasks={pendingTasks}
                title="Tareas Pendientes"
                onTaskComplete={(id, completed) => {
                  console.log(`Tarea ${id} marcada como ${completed ? 'completada' : 'pendiente'}`)
                  // Aquí implementaríamos la lógica para marcar la tarea como completada
                  // y actualizar el estado
                }}
              />
            </Suspense>
          </div>
        </>
      ) : (
        // Esqueletos de carga para los gráficos mientras se cargan las métricas
        <>
          <div className="grid gap-4 md:grid-cols-2">
            <ChartSkeleton height={300} />
            <ChartSkeleton height={300} />
          </div>
          <div className="grid gap-4 md:grid-cols-2">
            <ChartSkeleton height={300} />
            <ChartSkeleton height={300} />
          </div>
          <div className="grid gap-4 md:grid-cols-2">
            <ChartSkeleton height={300} />
            <ChartSkeleton height={300} />
          </div>
          <div className="grid gap-4 md:grid-cols-2">
            <ChartSkeleton height={300} />
            <ChartSkeleton height={300} />
          </div>
        </>
      )}
    </div>
  )
}
