import { supabaseAdmin } from './admin-client'
import { createClient } from './client'

/**
 * Función para verificar y corregir problemas con los roles de usuario
 * Esta función intenta asegurar que la función user_has_role funcione correctamente
 */
export async function verifyAndFixUserRoles() {
  try {
    console.log('Verificando y corrigiendo roles de usuario...')
    
    // Verificar si el cliente admin está disponible
    if (!supabaseAdmin) {
      console.error('Cliente admin no disponible, no se pueden verificar roles')
      return false
    }
    
    // Verificar la sesión actual
    const { data: sessionData, error: sessionError } = await supabaseAdmin.auth.getSession()
    
    if (sessionError) {
      console.error('Error al obtener la sesión:', sessionError)
      return false
    }
    
    if (!sessionData?.session) {
      console.log('No hay sesión activa, no se pueden verificar roles')
      return false
    }
    
    const userId = sessionData.session.user.id
    console.log('Verificando roles para el usuario:', userId)
    
    // Verificar si el usuario existe en la tabla pública de usuarios
    const { data: publicUser, error: publicUserError } = await supabaseAdmin
      .from('users')
      .select('id, role')
      .eq('id', userId)
      .single()
    
    if (publicUserError) {
      console.error('Error al verificar usuario en tabla pública:', publicUserError)
      
      // Intentar crear el usuario en la tabla pública si no existe
      try {
        // Obtener datos del usuario desde auth.users
        const { data: authUser, error: authUserError } = await supabaseAdmin.auth.admin.getUserById(userId)
        
        if (authUserError) {
          console.error('Error al obtener datos del usuario desde auth:', authUserError)
          return false
        }
        
        if (!authUser?.user) {
          console.error('Usuario no encontrado en auth')
          return false
        }
        
        // Extraer el rol de los metadatos
        const userRole = authUser.user.user_metadata?.role || ['user']
        const formattedRole = Array.isArray(userRole) ? userRole : [userRole]
        
        // Crear el usuario en la tabla pública
        const { error: insertError } = await supabaseAdmin
          .from('users')
          .insert({
            id: userId,
            email: authUser.user.email || '',
            first_name: authUser.user.user_metadata?.first_name || '',
            last_name: authUser.user.user_metadata?.last_name || '',
            role: formattedRole
          })
        
        if (insertError) {
          console.error('Error al crear usuario en tabla pública:', insertError)
          return false
        }
        
        console.log('Usuario creado correctamente en tabla pública')
        return true
      } catch (error) {
        console.error('Error inesperado al crear usuario:', error)
        return false
      }
    }
    
    // Verificar si el rol está correctamente formateado como array
    if (!publicUser.role || !Array.isArray(publicUser.role)) {
      console.log('Corrigiendo formato de rol para el usuario:', userId)
      
      // Obtener datos del usuario desde auth.users para verificar el rol correcto
      const { data: authUser, error: authUserError } = await supabaseAdmin.auth.admin.getUserById(userId)
      
      if (authUserError) {
        console.error('Error al obtener datos del usuario desde auth:', authUserError)
        return false
      }
      
      // Extraer el rol de los metadatos o usar el valor actual
      const userRole = authUser?.user?.user_metadata?.role || publicUser.role || ['user']
      const formattedRole = Array.isArray(userRole) ? userRole : [userRole]
      
      // Actualizar el rol en la tabla pública
      const { error: updateError } = await supabaseAdmin
        .from('users')
        .update({ role: formattedRole })
        .eq('id', userId)
      
      if (updateError) {
        console.error('Error al actualizar rol en tabla pública:', updateError)
        return false
      }
      
      console.log('Rol actualizado correctamente en tabla pública:', formattedRole)
    } else {
      console.log('El rol del usuario está correctamente formateado:', publicUser.role)
    }
    
    return true
  } catch (error) {
    console.error('Error inesperado al verificar roles:', error)
    return false
  }
}

/**
 * Función para verificar si el usuario actual tiene un rol específico
 */
export async function currentUserHasRole(roleToCheck: string): Promise<boolean> {
  try {
    // Usar el cliente normal para verificar la sesión actual
    const supabase = createClient()
    const { data: sessionData } = await supabase.auth.getSession()
    
    if (!sessionData?.session) {
      console.log('No hay sesión activa')
      return false
    }
    
    const userId = sessionData.session.user.id
    
    // Verificar el rol en la tabla pública
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('role')
      .eq('id', userId)
      .single()
    
    if (userError || !userData) {
      console.error('Error al verificar rol en tabla pública:', userError)
      
      // Intentar verificar en los metadatos de la sesión
      const userMetadata = sessionData.session.user.user_metadata
      const userRole = userMetadata?.role
      
      if (!userRole) {
        return false
      }
      
      // Verificar si el rol está en el array o es igual al rol buscado
      return Array.isArray(userRole) 
        ? userRole.includes(roleToCheck)
        : userRole === roleToCheck
    }
    
    // Verificar si el rol está en el array
    return Array.isArray(userData.role) 
      ? userData.role.includes(roleToCheck)
      : userData.role === roleToCheck
  } catch (error) {
    console.error('Error inesperado al verificar rol:', error)
    return false
  }
}
