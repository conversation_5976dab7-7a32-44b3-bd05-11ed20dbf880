/**
 * @ai-file-description: "Centralized project validation service with comprehensive validation rules"
 * @ai-related-files: ["data-validator-service.ts", "../middleware/validation-middleware.ts"]
 * @ai-owner: "Projects"
 */

import { z } from 'zod'

/**
 * Project validation service that centralizes all validation logic
 * 
 * @ai-responsibility: "Provides centralized validation for project data across the application"
 */
export class ProjectValidationService {
  
  /**
   * Validates project name with comprehensive rules
   */
  static validateProjectName(name: string): { isValid: boolean; message?: string; suggestion?: string } {
    if (!name || name.trim() === '') {
      return {
        isValid: false,
        message: 'El nombre del proyecto es obligatorio',
        suggestion: 'Ingrese un nombre descriptivo para el proyecto'
      }
    }

    if (name.length < 3) {
      return {
        isValid: false,
        message: 'El nombre debe tener al menos 3 caracteres',
        suggestion: 'Agregue más caracteres al nombre del proyecto'
      }
    }

    if (name.length > 100) {
      return {
        isValid: false,
        message: 'El nombre no puede exceder 100 caracteres',
        suggestion: 'Acorte el nombre del proyecto'
      }
    }

    // Check for invalid characters
    const invalidChars = /[<>:"/\\|?*]/
    if (invalidChars.test(name)) {
      return {
        isValid: false,
        message: 'El nombre contiene caracteres no válidos',
        suggestion: 'Evite usar caracteres especiales como < > : " / \\ | ? *'
      }
    }

    return { isValid: true }
  }

  /**
   * Validates project status
   */
  static validateProjectStatus(status: string): { isValid: boolean; message?: string; suggestion?: string } {
    const validStatuses = ['pending', 'planning', 'in_progress', 'completed', 'cancelled', 'on_hold']
    
    if (!status) {
      return {
        isValid: false,
        message: 'El estado del proyecto es obligatorio',
        suggestion: 'Seleccione un estado de la lista desplegable'
      }
    }

    if (!validStatuses.includes(status)) {
      return {
        isValid: false,
        message: 'Estado de proyecto no válido',
        suggestion: `Los estados válidos son: ${validStatuses.join(', ')}`
      }
    }

    return { isValid: true }
  }

  /**
   * Validates budget amount
   */
  static validateBudget(budget: string | null | undefined): { isValid: boolean; message?: string; suggestion?: string } {
    // Budget is optional
    if (!budget || budget.trim() === '') {
      return { isValid: true }
    }

    const numValue = parseFloat(budget)

    if (isNaN(numValue)) {
      return {
        isValid: false,
        message: 'El presupuesto debe ser un número válido',
        suggestion: 'Ingrese solo números, sin símbolos de moneda'
      }
    }

    if (numValue < 0) {
      return {
        isValid: false,
        message: 'El presupuesto no puede ser negativo',
        suggestion: 'Ingrese un valor positivo para el presupuesto'
      }
    }

    if (numValue > 999999999) {
      return {
        isValid: false,
        message: 'El presupuesto es demasiado alto',
        suggestion: 'Verifique que el monto sea correcto'
      }
    }

    return { isValid: true }
  }

  /**
   * Validates UUID format
   */
  static validateUUID(uuid: string | null | undefined): { isValid: boolean; message?: string; suggestion?: string } {
    // UUID is optional
    if (!uuid || uuid.trim() === '') {
      return { isValid: true }
    }

    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i

    if (!uuidRegex.test(uuid)) {
      return {
        isValid: false,
        message: 'Formato de ID inválido',
        suggestion: 'Seleccione una opción de la lista desplegable'
      }
    }

    return { isValid: true }
  }

  /**
   * Validates date range
   */
  static validateDateRange(startDate: Date | null | undefined, endDate: Date | null | undefined): { 
    isValid: boolean; 
    message?: string; 
    suggestion?: string 
  } {
    // Dates are optional
    if (!startDate || !endDate) {
      return { isValid: true }
    }

    if (startDate > endDate) {
      return {
        isValid: false,
        message: 'La fecha de inicio debe ser anterior a la fecha de fin',
        suggestion: 'Ajuste las fechas para que la fecha de inicio sea anterior'
      }
    }

    // Check if dates are too far in the past
    const oneYearAgo = new Date()
    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1)

    if (startDate < oneYearAgo) {
      return {
        isValid: false,
        message: 'La fecha de inicio es muy antigua',
        suggestion: 'Verifique que la fecha de inicio sea correcta'
      }
    }

    // Check if dates are too far in the future
    const fiveYearsFromNow = new Date()
    fiveYearsFromNow.setFullYear(fiveYearsFromNow.getFullYear() + 5)

    if (endDate > fiveYearsFromNow) {
      return {
        isValid: false,
        message: 'La fecha de fin es muy lejana',
        suggestion: 'Verifique que la fecha de fin sea realista'
      }
    }

    return { isValid: true }
  }

  /**
   * Validates project users array
   */
  static validateProjectUsers(users: Array<{user_id: string; role: string}>): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    if (!users || users.length === 0) {
      return { isValid: true, errors: [] }
    }

    // Check for duplicate users
    const userIds = users.map(user => user.user_id)
    const duplicateIds = userIds.filter((id, index) => userIds.indexOf(id) !== index)
    
    if (duplicateIds.length > 0) {
      errors.push('No se pueden asignar usuarios duplicados al proyecto')
    }

    // Validate each user
    users.forEach((user, index) => {
      const userValidation = this.validateUUID(user.user_id)
      if (!userValidation.isValid) {
        errors.push(`El usuario #${index + 1} tiene un ID inválido`)
      }

      if (!user.role || user.role.trim() === '') {
        errors.push(`El usuario #${index + 1} debe tener un rol asignado`)
      }
    })

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * Validates complete project data
   */
  static validateProjectData(data: unknown): { 
    isValid: boolean; 
    errors: Array<{ field: string; message: string; suggestion?: string }> 
  } {
    const errors: Array<{ field: string; message: string; suggestion?: string }> = []
    
    // Type guard to ensure data is an object
    if (!data || typeof data !== 'object') {
      errors.push({
        field: 'data',
        message: 'Los datos del proyecto no son válidos',
        suggestion: 'Verifique que está enviando un objeto de proyecto válido'
      })
      return {
        isValid: false,
        errors
      }
    }

    // Cast data to a typed object with optional properties
    const typedData = data as {
      name?: string;
      status?: string;
      budget?: string | null;
      client_id?: string | null;
      start_date?: Date | null;
      end_date?: Date | null;
      project_users?: Array<{user_id: string; role: string}>;
    }

    // Validate name
    const nameValidation = this.validateProjectName(typedData.name || '')
    if (!nameValidation.isValid) {
      errors.push({
        field: 'name',
        message: nameValidation.message!,
        suggestion: nameValidation.suggestion
      })
    }

    // Validate status
    const statusValidation = this.validateProjectStatus(typedData.status || '')
    if (!statusValidation.isValid) {
      errors.push({
        field: 'status',
        message: statusValidation.message!,
        suggestion: statusValidation.suggestion
      })
    }

    // Validate budget
    const budgetValidation = this.validateBudget(typedData.budget)
    if (!budgetValidation.isValid) {
      errors.push({
        field: 'budget',
        message: budgetValidation.message!,
        suggestion: budgetValidation.suggestion
      })
    }

    // Validate client_id
    const clientValidation = this.validateUUID(typedData.client_id)
    if (!clientValidation.isValid) {
      errors.push({
        field: 'client_id',
        message: clientValidation.message!,
        suggestion: clientValidation.suggestion
      })
    }

    // Validate date range
    const dateValidation = this.validateDateRange(typedData.start_date, typedData.end_date)
    if (!dateValidation.isValid) {
      errors.push({
        field: 'dates',
        message: dateValidation.message!,
        suggestion: dateValidation.suggestion
      })
    }

    // Validate project users
    const projectUsers = Array.isArray(typedData.project_users) ? typedData.project_users : []
    const usersValidation = this.validateProjectUsers(projectUsers)
    if (!usersValidation.isValid) {
      usersValidation.errors.forEach(error => {
        errors.push({
          field: 'project_users',
          message: error,
          suggestion: 'Revise la asignación de usuarios al proyecto'
        })
      })
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * Generates a unique root path for a project
   */
  static generateRootPath(projectName: string): string {
    const sanitizedName = projectName
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
      .substring(0, 50) // Limit length

    const timestamp = Date.now()
    return `/${sanitizedName}-${timestamp}`
  }

  /**
   * Sanitizes project data for database insertion
   */
  static sanitizeProjectData(data: unknown): unknown {
    // Type guard to ensure data is an object
    if (!data || typeof data !== 'object') {
      return {
        name: '',
        status: 'pending',
        progress_percent: 0,
        scan_count: 1
      }
    }

    // Cast data to a typed object with optional properties
    const typedData = data as {
      name?: string;
      description?: string;
      status?: string;
      start_date?: Date | null;
      end_date?: Date | null;
      client_id?: string;
      budget?: string;
      currency?: string;
      project_type?: string;
    }
    
    // Generate root path safely
    const rootPath = typedData.name ? this.generateRootPath(typedData.name) : '/'

    return {
      name: typedData.name?.trim() || '',
      description: typedData.description?.trim() || null,
      status: typedData.status || 'pending',
      start_date: typedData.start_date || null,
      end_date: typedData.end_date || null,
      client_id: typedData.client_id?.trim() || null,
      budget: typedData.budget?.trim() || null,
      currency: typedData.currency || 'CLP',
      project_type: typedData.project_type?.trim() || null,
      root_path: rootPath,
      progress_percent: 0,
      scan_count: 1
    }
  }
}
