import { createClient } from "@/lib/supabase/client";
import { supabaseAdmin } from "@/lib/supabase/admin-client";
import { tableExists } from "@/lib/supabase/table-utils";

export interface Project {
  id: string;
  name: string;
  description?: string;
  status?: string;
  created_at?: string;
  updated_at?: string;
  owner_id?: string;
  client_id?: string;
  budget?: number;
  start_date?: string;
  end_date?: string;
}

export interface ProjectDeleteResult {
  success: boolean;
  message: string;
  project_id: string;
  deleted_count: number;
  related_data: {
    documents: number;
    tasks: number;
    orders: number;
    storage_files: number;
  };
}

/**
 * Obtiene todos los proyectos
 * @returns Promise<Project[]> Lista de proyectos
 */
export async function getAllProjects(): Promise<Project[]> {
  try {
    const supabase = createClient();
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !sessionData?.session) {
      console.error('Error de sesión al obtener proyectos:', sessionError);
      return [];
    }

    // Verificar si la tabla existe
    const exists = await tableExists('projects');
    if (!exists) {
      console.warn('La tabla projects no existe');
      return [];
    }

    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error al obtener proyectos:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error inesperado al obtener proyectos:', error);
    return [];
  }
}

/**
 * Obtiene un proyecto por su ID
 * @param id ID del proyecto
 * @returns Promise<Project | null> Proyecto o null si no existe
 */
export async function getProjectById(id: string): Promise<Project | null> {
  try {
    const supabase = createClient();
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !sessionData?.session) {
      console.error('Error de sesión al obtener proyecto:', sessionError);
      return null;
    }

    // Verificar si la tabla existe
    const exists = await tableExists('projects');
    if (!exists) {
      console.warn('La tabla projects no existe');
      return null;
    }

    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error al obtener proyecto:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error inesperado al obtener proyecto:', error);
    return null;
  }
}

/**
 * Elimina un proyecto y sus datos relacionados
 * @param id ID del proyecto a eliminar
 * @returns Promise<ProjectDeleteResult> Resultado de la eliminación
 */
export async function deleteProject(id: string): Promise<ProjectDeleteResult> {
  try {
    const supabase = createClient();
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !sessionData?.session) {
      console.error('Error de sesión al eliminar proyecto:', sessionError);
      return {
        success: false,
        message: 'Error de sesión',
        project_id: id,
        deleted_count: 0,
        related_data: {
          documents: 0,
          tasks: 0,
          orders: 0,
          storage_files: 0
        }
      };
    }

    // Intentar usar la función RPC optimizada
    try {
      console.log('Intentando usar la función RPC delete_project...');
      const { data, error } = await supabase.rpc('delete_project', { project_id: id });

      if (!error && data) {
        console.log('Proyecto eliminado con función RPC');
        return data as ProjectDeleteResult;
      } else {
        // Si el error es porque la función no existe, usar el método tradicional
        const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

        if (error && error.message && error.message.includes('function') && errorMessage.includes('does not exist')) {
          console.log('La función delete_project no existe, usando método tradicional...');
        } else {
          console.error('Error al eliminar proyecto con función RPC:', error);
        }
        console.log('Usando método alternativo...');
      }
    } catch (rpcError) {
      // Si es un error de función no encontrada, usar el método tradicional
      if (rpcError instanceof Error && rpcError.message &&
          (rpcError.message.includes('function') && rpcError.message.includes('does not exist'))) {
        console.log('La función delete_project no existe, usando método tradicional...');
      } else {
        console.error('Excepción al usar función RPC para eliminar proyecto:', rpcError);
      }
      console.log('Usando método alternativo...');
    }

    // Si la función RPC falló, usar el método tradicional
    // Verificar si la tabla existe
    const exists = await tableExists('projects');
    if (!exists) {
      console.warn('La tabla projects no existe');
      return {
        success: false,
        message: 'La tabla de proyectos no existe',
        project_id: id,
        deleted_count: 0,
        related_data: {
          documents: 0,
          tasks: 0,
          orders: 0,
          storage_files: 0
        }
      };
    }

    // Primero, intentar eliminar documentos relacionados
    if (await tableExists('documents')) {
      try {
        await supabase
          .from('documents')
          .delete()
          .eq('project_id', id);
      } catch (docError) {
        console.warn('Error al eliminar documentos relacionados:', docError);
      }
    }

    // Intentar eliminar tareas relacionadas a través de órdenes de trabajo
    if (await tableExists('work_order_tasks') && await tableExists('work_orders')) {
      try {
        const { data: workOrders } = await supabase
          .from('work_orders')
          .select('id')
          .eq('project_id', id);

        if (workOrders && workOrders.length > 0) {
          const workOrderIds = workOrders.map(wo => wo.id);

          await supabase
            .from('work_order_tasks')
            .delete()
            .in('work_order_id', workOrderIds);
        }
      } catch (taskError) {
        console.warn('Error al eliminar tareas relacionadas:', taskError);
      }
    }

    // Intentar eliminar órdenes de trabajo relacionadas
    if (await tableExists('work_orders')) {
      try {
        await supabase
          .from('work_orders')
          .delete()
          .eq('project_id', id);
      } catch (orderError) {
        console.warn('Error al eliminar órdenes de trabajo relacionadas:', orderError);
      }
    }

    // Eliminar el proyecto
    const { error } = await supabase
      .from('projects')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error al eliminar proyecto:', error);
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

      return {
        success: false,
        message: `Error al eliminar proyecto: ${errorMessage}`,
        project_id: id,
        deleted_count: 0,
        related_data: {
          documents: 0,
          tasks: 0,
          orders: 0,
          storage_files: 0
        }
      };
    }

    return {
      success: true,
      message: 'Proyecto eliminado correctamente',
      project_id: id,
      deleted_count: 1,
      related_data: {
        documents: 0,
        tasks: 0,
        orders: 0,
        storage_files: 0
      }
    };
  } catch (error) {
    console.error('Error inesperado al eliminar proyecto:', error);
    return {
      success: false,
      message: `Error inesperado: ${error instanceof Error ? error.message : 'Error desconocido'}`,
      project_id: id,
      deleted_count: 0,
      related_data: {
        documents: 0,
        tasks: 0,
        orders: 0,
        storage_files: 0
      }
    };
  }
}
