"use client"

import { useEffect, useState } from "react"
import { projectsFixService } from "@/lib/services/projects-fix-service"

/**
 * Componente que se encarga de verificar y corregir problemas con proyectos al iniciar la aplicación
 * No renderiza nada visible, solo ejecuta la lógica de verificación
 */
export function ProjectsFixInitializer() {
  const [initialized, setInitialized] = useState(false)

  useEffect(() => {
    // Verificar si ya se ha ejecutado la verificación en esta sesión
    const hasRun = sessionStorage.getItem('projects_fix_initialized')
    
    if (hasRun === 'true') {
      console.log('La verificación de proyectos ya se ha ejecutado en esta sesión')
      setInitialized(true)
      return
    }
    
    // Ejecutar la verificación y corrección de proyectos
    const runFix = async () => {
      try {
        console.log('Iniciando verificación y corrección de proyectos...')
        await projectsFixService.verifyAndFixProjects()
        
        // Marcar como inicializado para no volver a ejecutar en esta sesión
        sessionStorage.setItem('projects_fix_initialized', 'true')
        setInitialized(true)
      } catch (error) {
        console.error('Error al verificar y corregir proyectos:', error)
      }
    }
    
    // Ejecutar después de un breve retraso para no bloquear la carga inicial
    const timer = setTimeout(() => {
      runFix()
    }, 2000)
    
    return () => clearTimeout(timer)
  }, [])
  
  // Este componente no renderiza nada visible
  return null
}
