/**
 * @ai-file-description: "Página para crear nuevos proyectos"
 * @ai-related-files: ["from-document/page.tsx", "from-analysis/[id]/page.tsx", "../[id]/page.tsx"]
 * @ai-owner: "Projects"
 */

"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { createClient } from "@/lib/supabase/client"
import { ProjectForm } from "@/components/features/projects/project-form"
import { toast } from "@/hooks/use-toast"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { FileText } from "lucide-react"
import Link from "next/link"
import { useI18n } from "@/i18n/i18n-context"
import { LanguageSwitcher } from "@/components/language-switcher"
import { UUIDErrorMessage } from "@/components/shared/uuid-error-message"
import { EnhancedFormValidationAlert, createValidationError } from "@/components/features/projects/enhanced-form-validation-alert"
import { errorH<PERSON><PERSON>, ErrorType } from "@/lib/services/error-handler-service"
import { dataValidator } from "@/lib/services/data-validator-service"

/**
 * Página para crear nuevos proyectos
 *
 * @ai-responsibility: "Proporciona interfaz para crear nuevos proyectos desde documentos"
 */
export default function NewProjectPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [validationErrors, setValidationErrors] = useState<any[]>([])
  const router = useRouter()
  const supabase = createClient()
  const { t } = useI18n()

  /**
   * Maneja el envío del formulario para crear un proyecto
   */
  const handleSubmit = async (data: unknown) => {
    // Limpiar errores previos
    setValidationErrors([]);
    setIsLoading(true)

    try {
      // Validación previa del lado del cliente
      const clientValidationErrors: unknown[] = [];

      // Validar campos requeridos
      if (!data.name || data.name.trim() === '') {
        clientValidationErrors.push(createValidationError(
          'El nombre del proyecto es obligatorio',
          'name',
          'error',
          'Ingrese un nombre descriptivo para el proyecto'
        ));
      } else if (data.name.length < 3) {
        clientValidationErrors.push(createValidationError(
          'El nombre debe tener al menos 3 caracteres',
          'name',
          'error',
          'Agregue más caracteres al nombre del proyecto'
        ));
      }

      // Validar estado
      if (!data.status) {
        clientValidationErrors.push(createValidationError(
          'El estado del proyecto es obligatorio',
          'status',
          'error',
          'Seleccione un estado de la lista desplegable'
        ));
      }

      // Validar y sanitizar los campos UUID
      const uuidFields = ['client_id'];
      const sanitizedData = dataValidator.sanitizeObject(data, uuidFields);

      // Validar client_id si se proporciona
      if (data.client_id && data.client_id.trim() !== '' && !dataValidator.isValidUUID(data.client_id)) {
        clientValidationErrors.push(createValidationError(
          'El ID del cliente no tiene un formato válido',
          'client_id',
          'error',
          'Seleccione un cliente de la lista desplegable o deje el campo vacío'
        ));
      }

      // Validar presupuesto si se proporciona
      if (data.budget && data.budget.trim() !== '' && isNaN(parseFloat(data.budget))) {
        clientValidationErrors.push(createValidationError(
          'El presupuesto debe ser un número válido',
          'budget',
          'error',
          'Ingrese solo números, sin símbolos de moneda'
        ));
      }

      // Validar que los usuarios tengan IDs válidos
      if (data.project_users && data.project_users.length > 0) {
        data.project_users.forEach((user: unknown, index: number) => {
          if (!dataValidator.isValidUUID(user.user_id)) {
            clientValidationErrors.push(createValidationError(
              `El usuario #${index + 1} tiene un ID inválido`,
              'project_users',
              'error',
              'Seleccione usuarios válidos de la lista'
            ));
          }
          if (!user.role || user.role.trim() === '') {
            clientValidationErrors.push(createValidationError(
              `El usuario #${index + 1} debe tener un rol asignado`,
              'project_users',
              'error',
              'Asigne un rol a cada usuario del proyecto'
            ));
          }
        });
      }

      // Si hay errores de validación del cliente, mostrarlos y no continuar
      if (clientValidationErrors.length > 0) {
        setValidationErrors(clientValidationErrors);
        setIsLoading(false);
        return;
      }

      // Crear el proyecto usando la API para mejor manejo de errores
      const response = await fetch('/api/projects', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: sanitizedData.name,
          description: sanitizedData.description,
          status: sanitizedData.status,
          start_date: sanitizedData.start_date?.toISOString().split('T')[0] || null,
          end_date: sanitizedData.end_date?.toISOString().split('T')[0] || null,
          client_id: sanitizedData.client_id,
          budget: sanitizedData.budget || null,
          currency: sanitizedData.currency,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();

        // Handle validation errors from the API
        if (response.status === 400 && errorData.errors) {
          const apiValidationErrors = errorData.errors.map((error: unknown) => {
            const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
            return createValidationError(
              errorMessage,
              error.field,
              'error',
              'Corrija este error para continuar'
            );
          });
          setValidationErrors(apiValidationErrors);
          setIsLoading(false);
          return;
        }

        // Handle specific database constraint errors
        if (errorData.error) {
          const errorMessage = errorData.error;
          let validationError;

          if (errorMessage.includes('estado no válido')) {
            validationError = createValidationError(
              errorMessage,
              'status',
              'error',
              'Seleccione un estado válido de la lista'
            );
          } else if (errorMessage.includes('ruta')) {
            validationError = createValidationError(
              'Ya existe un proyecto con un nombre similar',
              'name',
              'error',
              'Intente con un nombre diferente para el proyecto'
            );
          } else if (errorMessage.includes('campos obligatorios')) {
            validationError = createValidationError(
              errorMessage,
              'name',
              'error',
              'Complete todos los campos obligatorios'
            );
          } else {
            validationError = createValidationError(
              errorMessage,
              undefined,
              'error',
              'Revise los datos ingresados e intente nuevamente'
            );
          }

          setValidationErrors([validationError]);
          setIsLoading(false);
          return;
        }

        throw new Error('Error al crear el proyecto');
      }

      const projectData = await response.json();

      if (!projectData || !projectData.id) {
        throw new Error("No project data returned after creation")
      }

      const projectId = projectData.id

      // Agregar el usuario actual como propietario del proyecto
      const { error: userRoleError } = await supabase
        .from('project_users')
        .insert({
          project_id: projectId,
          user_id: (await supabase.auth.getUser()).data.user?.id,
          role: 'owner'
        })

      if (userRoleError) {
        console.error("Error al agregar usuario al proyecto:", userRoleError)
        // Continuar de todos modos ya que el proyecto fue creado
      }

      // Agregar usuarios del proyecto si se proporcionaron
      if (data.project_users && data.project_users.length > 0) {
        const projectUsers = data.project_users.map((user: unknown) => ({
          project_id: projectId,
          user_id: user.user_id,
          role: user.role
        }))

        const { error: usersError } = await supabase
          .from('project_users')
          .insert(projectUsers)

        if (usersError) {
          console.error("Error al agregar usuarios al proyecto:", usersError)
          // Continuar de todos modos ya que el proyecto fue creado
        }
      }

      toast({
        title: t('projects.projectCreated'),
        description: t('projects.projectCreatedDescription'),
      })

      // Redirigir a la lista de proyectos
      router.push("/dashboard/projects")
      router.refresh()
    } catch (error: unknown) {
      console.error("Error al crear el proyecto:", error)
      const errorMessage = error instanceof Error ? error.message : t('projects.errorCreatingProject');
      toast({
        title: t('common.error'),
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">{t('projects.newProject')}</h2>
          <p className="text-muted-foreground mt-2">
            {t('projects.documentDescription')}
          </p>
        </div>
        <LanguageSwitcher />
      </div>

      <div className="grid grid-cols-1 gap-6">
        {/* AI Document Analysis */}
        <Card>
          <CardHeader>
            <CardTitle>{t('projects.createFromDocument')}</CardTitle>
            <CardDescription>
              {t('projects.documentDescription')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              {t('projects.documentDescription')}
            </p>
            <Button className="w-full" asChild>
              <Link href="/dashboard/projects/new/from-document">
                <FileText className="mr-2 h-4 w-4" />
                {t('projects.uploadDocument')}
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>

      <div id="project-form" className="rounded-md border p-6">
        <h3 className="text-lg font-medium mb-4">{t('projects.projectDetails')}</h3>

        {/* Mostrar errores de validación si existen */}
        {validationErrors.length > 0 && (
          <EnhancedFormValidationAlert
            errors={validationErrors}
            onDismiss={() => setValidationErrors([])}
            showSuggestions={true}
            maxVisibleErrors={5}
          />
        )}

        <ProjectForm onSubmit={handleSubmit} isLoading={isLoading} />
      </div>
    </div>
  )
}
