-- <PERSON>ste script crea las funciones y tablas necesarias para el dashboard
-- Debe ejecutarse desde el Editor SQL en el panel de administración de Supabase

-- 1. Crear tabla de métricas de rendimiento si no existe
CREATE TABLE IF NOT EXISTS public.performance_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  category TEXT NOT NULL,
  efficiency NUMERIC NOT NULL DEFAULT 0,
  completion_rate NUMERIC NOT NULL DEFAULT 0,
  quality_score NUMERIC NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Crear tabla de asignación de recursos si no existe
CREATE TABLE IF NOT EXISTS public.resource_allocation (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  category TEXT NOT NULL,
  amount NUMERIC NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Crear función para obtener distribución de proyectos
CREATE OR REPLACE FUNCTION public.get_project_distribution()
RETURNS TABLE (name TEXT, value NUMERIC)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Verificar si existe la tabla de proyectos
  IF EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = 'public' AND table_name = 'projects'
  ) THEN
    -- Devolver distribución real de proyectos por estado
    RETURN QUERY
    SELECT 
      COALESCE(status, 'Sin estado') AS name,
      COUNT(*)::NUMERIC AS value
    FROM public.projects
    GROUP BY status
    ORDER BY value DESC;
  ELSE
    -- Si no existe la tabla, devolver datos de ejemplo
    RETURN QUERY
    SELECT name, value FROM (
      VALUES 
        ('Activo', 42),
        ('Completado', 28),
        ('Planificación', 18),
        ('En pausa', 12)
    ) AS t(name, value);
  END IF;
END;
$$;

-- 4. Crear función para obtener métricas del dashboard
CREATE OR REPLACE FUNCTION public.get_dashboard_metrics()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSON;
  projects_count INTEGER := 0;
  active_projects_count INTEGER := 0;
  orders_count INTEGER := 0;
  pending_orders_count INTEGER := 0;
  documents_count INTEGER := 0;
  users_count INTEGER := 0;
BEGIN
  -- Verificar y contar proyectos
  IF EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = 'public' AND table_name = 'projects'
  ) THEN
    SELECT COUNT(*) INTO projects_count FROM public.projects;
    
    SELECT COUNT(*) INTO active_projects_count 
    FROM public.projects
    WHERE status IN ('active', 'in_progress', 'planning');
  END IF;
  
  -- Verificar y contar órdenes de trabajo
  IF EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = 'public' AND table_name = 'work_orders'
  ) THEN
    SELECT COUNT(*) INTO orders_count FROM public.work_orders;
    
    SELECT COUNT(*) INTO pending_orders_count 
    FROM public.work_orders
    WHERE status = 'pending';
  END IF;
  
  -- Verificar y contar documentos
  IF EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = 'public' AND table_name = 'documents'
  ) THEN
    SELECT COUNT(*) INTO documents_count FROM public.documents;
  END IF;
  
  -- Contar usuarios
  SELECT COUNT(*) INTO users_count FROM auth.users;
  
  -- Construir objeto JSON con los resultados
  result := json_build_object(
    'projectsCount', projects_count,
    'activeProjectsCount', active_projects_count,
    'ordersCount', orders_count,
    'pendingOrdersCount', pending_orders_count,
    'documentsCount', documents_count,
    'usersCount', users_count
  );
  
  RETURN result;
END;
$$;

-- 5. Insertar datos de ejemplo en la tabla de métricas de rendimiento si está vacía
INSERT INTO public.performance_metrics (category, efficiency, completion_rate, quality_score)
SELECT * FROM (
  VALUES 
    ('Diseño', 85, 92, 78),
    ('Desarrollo', 78, 85, 90),
    ('Pruebas', 92, 88, 95),
    ('Implementación', 80, 75, 85),
    ('Soporte', 88, 90, 82)
) AS t(category, efficiency, completion_rate, quality_score)
WHERE NOT EXISTS (SELECT 1 FROM public.performance_metrics LIMIT 1);

-- 6. Insertar datos de ejemplo en la tabla de asignación de recursos si está vacía
INSERT INTO public.resource_allocation (category, amount)
SELECT * FROM (
  VALUES 
    ('Personal', 45000),
    ('Equipamiento', 28000),
    ('Software', 15000),
    ('Servicios', 12000),
    ('Otros', 5000)
) AS t(category, amount)
WHERE NOT EXISTS (SELECT 1 FROM public.resource_allocation LIMIT 1);

-- 7. Habilitar RLS en las tablas
ALTER TABLE public.performance_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.resource_allocation ENABLE ROW LEVEL SECURITY;

-- 8. Crear políticas para la tabla de métricas de rendimiento
DROP POLICY IF EXISTS "Allow authenticated users to read performance_metrics" ON public.performance_metrics;
CREATE POLICY "Allow authenticated users to read performance_metrics"
  ON public.performance_metrics FOR SELECT
  USING (auth.role() = 'authenticated');

-- 9. Crear políticas para la tabla de asignación de recursos
DROP POLICY IF EXISTS "Allow authenticated users to read resource_allocation" ON public.resource_allocation;
CREATE POLICY "Allow authenticated users to read resource_allocation"
  ON public.resource_allocation FOR SELECT
  USING (auth.role() = 'authenticated');
