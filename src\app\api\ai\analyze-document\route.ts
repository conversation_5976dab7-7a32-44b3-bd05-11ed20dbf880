/**
 * @ai-file-description: "API endpoint for analyzing documents with AI"
 * @ai-related-files: ["../create-project-from-analysis/route.ts", "../providers/route.ts"]
 * @ai-owner: "File-Based Projects"
 */

import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { DocumentAnalyzer } from '@/lib/ai-providers/document-analyzer';
import type { Database } from '@/lib/supabase/types';
import type { SupabaseClient } from '@supabase/supabase-js';

// Document data type that combines database fields with DocumentAnalyzer requirements
type DocumentData = {
  // Database fields
  id: string;
  filename: string;
  file_path: string;
  file_size: number;
  file_type: string;
  description: string | null;
  category: string | null;
  project_id: string | null;
  work_order_id: string | null;
  created_at: string;
  file_url: string;
  uploaded_by: string | null;
  // DocumentAnalyzer required fields
  fileUrl: string;
  fileName: string;
  fileType: string;
  metadata: Record<string, unknown>;
  [key: string]: unknown; // Allow additional properties
};

// Define document update and insert types for type safety

// Define the analysis result type
export interface DocumentAnalysisResult {
  project_name: string;
  description: string | null;
  start_date: string | null;
  end_date: string | null;
  budget: string | null;
  currency: string | null;
  client_name: string | null;
  deliverables: string[] | null;
  scope: string | null;
  team_requirements: string[] | null;
  tags: string[] | null;
  confidence_score: number;
  error_message?: string;
}

// Define the expected AI provider interface (matching ai_providers table structure for selected fields)
interface AIProvider {
  id: string;
  provider_name: string;
  api_key: string;
  model_name: string;
  max_tokens?: number | null; // Supabase might return null for optional number fields
  temperature?: number | null; // Supabase might return null for optional number fields
  // Add other fields like 'priority' if selected and used
}

// Define the configuration structure passed to the DocumentAnalyzer
interface ProviderConfig {
  providerId: string;
  apiKey: string;
  modelName: string;
  maxTokens?: number;
  temperature?: number;
}

// Defensive type guard for userData
function hasRole(obj: unknown): obj is { role: unknown } {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'role' in obj
  );
}

/**
 * POST handler for document analysis
 *
 * @ai-responsibility: "Handles document analysis requests and stores results"
 */
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { documentId, userId } = body; // Removed unused providerId

    if (!documentId) {
      return NextResponse.json(
        { error: 'Document ID is required' },
        { status: 400 }
      );
    }

    console.log('Starting document analysis request for document ID:', documentId);
    console.log('Request headers:', Object.fromEntries(request.headers.entries()));

    // Initialize Supabase client
    const supabase = await createClient();
  
    // Extraer el token de autorización directamente del encabezado
    const authHeader = request.headers.get('authorization') || '';
    const token = authHeader.replace('Bearer ', '');

    console.log('Token de autorización disponible:', !!token);

    // Extraer el ID de usuario del encabezado o del cuerpo
    const headerUserId = request.headers.get('x-user-id');
    const userIdFromRequest = headerUserId || userId;

    console.log('ID de usuario del encabezado:', headerUserId);
    console.log('ID de usuario del cuerpo:', userId);

    // Verificar si tenemos un ID de usuario válido
    if (!userIdFromRequest) {
      console.error('No se proporcionó ID de usuario en los encabezados ni en el cuerpo');
      return NextResponse.json(
        { error: 'Unauthorized: No user ID provided' },
        { status: 401 }
      );
    }

    // Verificar que el usuario existe en la base de datos
    console.log('Verificando que el usuario existe en la base de datos...');
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id, role')
      .eq('id', userIdFromRequest)
      .single();

    if (userError || !userData) {
      console.error('Error al verificar el usuario:', userError?.message || 'No se encontró el usuario');
      return NextResponse.json(
        { error: 'Unauthorized: Invalid user ID' },
        { status: 401 }
      );
    }

    console.log('Usuario encontrado:', userData);

    // Ya tenemos la información del usuario en userData

    const authenticatedUserId = userIdFromRequest;
    console.log('User authenticated:', authenticatedUserId);

    // Get document data
    console.log('Getting document data for ID:', documentId);
    let documentData: DocumentData;

    try {
      // Get the document from the database
      const { data: document, error: documentError } = await supabase
        .from('documents')
        .select('*')
        .eq('id', documentId)
        .single();
      
      if (documentError || !document) {
        console.error('Error fetching document:', documentError);
        return NextResponse.json(
          { 
            error: 'Failed to fetch document', 
            details: documentError?.message || 'Document not found' 
          },
          { status: 400 }
        );
      }
      
      // Create document data with required fields
      const doc = document as unknown as DocumentData;
      documentData = {
        ...doc,
        fileUrl: doc.file_path || '',
        fileName: doc.filename || '',
        fileType: doc.file_type || '',
        metadata: doc.metadata || {}
      };
      
      // Set file extension based on filename if not already set
      if (!documentData.fileType && documentData.fileName) {
        const fileExtension = documentData.fileName.split('.').pop()?.toLowerCase() || '';
        documentData.fileType = fileExtension;
      }
      
      console.log('Document data prepared:', {
        id: documentData.id,
        fileName: documentData.fileName,
        fileType: documentData.fileType,
        hasMetadata: !!documentData.metadata
      });
      
    } catch (error) {
      console.error('Error processing document:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return NextResponse.json(
        { 
          error: 'Failed to process document', 
          details: errorMessage 
        },
        { status: 500 }
      );
    }
    
    // Log document details for debugging
    console.log('Document found:', documentData.fileName, 'File path:', documentData.file_path);

    // Check if user has access to the document
    console.log('Checking document access. Document uploaded by:', documentData.uploaded_by, 'Current user:', authenticatedUserId);

    // Verify if user is admin
    const isAdmin =
      hasRole(userData) &&
      (
        Array.isArray(userData.role)
          ? userData.role.includes('admin')
          : userData.role === 'admin'
      );
    console.log('User role check:', isAdmin ? 'Admin user' : 'Regular user');

    if (isAdmin) {
      console.log('Admin user has access to all documents');
    } else if (documentData.uploaded_by !== authenticatedUserId) {
      console.log('Document not uploaded by current user, checking project access...');
      // Check if user has access to the project
      if (documentData.project_id) {
        console.log('Document belongs to project:', documentData.project_id);
        const { data: projectAccess, error: projectError } = await supabase
          .from('project_users')
          .select('*')
          .eq('project_id', documentData.project_id)
          .eq('user_id', authenticatedUserId)
          .single();

        if (projectError) {
          console.error('Error checking project access:', projectError);
          return NextResponse.json(
            { error: 'Error checking document access: ' + (projectError?.message || 'Unknown error') },
            { status: 403 }
          );
        }

        if (!projectAccess) {
          console.error('User does not have access to the project');
          return NextResponse.json(
            { error: 'You do not have access to this document (no project access)' },
            { status: 403 }
          );
        }

        console.log('User has access to the project');
      } else {
        console.log('Document does not belong to any project, checking uploader...');
        // If document doesn't belong to any project, only the uploader can access it
        if (documentData.uploaded_by !== authenticatedUserId) {
          console.error('User is not authorized to access this document');
          return NextResponse.json(
            { error: 'You do not have permission to access this document' },
            { status: 403 }
          );
        }
      }
    }

    // Get provider configuration
    let providerConfig: ProviderConfig; // Use the new global ProviderConfig interface
    try {
      // The local AIProvider interface definition is removed as it's now global.

      // Get the highest priority provider
      // Use .returns<AIProvider[]>() for better type inference from Supabase
      const { data, error } = await supabase
        .from('ai_providers')
        .select('id, provider_name, api_key, model_name, max_tokens, temperature') // Be specific about selected fields
        .order('priority', { ascending: true })
        .returns<AIProvider[]>(); // Ensures 'data' is AIProvider[] or null

      if (error) {
        console.error('Error fetching AI providers:', error);
        return NextResponse.json(
          { error: 'Failed to fetch AI providers: ' + error.message },
          { status: 500 }
        );
      }

      // Check if data is null or empty, or if the first element is null
      if (!data || data.length === 0 || !data[0]) {
        console.error('No AI providers found or data is invalid');
        return NextResponse.json(
          { error: 'No AI providers configured or data invalid' },
          { status: 500 }
        );
      }
      
      const provider: AIProvider = data[0]; // provider is now strongly typed as AIProvider

      // Runtime check for essential string properties for robustness.
      if (typeof provider.provider_name !== 'string' || 
          typeof provider.api_key !== 'string' || 
          typeof provider.model_name !== 'string') {
        console.error('Invalid provider data structure - essential fields are not strings:', provider);
        return NextResponse.json(
          { error: 'Provider configuration has invalid field types' },
          { status: 500 }
        );
      }
      
      providerConfig = {
        providerId: provider.provider_name, // Direct assignment, type checked
        apiKey: provider.api_key,           // Direct assignment, type checked
        modelName: provider.model_name,       // Direct assignment, type checked
        // Handle null from DB for optional fields before Number conversion
        maxTokens: (provider.max_tokens !== null && provider.max_tokens !== undefined) ? Number(provider.max_tokens) : undefined,
        temperature: (provider.temperature !== null && provider.temperature !== undefined) ? Number(provider.temperature) : undefined
      };
      
      console.log('Using provider:', providerConfig.providerId);
    } catch (error) {
      console.error('Error fetching provider configuration:', error);
      return NextResponse.json(
        { error: 'Failed to fetch provider configuration' },
        { status: 500 }
      );
    }

    // Create analysis record with proper typing
    let analysisRecord: { id: string };
    try {
      // Define the expected analysis record interface
      interface AnalysisRecord {
        id: string;
        document_id: string;
        status: string;
        provider: string;
        model: string;
        created_at: string;
        updated_at: string;
      }

      const { data, error } = await supabase
        .from('ai_document_analyses')
        .insert({
          document_id: documentData.id,
          status: 'pending',
          provider: providerConfig.providerId,
          model: providerConfig.modelName,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        } as unknown) // Type assertion to handle Supabase types temporarily until type definition is updated
        .select()
        .single();

      if (error || !data) {
        throw new Error(error?.message || 'Failed to create analysis record');
      }
      
      // Type guard to verify the data has the required id field
      if (!data || typeof data !== 'object' || !('id' in data)) {
        throw new Error('Invalid analysis record structure');
      }
      
      // Now TypeScript knows data has an id property
      const typedData = data as AnalysisRecord;
      analysisRecord = { id: typedData.id };
      console.log('Created analysis record with ID:', analysisRecord.id);
      
    } catch (error) {
      console.error('Error creating analysis record:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return NextResponse.json(
        { 
          error: 'Failed to create analysis record',
          details: errorMessage
        },
        { status: 500 }
      );
    }

    // In development, we can call the background function directly
    const analysisId = analysisRecord.id;

    // Cast supabase client to any to avoid type issues with the background function
    analyzeDocumentBackground(
      documentData,
      {
        providerId: providerConfig.providerId,
        apiKey: providerConfig.apiKey,
        modelName: providerConfig.modelName,
        maxTokens: providerConfig.maxTokens,
        temperature: providerConfig.temperature
      },
      analysisId,
      supabase
    ).catch((error: unknown) => {
      console.error('Error in background analysis:', error instanceof Error ? error.message : String(error));
    });

    return NextResponse.json({
      id: analysisRecord.id,
      status: 'pending',
      message: 'Document analysis started'
    });
  } catch (error) {
    console.error('Unexpected error analyzing document:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

/**
 * Background function to analyze document and update database
 * This function is intended to run only on the server.
 *
 * @param document Document data
 * @param config Provider configuration
 * @param analysisId Analysis record ID
 * @param supabase Supabase client
 */
async function analyzeDocumentBackground(
  document: DocumentData,
  config: {
    providerId: string;
    apiKey: string;
    modelName: string;
    maxTokens?: number;
    temperature?: number;
  },
  analysisId: string,
  supabase: SupabaseClient<Database>
) {
  'use server'; // Ensure this function runs on the server

  console.log('Starting background document analysis:', { documentId: document.id, provider: config.providerId });
  console.log('Environment check inside analyzeDocumentBackground (typeof window):', typeof window); // Log typeof window
  console.log('API Key being used for provider:', config.apiKey ? `${config.apiKey.substring(0, 5)}...` : 'No API Key'); // Log the API key (truncated)

  try {
    // Update status to processing
    await supabase
      .from('ai_document_analyses')
      .update({
        status: 'processing',
        updated_at: new Date().toISOString()
      })
      .eq('id', analysisId);

    // Get the document file URL
    let fileUrl = document.fileUrl;

    // If the URL is a storage URL, get a signed URL
    if (fileUrl.includes('storage/v1/object')) {
      try {
        console.log('Getting signed URL for document:', document.id);
        // Extract the file path from the URL
        let filePath = '';

        // Handle different URL formats
        if (fileUrl.includes('/object/public/')) {
          filePath = fileUrl.split('/object/public/')[1];
        } else if (fileUrl.includes('/object/authenticated/')) {
          filePath = fileUrl.split('/object/authenticated/')[1];
        } else {
          filePath = fileUrl.split('/').pop() || '';
        }

        console.log('Extracted file path:', filePath);

        // Create signed URL with longer expiry (5 minutes)
        const { data: signedUrlData, error: signedUrlError } = await supabase
          .storage
          .from('documents')
          .createSignedUrl(filePath, 300); // 300 seconds (5 minutes) expiry

        if (signedUrlError) {
          console.error('Error getting signed URL:', signedUrlError);
          throw new Error(`Failed to get signed URL: ${signedUrlError.message}`);
        } else if (signedUrlData?.signedUrl) {
          fileUrl = signedUrlData.signedUrl;
          console.log('Got signed URL for document');
        } else {
          console.warn('No signed URL returned, using original URL');
        }
      } catch (urlError) {
        console.error('Error creating signed URL:', urlError);
        // Continue with original URL as fallback
        console.log('Using original URL as fallback');
      }
    }

    // Prepare document data for analysis
    const documentData: DocumentData = {
      ...document,
      fileUrl: document.file_path || '',
      fileName: document.filename || '',
      fileType: document.file_type || '',
      metadata: document.metadata || {}
    };

    console.log('Analyzing document with provider:', config.providerId);

    // Analyze document
    console.log('Calling DocumentAnalyzer.analyzeDocument with config:', {
      providerId: config.providerId,
      modelName: config.modelName,
      hasApiKey: !!config.apiKey
    });

    const result = await DocumentAnalyzer.analyzeDocument(documentData, config);

    // Check if the result contains an error message
    if (result.error_message) {
      console.warn(`Document analysis completed with error: ${result.error_message}`);

      // Update analysis record with partial results and error
      const { error: updateError } = await supabase
        .from('ai_document_analyses')
        .update({
          status: 'completed_with_errors',
          error_message: result.error_message,
          analysis_result: result,
          updated_at: new Date().toISOString(),
          completed_at: new Date().toISOString()
        })
        .eq('id', analysisId);

      if (updateError) {
        console.error('Error updating analysis record with partial results:', updateError);
      } else {
        console.log('Analysis record updated with partial results and error');
      }
    } else {
      console.log('Document analysis completed successfully');

      // Update analysis record with successful results
      const { error: updateError } = await supabase
        .from('ai_document_analyses')
        .update({
          status: 'completed',
          analysis_result: result,
          updated_at: new Date().toISOString(),
          completed_at: new Date().toISOString()
        })
        .eq('id', analysisId);

      if (updateError) {
        console.error('Error updating analysis record with results:', updateError);
      } else {
        console.log('Analysis record updated with successful results');
      }
    }
  } catch (error) {
    console.error('Error in background document analysis:', error);

    // Handle errors in the background process
    try {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('Error in background process:', error);
      
      // Ensure we have a valid analysis ID before updating
      if (!analysisId || typeof analysisId !== 'string') {
        console.error('Invalid analysis ID');
        return;
      }

      if (!supabase) {
        console.error('No Supabase client available');
        return;
      }

      // Update the analysis record with error status
      const { error: updateError } = await supabase
        .from('ai_document_analyses')
        .update({
          status: 'failed',
          error_message: errorMessage,
          updated_at: new Date().toISOString()
        } as const)
        .eq('id', analysisId);
      
      if (updateError) {
        console.error('Error updating analysis record:', updateError.message);
      } else {
        console.log('Analysis record updated with error status');
      }
    } catch (err) {
      console.error('Failed to update analysis record with error:', err);
    }
  }
}
