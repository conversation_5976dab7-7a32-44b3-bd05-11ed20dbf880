'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { But<PERSON> } from '@/components/ui/button'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  Info,
  HelpCircle,
  BookOpen,
  Thermometer,
  Zap,
  Layers,
  Percent,
  BarChart4,
  Cpu,
  Brain
} from '@/components/ui/icons'

// Interfaz para los parámetros del modelo
interface ParameterInfo {
  id: string;
  name: string;
  description: string;
  impact: string;
  recommendedValues: {
    value: number | string;
    description: string;
    useCase: string;
  }[];
  technicalDetails: string;
  modelSpecific?: boolean;
  modelTypes?: string[];
}

// Componente principal para la guía de parámetros
export function ModelParametersGuide() {
  const [activeTab, setActiveTab] = useState('temperature')

  // Definiciones de parámetros con explicaciones detalladas
  const parameters: ParameterInfo[] = [
    {
      id: 'temperature',
      name: 'Temperatura',
      description: 'Controla la aleatoriedad y creatividad de las respuestas del modelo. Valores más bajos generan respuestas más deterministas y enfocadas, mientras que valores más altos producen respuestas más creativas y diversas.',
      impact: 'Afecta directamente la variabilidad de las respuestas. Con temperatura baja, el modelo tiende a elegir las palabras más probables, resultando en respuestas más predecibles y consistentes. Con temperatura alta, el modelo considera opciones menos probables, generando respuestas más creativas pero potencialmente menos precisas.',
      recommendedValues: [
        { value: 0.0, description: 'Completamente determinista', useCase: 'Análisis factual, extracción de información estructurada' },
        { value: 0.2, description: 'Muy enfocado', useCase: 'Análisis de documentos, extracción de datos' },
        { value: 0.5, description: 'Equilibrado', useCase: 'Resúmenes, respuestas generales' },
        { value: 0.7, description: 'Moderadamente creativo', useCase: 'Generación de contenido, ideas' },
        { value: 1.0, description: 'Muy creativo', useCase: 'Brainstorming, generación de alternativas' }
      ],
      technicalDetails: 'Técnicamente, la temperatura es un parámetro que controla la distribución de probabilidad durante el muestreo. Una temperatura más baja hace que la distribución sea más "puntiaguda", favoreciendo tokens de alta probabilidad, mientras que una temperatura más alta "aplana" la distribución, dando más oportunidades a tokens menos probables.'
    },
    {
      id: 'maxTokens',
      name: 'Tokens Máximos',
      description: 'Limita la longitud máxima de la respuesta generada por el modelo. Un token representa aproximadamente 4 caracteres en inglés o 3/4 de una palabra.',
      impact: 'Determina cuánto contenido puede generar el modelo en una sola respuesta. Establecer un límite demasiado bajo puede resultar en respuestas truncadas, mientras que un límite demasiado alto puede generar respuestas excesivamente largas o consumir recursos innecesarios.',
      recommendedValues: [
        { value: 500, description: 'Respuesta corta', useCase: 'Respuestas concisas, extracción de datos específicos' },
        { value: 1000, description: 'Respuesta media', useCase: 'Análisis básico de documentos' },
        { value: 2000, description: 'Respuesta detallada', useCase: 'Análisis completo de documentos, generación de informes' },
        { value: 4000, description: 'Respuesta extensa', useCase: 'Análisis exhaustivo, documentos largos' }
      ],
      technicalDetails: 'Los tokens son las unidades básicas que el modelo procesa. Cada modelo tiene un límite máximo de tokens que puede generar en una respuesta, independientemente del valor que establezcas. Este parámetro te permite limitar la respuesta por debajo de ese máximo.'
    },
    {
      id: 'contextLength',
      name: 'Longitud de Contexto',
      description: 'Define cuánto texto puede procesar el modelo en una sola solicitud. Valores más altos permiten analizar documentos más largos, pero consumen más recursos de memoria y computación.',
      impact: 'Afecta la capacidad del modelo para "recordar" y procesar información de todo el documento. Una longitud de contexto mayor permite al modelo considerar más información al generar respuestas, lo que puede mejorar la coherencia y precisión, especialmente para documentos largos.',
      recommendedValues: [
        { value: 4096, description: 'Contexto estándar', useCase: 'Documentos cortos, consultas simples' },
        { value: 8192, description: 'Contexto amplio', useCase: 'Documentos de tamaño medio, análisis detallado' },
        { value: 16384, description: 'Contexto extenso', useCase: 'Documentos largos, análisis complejo' },
        { value: 32768, description: 'Contexto máximo', useCase: 'Documentos muy extensos, análisis exhaustivo' }
      ],
      technicalDetails: 'La longitud de contexto representa la "ventana de atención" del modelo, es decir, cuántos tokens puede considerar simultáneamente. Modelos como Gemma 3 y Llama 3 soportan contextos largos, pero aumentar este valor incrementa significativamente el uso de memoria GPU y puede ralentizar la inferencia.',
      modelSpecific: true,
      modelTypes: ['local']
    },
    {
      id: 'topP',
      name: 'Top P (Nucleus Sampling)',
      description: 'Controla la diversidad mediante muestreo de núcleo. El modelo considera solo las opciones más probables que suman la probabilidad p. Útil para controlar la aleatoriedad de manera diferente a la temperatura.',
      impact: 'Afecta qué tokens puede seleccionar el modelo en cada paso de generación. Un valor más bajo hace que el modelo considere solo las opciones más probables, mientras que un valor más alto permite considerar opciones menos probables. A diferencia de la temperatura, Top P establece un umbral dinámico basado en la distribución de probabilidad.',
      recommendedValues: [
        { value: 0.5, description: 'Muy enfocado', useCase: 'Extracción de datos estructurados, análisis factual' },
        { value: 0.7, description: 'Enfocado', useCase: 'Análisis de documentos, respuestas precisas' },
        { value: 0.9, description: 'Equilibrado', useCase: 'Análisis general, respuestas naturales' },
        { value: 0.95, description: 'Diverso', useCase: 'Generación creativa, múltiples perspectivas' }
      ],
      technicalDetails: 'Top P (o nucleus sampling) selecciona tokens de forma que la suma de sus probabilidades no exceda el valor p. Por ejemplo, con p=0.9, el modelo seleccionará entre los tokens más probables que suman el 90% de la probabilidad total. Se puede usar junto con temperatura para un control más fino de la generación.'
    },
    {
      id: 'frequencyPenalty',
      name: 'Penalización de Frecuencia',
      description: 'Reduce la probabilidad de que el modelo repita los mismos tokens o frases. Valores más altos penalizan más fuertemente la repetición.',
      impact: 'Ayuda a evitar que el modelo se quede "atascado" repitiendo las mismas palabras o frases. Un valor más alto fomenta respuestas más diversas y menos repetitivas, lo que puede ser útil para generar texto más natural y variado.',
      recommendedValues: [
        { value: 0.0, description: 'Sin penalización', useCase: 'Análisis factual, extracción de datos' },
        { value: 0.3, description: 'Penalización leve', useCase: 'Análisis de documentos, respuestas estructuradas' },
        { value: 0.7, description: 'Penalización moderada', useCase: 'Generación de contenido, respuestas naturales' },
        { value: 1.0, description: 'Penalización alta', useCase: 'Generación creativa, evitar repeticiones' }
      ],
      technicalDetails: 'La penalización de frecuencia modifica las probabilidades de los tokens en función de cuántas veces han aparecido ya en la respuesta. Técnicamente, reduce el logaritmo de la probabilidad de un token proporcionalmente a su frecuencia en el texto generado hasta el momento.',
      modelSpecific: true,
      modelTypes: ['cloud']
    },
    {
      id: 'presencePenalty',
      name: 'Penalización de Presencia',
      description: 'Reduce la probabilidad de que el modelo repita temas o conceptos. A diferencia de la penalización de frecuencia, penaliza la presencia de un token, no cuántas veces aparece.',
      impact: 'Fomenta que el modelo explore nuevos temas y conceptos en lugar de centrarse en los ya mencionados. Un valor más alto incentiva al modelo a hablar sobre temas nuevos, lo que puede resultar en respuestas más diversas temáticamente.',
      recommendedValues: [
        { value: 0.0, description: 'Sin penalización', useCase: 'Análisis enfocado, extracción de datos específicos' },
        { value: 0.3, description: 'Penalización leve', useCase: 'Análisis de documentos, mantener enfoque' },
        { value: 0.7, description: 'Penalización moderada', useCase: 'Generación de contenido variado' },
        { value: 1.0, description: 'Penalización alta', useCase: 'Exploración de múltiples aspectos o temas' }
      ],
      technicalDetails: 'La penalización de presencia aplica una penalización fija a todos los tokens que ya han aparecido en la respuesta, independientemente de cuántas veces hayan aparecido. Esto ayuda a evitar que el modelo se centre demasiado en ciertos temas o conceptos.',
      modelSpecific: true,
      modelTypes: ['cloud']
    }
  ];

  // Encontrar el parámetro activo
  const activeParameter = parameters.find(p => p.id === activeTab) || parameters[0];

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <BookOpen className="h-5 w-5 text-primary" />
        <h2 className="text-2xl font-bold tracking-tight">Guía de Parámetros de Modelos</h2>
      </div>

      <Alert>
        <Info className="h-4 w-4" />
        <AlertTitle>Optimización de modelos</AlertTitle>
        <AlertDescription>
          Ajustar estos parámetros puede mejorar significativamente los resultados del análisis de documentos.
          Experimenta con diferentes configuraciones para encontrar la óptima para tu caso de uso.
        </AlertDescription>
      </Alert>

      <div className="grid gap-6 md:grid-cols-3">
        <div className="md:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Parámetros</CardTitle>
              <CardDescription>
                Selecciona un parámetro para ver información detallada
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <Tabs
                orientation="vertical"
                value={activeTab}
                onValueChange={setActiveTab}
                className="w-full"
              >
                <TabsList className="flex flex-col h-auto w-full rounded-none">
                  {parameters.map(param => (
                    <TabsTrigger
                      key={param.id}
                      value={param.id}
                      className="justify-start px-4 py-3 text-left"
                    >
                      <div className="flex items-center">
                        {param.id === 'temperature' && <Thermometer className="h-4 w-4 mr-2" />}
                        {param.id === 'maxTokens' && <Zap className="h-4 w-4 mr-2" />}
                        {param.id === 'contextLength' && <Layers className="h-4 w-4 mr-2" />}
                        {param.id === 'topP' && <Percent className="h-4 w-4 mr-2" />}
                        {param.id === 'frequencyPenalty' && <BarChart4 className="h-4 w-4 mr-2" />}
                        {param.id === 'presencePenalty' && <BarChart4 className="h-4 w-4 mr-2" />}
                        <span>{param.name}</span>
                      </div>
                      {param.modelSpecific && (
                        <Badge variant="outline" className="ml-2 text-xs">
                          {param.modelTypes?.includes('local') ? 'Local' : 'Cloud'}
                        </Badge>
                      )}
                    </TabsTrigger>
                  ))}
                </TabsList>
              </Tabs>
            </CardContent>
          </Card>
        </div>

        <div className="md:col-span-2">
          <Card className="h-full">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-xl">
                    {activeParameter.name}
                    {activeParameter.modelSpecific && (
                      <Badge variant="outline" className="ml-2">
                        {activeParameter.modelTypes?.includes('local') ? (
                          <span className="flex items-center">
                            <Cpu className="h-3 w-3 mr-1" />
                            Solo modelos locales
                          </span>
                        ) : (
                          <span className="flex items-center">
                            <Brain className="h-3 w-3 mr-1" />
                            Solo modelos cloud
                          </span>
                        )}
                      </Badge>
                    )}
                  </CardTitle>
                  <CardDescription>
                    {activeParameter.description}
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="text-sm font-medium mb-2">Impacto en los resultados</h3>
                <p className="text-sm text-muted-foreground">
                  {activeParameter.impact}
                </p>
              </div>

              <Separator />

              <div>
                <h3 className="text-sm font-medium mb-2">Valores recomendados</h3>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Valor</TableHead>
                      <TableHead>Descripción</TableHead>
                      <TableHead>Caso de uso</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {activeParameter.recommendedValues.map((rec, index) => (
                      <TableRow key={index}>
                        <TableCell className="font-medium">{rec.value}</TableCell>
                        <TableCell>{rec.description}</TableCell>
                        <TableCell>{rec.useCase}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              <Separator />

              <div>
                <h3 className="text-sm font-medium mb-2">Detalles técnicos</h3>
                <p className="text-sm text-muted-foreground">
                  {activeParameter.technicalDetails}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
