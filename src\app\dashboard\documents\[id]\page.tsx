"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON>ef<PERSON>, Download, Trash2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { DocumentPreview } from "@/components/features/documents/document-preview"
import { createClient } from "@/lib/supabase/client"
import { toast } from "@/hooks/use-toast"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import Link from "next/link"
import { format } from "date-fns"
import { es } from "date-fns/locale"

// Definir la interfaz correctamente según las restricciones de Next.js App Router
type DocumentPageProps = {
  params: {
    id: string
  }
  searchParams: { [key: string]: string | string[] }
}

export default function DocumentPage({ params }: DocumentPageProps) {
  const router = useRouter()
  const [document, setDocument] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const supabase = createClient()

  useEffect(() => {
    const fetchDocument = async () => {
      setIsLoading(true)
      setError(null)

      try {
        const { data, error } = await supabase
          .from('documents')
          .select(`
            *,
            project:project_id(id, name),
            work_order:work_order_id(id, title),
            uploaded_by_user:uploaded_by(id, email, full_name)
          `)
          .eq('id', params.id)
          .single()

        if (error) throw error

        if (!data) {
          throw new Error("Documento no encontrado")
        }

        setDocument(data)
      } catch (error: unknown) {
        console.error("Error al cargar el documento:", error)
        const errorMessage = error instanceof Error ? error.message : "Ha ocurrido un error al cargar el documento";
        setError(errorMessage)
      } finally {
        setIsLoading(false)
      }
    }

    fetchDocument()
  }, [params.id, supabase])

  const handleDownload = () => {
    if (document?.public_url) {
      // Create a DOM element for the link
      const link = document.createElement("a")
      link.href = document.public_url
      link.download = document.filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    } else {
      toast({
        title: "Error",
        description: "No se puede descargar este documento",
        variant: "destructive",
      })
    }
  }

  const handleDelete = async () => {
    try {
      // Eliminar registro de la base de datos
      const { error: dbError } = await supabase
        .from("documents")
        .delete()
        .eq("id", params.id)

      if (dbError) throw dbError

      toast({
        title: "Documento eliminado",
        description: "El documento se ha eliminado correctamente",
      })

      // Redirigir a la lista de documentos
      router.push("/dashboard/documents")
      router.refresh()
    } catch (error: unknown) {
      console.error("Error al eliminar el documento:", error)
      const errorMessage = error instanceof Error ? error.message : "Ha ocurrido un error al eliminar el documento";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    }
  }

  if (isLoading) {
    return (
      <div className="flex h-[500px] items-center justify-center">
        <div className="flex flex-col items-center">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          <p className="mt-2 text-sm text-muted-foreground">Cargando documento...</p>
        </div>
      </div>
    )
  }

  if (error || !document) {
    return (
      <div className="flex h-[500px] flex-col items-center justify-center">
        <p className="text-center text-muted-foreground">{error || "Documento no encontrado"}</p>
        <Button
          variant="outline"
          className="mt-4"
          asChild
        >
          <Link href="/dashboard/documents">
            Volver a documentos
          </Link>
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="icon" asChild>
            <Link href="/dashboard/documents">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h2 className="text-3xl font-bold tracking-tight">{document.filename}</h2>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={handleDownload}
          >
            <Download className="mr-2 h-4 w-4" />
            Descargar
          </Button>
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="destructive">
                <Trash2 className="mr-2 h-4 w-4" />
                Eliminar
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>¿Eliminar documento?</AlertDialogTitle>
                <AlertDialogDescription>
                  Esta acción no se puede deshacer. ¿Estás seguro de que quieres eliminar este documento?
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancelar</AlertDialogCancel>
                <AlertDialogAction
                  onClick={handleDelete}
                  className="bg-red-600 hover:bg-red-700"
                >
                  Eliminar
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <DocumentPreview document={document} height={600} />
        </div>
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Información</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {document.description && (
                  <div>
                    <h3 className="font-medium">Descripción</h3>
                    <p className="text-sm text-muted-foreground">{document.description}</p>
                  </div>
                )}
                {document.category && (
                  <div>
                    <h3 className="font-medium">Categoría</h3>
                    <p className="text-sm text-muted-foreground">{document.category}</p>
                  </div>
                )}
                <div>
                  <h3 className="font-medium">Tamaño</h3>
                  <p className="text-sm text-muted-foreground">
                    {document.file_size < 1024 * 1024
                      ? `${(document.file_size / 1024).toFixed(2)} KB`
                      : `${(document.file_size / 1024 / 1024).toFixed(2)} MB`}
                  </p>
                </div>
                <div>
                  <h3 className="font-medium">Tipo</h3>
                  <p className="text-sm text-muted-foreground">{document.file_type}</p>
                </div>
                <div>
                  <h3 className="font-medium">Fecha de subida</h3>
                  <p className="text-sm text-muted-foreground">
                    {format(new Date(document.created_at), "PPP", { locale: es })}
                  </p>
                </div>
                {document.uploaded_by_user && (
                  <div>
                    <h3 className="font-medium">Subido por</h3>
                    <p className="text-sm text-muted-foreground">
                      {document.uploaded_by_user.full_name || document.uploaded_by_user.email}
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Relaciones</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium">Proyecto</h3>
                  {document.project ? (
                    <p className="text-sm">
                      <Link
                        href={`/dashboard/projects/${document.project.id}`}
                        className="text-primary hover:underline"
                      >
                        {document.project.name}
                      </Link>
                    </p>
                  ) : (
                    <p className="text-sm text-muted-foreground">No asociado a ningún proyecto</p>
                  )}
                </div>
                <div>
                  <h3 className="font-medium">Orden de trabajo</h3>
                  {document.work_order ? (
                    <p className="text-sm">
                      <Link
                        href={`/dashboard/work-orders/${document.work_order.id}`}
                        className="text-primary hover:underline"
                      >
                        {document.work_order.title}
                      </Link>
                    </p>
                  ) : (
                    <p className="text-sm text-muted-foreground">No asociado a ninguna orden de trabajo</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
