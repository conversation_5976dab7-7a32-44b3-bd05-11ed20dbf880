# File-Based Project Creation Integration Planning

## Overview

This document outlines the plan for integrating file-based project creation capabilities into the AdminCore  project management system. The integration will allow users to create projects by uploading and analyzing documents using various AI providers including Google's Gemini, OpenAI, and DeepSeek.

## Vision

Enable project managers to quickly create and populate project details by uploading relevant documents (contracts, specifications, RFPs, etc.) and using AI to extract key information such as project scope, timelines, budgets, and deliverables.

## Architecture

### High-Level Architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  Document       │     │  AI Provider    │     │  Project        │
│  Upload         │────▶│  Processing     │────▶│  Creation       │
│  Component      │     │  Service        │     │  Component      │
└─────────────────┘     └─────────────────┘     └─────────────────┘
        │                       │                       │
        ▼                       ▼                       ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  Storage        │     │  AI Provider    │     │  Database       │
│  Service        │     │  Factory        │     │  Service        │
└─────────────────┘     └─────────────────┘     └─────────────────┘
        │                       │                       │
        │                       │                       │
        ▼                       ▼                       ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  Supabase       │     │  AI Provider    │     │  Supabase       │
│  Storage        │     │  APIs           │     │  Database       │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

### Components

1. **Document Upload Component**
   - Enhanced version of the existing document upload component
   - Support for multiple file types (PDF, DOCX, TXT, etc.)
   - Progress indicators and validation
   - Option to select AI provider for analysis

2. **AI Provider Processing Service**
   - Provider-agnostic interface for AI document processing
   - Support for multiple AI providers (Gemini, OpenAI, DeepSeek)
   - Configurable processing options
   - Fallback mechanism for provider failures

3. **Project Creation Component**
   - Form pre-population based on AI analysis
   - Interactive editing of AI-suggested fields
   - Confidence scoring for extracted information
   - Option to save or discard AI suggestions

4. **AI Provider Factory**
   - Factory pattern for creating provider instances
   - Configuration management for each provider
   - Provider selection based on availability and priority

5. **Storage Service**
   - Integration with existing document storage system
   - File type detection and validation
   - Text extraction from different document formats

6. **Database Service**
   - Storage of AI analysis results
   - Project creation from analysis data
   - Tracking of AI-generated projects

## Technical Stack

### Frontend
- Next.js (existing)
- React (existing)
- Tailwind CSS + Shadcn/UI (existing)
- New components for AI interaction and file processing

### Backend
- Next.js API routes
- Supabase for storage and database (existing)
- AI provider SDKs/APIs:
  - Google Generative AI SDK for Gemini
  - OpenAI SDK for GPT models
  - DeepSeek API client

### Document Processing
- PDF.js for PDF text extraction
- Mammoth.js for DOCX processing
- Simple text parsing for TXT files

## Database Schema Changes

### New Tables

#### `ai_document_analyses`
- `id` (UUID, PK)
- `document_id` (UUID, FK to documents)
- `provider` (TEXT) - The AI provider used
- `analysis_data` (JSONB) - The full analysis results
- `created_at` (TIMESTAMP)
- `status` (TEXT) - 'pending', 'completed', 'failed'
- `error_message` (TEXT)
- `confidence_score` (NUMERIC) - Overall confidence in the analysis

#### `ai_provider_configs`
- `id` (UUID, PK)
- `provider_name` (TEXT) - 'gemini', 'openai', 'deepseek'
- `api_key` (TEXT, encrypted)
- `model_name` (TEXT)
- `is_active` (BOOLEAN)
- `priority` (INTEGER) - For fallback ordering
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

### Modified Tables

#### `projects`
- Add `ai_generated` (BOOLEAN) - Flag for AI-generated projects
- Add `source_document_id` (UUID, FK to documents) - Reference to the source document
- Add `ai_provider` (TEXT) - The AI provider used for generation

## API Endpoints

### Document Analysis
- `POST /api/ai/analyze-document` - Submit a document for AI analysis
- `GET /api/ai/analysis/:id` - Get analysis results
- `POST /api/ai/create-project-from-analysis` - Create a project from analysis

### AI Provider Management
- `GET /api/ai/providers` - List available providers
- `POST /api/ai/providers` - Add/update provider configuration
- `DELETE /api/ai/providers/:id` - Remove provider configuration
- `GET /api/ai/providers/active` - Get currently active providers

## Environment Variables

```
# Google Gemini API
GEMINI_API_KEY=your_gemini_api_key
GEMINI_MODEL=gemini-pro

# OpenAI API
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4

# DeepSeek API
DEEPSEEK_API_KEY=your_deepseek_api_key
DEEPSEEK_MODEL=deepseek-chat
```

## Security Considerations

1. **API Key Management**
   - All API keys will be stored encrypted in the database
   - Keys will never be exposed to the client-side
   - Server-side only access to AI provider APIs

2. **Document Security**
   - Enforce proper access controls for uploaded documents
   - Implement secure document storage with Supabase Storage
   - Ensure document content is only processed server-side

3. **Data Privacy**
   - Implement proper data retention policies
   - Ensure no sensitive data is stored in logs
   - Provide options to delete analysis data

## Implementation Phases

### Phase 1: Foundation (1-2 weeks)
- Set up AI provider integration framework
- Implement document upload with AI processing option
- Create database schema changes
- Implement basic text extraction from documents

### Phase 2: Gemini Integration (1 week)
- Implement Gemini API client
- Create project information extraction prompts
- Build UI for reviewing AI suggestions
- Implement form pre-population from Gemini analysis

### Phase 3: Provider Expansion (2 weeks)
- Add support for OpenAI
- Add support for DeepSeek
- Implement provider fallback mechanism
- Create provider selection UI

### Phase 4: Advanced Features (1-2 weeks)
- Implement confidence scoring
- Add support for multiple document analysis
- Create templates for different document types
- Implement batch processing for multiple files

## Testing Strategy

1. **Unit Testing**
   - Test AI provider integration modules
   - Test document processing utilities
   - Test project creation from analysis data

2. **Integration Testing**
   - Test end-to-end document upload to project creation
   - Test provider fallback mechanisms
   - Test with various document types and formats

3. **Performance Testing**
   - Measure document processing times
   - Optimize for large documents
   - Test concurrent processing

## Constraints and Limitations

1. **AI Provider Limitations**
   - API rate limits
   - Token/character limits
   - Cost considerations

2. **Document Type Support**
   - Initial focus on text-based documents (PDF, DOCX, TXT)
   - Limited support for image-based documents
   - No support for specialized formats initially

3. **Language Support**
   - Primary focus on Spanish and English documents
   - Limited support for other languages

## Success Metrics

1. **Accuracy**
   - Percentage of correctly extracted project fields
   - Reduction in manual data entry time

2. **Performance**
   - Document processing time
   - End-to-end project creation time

3. **User Adoption**
   - Percentage of projects created using AI
   - User satisfaction ratings

## References

- [Google Gemini API Documentation](https://ai.google.dev/docs)
- [OpenAI API Documentation](https://platform.openai.com/docs/api-reference)
- [DeepSeek API Documentation](https://platform.deepseek.com/api-reference)
- [Supabase Storage Documentation](https://supabase.com/docs/guides/storage)
- [PDF.js Documentation](https://mozilla.github.io/pdf.js/)
- [Mammoth.js Documentation](https://github.com/mwilliamson/mammoth.js)
