/**
 * @ai-file-description: "API endpoints for managing AI providers"
 * @ai-related-files: ["../analyze-document/route.ts", "../create-project-from-analysis/route.ts"]
 * @ai-owner: "File-Based Projects"
 */

// Define the interface for AI provider configurations
interface AIProviderConfig {
  id?: number; // Assuming 'id' is the primary key in the 'ai_provider_configs' table
  provider_name: string;
  api_key?: string; // Optional as it's masked for admins and not sent for non-admins
  model_name: string;
  priority: number;
  is_active: boolean;
  created_at?: string; // ISO date string
  updated_at?: string; // ISO date string
}

import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server-client';
import { ProviderFactory } from '@/lib/ai-providers/provider-factory';
import { cookies } from 'next/headers';

/**
 * GET handler for retrieving AI providers
 *
 * @ai-responsibility: "Retrieves available AI providers"
 */
export async function GET(request: Request) {
  try {
    // Create Supabase client
    const cookieStore = cookies();
    const supabase = createClient(cookieStore);

    // Get user data for authorization
    // No pasar parámetros a getUser() para compatibilidad con todas las versiones de Supabase
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError || !userData.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user has admin role
    const userId = userData.user.id;
    const { data: userProfile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', userId)
      .single();

    const isAdmin = userProfile?.role?.includes('admin') || false;

    // Get query parameters
    const url = new URL(request.url);
    const activeOnly = url.searchParams.get('active') === 'true';

    if (isAdmin) {
      // Admins can see all providers with API keys (masked)
      let query = supabase
        .from('ai_provider_configs')
        .select('*')
        .order('priority', { ascending: true });

      if (activeOnly) {
        query = query.eq('is_active', true);
      }

      const { data: providers, error: providersError } = await query;

      if (providersError) {
        console.error('Error fetching providers:', providersError);
        return NextResponse.json(
          { error: 'Failed to fetch providers' },
          { status: 500 }
        );
      }

      // Mask API keys
      const maskedProviders = providers.map((provider: AIProviderConfig) => ({
        ...provider,
        api_key: provider.api_key ? '********' : null
      }));

      return NextResponse.json(maskedProviders);
    } else {
      // Non-admins can only see active providers without API keys
      const { data: providers, error: providersError } = await supabase
        .from('ai_provider_configs')
        .select('provider_name, model_name, priority, is_active')
        .eq('is_active', true)
        .order('priority', { ascending: true });

      if (providersError) {
        console.error('Error fetching providers:', providersError);
        return NextResponse.json(
          { error: 'Failed to fetch providers' },
          { status: 500 }
        );
      }

      // Format providers for UI
      const formattedProviders = providers.map((provider: AIProviderConfig) => ({
        id: provider.provider_name, // Using provider_name as id for the formatted response
        name: ProviderFactory.getProviderDisplayName(provider.provider_name),
        model: provider.model_name,
        priority: provider.priority,
        is_active: provider.is_active
      }));

      return NextResponse.json(formattedProviders);
    }
  } catch (error) {
    console.error('Unexpected error fetching providers:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST handler for creating or updating AI providers
 *
 * @ai-responsibility: "Creates or updates AI provider configurations"
 */
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { provider_name, api_key, model_name, is_active, priority } = body;

    if (!provider_name || !api_key || !model_name) {
      return NextResponse.json(
        { error: 'Provider name, API key, and model name are required' },
        { status: 400 }
      );
    }

    // Create Supabase client
    const cookieStore = cookies();
    const supabase = createClient(cookieStore);

    // Get user data for authorization
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError || !userData.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user has admin role
    const userId = userData.user.id;
    const { data: userProfile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', userId)
      .single();

    const isAdmin = userProfile?.role?.includes('admin') || false;

    if (!isAdmin) {
      return NextResponse.json(
        { error: 'Only administrators can manage AI providers' },
        { status: 403 }
      );
    }

    // Check if provider exists
    const { data: existingProvider } = await supabase
      .from('ai_provider_configs')
      .select('*')
      .eq('provider_name', provider_name)
      .single();

    let result;

    if (existingProvider) {
      // Update existing provider
      result = await supabase
        .from('ai_provider_configs')
        .update({
          api_key,
          model_name,
          is_active: is_active !== undefined ? is_active : true,
          priority: priority !== undefined ? priority : existingProvider.priority,
          updated_at: new Date().toISOString()
        })
        .eq('provider_name', provider_name)
        .select();
    } else {
      // Create new provider
      // Get highest priority if not specified
      let newPriority = priority;

      if (newPriority === undefined) {
        const { data: highestPriority } = await supabase
          .from('ai_provider_configs')
          .select('priority')
          .order('priority', { ascending: false })
          .limit(1)
          .single();

        newPriority = highestPriority ? highestPriority.priority + 1 : 1;
      }

      result = await supabase
        .from('ai_provider_configs')
        .insert({
          provider_name,
          api_key,
          model_name,
          is_active: is_active !== undefined ? is_active : true,
          priority: newPriority,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select();
    }

    if (result.error) {
      console.error('Error saving provider:', result.error);
      return NextResponse.json(
        { error: 'Failed to save provider configuration' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      id: result.data[0].provider_name,
      name: ProviderFactory.getProviderDisplayName(result.data[0].provider_name),
      model: result.data[0].model_name,
      priority: result.data[0].priority,
      is_active: result.data[0].is_active
    });
  } catch (error) {
    console.error('Unexpected error saving AI provider:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

/**
 * DELETE handler for removing AI providers
 *
 * @ai-responsibility: "Deletes AI provider configurations"
 */
export async function DELETE(request: Request) {
  try {
    // Get provider name from URL
    const url = new URL(request.url);
    const providerName = url.searchParams.get('provider');

    if (!providerName) {
      return NextResponse.json(
        { error: 'Provider name is required' },
        { status: 400 }
      );
    }

    // Create Supabase client
    const cookieStore = cookies();
    const supabase = createClient(cookieStore);

    // Get user data for authorization
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError || !userData.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user has admin role
    const userId = userData.user.id;
    const { data: userProfile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', userId)
      .single();

    const isAdmin = userProfile?.role?.includes('admin') || false;

    if (!isAdmin) {
      return NextResponse.json(
        { error: 'Only administrators can manage AI providers' },
        { status: 403 }
      );
    }

    // Delete provider
    const { error: deleteError } = await supabase
      .from('ai_provider_configs')
      .delete()
      .eq('provider_name', providerName);

    if (deleteError) {
      console.error('Error deleting provider:', deleteError);
      return NextResponse.json(
        { error: 'Failed to delete provider configuration' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: 'Provider configuration deleted successfully'
    });
  } catch (error) {
    console.error('Unexpected error deleting AI provider:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
