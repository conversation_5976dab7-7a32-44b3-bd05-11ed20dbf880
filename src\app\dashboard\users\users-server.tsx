import { getUsersFromAdminApi, getUsersFromPublicTable } from '@/lib/supabase/admin-client'
import { supabaseConfig, validateSupabaseConfig } from '@/lib/supabase/config'

export async function getServerSideUsers() {
  try {
    // Validar la configuración de Supabase
    const isConfigValid = validateSupabaseConfig()

    console.log('Supabase config validation result:', isConfigValid)
    console.log('Supabase URL:', supabaseConfig.url)
    console.log('Service Role Key exists:', !!supabaseConfig.serviceRoleKey)
    console.log('Service Role Key length:', supabaseConfig.serviceRoleKey.length)

    if (!isConfigValid) {
      console.error('Invalid Supabase configuration')
      // Continuar con datos hardcodeados como último recurso
    }

    // Intentar obtener usuarios desde la API de administración
    console.log('Attempting to get users from Admin API')
    const adminUsers = await getUsersFromAdminApi()

    if (adminUsers && adminUsers.length > 0) {
      console.log('Successfully retrieved users from Admin API:', adminUsers.length)
      return adminUsers
    }

    // Si no se pudieron obtener usuarios desde la API de administración, intentar con la tabla pública
    console.log('Attempting to get users from public table')
    const publicUsers = await getUsersFromPublicTable()

    if (publicUsers && publicUsers.length > 0) {
      console.log('Successfully retrieved users from public table:', publicUsers.length)
      return publicUsers
    }

    console.log('Failed to retrieve users from both sources')

    // Devolver un array vacío en lugar de datos hardcodeados
    console.log('No users found, returning empty array')
    return []

  } catch (error) {
    console.error('Unexpected error in getServerSideUsers:', error)
    console.error('Error details:', JSON.stringify(error))

    // Devolver un array vacío en lugar de datos hardcodeados
    console.log('Error occurred, returning empty array')
    return []
  }
}
