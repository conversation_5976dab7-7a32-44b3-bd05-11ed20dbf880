/**
 * @ai-file-description: "Fallback implementation for canvas-related functionality"
 * @ai-related-files: ["pdf-worker.ts", "document-text-extractor.ts"]
 * @ai-owner: "File-Based Projects"
 */

// Interface for window with canvas polyfills
interface WindowWithCanvasPolyfill extends Window {
  HTMLCanvasElement: {
    prototype: {
      getContext: () => CanvasRenderingContext2D | null;
      toDataURL: () => string;
      toBlob: (callback: (blob: Blob | null) => void) => void;
    };
  };
}

/**
 * Check if canvas is available in the current environment
 * @returns boolean indicating if canvas is available
 */
export function isCanvasAvailable(): boolean {
  if (typeof window === 'undefined') return false;

  try {
    // Try to create a canvas element
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    return !!context;
  } catch (error) {
    console.warn('Canvas is not available:', error);
    return false;
  }
}

/**
 * Safely check if canvas is available without throwing errors
 * This is useful for SSR environments
 */
export function safelyCheckCanvas(): boolean {
  try {
    return isCanvasAvailable();
  } catch (error) {
    return false;
  }
}

/**
 * Create a mock canvas implementation for environments where canvas is not available
 * This is used as a fallback when the real canvas is not available
 */
export function createMockCanvas() {
  if (typeof window === 'undefined') return null;

  // Only create mock if real canvas is not available
  if (isCanvasAvailable()) return null;

  try {
    // Create a mock canvas implementation
    const mockCanvas = {
      getContext: () => ({
        drawImage: () => {},
        getImageData: () => ({ data: new Uint8ClampedArray() }),
        putImageData: () => {},
        createImageData: () => ({ data: new Uint8ClampedArray() }),
        fillRect: () => {},
        fillText: () => {},
        measureText: () => ({ width: 0 }),
        clearRect: () => {},
      }),
      toDataURL: () => 'data:,',
      toBlob: (callback: (blob: Blob | null) => void) => callback(new Blob()),
      width: 0,
      height: 0,
    };

    console.log('Created mock canvas implementation');
    return mockCanvas;
  } catch (error) {
    console.error('Failed to create mock canvas:', error);
    return null;
  }
}

/**
 * Initialize canvas fallback if needed
 * This should be called early in the application lifecycle
 */
export function initCanvasFallback(): void {
  if (typeof window === 'undefined') return;

  try {
    // Check if canvas is available
    if (!isCanvasAvailable()) {
      console.warn('Canvas is not available, initializing fallback');

      // Create mock canvas
      const mockCanvas = createMockCanvas();

      // Attempt to polyfill canvas
      if (mockCanvas) {
        const windowWithPolyfill = window as unknown as WindowWithCanvasPolyfill;
        windowWithPolyfill.HTMLCanvasElement.prototype.getContext =
          mockCanvas.getContext;
        windowWithPolyfill.HTMLCanvasElement.prototype.toDataURL =
          mockCanvas.toDataURL;
        windowWithPolyfill.HTMLCanvasElement.prototype.toBlob =
          mockCanvas.toBlob;

        console.log('Canvas fallback initialized');
      }
    }
  } catch (error) {
    console.error('Failed to initialize canvas fallback:', error);
  }
}
