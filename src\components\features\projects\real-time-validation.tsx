/**
 * @ai-file-description: "Real-time validation component for project forms"
 * @ai-related-files: ["project-form.tsx", "../../ui/form.tsx"]
 * @ai-owner: "Projects"
 */

"use client"

import { useState, useEffect } from "react"
import { Check, X, AlertCircle } from "lucide-react"
import { cn } from "@/lib/utils"

interface ValidationState {
  isValid: boolean
  isValidating: boolean
  message?: string
  type?: 'success' | 'error' | 'warning'
}

interface RealTimeValidationProps {
  value: unknown
  validator: (value: unknown) => Promise<ValidationState> | ValidationState
  debounceMs?: number
  className?: string
  showIcon?: boolean
}

/**
 * Real-time validation component that provides immediate feedback
 *
 * @ai-responsibility: "Provides real-time validation feedback for form fields"
 */
export function RealTimeValidation({
  value,
  validator,
  debounceMs = 300,
  className,
  showIcon = true
}: RealTimeValidationProps) {
  const [validationState, setValidationState] = useState<ValidationState>({
    isValid: true,
    isValidating: false
  })

  useEffect(() => {
    if (value === undefined || value === null || value === '') {
      setValidationState({
        isValid: true,
        isValidating: false
      })
      return
    }

    setValidationState(prev => ({ ...prev, isValidating: true }))

    const timeoutId = setTimeout(async () => {
      try {
        const result = await validator(value)
        setValidationState({
          ...result,
          isValidating: false
        })
      } catch (error) {
        setValidationState({
          isValid: false,
          isValidating: false,
          message: 'Error de validación',
          type: 'error'
        })
      }
    }, debounceMs)

    return () => clearTimeout(timeoutId)
  }, [value, validator, debounceMs])

  if (!validationState.message && validationState.isValid && !validationState.isValidating) {
    return null
  }

  const getIcon = () => {
    if (!showIcon) return null

    if (validationState.isValidating) {
      return <AlertCircle className="h-4 w-4 animate-spin text-blue-500" />
    }

    if (validationState.isValid) {
      return <Check className="h-4 w-4 text-green-500" />
    }

    return <X className="h-4 w-4 text-red-500" />
  }

  const getTextColor = () => {
    if (validationState.isValidating) return 'text-blue-600'
    if (validationState.isValid) return 'text-green-600'
    return 'text-red-600'
  }

  return (
    <div className={cn(
      "flex items-center gap-2 text-sm mt-1",
      getTextColor(),
      className
    )}>
      {getIcon()}
      {validationState.message && (
        <span>{validationState.message}</span>
      )}
    </div>
  )
}

/**
 * Validation functions for common project form fields
 */
export const projectValidators = {
  /**
   * Validates project name
   */
  name: (value: string): ValidationState => {
    if (!value || value.trim() === '') {
      return {
        isValid: false,
        isValidating: false,
        message: 'El nombre es obligatorio',
        type: 'error'
      }
    }

    if (value.length < 3) {
      return {
        isValid: false,
        isValidating: false,
        message: 'El nombre debe tener al menos 3 caracteres',
        type: 'error'
      }
    }

    if (value.length > 100) {
      return {
        isValid: false,
        isValidating: false,
        message: 'El nombre no puede exceder 100 caracteres',
        type: 'warning'
      }
    }

    return {
      isValid: true,
      isValidating: false,
      message: 'Nombre válido',
      type: 'success'
    }
  },

  /**
   * Validates budget amount
   */
  budget: (value: string): ValidationState => {
    if (!value || value.trim() === '') {
      return {
        isValid: true,
        isValidating: false,
        message: undefined
      }
    }

    const numValue = parseFloat(value)

    if (isNaN(numValue)) {
      return {
        isValid: false,
        isValidating: false,
        message: 'El presupuesto debe ser un número válido',
        type: 'error'
      }
    }

    if (numValue < 0) {
      return {
        isValid: false,
        isValidating: false,
        message: 'El presupuesto no puede ser negativo',
        type: 'error'
      }
    }

    if (numValue > 999999999) {
      return {
        isValid: false,
        isValidating: false,
        message: 'El presupuesto es demasiado alto',
        type: 'warning'
      }
    }

    return {
      isValid: true,
      isValidating: false,
      message: 'Presupuesto válido',
      type: 'success'
    }
  },

  /**
   * Validates UUID format
   */
  uuid: (value: string): ValidationState => {
    if (!value || value.trim() === '') {
      return {
        isValid: true,
        isValidating: false,
        message: undefined
      }
    }

    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i

    if (!uuidRegex.test(value)) {
      return {
        isValid: false,
        isValidating: false,
        message: 'Formato de ID inválido',
        type: 'error'
      }
    }

    return {
      isValid: true,
      isValidating: false,
      message: 'ID válido',
      type: 'success'
    }
  },

  /**
   * Validates date range
   */
  dateRange: (startDate: Date | undefined, endDate: Date | undefined) => {
    return (value: Date | undefined): ValidationState => {
      if (!value) {
        return {
          isValid: true,
          isValidating: false,
          message: undefined
        }
      }

      if (startDate && endDate && startDate > endDate) {
        return {
          isValid: false,
          isValidating: false,
          message: 'La fecha de inicio debe ser anterior a la fecha de fin',
          type: 'error'
        }
      }

      return {
        isValid: true,
        isValidating: false,
        message: 'Fecha válida',
        type: 'success'
      }
    }
  }
}
