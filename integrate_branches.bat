@echo off
echo ===========================================
echo Branch Integration Script
echo ===========================================

:: Configuration
set MAIN_BRANCH=main
set GIT_REPO_PATH=c:\Users\<USER>\Documents\admincore

:: Change to repository directory
cd /d %GIT_REPO_PATH%

:: Ensure working directory is clean
git status
set /p "confirm=Is the working directory clean? (y/n) "
if /i not "%confirm%"=="y" (
    echo Please commit or stash your changes before running this script.
    pause
    exit /b 1
)

:: Fetch all remote branches
echo.
echo Fetching all remote branches...
git fetch --all

:: Get list of all remote branches
for /f "tokens=2" %%b in ('git branch -r ^| findstr /v "HEAD" ^| findstr /v "%MAIN_BRANCH%"') do (
    set "BRANCH=%%~nb"
    
    echo.
    echo ===========================================
    echo Processing branch: !BRANCH!
    echo ===========================================
    
    :: Checkout the branch
    git checkout !BRANCH!
    
    :: Pull latest changes
    git pull origin !BRANCH!
    
    :: Checkout main and update it
    git checkout %MAIN_BRANCH%
    git pull origin %MAIN_BRANCH%
    
    :: Merge the branch into main
    echo.
    echo Merging !BRANCH! into %MAIN_BRANCH%...
    set "merge_output="
    for /f "delims=" %%m in ('git merge --no-ff !BRANCH! 2^>^&1') do (
        set "merge_output=!merge_output!%%m"
        echo %%m
    )
    
    if "!merge_output:CONFLICT=!" neq "%merge_output%" (
        echo.
        echo ===========================================
        echo CONFLICT DETECTED in !BRANCH!
        echo Please resolve conflicts and then continue.
        echo ===========================================
        pause
    ) else (
        echo.
        echo Successfully merged !BRANCH! into %MAIN_BRANCH%
    )
    
    echo.
    echo Press any key to continue to next branch...
    pause >nul
)

echo.
echo ===========================================
echo All branches have been processed!
echo ===========================================
pause
