"use client"

import { useState, Suspense } from "react"
import { use<PERSON><PERSON>er, useSearch<PERSON>ara<PERSON> } from "next/navigation"
import { createClient } from "@/lib/supabase/client"
import { ServiceRequestForm } from "@/components/features/service-requests/service-request-form"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import Link from "next/link"
import { serviceRequestService } from "@/lib/services/service-request-service"

function NewServiceRequestPageInner() {
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()
  const searchParams = useSearchParams()

  // Get client_id and equipment_id from query parameters if available
  const clientId = searchParams.get('client_id') || undefined
  const equipmentId = searchParams.get('equipment_id') || undefined

  const handleSubmit = async (data: unknown) => {
    setIsLoading(true)
    try {
      // Use the service to create the service request
      const { data: serviceRequest, error } = await serviceRequestService.createServiceRequest({
        title: data.title,
        description: data.description,
        client_id: data.client_id,
        priority: data.priority,
        source: data.source,
        assigned_to: data.assigned_to,
        due_date: data.due_date ? data.due_date.toISOString() : undefined,
        location: data.location,
        estimated_hours: data.estimated_hours,
        is_billable: data.is_billable,
        external_reference: data.external_reference,
        equipment_id: data.equipment_id,
      })

      if (error) throw error

      toast({
        title: "Service Request Created",
        description: "The service request has been created successfully.",
      })

      // Redirect to the service request detail page
      router.push(`/dashboard/service-requests/${serviceRequest?.id}`)
    } catch (error) {
      console.error("Error creating service request:", error)
      toast({
        title: "Error",
        description: "There was an error creating the service request. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <Button variant="outline" size="sm" asChild>
            <Link href="/dashboard/service-requests">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Service Requests
            </Link>
          </Button>
          <h2 className="text-3xl font-bold tracking-tight mt-2">New Service Request</h2>
          <p className="text-muted-foreground mt-1">
            Create a new technical service request.
          </p>
        </div>
      </div>

      <div className="border rounded-lg p-6">
        <ServiceRequestForm
          onSubmit={handleSubmit}
          isLoading={isLoading}
          clientId={clientId}
          equipmentId={equipmentId}
        />
      </div>
    </div>
  )
}

export default function NewServiceRequestPage() {
  return (
    <Suspense fallback={<div className="p-8 text-center">Cargando...</div>}>
      <NewServiceRequestPageInner />
    </Suspense>
  )
}
