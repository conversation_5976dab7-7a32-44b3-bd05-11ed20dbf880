'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/use-auth'
import { GitHubService, type GitHubConnection, type GitHubRepository } from '@/lib/services/github-service'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { GitHubAuthButton } from '@/components/auth/github-auth-button'
import { Separator } from '@/components/ui/separator'
import { FaGithub, FaSync, FaExternalLinkAlt, FaLock, FaUnlock } from 'react-icons/fa'
import { Loader2 } from 'lucide-react'

// This is a client component that will be hydrated on the client
// We'll move the data fetching to a server component in the future if needed

export default function GitHubIntegrationPage() {
  const { signInWithGitHub } = useAuth()
  const [connection, setConnection] = useState<GitHubConnection | null>(null);
  const [repositories, setRepositories] = useState<GitHubRepository[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSyncing, setIsSyncing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastSynced, setLastSynced] = useState<string | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const githubService = new GitHubService()

  useEffect(() => {
    loadGitHubData()
  }, [])

  const loadGitHubData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Fetch data from API route
      const response = await fetch('/api/github', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        cache: 'no-store', // Prevent caching to get fresh data
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to fetch GitHub data');
      }

      const data = await response.json();
      setConnection(data.connection || null);
      setRepositories(data.repositories || []);
      setIsConnected(!!data.connection);

      if (data.lastSynced) {
        setLastSynced(data.lastSynced);
      }
    } catch (e: unknown) {
      console.error('Error loading GitHub data:', e);
      const errorMessage = e instanceof Error ? e.message : 'Error loading GitHub data';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSyncRepositories = async () => {
    try {
      setIsSyncing(true);
      setError(null);

      const response = await fetch('/api/github/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to sync repositories');
      }

      const result = await response.json();

      // Update local state with synced data
      if (result.success) {
        setConnection(result.connection || null);
        setRepositories(result.repositories || []);
        setIsConnected(!!result.connection);
        setLastSynced(new Date().toISOString());

        // Show success message
        // You can add a toast notification here if you have a notification system
        console.log('Repositories synced successfully');
      } else {
        throw new Error(result.error || 'Synchronization failed');
      }
    } catch (e: unknown) {
      console.error('Error syncing repositories:', e);
      const errorMessage = e instanceof Error ? e.message : 'Error syncing repositories';
      setError(errorMessage);
    } finally {
      setIsSyncing(false);
    }
  };

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-3xl font-bold mb-6">Integración con GitHub</h1>

      {isLoading ? (
        <div className="flex justify-center items-center h-40">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Cargando...</span>
        </div>
      ) : connection ? (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="flex items-center">
                    <FaGithub className="mr-2 h-6 w-6" />
                    Cuenta de GitHub conectada
                  </CardTitle>
                  <CardDescription className="mt-1">
                    Conectado como <strong>{connection.github_username}</strong>
                    {lastSynced && (
                      <span className="block text-xs text-muted-foreground mt-1">
                        Última sincronización: {new Date(lastSynced).toLocaleString()}
                      </span>
                    )}
                  </CardDescription>
                </div>
                <Button
                  variant="outline"
                  onClick={handleSyncRepositories}
                  disabled={isSyncing}
                  className="flex items-center"
                >
                  {isSyncing ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Sincronizando...
                    </>
                  ) : (
                    <>
                      <FaSync className="mr-2 h-4 w-4" />
                      Sincronizar
                    </>
                  )}
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-4">
                {connection.github_avatar_url && (
                  <img
                    src={connection.github_avatar_url}
                    alt={connection.github_username}
                    className="h-16 w-16 rounded-full border border-gray-200"
                  />
                )}
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <p className="font-medium">{connection.github_username}</p>
                    {connection.github_email && (
                      <span className="text-sm text-muted-foreground">
                        ({connection.github_email})
                      </span>
                    )}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    <p>Conectado el {new Date(connection.created_at).toLocaleDateString()}</p>
                    {connection.last_synced_at && (
                      <p>Última actividad: {new Date(connection.last_synced_at).toLocaleString()}</p>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <h2 className="text-2xl font-bold mt-8 mb-4">Repositorios disponibles</h2>

          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}

          {repositories.length === 0 ? (
            <div className="text-center p-8 bg-gray-50 rounded-lg">
              <p className="text-gray-500">No se encontraron repositorios. Haz clic en "Sincronizar repositorios" para obtener tus repositorios de GitHub.</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {repositories.map((repo) => (
                <Card key={repo.id}>
                  <CardHeader>
                    <CardTitle className="flex items-center text-lg">
                      {repo.is_private ? (
                        <FaLock className="mr-2 h-4 w-4" />
                      ) : (
                        <FaUnlock className="mr-2 h-4 w-4" />
                      )}
                      {repo.repository_name}
                    </CardTitle>
                    <CardDescription>
                      {repo.repository_description || 'Sin descripción'}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p><strong>Rama predeterminada:</strong> {repo.default_branch}</p>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button variant="outline" asChild>
                      <a href={repo.repository_url} target="_blank" rel="noopener noreferrer" className="flex items-center">
                        <FaExternalLinkAlt className="mr-2 h-4 w-4" />
                        Ver en GitHub
                      </a>
                    </Button>
                    <Button variant="default">
                      Sincronizar proyecto
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </div>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Conectar con GitHub</CardTitle>
            <CardDescription>
              Conecta tu cuenta de GitHub para sincronizar tus repositorios con AdminCore
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="mb-4">
              Al conectar tu cuenta de GitHub, podrás:
            </p>
            <ul className="list-disc pl-5 mb-4 space-y-1">
              <li>Sincronizar repositorios como proyectos</li>
              <li>Mantener tus proyectos actualizados automáticamente</li>
              <li>Acceder a información de commits y ramas</li>
            </ul>

            {error && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                {error}
              </div>
            )}
          </CardContent>
          <CardFooter>
            <GitHubAuthButton mode="signin" fullWidth />
          </CardFooter>
        </Card>
      )}
    </div>
  )
}
