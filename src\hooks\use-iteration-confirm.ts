import { useState, useCallback } from "react"

interface UseIterationConfirmOptions {
  onConfirm: () => void
  onCancel?: () => void
}

export function useIterationConfirm({ onConfirm, onCancel }: UseIterationConfirmOptions) {
  const [showConfirm, setShowConfirm] = useState(false)

  const handleConfirm = useCallback(() => {
    setShowConfirm(false)
    onConfirm()
  }, [onConfirm])

  const handleCancel = useCallback(() => {
    setShowConfirm(false)
    onCancel?.()
  }, [onCancel])

  const requestConfirmation = useCallback(() => {
    setShowConfirm(true)
  }, [])

  return {
    showConfirm,
    requestConfirmation,
    handleConfirm,
    handleCancel
  }
}