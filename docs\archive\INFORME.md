# INFORME DE AUDITORÍA DE DOCUMENTACIÓN – AdminCore  

> **Objetivo:** Inventariar la carpeta `docs/`, clasificar cada archivo/carpeta por temática y proponer su destino (fusionar en `PLANNING.md`, fusionar en `TASK.md`, o eliminar/archivar).  
> **Fecha:** 10-Jun-2025  

---

## 1 · Matriz de Archivos y Destino Propuesto

| Ítem | Tipo | Descripción Breve | Destino | Observaciones |
|------|------|-------------------|---------|---------------|
| `planning.md` | MD | Plan general de implementación, visión, estrategia por fases. | **KEEP → PLANNING.md** | Servirá como núcleo; re-estructurar secciones y actualizar métricas. |
| `implementation-status-report.md` | MD | Porcentaje de avance por módulo, problemas y faltantes. | REFERENCIA → PLANNING.md (sección *Estado Actual*) | Consolidar tablas de % en sección Estado / Riesgos. |
| `improvement-roadmap.md` | MD | Roadmap de mejoras a 6 meses. | KEEP → PLANNING.md (*Roadmap*) | Unificar con hitos futuros. |
| `testing-strategy.md` | MD | Estrategia de testing, cobertura, tooling. | KEEP → PLANNING.md (*Testing & QA*) | Añadir a sección Calidad. |
| `action-plan.md` | MD | Plan detallado Fase 1 con tareas diarias. | KEEP → TASK.md | Migrar bullets a backlog activo. |
| `tasks.md` | MD | Backlog granular por módulo. | KEEP → TASK.md | Combinar con `updated-tasks.md`. |
| `updated-tasks.md` | MD | Versión posterior de backlog. | MERGE → TASK.md y luego **DELETE** | Resolver solapamientos con `tasks.md`. |
| `progress.md` | MD | Registro de progreso y problemas. | KEEP → TASK.md (Sección *Hecho* / *Notas*) | Mantener histórico. |
| `ai-integration/` | Carpeta | Contiene PLANNING.md y TASK.md específicos de IA + reglas. | FUSIONAR contenido y luego **DELETE** | Extraer reglas útiles (API, providers). |
| `file-based-projects/` | Carpeta | Especificaciones del módulo *file-based projects*. | KEEP → PLANNING.md (Sección Módulos). | Revisar duplicados con planning general. |
| `github-integration/` | Carpeta | Guía de integración GitHub (webhooks, tokens). | KEEP → PLANNING.md (Integraciones externas). | Asegurar tokens seguros. |

> **Leyenda:**  
> • **KEEP → PLANNING.md / TASK.md** = Se fusionará el contenido relevante.  
> • **DELETE** = Se eliminará tras la consolidación.  
> • REFERENCIA = No se copia íntegro; solo datos clave al PLANNING.  

---

## 2 · Duplicados / Solapamientos Detectados

1. `tasks.md` vs `updated-tasks.md` – contienen listas similares de backlog.  
2. Subcarpeta `ai-integration/` ya incluye PLANNING/TASK para IA; duplican estructura raíz.  
3. Varias fechas diferentes de generación (dic 2024) → actualizar cronología tras fusión.

---

## 3 · Brechas Identificadas (para 100 % instalación)

| Categoría | Brecha | Recomendación |
|-----------|--------|---------------|
| **CI/CD** | Falta documentación de pipeline completo y variables de entorno. | Añadir sección CI/CD en PLANNING.md con flujo de GitHub Actions, deploy Vercel/Supabase cli. |
| **Entorno local** | Pasos de instalación parciales; no se documentan dependencias nativas. | Crear guía *Getting-Started* dentro de PLANNING.md. |
| **RLS & Security** | RLS policies solo mencionadas; sin SQL final ni enlaces. | Incluir scripts completos en PLANNING.md o repositorio `/sql`. |
| **Configuración Supabase** | Variables `.env` y seed de datos no documentados. | Agregar sección *Supabase Setup*. |
| **Métricas de calidad** | Objetivos de ESLint/coverage aparecen en 3 documentos distintos. | Centralizar métricas en PLANNING.md. |

---

## 4 · Siguiente Paso Propuesto

1. Aprobar esta matriz y brechas.  
2. Ejecutar fusión de contenidos a `PLANNING.md` y `TASK.md`.  
3. Eliminar/archivar elementos marcados para borrar.  
4. Actualizar estructura de carpetas y referencias en README general.

---

**Fin del informe**
