"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Trash2, <PERSON>ader2, <PERSON><PERSON>ler<PERSON> } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { toast } from "@/hooks/use-toast"
import { createClient } from "@/lib/supabase/client"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

interface AdminDeleteButtonProps {
  projectId: string
  projectName: string
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
  size?: "default" | "sm" | "lg" | "icon"
  className?: string
}

export function AdminDeleteButton({
  projectId,
  projectName,
  variant = "destructive",
  size = "default",
  className = "",
}: AdminDeleteButtonProps) {
  const router = useRouter()
  const supabase = createClient()
  const [isDeleting, setIsDeleting] = useState(false)

  const handleDelete = async () => {
    setIsDeleting(true)
    try {
      console.log(`Admin deleting project with ID: ${projectId}`);

      // Verificar si el proyecto existe antes de intentar eliminarlo
      try {
        const { data: projectData, error: projectError } = await supabase
          .from('projects')
          .select('id, name')
          .eq('id', projectId)
          .maybeSingle(); // Usar maybeSingle en lugar de single para evitar errores

        if (projectError) {
          console.error("Error al verificar el proyecto:", projectError);
          throw new Error("Error al verificar el proyecto: " + projectError.message);
        }

        if (!projectData) {
          console.warn("El proyecto no existe o ya fue eliminado");
          toast({
            title: "Advertencia",
            description: "El proyecto no existe o ya fue eliminado.",
            variant: "warning",
          });
          setIsDeleting(false);

          // Redirigir a la lista de proyectos ya que el proyecto no existe
          router.push("/dashboard/projects");
          router.refresh();
          return;
        }
      } catch (verifyError) {
        console.error("Error al verificar el proyecto:", verifyError);
        toast({
          title: "Error",
          description: "No se pudo verificar el proyecto. Es posible que ya haya sido eliminado.",
          variant: "destructive",
        });
        setIsDeleting(false);
        return;
      }

      // Usar la función de administrador para eliminar el proyecto
      const { data: result, error } = await supabase
        .rpc('admin_delete_project', { p_id: projectId });

      if (error) {
        console.error("Error al eliminar el proyecto:", error);
        const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

        throw new Error(`Error al eliminar el proyecto: ${errorMessage}`);
      }

      if (result === false) {
        throw new Error("No se pudo eliminar el proyecto. Es posible que no exista.");
      }

      toast({
        title: "Proyecto eliminado por administrador",
        description: "El proyecto ha sido eliminado correctamente",
      });

      // Forzar actualización del localStorage para que otras páginas se actualicen
      localStorage.setItem('projects_last_updated', Date.now().toString());

      // Pequeña pausa para asegurar que la eliminación se complete
      await new Promise(resolve => setTimeout(resolve, 500));

      // Verificar si el proyecto fue realmente eliminado
      try {
        const { data: checkData, error: checkError } = await supabase
          .from('projects')
          .select('id')
          .eq('id', projectId)
          .maybeSingle(); // Usar maybeSingle en lugar de single para evitar errores

        if (!checkError && checkData) {
          // El proyecto todavía existe, mostrar advertencia
          console.warn("El proyecto no fue eliminado correctamente");
          toast({
            title: "Advertencia",
            description: "El proyecto parece seguir existiendo a pesar de la eliminación administrativa.",
            variant: "destructive",
          });
          setIsDeleting(false);
          return;
        } else {
          // Si no hay datos, significa que el proyecto fue eliminado correctamente
          console.log("Proyecto eliminado correctamente verificado por admin");
        }
      } catch (checkError) {
        // Si hay un error, consideramos que el proyecto fue eliminado
        // ya que el error más común es que el proyecto no existe
        console.log("Verificación de eliminación admin completada (con error, lo cual es esperado):", checkError);
        // No hacemos nada más, continuamos con la redirección
      }

      // Redirigir a la lista de proyectos
      router.push("/dashboard/projects");

      // Forzar actualización de la página
      router.refresh();

      // Recargar la página después de un breve retraso para asegurar que se actualice completamente
      setTimeout(() => {
        window.location.href = "/dashboard/projects";
      }, 1000);
    } catch (error) {
      console.error("Error al eliminar el proyecto:", error);

      // Mostrar mensaje de error específico si está disponible
      const errorMessage = error instanceof Error ? error.message : "No se pudo eliminar el proyecto. Intente nuevamente.";

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  }

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button variant={variant} size={size} className={className}>
          <ShieldAlert className="mr-2 h-4 w-4" /> Eliminar como Admin
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>¿Eliminar este proyecto como administrador?</AlertDialogTitle>
          <AlertDialogDescription>
            Esta acción no se puede deshacer. ¿Estás seguro de que quieres eliminar el proyecto "{projectName}" usando privilegios de administrador?
            <br /><br />
            <span className="text-red-500 font-semibold">Advertencia: Esta acción omite todas las verificaciones de permisos normales.</span>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>Cancelar</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={isDeleting}
            className="bg-red-500 hover:bg-red-600"
          >
            {isDeleting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Eliminando...
              </>
            ) : (
              <>
                <ShieldAlert className="mr-2 h-4 w-4" />
                Eliminar como Admin
              </>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
