# AdminCore Testing Strategy

**Generated:** December 2024  
**Current Coverage:** 35%  
**Target Coverage:** 90%  
**Current Status:** 33 failing tests out of 114

## Current Testing Issues

### Failing Tests Analysis
```
FAIL __tests__/components/dashboard/metrics-cards.test.tsx
FAIL __tests__/components/dashboard/charts/line-chart.test.tsx
FAIL __tests__/components/projects/project-form.test.tsx
FAIL __tests__/components/work-orders/kanban-board.test.tsx
FAIL __tests__/pages/dashboard.test.tsx
FAIL __tests__/utils/supabase.test.tsx
```

### Root Causes
1. **Supabase Client Mocking Issues** - Tests fail due to improper mocking
2. **React Component Testing Setup** - Missing proper test utilities
3. **Authentication Context** - Tests don't handle auth state properly
4. **Async Operations** - Improper handling of promises and loading states
5. **TypeScript Errors** - Type mismatches in test files

## Testing Framework Setup

### Current Stack
- **Jest** - Test runner
- **React Testing Library** - Component testing
- **@testing-library/jest-dom** - DOM matchers
- **MSW (Mock Service Worker)** - API mocking

### Required Additions
```bash
npm install --save-dev @testing-library/user-event
npm install --save-dev jest-environment-jsdom
npm install --save-dev @types/jest
```

### Jest Configuration Update
```javascript
// jest.config.js
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/types/**/*',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
};
```

## Testing Strategy by Module

### 1. Dashboard Module Testing

#### Components to Test
- `MetricsCards` - Data display and loading states
- `LineChart` - Chart rendering and interactions
- `ActivityTimeline` - List rendering and updates
- `TasksSection` - CRUD operations

#### Test Examples
```typescript
// __tests__/components/dashboard/metrics-cards.test.tsx
import { render, screen } from '@testing-library/react';
import { MetricsCards } from '@/components/dashboard/metrics-cards';
import { createMockSupabaseClient } from '@/test-utils/supabase-mock';

describe('MetricsCards', () => {
  it('displays loading state initially', () => {
    render(<MetricsCards />);
    expect(screen.getByTestId('metrics-loading')).toBeInTheDocument();
  });

  it('displays metrics data when loaded', async () => {
    const mockData = { totalProjects: 10, activeOrders: 5 };
    render(<MetricsCards data={mockData} />);
    
    expect(await screen.findByText('10')).toBeInTheDocument();
    expect(await screen.findByText('5')).toBeInTheDocument();
  });
});
```

### 2. Projects Module Testing

#### Test Coverage Areas
- **CRUD Operations** - Create, read, update, delete projects
- **Form Validation** - Input validation and error handling
- **File Attachments** - Upload and display functionality
- **User Assignment** - Multi-user project management
- **Status Management** - Project lifecycle states

#### Critical Test Cases
```typescript
// __tests__/components/projects/project-form.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ProjectForm } from '@/components/projects/project-form';

describe('ProjectForm', () => {
  it('validates required fields', async () => {
    const user = userEvent.setup();
    render(<ProjectForm />);
    
    const submitButton = screen.getByRole('button', { name: /save/i });
    await user.click(submitButton);
    
    expect(screen.getByText('Project name is required')).toBeInTheDocument();
  });

  it('submits form with valid data', async () => {
    const mockOnSubmit = jest.fn();
    const user = userEvent.setup();
    
    render(<ProjectForm onSubmit={mockOnSubmit} />);
    
    await user.type(screen.getByLabelText(/project name/i), 'Test Project');
    await user.type(screen.getByLabelText(/description/i), 'Test Description');
    await user.click(screen.getByRole('button', { name: /save/i }));
    
    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        name: 'Test Project',
        description: 'Test Description',
      });
    });
  });
});
```

### 3. Work Orders Module Testing

#### Focus Areas
- **Kanban Board** - Drag and drop functionality
- **Status Transitions** - Valid state changes
- **User Assignment** - Assignment logic
- **Time Tracking** - Duration calculations

### 4. Documents Module Testing

#### Test Scenarios
- **File Upload** - Single and multiple files
- **File Validation** - Type and size restrictions
- **Error Handling** - Upload failures and recovery
- **Grid/List Views** - Display modes

### 5. Users Module Testing

#### Coverage Areas
- **User CRUD** - Complete user management
- **Role Assignment** - Permission testing
- **Document Management** - User-specific documents
- **Status Management** - Active/inactive states

## Test Utilities and Mocks

### Supabase Client Mock
```typescript
// src/test-utils/supabase-mock.ts
import { createClient } from '@supabase/supabase-js';

export const createMockSupabaseClient = () => {
  return {
    from: jest.fn(() => ({
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      single: jest.fn(),
    })),
    auth: {
      getUser: jest.fn(),
      signIn: jest.fn(),
      signOut: jest.fn(),
    },
    storage: {
      from: jest.fn(() => ({
        upload: jest.fn(),
        download: jest.fn(),
        remove: jest.fn(),
      })),
    },
  };
};
```

### Authentication Context Mock
```typescript
// src/test-utils/auth-mock.tsx
import { ReactNode } from 'react';
import { AuthContext } from '@/contexts/auth-context';

interface MockAuthProviderProps {
  children: ReactNode;
  user?: any;
  loading?: boolean;
}

export const MockAuthProvider = ({ 
  children, 
  user = null, 
  loading = false 
}: MockAuthProviderProps) => {
  const mockValue = {
    user,
    loading,
    signIn: jest.fn(),
    signOut: jest.fn(),
    signUp: jest.fn(),
  };

  return (
    <AuthContext.Provider value={mockValue}>
      {children}
    </AuthContext.Provider>
  );
};
```

### Custom Render Function
```typescript
// src/test-utils/render.tsx
import { render, RenderOptions } from '@testing-library/react';
import { ReactElement } from 'react';
import { MockAuthProvider } from './auth-mock';

const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return (
    <MockAuthProvider>
      {children}
    </MockAuthProvider>
  );
};

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options });

export * from '@testing-library/react';
export { customRender as render };
```

## Integration Testing Strategy

### API Integration Tests
```typescript
// __tests__/integration/projects-api.test.ts
import { createMockSupabaseClient } from '@/test-utils/supabase-mock';
import { ProjectsService } from '@/services/projects';

describe('Projects API Integration', () => {
  let projectsService: ProjectsService;
  let mockSupabase: any;

  beforeEach(() => {
    mockSupabase = createMockSupabaseClient();
    projectsService = new ProjectsService(mockSupabase);
  });

  it('creates a project successfully', async () => {
    const projectData = { name: 'Test Project', description: 'Test' };
    mockSupabase.from().insert.mockResolvedValue({ data: projectData });

    const result = await projectsService.createProject(projectData);
    
    expect(mockSupabase.from).toHaveBeenCalledWith('projects');
    expect(result).toEqual(projectData);
  });
});
```

### End-to-End Testing with Playwright

```typescript
// e2e/projects.spec.ts
import { test, expect } from '@playwright/test';

test('user can create a new project', async ({ page }) => {
  await page.goto('/dashboard/projects');
  await page.click('text=New Project');
  
  await page.fill('[data-testid=project-name]', 'E2E Test Project');
  await page.fill('[data-testid=project-description]', 'Created by E2E test');
  
  await page.click('button:has-text("Save")');
  
  await expect(page.locator('text=E2E Test Project')).toBeVisible();
});
```

## Test Data Management

### Fixtures and Factories
```typescript
// src/test-utils/factories.ts
export const createMockProject = (overrides = {}) => ({
  id: '1',
  name: 'Test Project',
  description: 'Test Description',
  status: 'active',
  created_at: new Date().toISOString(),
  ...overrides,
});

export const createMockUser = (overrides = {}) => ({
  id: '1',
  email: '<EMAIL>',
  name: 'Test User',
  role: 'user',
  ...overrides,
});
```

## Performance Testing

### Load Testing with Jest
```typescript
// __tests__/performance/dashboard-load.test.ts
describe('Dashboard Performance', () => {
  it('renders dashboard within performance budget', async () => {
    const startTime = performance.now();
    
    render(<Dashboard />);
    await waitFor(() => {
      expect(screen.getByTestId('dashboard-content')).toBeInTheDocument();
    });
    
    const endTime = performance.now();
    const renderTime = endTime - startTime;
    
    expect(renderTime).toBeLessThan(1000); // 1 second budget
  });
});
```

## Continuous Integration Setup

### GitHub Actions Workflow
```yaml
# .github/workflows/test.yml
name: Test Suite
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run lint
      - run: npm run type-check
      - run: npm run test:ci
      - run: npm run build
```

## Testing Checklist

### Before Each Release
- [ ] All unit tests passing
- [ ] Integration tests passing
- [ ] E2E tests passing
- [ ] Coverage above 80%
- [ ] No TypeScript errors
- [ ] No ESLint warnings
- [ ] Performance tests within budget
- [ ] Security tests passing

### Test Maintenance
- [ ] Update tests when features change
- [ ] Remove obsolete tests
- [ ] Refactor test utilities
- [ ] Update test documentation
- [ ] Review test coverage reports

This comprehensive testing strategy will ensure AdminCore achieves high quality and reliability standards.
