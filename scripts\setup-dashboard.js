// Script para configurar las funciones y tablas del dashboard
// Ejecutar con: node scripts/setup-dashboard.js

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Cargar variables de entorno
require('dotenv').config();

// Crear cliente de Supabase
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Error: NEXT_PUBLIC_SUPABASE_URL y SUPABASE_SERVICE_ROLE_KEY deben estar definidos en el archivo .env');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Ejecuta SQL directamente en la base de datos
 * @param sql Script SQL a ejecutar
 */
async function executeDirectSQL(sql) {
  // Dividir el script en sentencias individuales
  const statements = sql
    .split(';')
    .map(stmt => stmt.trim())
    .filter(stmt => stmt.length > 0);

  // Ejecutar cada sentencia por separado
  for (const stmt of statements) {
    try {
      // Usar el método rpc con la función pgSQL para ejecutar SQL directamente
      // Esta es la forma recomendada en Supabase v2
      await supabase.rpc('pgSQL', { query: `${stmt};` });
    } catch (error) {
      // Si la función pgSQL no existe, intentar con el método alternativo
      try {
        console.log(`Intentando método alternativo para ejecutar SQL: ${stmt}`);
        // Usar REST API para ejecutar SQL directamente
        const response = await fetch(`${supabaseUrl}/rest/v1/`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${supabaseServiceKey}`,
            'apikey': supabaseServiceKey,
            'Prefer': 'params=single-object'
          },
          body: JSON.stringify({
            query: `${stmt};`
          })
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(`Error en respuesta API: ${JSON.stringify(errorData)}`);
        }
      } catch (alternativeError) {
        console.error(`Error al ejecutar sentencia SQL (método alternativo): ${stmt}`, alternativeError);
        throw alternativeError;
      }
    }
  }
}

/**
 * Configura las funciones y tablas del dashboard
 */
async function setupDashboardFunctions() {
  try {
    console.log('Configurando funciones y tablas del dashboard...');

    // Leer los archivos SQL
    const helperSqlPath = path.join(__dirname, '..', 'src', 'lib', 'supabase', 'sql', 'function-helpers.sql');
    const dashboardSqlPath = path.join(__dirname, '..', 'src', 'lib', 'supabase', 'sql', 'dashboard-functions.sql');
    const projectSqlPath = path.join(__dirname, '..', 'src', 'lib', 'supabase', 'sql', 'project-functions.sql');

    const helperSqlContent = fs.readFileSync(helperSqlPath, 'utf8');
    const dashboardSqlContent = fs.readFileSync(dashboardSqlPath, 'utf8');
    const projectSqlContent = fs.readFileSync(projectSqlPath, 'utf8');

    // Ejecutar primero las funciones auxiliares
    try {
      console.log('Ejecutando funciones auxiliares...');
      await executeDirectSQL(helperSqlContent);
      console.log('Funciones auxiliares ejecutadas correctamente');
    } catch (helperError) {
      console.error('Error al ejecutar funciones auxiliares:', helperError);
      return false;
    }

    // Ejecutar las funciones del dashboard
    try {
      console.log('Ejecutando funciones del dashboard...');
      await supabase.rpc('exec_sql', { sql: dashboardSqlContent });
      console.log('Funciones del dashboard ejecutadas correctamente');
    } catch (dashboardError) {
      console.error('Error al ejecutar funciones del dashboard:', dashboardError);
      try {
        console.log('Intentando ejecutar directamente...');
        await executeDirectSQL(dashboardSqlContent);
        console.log('Funciones del dashboard ejecutadas correctamente');
      } catch (directError) {
        console.error('Error al ejecutar directamente las funciones del dashboard:', directError);
        return false;
      }
    }

    // Ejecutar las funciones de proyectos
    try {
      console.log('Ejecutando funciones de proyectos...');
      await supabase.rpc('exec_sql', { sql: projectSqlContent });
      console.log('Funciones de proyectos ejecutadas correctamente');
    } catch (projectError) {
      console.error('Error al ejecutar funciones de proyectos:', projectError);
      try {
        console.log('Intentando ejecutar directamente...');
        await executeDirectSQL(projectSqlContent);
        console.log('Funciones de proyectos ejecutadas correctamente');
      } catch (directError) {
        console.error('Error al ejecutar directamente las funciones de proyectos:', directError);
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error('Error al configurar funciones y tablas del dashboard:', error);
    return false;
  }
}

async function main() {
  try {
    console.log('Iniciando configuración del dashboard...');

    // Configurar las funciones y tablas del dashboard
    const success = await setupDashboardFunctions();

    if (success) {
      console.log('Configuración del dashboard completada con éxito');
      process.exit(0);
    } else {
      console.error('Error al configurar el dashboard');
      process.exit(1);
    }
  } catch (error) {
    console.error('Error inesperado:', error);
    process.exit(1);
  }
}

main();
