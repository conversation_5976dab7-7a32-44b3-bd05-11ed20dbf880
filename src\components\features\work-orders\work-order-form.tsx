"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { CalendarIcon, Loader2, Check, ChevronsUpDown } from "lucide-react"
import { format } from "date-fns"
import { es } from "date-fns/locale"

import { Button } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { createClient } from "@/lib/supabase/client"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command"
import { cn } from "@/lib/utils"
import { dataValidator } from "@/lib/services/data-validator-service"
import { errorHandler } from "@/lib/services/error-handler-service"
import { FormValidationAlert } from "@/components/ui/form-validation-alert"

// Esquema de validación para el formulario
const workOrderFormSchema = z.object({
  title: z.string({
    required_error: "El título es obligatorio",
  }).min(3, {
    message: "El título debe tener al menos 3 caracteres.",
  }).refine(
    (val) => val && val.trim() !== '',
    {
      message: "El título no puede estar vacío."
    }
  ),
  description: z.string().optional()
    .transform(val => val === '' ? null : val), // Convertir string vacío a null
  status: z.enum(["pending", "in_progress", "completed", "cancelled"] as const, {
    required_error: "El estado es obligatorio",
  }),
  priority: z.enum(["low", "medium", "high", "critical"] as const, {
    required_error: "La prioridad es obligatoria",
  }),
  project_id: z.string().optional()
    .transform(val => val === '' ? null : val), // Convertir string vacío a null
  assigned_to: z.string().optional() // Mantenemos para compatibilidad
    .transform(val => val === '' ? null : val), // Convertir string vacío a null
  assigned_users: z.array(z.string()).default([]),
  due_date: z.date().optional(),
})

type WorkOrderFormValues = z.infer<typeof workOrderFormSchema>

// Valores por defecto para una nueva orden
const defaultValues: Partial<WorkOrderFormValues> = {
  title: "",
  description: "",
  status: "pending",
  priority: "medium",
}

interface Project {
  id: string
  name: string
}

interface User {
  id: string
  email: string
  full_name?: string
}

interface WorkOrderFormProps {
  initialData?: Partial<WorkOrderFormValues>
  onSubmit: (data: WorkOrderFormValues) => void
  isLoading?: boolean
  projectId?: string
}

interface AssignedUser {
  id: string
  full_name?: string
  email: string
}

export function WorkOrderForm({
  initialData,
  onSubmit,
  isLoading = false,
  projectId,
}: WorkOrderFormProps) {
  const [projects, setProjects] = useState<Project[]>([])
  const [users, setUsers] = useState<User[]>([])
  const [selectedUsers, setSelectedUsers] = useState<AssignedUser[]>([])
  const [isLoadingProjects, setIsLoadingProjects] = useState(false)
  const [isLoadingUsers, setIsLoadingUsers] = useState(false)
  const [openProjectSelect, setOpenProjectSelect] = useState(false)
  const [openUserSelect, setOpenUserSelect] = useState(false)
  const [validationErrors, setValidationErrors] = useState<string[]>([])
  const supabase = createClient()

  const form = useForm<WorkOrderFormValues>({
    resolver: zodResolver(workOrderFormSchema),
    defaultValues: {
      ...defaultValues,
      ...initialData,
      project_id: projectId || initialData?.project_id,
      assigned_users: [],
    },
  })

  // Cargar proyectos
  useEffect(() => {
    const loadProjects = async () => {
      setIsLoadingProjects(true)
      try {
        const { data, error } = await supabase
          .from("projects")
          .select("id, name")
          .order("name")

        if (error) throw error
        setProjects(data || [])
      } catch (error) {
        console.error("Error al cargar proyectos:", error)
      } finally {
        setIsLoadingProjects(false)
      }
    }

    loadProjects()
  }, [supabase])

  // Cargar usuarios
  useEffect(() => {
    const loadUsers = async () => {
      setIsLoadingUsers(true)
      try {
        const { data, error } = await supabase
          .from("users")
          .select("id, email, full_name")
          .order("full_name")

        if (error) throw error
        setUsers(data || [])
      } catch (error) {
        console.error("Error al cargar usuarios:", error)
      } finally {
        setIsLoadingUsers(false)
      }
    }

    loadUsers()
  }, [supabase])

  // Cargar usuarios asignados si hay datos iniciales
  useEffect(() => {
    const loadAssignedUsers = async () => {
      // Si tenemos un ID de orden de trabajo en initialData, cargamos los usuarios asignados
      if (initialData?.id) {
        try {
          const { data, error } = await supabase
            .from("work_order_users")
            .select("user_id, users:user_id(id, email, full_name)")
            .eq("work_order_id", initialData.id)

          if (error) throw error

          if (data && data.length > 0) {
            // Formatear los datos para el estado
            const formattedUsers = data.map(item => ({
              id: item.users.id,
              email: item.users.email,
              full_name: item.users.full_name
            }));

            setSelectedUsers(formattedUsers);

            // Actualizar el formulario con los IDs de usuarios
            const userIds = formattedUsers.map(user => user.id);
            form.setValue("assigned_users", userIds);
          }

          // Si hay un assigned_to, añadirlo también a assigned_users para compatibilidad
          if (initialData.assigned_to && !form.getValues("assigned_users").includes(initialData.assigned_to)) {
            const user = users.find(u => u.id === initialData.assigned_to);
            if (user) {
              const updatedUsers = [...selectedUsers, {
                id: user.id,
                email: user.email,
                full_name: user.full_name
              }];
              setSelectedUsers(updatedUsers);
              form.setValue("assigned_users", updatedUsers.map(u => u.id));
            }
          }
        } catch (error) {
          console.error("Error al cargar usuarios asignados:", error);
        }
      }
    };

    if (users.length > 0) {
      loadAssignedUsers();
    }
  }, [initialData?.id, users, supabase, form])

  useEffect(() => {
    if (initialData?.assigned_to) {
      setSelectedUsers(initialData.assigned_to);
    }
  }, [initialData?.assigned_to, setSelectedUsers]);

  const handleSubmit = (data: WorkOrderFormValues) => {
    // Limpiar errores previos
    setValidationErrors([]);

    // Validar campos requeridos
    const requiredFields = ['title', 'status', 'priority'];
    const { isValid, errors } = dataValidator.validateRequiredFields(data, requiredFields);

    if (!isValid) {
      // Mostrar errores en los campos correspondientes
      Object.entries(errors).forEach(([field, message]) => {
        form.setError(field as any, {
          type: 'required',
          message
        });
      });

      // Agregar errores a la lista de validación para mostrar en el alert
      const errorMessages = Object.values(errors);
      setValidationErrors(errorMessages);
      return;
    }

    // Sanitizar campos de texto para convertir strings vacíos a null
    const textFields = ['description'];
    const sanitizedData = dataValidator.sanitizeTextFields(data, textFields);

    // Si hay usuarios seleccionados, asegurarse de que estén en assigned_users
    if (selectedUsers.length > 0) {
      sanitizedData.assigned_users = selectedUsers.map(user => user.id);

      // Para compatibilidad, si no hay assigned_to pero hay usuarios seleccionados,
      // asignar el primero como assigned_to
      if (!sanitizedData.assigned_to && sanitizedData.assigned_users.length > 0) {
        sanitizedData.assigned_to = sanitizedData.assigned_users[0];
      }
    }

    onSubmit(sanitizedData);
  }

  // Función para añadir un usuario a la selección
  const addUserToSelection = (userId: string) => {
    const user = users.find(u => u.id === userId);
    if (!user) return;

    // Verificar si el usuario ya está seleccionado
    if (selectedUsers.some(u => u.id === userId)) return;

    const newUser = {
      id: user.id,
      email: user.email,
      full_name: user.full_name
    };

    const updatedUsers = [...selectedUsers, newUser];
    setSelectedUsers(updatedUsers);

    // Actualizar el campo assigned_users en el formulario
    const userIds = updatedUsers.map(u => u.id);
    form.setValue("assigned_users", userIds);

    // Si no hay un usuario principal asignado, asignar este
    if (!form.getValues("assigned_to")) {
      form.setValue("assigned_to", userId);
    }
  }

  // Función para eliminar un usuario de la selección
  const removeUserFromSelection = (userId: string) => {
    const updatedUsers = selectedUsers.filter(u => u.id !== userId);
    setSelectedUsers(updatedUsers);

    // Actualizar el campo assigned_users en el formulario
    const userIds = updatedUsers.map(u => u.id);
    form.setValue("assigned_users", userIds);

    // Si el usuario eliminado era el assigned_to, actualizar ese campo
    if (form.getValues("assigned_to") === userId) {
      form.setValue("assigned_to", updatedUsers.length > 0 ? updatedUsers[0].id : undefined);
    }
  }

  return (
    <Form {...form}>
      <FormValidationAlert errors={validationErrors} />
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <div className="text-sm text-muted-foreground mb-4">
          Los campos marcados con <span className="text-red-500">*</span> son obligatorios.
        </div>
        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center">
                Título
                <span className="text-red-500 ml-1">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  placeholder="Título de la orden de trabajo"
                  {...field}
                  className={form.formState.errors.title ? "border-red-500" : ""}
                  required
                />
              </FormControl>
              <FormDescription>
                Título descriptivo para identificar la orden de trabajo.
              </FormDescription>
              <FormMessage className="text-red-500" />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Descripción</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Descripción detallada de la orden de trabajo"
                  className="min-h-[120px]"
                  {...field}
                  value={field.value || ""}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center">
                  Estado
                  <span className="text-red-500 ml-1">*</span>
                </FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger className={form.formState.errors.status ? "border-red-500" : ""}>
                      <SelectValue placeholder="Seleccione un estado" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="pending">Pendiente</SelectItem>
                    <SelectItem value="in_progress">En Progreso</SelectItem>
                    <SelectItem value="completed">Completada</SelectItem>
                    <SelectItem value="cancelled">Cancelada</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage className="text-red-500" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="priority"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center">
                  Prioridad
                  <span className="text-red-500 ml-1">*</span>
                </FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger className={form.formState.errors.priority ? "border-red-500" : ""}>
                      <SelectValue placeholder="Seleccione una prioridad" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="low">Baja</SelectItem>
                    <SelectItem value="medium">Media</SelectItem>
                    <SelectItem value="high">Alta</SelectItem>
                    <SelectItem value="critical">Crítica</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage className="text-red-500" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="project_id"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Proyecto</FormLabel>
                <Popover
                  open={openProjectSelect}
                  onOpenChange={setOpenProjectSelect}
                >
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        role="combobox"
                        className={cn(
                          "w-full justify-between",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {field.value
                          ? projects.find(
                              (project) => project.id === field.value
                            )?.name || "Seleccionar proyecto"
                          : "Seleccionar proyecto"}
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-[300px] p-0">
                    <Command>
                      <CommandInput placeholder="Buscar proyecto..." />
                      <CommandEmpty>No se encontraron proyectos.</CommandEmpty>
                      <CommandGroup>
                        {projects.map((project) => (
                          <CommandItem
                            key={project.id}
                            value={project.id}
                            onSelect={(value) => {
                              form.setValue("project_id", value)
                              setOpenProjectSelect(false)
                            }}
                          >
                            <Check
                              className={cn(
                                "mr-2 h-4 w-4",
                                project.id === field.value
                                  ? "opacity-100"
                                  : "opacity-0"
                              )}
                            />
                            {project.name}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </Command>
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="assigned_users"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Usuarios Asignados</FormLabel>
                <div className="space-y-4">
                  {/* Lista de usuarios seleccionados */}
                  {selectedUsers.length > 0 && (
                    <div className="flex flex-wrap gap-2 mb-2">
                      {selectedUsers.map((user) => (
                        <div
                          key={user.id}
                          className="flex items-center gap-1 bg-secondary text-secondary-foreground px-3 py-1 rounded-full text-sm"
                        >
                          <span>{user.full_name || user.email}</span>
                          <button
                            type="button"
                            onClick={() => removeUserFromSelection(user.id)}
                            className="text-secondary-foreground/70 hover:text-secondary-foreground"
                          >
                            ×
                          </button>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Selector de usuarios */}
                  <Popover
                    open={openUserSelect}
                    onOpenChange={setOpenUserSelect}
                  >
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          role="combobox"
                          className="w-full justify-between"
                        >
                          <span>Añadir usuarios</span>
                          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-[300px] p-0">
                      <Command>
                        <CommandInput placeholder="Buscar usuario..." />
                        <CommandEmpty>No se encontraron usuarios.</CommandEmpty>
                        <CommandGroup>
                          {users
                            .filter(user => !selectedUsers.some(u => u.id === user.id))
                            .map((user) => (
                              <CommandItem
                                key={user.id}
                                value={user.id}
                                onSelect={(value) => {
                                  addUserToSelection(value);
                                  setOpenUserSelect(false);
                                }}
                              >
                                <div className="flex flex-col">
                                  <span>
                                    {user.full_name || "Usuario sin nombre"}
                                  </span>
                                  <span className="text-xs text-muted-foreground">
                                    {user.email}
                                  </span>
                                </div>
                              </CommandItem>
                          ))}
                        </CommandGroup>
                      </Command>
                    </PopoverContent>
                  </Popover>

                  {selectedUsers.length === 0 && (
                    <p className="text-sm text-muted-foreground">
                      No hay usuarios asignados. Añade uno o más usuarios a esta orden de trabajo.
                    </p>
                  )}
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="due_date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Fecha límite</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={"outline"}
                        className={`w-full pl-3 text-left font-normal ${
                          !field.value && "text-muted-foreground"
                        }`}
                      >
                        {field.value ? (
                          format(field.value, "PPP", { locale: es })
                        ) : (
                          <span>Seleccionar fecha</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      initialFocus
                      locale={es}
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex justify-end space-x-4">
          <Button variant="outline" type="button">
            Cancelar
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {initialData ? "Actualizar orden" : "Crear orden"}
          </Button>
        </div>
      </form>
    </Form>
  )
}
