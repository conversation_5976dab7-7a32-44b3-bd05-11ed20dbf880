/**
 * @ai-file-description: "Configuración de buckets de almacenamiento en Supabase"
 * @ai-owner: "Supabase Integration"
 */

import { createClient } from './client';

// Definición de buckets necesarios y sus configuraciones
const REQUIRED_BUCKETS = {
  documents: {
    public: false,
    fileSizeLimit: 10 * 1024 * 1024, // 10MB
    allowedMimeTypes: [
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/msword',
      'text/plain',
      'image/jpeg',
      'image/png'
    ]
  },
  projects: {
    public: false,
    fileSizeLimit: 10 * 1024 * 1024, // 10MB
    allowedMimeTypes: [
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/msword',
      'text/plain'
    ]
  }
};

/**
 * Configura los buckets de almacenamiento necesarios para la aplicación
 * y establece las políticas de seguridad adecuadas.
 *
 * Optimizado para reducir operaciones innecesarias y mejorar el rendimiento.
 */
export async function setupStorageBuckets() {
  // Si estamos en el cliente web, verificar si ya se ha comprobado antes
  if (typeof window !== 'undefined') {
    const storageInitialized = localStorage.getItem('storage_buckets_initialized');
    if (storageInitialized === 'true') {
      return { success: true, message: 'Buckets ya verificados en esta sesión' };
    }
  }

  try {
    const supabase = createClient();

    // Verificar si los buckets existen
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();

    if (bucketsError) {
      // Si hay un error al listar buckets, podría ser un problema de permisos
      console.warn('No se pudieron listar buckets:', bucketsError.message);
      return {
        success: true,
        message: 'No se pudieron verificar los buckets, posiblemente por permisos'
      };
    }

    // Verificar qué buckets faltan
    const existingBuckets = new Set(buckets.map(b => b.name));
    const missingBuckets = Object.keys(REQUIRED_BUCKETS).filter(
      name => !existingBuckets.has(name)
    );

    // Si todos los buckets existen, no hacemos nada
    if (missingBuckets.length === 0) {
      if (typeof window !== 'undefined') {
        localStorage.setItem('storage_buckets_initialized', 'true');
      }
      return { success: true, message: 'Todos los buckets necesarios existen' };
    }

    // Si estamos en el cliente web, no intentamos crear buckets
    if (typeof window !== 'undefined') {
      console.log('Faltan buckets, pero no se pueden crear desde el cliente web');
      return {
        success: true,
        message: 'Faltan buckets, pero no se pueden crear desde el cliente web',
        missingBuckets
      };
    }

    // A partir de aquí, solo se ejecuta en el servidor

    // Crear los buckets que faltan
    const creationResults = await Promise.all(
      missingBuckets.map(async (bucketName) => {
        try {
          const config = REQUIRED_BUCKETS[bucketName as keyof typeof REQUIRED_BUCKETS];
          const { error } = await supabase.storage.createBucket(bucketName, config);

          if (error) {
            console.error(`Error al crear bucket ${bucketName}:`, error);
            return { name: bucketName, success: false, error };
          }

          console.log(`Bucket ${bucketName} creado correctamente`);
          return { name: bucketName, success: true };
        } catch (err) {
          console.error(`Excepción al crear bucket ${bucketName}:`, err);
          return { name: bucketName, success: false, error: err };
        }
      })
    );

    // Verificar si todos los buckets se crearon correctamente
    const allSuccessful = creationResults.every(result => result.success);

    if (allSuccessful) {
      console.log('Todos los buckets faltantes fueron creados correctamente');
      return { success: true, creationResults };
    } else {
      console.warn('Algunos buckets no pudieron ser creados:',
        creationResults.filter(r => !r.success).map(r => r.name).join(', '));
      return {
        success: false,
        message: 'Algunos buckets no pudieron ser creados',
        creationResults
      };
    }
  } catch (error) {
    console.error('Error inesperado al configurar buckets:', error);
    return { success: false, error };
  }
}
