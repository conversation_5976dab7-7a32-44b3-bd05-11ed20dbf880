"use client"

import { useIterationConfirm } from "@/hooks/use-iteration-confirm"
import { ConfirmDialog } from "@/components/shared/ui/confirm-dialog"
import { But<PERSON> } from "@/components/ui/button"

interface IterationConfirmProps {
  onConfirm: () => void
  onCancel?: () => void
  buttonText?: string
}

export function IterationConfirm({
  onConfirm,
  onCancel,
  buttonText = "Continuar iteración"
}: IterationConfirmProps) {
  const {
    showConfirm,
    requestConfirmation,
    handleConfirm,
    handleCancel
  } = useIterationConfirm({
    onConfirm,
    onCancel
  })

  return (
    <>
      <Button onClick={requestConfirmation}>
        {buttonText}
      </Button>

      <ConfirmDialog
        isOpen={showConfirm}
        onClose={handleCancel}
        onConfirm={handleConfirm}
        title="Confirmar iteración"
        description="¿Desea continuar con la iteración?"
      />
    </>
  )
}