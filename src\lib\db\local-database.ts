/**
 * @file Base de datos local usando IndexedDB a través de Dexie
 * @description Implementa un sistema de caché local para reducir peticiones a Supabase
 */

import Dexie, { Table } from 'dexie';

// Tipos de datos para las tablas
export interface CachedProject {
  id: string;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
  owner_id?: string;
  user_id?: string;
  status?: string;
  progress_percent?: number;
  client_id?: string;
  budget?: number;
  currency?: string;
  start_date?: string;
  end_date?: string;
  ai_provider?: string;
  ai_model?: string;
  document_id?: string;
  // Campo para control de sincronización
  _synced?: boolean;
  _local_updated_at?: number;
}

export interface CachedDocument {
  id: string;
  name: string;
  size: number;
  type: string;
  created_at: string;
  updated_at: string;
  user_id: string;
  bucket_id?: string;
  path?: string;
  // Campo para control de sincronización
  _synced?: boolean;
  _local_updated_at?: number;
}

export interface CachedDocumentAnalysis {
  id: string;
  document_id: string;
  provider: string;
  status: string;
  analysis_data: unknown;
  confidence_score: number;
  created_at: string;
  updated_at: string;
  // Campo para control de sincronización
  _synced?: boolean;
  _local_updated_at?: number;
}

export interface CachedUser {
  id: string;
  email: string;
  created_at: string;
  updated_at: string;
  role?: string;
  // Campos adicionales que puedas necesitar
  _synced?: boolean;
  _local_updated_at?: number;
}

export interface SyncLog {
  id?: number; // Auto-incremental
  table: string;
  operation: 'fetch' | 'push' | 'delete';
  timestamp: number;
  records_count: number;
  status: 'success' | 'error';
  error_message?: string;
}

export interface SyncConfig {
  id?: number; // Solo habrá un registro, con id=1
  enabled: boolean;
  interval_minutes: number;
  last_sync: number;
  sync_on_startup: boolean;
  sync_on_network_reconnect: boolean;
  tables_to_sync: string[];
}

export interface PendingOperation {
  id?: number; // Auto-incremental
  table: string;
  operation: 'insert' | 'update' | 'delete';
  record_id: string;
  data: unknown;
  timestamp: number;
  attempts: number;
  priority: number; // 1-5, donde 5 es la más alta
}

/**
 * Clase para manejar la base de datos local
 */
export class LocalDatabase extends Dexie {
  // Tablas
  projects!: Table<CachedProject, string>;
  documents!: Table<CachedDocument, string>;
  documentAnalyses!: Table<CachedDocumentAnalysis, string>;
  users!: Table<CachedUser, string>;
  syncLogs!: Table<SyncLog, number>;
  syncConfig!: Table<SyncConfig, number>;
  pendingOperations!: Table<PendingOperation, number>;

  constructor() {
    super('Admin4HumansDB');

    this.version(2).stores({
      projects: 'id, user_id, created_at, updated_at, status, _synced, _local_updated_at',
      documents: 'id, user_id, created_at, updated_at, type, _synced, _local_updated_at',
      documentAnalyses: 'id, document_id, provider, status, confidence_score, _synced, _local_updated_at',
      users: 'id, email, role, _synced, _local_updated_at',
      syncLogs: '++id, table, operation, timestamp, status',
      syncConfig: 'id',
      pendingOperations: '++id, table, operation, record_id, timestamp, priority'
    });
  }

  /**
   * Inicializa la configuración de sincronización con valores predeterminados
   */
  async initSyncConfig(): Promise<SyncConfig> {
    const existingConfig = await this.syncConfig.get(1);

    if (!existingConfig) {
      const defaultConfig: SyncConfig = {
        id: 1,
        enabled: true,
        interval_minutes: 5, // Sincronizar cada 5 minutos
        last_sync: 0, // Nunca se ha sincronizado
        sync_on_startup: true,
        sync_on_network_reconnect: true,
        tables_to_sync: ['projects', 'documents', 'ai_document_analyses', 'users']
      };

      await this.syncConfig.put(defaultConfig);
      return defaultConfig;
    }

    return existingConfig;
  }

  /**
   * Actualiza la configuración de sincronización
   */
  async updateSyncConfig(config: Partial<SyncConfig>): Promise<SyncConfig> {
    const existingConfig = await this.syncConfig.get(1);

    if (!existingConfig) {
      return this.initSyncConfig();
    }

    const updatedConfig = { ...existingConfig, ...config };
    await this.syncConfig.put(updatedConfig);
    return updatedConfig;
  }

  /**
   * Registra una operación de sincronización
   */
  async logSync(log: Omit<SyncLog, 'id'>): Promise<number> {
    return this.syncLogs.add(log);
  }

  /**
   * Agrega una operación pendiente a la cola
   */
  async addPendingOperation(operation: Omit<PendingOperation, 'id' | 'attempts' | 'timestamp'>): Promise<number> {
    const pendingOp: Omit<PendingOperation, 'id'> = {
      ...operation,
      attempts: 0,
      timestamp: Date.now()
    };

    return this.pendingOperations.add(pendingOp);
  }

  /**
   * Obtiene las operaciones pendientes ordenadas por prioridad
   */
  async getPendingOperations(limit: number = 10): Promise<PendingOperation[]> {
    return this.pendingOperations
      .orderBy('priority')
      .reverse() // Mayor prioridad primero
      .limit(limit)
      .toArray();
  }

  /**
   * Marca una operación pendiente como completada (la elimina)
   */
  async completePendingOperation(id: number): Promise<void> {
    await this.pendingOperations.delete(id);
  }

  /**
   * Incrementa el contador de intentos de una operación pendiente
   */
  async incrementOperationAttempts(id: number): Promise<void> {
    const operation = await this.pendingOperations.get(id);

    if (operation) {
      operation.attempts += 1;
      await this.pendingOperations.put(operation);
    }
  }

  /**
   * Limpia los registros de sincronización antiguos (más de 7 días)
   */
  async cleanupOldSyncLogs(): Promise<number> {
    const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
    return this.syncLogs.where('timestamp').below(sevenDaysAgo).delete();
  }

  /**
   * Obtiene estadísticas de sincronización
   */
  async getSyncStats(): Promise<{
    lastSync: number;
    totalPendingOperations: number;
    syncLogsLast24h: number;
    errorLogsLast24h: number;
  }> {
    const config = await this.syncConfig.get(1) || { last_sync: 0 };
    const pendingOpsCount = await this.pendingOperations.count();

    const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
    const recentLogs = await this.syncLogs.where('timestamp').above(oneDayAgo).toArray();

    return {
      lastSync: config.last_sync,
      totalPendingOperations: pendingOpsCount,
      syncLogsLast24h: recentLogs.length,
      errorLogsLast24h: recentLogs.filter(log => log.status === 'error').length
    };
  }
}

// Exportar una instancia única de la base de datos
// Asegurarse de que solo se inicializa en el cliente
let _localDb: LocalDatabase | null = null;

export const localDb = typeof window !== 'undefined'
  ? (_localDb || (_localDb = new LocalDatabase()))
  : (new Proxy({} as LocalDatabase, {
      get: (target, prop) => {
        // Proporcionar implementaciones de no-op para métodos comunes
        if (prop === 'then') return undefined; // Para que sea "thenable" y no cause problemas con await
        console.warn(`Intentando acceder a IndexedDB en el servidor: ${String(prop)}`);
        return () => Promise.resolve(null);
      }
    }));
