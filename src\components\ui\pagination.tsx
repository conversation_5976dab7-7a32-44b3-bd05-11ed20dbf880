// Componente de paginación mínimo para evitar errores de importación en Next.js
import React from "react";

export const Pagination = ({ children }: { children: React.ReactNode }) => (
  <nav>{children}</nav>
);

export const PaginationContent = ({ children }: { children: React.ReactNode }) => (
  <div>{children}</div>
);

export const PaginationItem = ({ children }: { children: React.ReactNode }) => (
  <div>{children}</div>
);

export const PaginationLink = ({ children, isActive, onClick }: { children: React.ReactNode, isActive?: boolean, onClick?: () => void }) => (
  <button style={{ fontWeight: isActive ? "bold" : "normal" }} onClick={onClick}>{children}</button>
);

export const PaginationPrevious = (props: unknown) => <button {...props}>{props.children || "Anterior"}</button>;
export const PaginationNext = (props: unknown) => <button {...props}>{props.children || "Siguiente"}</button>;
export const PaginationEllipsis = () => <span>...</span>;
