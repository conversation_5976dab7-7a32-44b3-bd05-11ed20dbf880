import { cn } from "@/lib/utils"

interface DashboardShellProps {
  children: React.ReactNode
  heading: string
  description?: string
  className?: string
}

export function DashboardShell({
  children,
  heading,
  description,
  className
}: DashboardShellProps) {
  return (
    <div className={cn("container grid items-center gap-6 pb-8 pt-6 md:py-10", className)}>
      <div className="flex max-w-[980px] flex-col items-start gap-2">
        <h1 className="text-3xl font-bold leading-tight tracking-tighter md:text-4xl">
          {heading}
        </h1>
        {description && (
          <p className="text-lg text-muted-foreground">
            {description}
          </p>
        )}
      </div>
      <div>{children}</div>
    </div>
  )
}