'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/lib/supabase/client';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
// import { Skeleton } from '@/components/ui/skeleton'; // Eliminada importación de Skeleton
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, BrainCircuit } from 'lucide-react';

interface AIProjectDetailsProps {
  projectId: string;
}

export function AIProjectDetails({ projectId }: AIProjectDetailsProps) {
  const [aiProjectData, setAIProjectData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const supabase = createClient();

  useEffect(() => {
    async function fetchAIProjectData() {
      try {
        setLoading(true);
        setError(null);

        const { data, error } = await supabase
          .from('ai_projects')
          .select('*')
          .eq('project_id', projectId)
          .single();

        if (error) {
          if (error.code === 'PGRST116') {
            // No se encontraron datos de IA para este proyecto
            setAIProjectData(null);
          } else {
            console.error('Error fetching AI project data:', error);
            setError('Error al cargar los datos de IA del proyecto');
          }
        } else {
          setAIProjectData(data);
        }
      } catch (err) {
        console.error('Unexpected error:', err);
        setError('Error inesperado al cargar los datos');
      } finally {
        setLoading(false);
      }
    }

    if (projectId) {
      fetchAIProjectData();
    }
  }, [projectId]);

  useEffect(() => {
    if (supabase) {
      loadProjectDetails();
    }
  }, [supabase, loadProjectDetails]);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BrainCircuit className="mr-2 h-5 w-5" />
            {/* <Skeleton className="h-6 w-48" /> */} {/* Eliminado uso de Skeleton */}
            <div className="h-6 w-48 bg-gray-200 rounded"></div> {/* Marcador de posición */}
          </CardTitle>
          <CardDescription>
            {/* <Skeleton className="h-4 w-full" /> */} {/* Eliminado uso de Skeleton */}
            <div className="h-4 w-full bg-gray-200 rounded"></div> {/* Marcador de posición */}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* <Skeleton className="h-4 w-full" /> */} {/* Eliminado uso de Skeleton */}
            <div className="h-4 w-full bg-gray-200 rounded"></div> {/* Marcador de posición */}
            {/* <Skeleton className="h-4 w-full" /> */} {/* Eliminado uso de Skeleton */}
            <div className="h-4 w-full bg-gray-200 rounded"></div> {/* Marcador de posición */}
            {/* <Skeleton className="h-4 w-3/4" /> */} {/* Eliminado uso de Skeleton */}
            <div className="h-4 w-3/4 bg-gray-200 rounded"></div> {/* Marcador de posición */}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (!aiProjectData) {
    return null; // No mostrar nada si no hay datos de IA
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <BrainCircuit className="mr-2 h-5 w-5" />
          Detalles de IA
        </CardTitle>
        <CardDescription>
          Información adicional generada por IA para este proyecto
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {aiProjectData.ai_provider && (
            <div>
              <h4 className="text-sm font-medium">Proveedor de IA</h4>
              <p className="text-sm text-muted-foreground">
                <Badge variant="outline">{aiProjectData.ai_provider}</Badge>
              </p>
            </div>
          )}

          {aiProjectData.confidence_score !== null && (
            <div>
              <h4 className="text-sm font-medium">Confianza del análisis</h4>
              <div className="w-full bg-muted rounded-full h-2.5 mt-1">
                <div
                  className="bg-primary h-2.5 rounded-full"
                  style={{ width: `${Math.min(100, Math.max(0, aiProjectData.confidence_score * 100))}%` }}
                ></div>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                {Math.round(aiProjectData.confidence_score * 100)}% de confianza
              </p>
            </div>
          )}

          {aiProjectData.scope && (
            <div>
              <h4 className="text-sm font-medium">Alcance del proyecto</h4>
              <p className="text-sm text-muted-foreground">{aiProjectData.scope}</p>
            </div>
          )}

          {aiProjectData.team_requirements && aiProjectData.team_requirements.length > 0 && (
            <div>
              <h4 className="text-sm font-medium">Equipo requerido</h4>
              <div className="flex flex-wrap gap-1 mt-1">
                {aiProjectData.team_requirements.map((req: string, index: number) => (
                  <Badge key={index} variant="secondary">{req}</Badge>
                ))}
              </div>
            </div>
          )}

          {aiProjectData.client_name && (
            <div>
              <h4 className="text-sm font-medium">Cliente sugerido</h4>
              <p className="text-sm text-muted-foreground">{aiProjectData.client_name}</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
