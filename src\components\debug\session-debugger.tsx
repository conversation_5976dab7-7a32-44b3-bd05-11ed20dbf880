'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/lib/supabase/client';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';
import { RefreshCw, AlertCircle, CheckCircle, XCircle } from 'lucide-react';

export function SessionDebugger() {
  const [sessionData, setSessionData] = useState<any>(null);
  const [cookies, setCookies] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const supabase = createClient();

  const checkSession = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Obtener la sesión actual
      const { data, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError) {
        setError(sessionError.message);
        setSessionData(null);
      } else {
        setSessionData(data);
        
        // Obtener cookies disponibles
        const allCookies = document.cookie.split(';').map(cookie => cookie.trim());
        setCookies(allCookies);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error inesperado');
      setSessionData(null);
    } finally {
      setLoading(false);
    }
  };

  const refreshSession = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Intentar refrescar la sesión
      const { data, error: refreshError } = await supabase.auth.refreshSession();
      
      if (refreshError) {
        setError(refreshError.message);
      } else {
        setSessionData(data);
        
        // Obtener cookies actualizadas
        const allCookies = document.cookie.split(';').map(cookie => cookie.trim());
        setCookies(allCookies);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error inesperado');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    checkSession();
  }, []);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Depurador de Sesión
          <Button 
            variant="outline" 
            size="sm" 
            onClick={checkSession} 
            disabled={loading}
          >
            {loading ? <RefreshCw className="h-4 w-4 animate-spin" /> : <RefreshCw className="h-4 w-4" />}
            <span className="ml-2">Actualizar</span>
          </Button>
        </CardTitle>
        <CardDescription>
          Herramienta para diagnosticar problemas de autenticación y sesión
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <h3 className="text-sm font-medium">Estado de sesión:</h3>
            {sessionData?.session ? (
              <Badge variant="success" className="bg-green-100 text-green-800">
                <CheckCircle className="h-3 w-3 mr-1" />
                Activa
              </Badge>
            ) : (
              <Badge variant="destructive" className="bg-red-100 text-red-800">
                <XCircle className="h-3 w-3 mr-1" />
                Inactiva
              </Badge>
            )}
          </div>

          <Button 
            variant="secondary" 
            size="sm" 
            onClick={refreshSession} 
            disabled={loading || !sessionData?.session}
          >
            Refrescar Token
          </Button>
        </div>

        <Accordion type="single" collapsible className="w-full">
          <AccordionItem value="session">
            <AccordionTrigger>Datos de Sesión</AccordionTrigger>
            <AccordionContent>
              {sessionData?.session ? (
                <div className="space-y-2 text-sm">
                  <div>
                    <span className="font-medium">Usuario:</span> {sessionData.session.user.email}
                  </div>
                  <div>
                    <span className="font-medium">ID:</span> {sessionData.session.user.id}
                  </div>
                  <div>
                    <span className="font-medium">Expira:</span> {new Date(sessionData.session.expires_at * 1000).toLocaleString()}
                  </div>
                  <div>
                    <span className="font-medium">Token:</span> 
                    <div className="mt-1 p-2 bg-gray-100 rounded text-xs overflow-x-auto">
                      {sessionData.session.access_token.substring(0, 20)}...
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-sm text-muted-foreground">
                  No hay sesión activa
                </div>
              )}
            </AccordionContent>
          </AccordionItem>
          
          <AccordionItem value="cookies">
            <AccordionTrigger>Cookies ({cookies.length})</AccordionTrigger>
            <AccordionContent>
              {cookies.length > 0 ? (
                <ul className="space-y-1 text-sm">
                  {cookies.map((cookie, index) => (
                    <li key={index} className="p-1 bg-gray-50 rounded">
                      {cookie}
                    </li>
                  ))}
                </ul>
              ) : (
                <div className="text-sm text-muted-foreground">
                  No se encontraron cookies
                </div>
              )}
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </CardContent>
    </Card>
  );
}
