'use client'

import { useState, useEffect, Suspense } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { useAuth } from "@/hooks/use-auth"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { AlertCircle } from "lucide-react"
import { FaGithub } from "react-icons/fa"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import { GoogleButton } from "@/components/ui/google-button"
import { GoogleAuthButton } from "@/components/auth/google-auth-button"
import { GitHubAuthButton } from "@/components/auth/github-auth-button"
import { RateLimitWarning } from "./rate-limit-warning"
import Link from "next/link"
import { useSearchParams } from "next/navigation"

const formSchema = z.object({
  email: z.string().email("Correo electrónico inválido"),
  password: z.string().min(1, "La contraseña es requerida"),
  rememberMe: z.boolean().default(false),
})

export function LoginForm() {
  const { signIn, signInWithGoogle, signInWithGitHub, error: authError, isLoading } = useAuth()
  const [error, setError] = useState<string | null>(authError)

  // Componente interno para manejar useSearchParams con Suspense
  function SearchParamsHandler() {
    const searchParams = useSearchParams()

    // Manejar errores de URL (por ejemplo, de la redirección OAuth)
    useEffect(() => {
      const urlError = searchParams.get('error')
      const errorDescription = searchParams.get('error_description')
      const from = searchParams.get('from')
      const sessionError = searchParams.get('session_error')

      // Manejar diferentes tipos de errores
      if (urlError) {
        console.log('Error detectado en URL:', urlError, errorDescription);
        setError(errorDescription || `Error de autenticación: ${urlError}`)
      } else if (sessionError) {
        console.log('Error de sesión detectado:', sessionError);
        setError('La sesión ha expirado o no es válida. Por favor, inicie sesión nuevamente.')
      } else if (authError) {
        setError(authError)
      }

      // Guardar la URL de origen para redirigir después del login
      if (from) {
        localStorage.setItem('auth_redirect_after_login', from)
      }
    }, [searchParams])

    return null
  }

  // Actualizar el error cuando cambia authError
  useEffect(() => {
    setError(authError)
  }, [authError])

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
      rememberMe: false,
    },
  })

  async function onSubmit(values: z.infer<typeof formSchema>) {
    await signIn(values.email, values.password)
  }

  return (
    <div className="space-y-6">
      {/* Componente para manejar parámetros de búsqueda */}
      <Suspense fallback={null}>
        <SearchParamsHandler />
      </Suspense>

      {/* Mostrar advertencia de rate limit si es necesario */}
      <RateLimitWarning />

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Botones de inicio de sesión con proveedores externos */}
      <div className="space-y-4">
        <Button
          onClick={signInWithGoogle}
          disabled={isLoading}
          className="w-full flex items-center justify-center gap-2"
          size="lg"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 48 48">
            <path fill="#FFC107" d="M43.611 20.083H42V20H24v8h11.303c-1.649 4.657-6.08 8-11.303 8c-6.627 0-12-5.373-12-12s5.373-12 12-12c3.059 0 5.842 1.154 7.961 3.039l5.657-5.657C34.046 6.053 29.268 4 24 4C12.955 4 4 12.955 4 24s8.955 20 20 20s20-8.955 20-20c0-1.341-.138-2.65-.389-3.917z"/>
            <path fill="#FF3D00" d="m6.306 14.691l6.571 4.819C14.655 15.108 18.961 12 24 12c3.059 0 5.842 1.154 7.961 3.039l5.657-5.657C34.046 6.053 29.268 4 24 4C16.318 4 9.656 8.337 6.306 14.691z"/>
            <path fill="#4CAF50" d="M24 44c5.166 0 9.86-1.977 13.409-5.192l-6.19-5.238A11.91 11.91 0 0 1 24 36c-5.202 0-9.619-3.317-11.283-7.946l-6.522 5.025C9.505 39.556 16.227 44 24 44z"/>
            <path fill="#1976D2" d="M43.611 20.083H42V20H24v8h11.303a12.04 12.04 0 0 1-4.087 5.571l.003-.002l6.19 5.238C36.971 39.205 44 34 44 24c0-1.341-.138-2.65-.389-3.917z"/>
          </svg>
          {isLoading ? "Iniciando sesión..." : "Iniciar sesión con Google"}
        </Button>

        <Button
          onClick={signInWithGitHub}
          disabled={isLoading}
          variant="outline"
          className="w-full flex items-center justify-center gap-2"
          size="lg"
        >
          <FaGithub className="h-5 w-5" />
          {isLoading ? "Iniciando sesión..." : "Iniciar sesión con GitHub"}
        </Button>
      </div>

      <div className="relative my-4">
        <div className="absolute inset-0 flex items-center">
          <Separator className="w-full" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-background px-2 text-muted-foreground">
            O inicia sesión con tu correo
          </span>
        </div>
      </div>

      {/* Formulario de inicio de sesión con correo y contraseña */}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Correo electrónico</FormLabel>
                <FormControl>
                  <Input placeholder="<EMAIL>" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <div className="flex items-center justify-between">
                  <FormLabel>Contraseña</FormLabel>
                  <Link
                    href="/auth/forgot-password"
                    className="text-xs text-primary hover:underline"
                  >
                    ¿Olvidaste tu contraseña?
                  </Link>
                </div>
                <FormControl>
                  <Input
                    type="password"
                    autoComplete="current-password"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="rememberMe"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
                <FormLabel className="text-sm font-normal">
                  Recordarme
                </FormLabel>
              </FormItem>
            )}
          />

          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading ? "Iniciando sesión..." : "Iniciar sesión"}
          </Button>
        </form>
      </Form>

      <div className="text-center space-y-2">
        <p className="text-sm text-muted-foreground">
          ¿No tienes una cuenta?
        </p>
        <Button variant="outline" className="w-full" asChild>
          <Link href="/auth/register">
            Crear una cuenta
          </Link>
        </Button>
      </div>
    </div>
  )
}