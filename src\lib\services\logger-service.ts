/**
 * @file Logger Service
 * @description Centralized logging service for the application
 */

/**
 * Log levels
 */
export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
  FATAL = 'fatal',
}

/**
 * Log entry interface
 */
interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  data?: unknown;
}

import * as Sentry from '@sentry/nextjs';

/**
 * Logger service for centralized logging
 */
class LoggerService {
  private static instance: LoggerService;
  private minLevel: LogLevel = LogLevel.INFO;
  private logs: LogEntry[] = [];
  private maxLogs: number = 1000;
  
  /**
   * Constructor is private to implement Singleton pattern
   */
  private constructor() {
    // Initialize logger
    this.setMinLevel(
      process.env.NODE_ENV === 'production' 
        ? LogLevel.INFO 
        : LogLevel.DEBUG
    );
  }
  
  /**
   * Get the singleton instance
   */
  public static getInstance(): LoggerService {
    if (!LoggerService.instance) {
      LoggerService.instance = new LoggerService();
    }
    return LoggerService.instance;
  }
  
  /**
   * Set the minimum log level
   * @param level Minimum log level to record
   */
  public setMinLevel(level: LogLevel): void {
    this.minLevel = level;
  }
  
  /**
   * Log a debug message
   * @param message Log message
   * @param data Optional data to log
   */
  public debug(message: string, data?: unknown): void {
    this.log(LogLevel.DEBUG, message, data);
  }
  
  /**
   * Log an info message
   * @param message Log message
   * @param data Optional data to log
   */
  public info(message: string, data?: unknown): void {
    this.log(LogLevel.INFO, message, data);
  }
  
  /**
   * Log a warning message
   * @param message Log message
   * @param data Optional data to log
   */
  public warn(message: string, data?: unknown): void {
    this.log(LogLevel.WARN, message, data);
  }
  
  /**
   * Log an error message
   * @param message Log message
   * @param data Optional data to log
   */
  public error(message: string, data?: unknown): void {
    this.log(LogLevel.ERROR, message, data);
  }
  
  /**
   * Get all logs
   * @returns Array of log entries
   */
  public getLogs(): LogEntry[] {
    return [...this.logs];
  }
  
  /**
   * Clear all logs
   */
  public clearLogs(): void {
    this.logs = [];
  }
  
  /**
   * Internal log method
   * @param level Log level
   * @param message Log message
   * @param data Optional data to log
   */
  private log(level: LogLevel, message: string, data?: unknown): void {
    // Skip if below minimum level
    if (!this.shouldLog(level)) {
      return;
    }
    
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      data,
    };
    
    // Add to in-memory logs
    this.logs.push(entry);
    
    // Trim logs if exceeding max
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }
    
    // Log to console in development
    if (process.env.NODE_ENV !== 'production') {
      this.logToConsole(entry);
    }
    
    // In production, you might want to send logs to a service
    if (process.env.NODE_ENV === 'production') {
      if (level === 'error' || level === 'fatal') {
      Sentry.captureException(new Error(message), { extra: data && typeof data === 'object' ? data as Record<string, unknown> : { data } });
    } else {
      Sentry.captureMessage(message, {
        level: level as Sentry.SeverityLevel,
        extra: data && typeof data === 'object' ? data as Record<string, unknown> : { data },
      });
    }
    }
  }
  
  /**
   * Determine if a log level should be recorded
   * @param level Log level to check
   * @returns True if the log should be recorded
   */
  private shouldLog(level: LogLevel): boolean {
    const levels = [LogLevel.DEBUG, LogLevel.INFO, LogLevel.WARN, LogLevel.ERROR];
    const minLevelIndex = levels.indexOf(this.minLevel);
    const currentLevelIndex = levels.indexOf(level);
    
    return currentLevelIndex >= minLevelIndex;
  }
  
  /**
   * Log to console with appropriate formatting
   * @param entry Log entry to output
   */
  private logToConsole(entry: LogEntry): void {
    const { level, message, data } = entry;
    
    switch (level) {
      case LogLevel.DEBUG:
        console.debug(`[DEBUG] ${message}`, data || '');
        break;
      case LogLevel.INFO:
        console.info(`[INFO] ${message}`, data || '');
        break;
      case LogLevel.WARN:
        console.warn(`[WARN] ${message}`, data || '');
        break;
      case LogLevel.ERROR:
        console.error(`[ERROR] ${message}`, data || '');
        break;
    }
  }
}

// Export singleton instance
export const logger = LoggerService.getInstance();
