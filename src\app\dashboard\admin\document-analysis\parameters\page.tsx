import { Metada<PERSON> } from "next"
import { DashboardShell } from "@/components/shared/layout/dashboard-shell"
import { ModelParametersGuide } from "@/components/features/admin/model-parameters-guide"

export const metadata: Metadata = {
  title: "Guía de Parámetros | AdminCore ",
  description: "Guía detallada de parámetros para modelos de análisis de documentos",
}

export default function ModelParametersGuidePage() {
  return (
    <DashboardShell heading="Parámetros del Modelo">
      <ModelParametersGuide />
    </DashboardShell>
  )
}
