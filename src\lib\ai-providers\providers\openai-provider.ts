/**
 * @ai-file-description: "OpenAI provider implementation for document analysis"
 * @ai-related-files: ["../base-provider.ts", "../provider-interface.ts", "../provider-factory.ts"]
 * @ai-owner: "File-Based Projects"
 */

import OpenAI from 'openai';
import { BaseAIProvider } from '../base-provider';
import { AIProviderConfig, DocumentAnalysisResult } from '../provider-interface';

/**
 * OpenAI provider implementation
 * 
 * @ai-responsibility: "Implements document analysis using OpenAI models"
 */
export class OpenAIProvider extends BaseAIProvider {
  private openai: OpenAI;
  
  constructor(config: AIProviderConfig) {
    super(config);
    this.openai = new OpenAI({
      apiKey: this.apiKey
    });
  }
  
  /**
   * Gets the name of the provider
   */
  getProviderName(): string {
    return 'openai';
  }
  
  /**
   * Gets the display name of the provider for UI
   */
  getProviderDisplayName(): string {
    return 'OpenAI';
  }
  
  /**
   * Analyzes document text using OpenAI
   * 
   * @param documentText The text content of the document
   * @returns Structured project information
   */
  async analyzeDocument(documentText: string): Promise<DocumentAnalysisResult> {
    try {
      if (!this.validateConfig()) {
        throw new Error('Invalid OpenAI configuration');
      }
      
      // Prepare the prompt
      const systemPrompt = this.getSystemPrompt();
      const userPrompt = this.getUserPrompt(documentText);
      
      // Call OpenAI API
      const response = await this.openai.chat.completions.create({
        model: this.modelName || 'gpt-4',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: this.temperature,
        max_tokens: this.maxTokens,
        response_format: { type: 'json_object' }
      });
      
      // Extract and parse the response
      const content = response.choices[0]?.message?.content;
      
      if (!content) {
        throw new Error('Empty response from OpenAI');
      }
      
      let parsedResponse;
      try {
        parsedResponse = JSON.parse(content);
      } catch (e) {
        console.error('Failed to parse OpenAI response as JSON:', e);
        console.log('Raw response:', content);
        throw new Error('Failed to parse OpenAI response as JSON');
      }
      
      return this.processResponse(parsedResponse);
    } catch (error) {
      console.error('Error analyzing document with OpenAI:', error);
      throw error;
    }
  }
  
  /**
   * Gets the system prompt for document analysis
   * Customized for OpenAI's capabilities
   */
  protected getSystemPrompt(): string {
    return `
You are a specialized AI assistant for engineering project management. Your task is to analyze project documents and extract key information in a structured format.

Extract the following information from the document:
1. Project name
2. Project description
3. Start date (in ISO format YYYY-MM-DD)
4. End date (in ISO format YYYY-MM-DD)
5. Budget amount (as a string)
6. Currency (USD, CLP, etc.)
7. Client name
8. Key deliverables (as an array)
9. Project scope
10. Team requirements (as an array)
11. Tags (as an array of keywords)

Format your response as a JSON object with these fields. If information is not found, use null.
Provide a confidence_score between 0 and 1 indicating your confidence in the extracted information.

IMPORTANT: Your response must be valid JSON. You must respond with a JSON object only, no additional text.
`;
  }
}
