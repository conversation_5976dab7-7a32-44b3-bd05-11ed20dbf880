/**
 * @ai-file-description: "Page for creating a project from AI document analysis"
 * @ai-related-files: ["../../from-document/page.tsx", "../../../[id]/page.tsx"]
 * @ai-owner: "File-Based Projects"
 */

import { Metadata } from "next";
import { redirect } from "next/navigation";
import { cookies } from "next/headers";
import { createClient } from "@/lib/supabase/server-client";
import { ProjectFormFromAnalysis } from "@/components/features/projects/project-form-from-analysis";
// Importaciones de componentes de layout eliminadas ya que no se encontraron
// import { DashboardShell } from "@/components/shared/layout/dashboard-shell";
// import { DashboardHeader } from "@/components/shared/layout/dashboard-header";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, Loader2 } from "lucide-react";

export const metadata: Metadata = {
  title: "Create Project from Analysis",
  description: "Create a new project based on AI document analysis",
};

interface PageProps {
  params: {
    id: string;
  };
}

/**
 * Page for creating a project from AI document analysis
 *
 * @ai-responsibility: "Displays project creation form with pre-filled data from AI analysis"
 */
export default async function ProjectFromAnalysisPage({ params }: PageProps) {
  const { id } = params as { id: string };
  const cookieStore = await cookies();
  const supabase = createClient(cookieStore);

  // Check if user is authenticated
  // No pasar parámetros a getUser() para compatibilidad con todas las versiones de Supabase
  const { data: userData, error: userError } = await supabase.auth.getUser();

  if (userError || !userData.user) {
    redirect("/auth/signin");
  }

  // Get analysis data
  const { data: analysis, error: analysisError } = await supabase
    .from("ai_document_analyses")
    .select("*, document:document_id(*)")
    .eq("id", id as any)
    .single();

  if (analysisError) {
    return (
      // Reemplazado DashboardShell y DashboardHeader con divs básicos
      <div className="container grid items-center gap-6 pb-8 pt-6 md:py-10">
        <div className="flex flex-col items-start gap-2">
          <h1 className="text-3xl font-extrabold leading-tight tracking-tighter md:text-4xl">
            Create Project from Analysis
          </h1>
          <p className="max-w-[700px] text-lg text-muted-foreground">
            Create a new project based on AI document analysis
          </p>
        </div>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            Analysis not found or you don't have access to it.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Check if analysis is still pending
  if ((analysis as any).status === "pending") {
    return (
      // Reemplazado DashboardShell y DashboardHeader con divs básicos
      <div className="container grid items-center gap-6 pb-8 pt-6 md:py-10">
        <div className="flex flex-col items-start gap-2">
          <h1 className="text-3xl font-extrabold leading-tight tracking-tighter md:text-4xl">
            Create Project from Analysis
          </h1>
          <p className="max-w-[700px] text-lg text-muted-foreground">
            Create a new project based on AI document analysis
          </p>
        </div>
        <div className="flex flex-col items-center justify-center py-12">
          <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
          <h2 className="text-xl font-semibold mb-2">Analysis in Progress</h2>
          <p className="text-muted-foreground text-center max-w-md">
            We're analyzing your document. This may take a few moments.
            The page will automatically update when the analysis is complete.
          </p>
        </div>
      </div>
    );
  }

  // Check if analysis failed
  if ((analysis as any).status === "failed") {
    return (
      // Reemplazado DashboardShell y DashboardHeader con divs básicos
      <div className="container grid items-center gap-6 pb-8 pt-6 md:py-10">
        <div className="flex flex-col items-start gap-2">
          <h1 className="text-3xl font-extrabold leading-tight tracking-tighter md:text-4xl">
            Create Project from Analysis
          </h1>
          <p className="max-w-[700px] text-lg text-muted-foreground">
            Create a new project based on AI document analysis
          </p>
        </div>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Analysis Failed</AlertTitle>
          <AlertDescription>
            {(analysis as any).error_message || "An error occurred during document analysis."}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    // Reemplazado DashboardShell y DashboardHeader con divs básicos
    <div className="container grid items-center gap-6 pb-8 pt-6 md:py-10">
      <div className="flex flex-col items-start gap-2">
        <h1 className="text-3xl font-extrabold leading-tight tracking-tighter md:text-4xl">
          Crear Proyecto desde Análisis
        </h1>
        <p className="max-w-[700px] text-lg text-muted-foreground">
          Revisa y edita la información extraída del documento antes de crear el proyecto
        </p>
      </div>

      <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg mb-6 border border-blue-200 dark:border-blue-800">
        <h2 className="text-lg font-semibold mb-2 flex items-center">
          <AlertCircle className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400" />
          Información Importante
        </h2>
        <p className="text-sm text-muted-foreground mb-2">
          La IA ha analizado tu documento y ha extraído la información del proyecto. Ahora debes:
        </p>
        <ol className="list-decimal pl-5 text-sm space-y-1">
          <li>Revisar la información extraída y corregirla si es necesario</li>
          <li>Completar cualquier campo faltante</li>
          <li>Hacer clic en "Crear Proyecto" para finalizar</li>
        </ol>
      </div>

      <ProjectFormFromAnalysis analysis={analysis as any} />
    </div>
  );
}
