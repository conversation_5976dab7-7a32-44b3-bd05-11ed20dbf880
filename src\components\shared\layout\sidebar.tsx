import Link from "next/link"
import { usePathname } from "next/navigation"
import { MainNav } from "./main-nav"
import { cn } from "@/lib/utils"
import {
  Activity,
  BarChart3,
  Boxes,
  ClipboardList,
  FileText,
  FolderOpen,
  Settings,
  <PERSON><PERSON>,
  Users
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

export function Sidebar({ className }: { className?: string }) {
  const pathname = usePathname()

  return (
    <div className={cn("flex flex-col h-full bg-card", className)}>
      <div className="px-3 py-2 flex-1">
        <Link href="/dashboard" className="flex items-center pl-3 mb-10">
          <h1 className="text-2xl font-bold tracking-tight">
            AdminCore
          </h1>
        </Link>
        <MainNav />
      </div>
    </div>
  )
}