"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { createClient } from "@/lib/supabase/client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { toast } from "@/components/shared/ui/use-toast"
import { format } from "date-fns"
import { es } from "date-fns/locale"
import { Pencil, Trash2, ArrowLeft, Clock, Calendar, User, Users } from "lucide-react"
import Link from "next/link"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialog<PERSON>itle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

// Función para obtener el color del estado
const getStatusColor = (status: string) => {
  switch (status) {
    case "pending":
      return "bg-yellow-500"
    case "in_progress":
      return "bg-blue-500"
    case "completed":
      return "bg-green-500"
    case "cancelled":
      return "bg-red-500"
    default:
      return "bg-gray-500"
  }
}

// Función para obtener el texto del estado
const getStatusText = (status: string) => {
  switch (status) {
    case "pending":
      return "Pendiente"
    case "in_progress":
      return "En Progreso"
    case "completed":
      return "Completada"
    case "cancelled":
      return "Cancelada"
    default:
      return status
  }
}

// Función para obtener el color de la prioridad
const getPriorityColor = (priority: string) => {
  switch (priority) {
    case "low":
      return "bg-green-500"
    case "medium":
      return "bg-yellow-500"
    case "high":
      return "bg-orange-500"
    case "critical":
      return "bg-red-500"
    default:
      return "bg-gray-500"
  }
}

// Función para obtener el texto de la prioridad
const getPriorityText = (priority: string) => {
  switch (priority) {
    case "low":
      return "Baja"
    case "medium":
      return "Media"
    case "high":
      return "Alta"
    case "critical":
      return "Crítica"
    default:
      return priority
  }
}

interface WorkOrderUser {
  user_id: string
  role: string
  user: {
    id: string
    email: string
    full_name?: string
  }
}

export default function WorkOrderDetailPage({ params }: { params: { id: string } }) {
  const [workOrder, setWorkOrder] = useState<any>(null)
  const [assignedUsers, setAssignedUsers] = useState<WorkOrderUser[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isDeleting, setIsDeleting] = useState(false)
  const router = useRouter()
  const supabase = createClient()

  useEffect(() => {
    const fetchWorkOrder = async () => {
      setIsLoading(true)
      try {
        // Obtener la orden de trabajo
        const { data: workOrderData, error: workOrderError } = await supabase
          .from("work_orders")
          .select(`
            *,
            project:project_id(id, name),
            assigned_user:assigned_to(id, email, full_name)
          `)
          .eq("id", params.id)
          .single()

        if (workOrderError) throw workOrderError
        setWorkOrder(workOrderData)

        // Obtener los usuarios asignados
        const { data: usersData, error: usersError } = await supabase
          .from("work_order_users")
          .select(`
            user_id,
            role,
            user:user_id(id, email, full_name)
          `)
          .eq("work_order_id", params.id)

        if (usersError) throw usersError
        setAssignedUsers(usersData || [])
      } catch (error) {
        console.error("Error al cargar la orden de trabajo:", error)
        toast({
          title: "Error",
          description: "No se pudo cargar la orden de trabajo.",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchWorkOrder()
  }, [params.id, supabase])

  const handleDelete = async () => {
    setIsDeleting(true)
    try {
      const { error } = await supabase
        .from("work_orders")
        .delete()
        .eq("id", params.id)

      if (error) throw error

      toast({
        title: "Orden de trabajo eliminada",
        description: "La orden de trabajo se ha eliminado correctamente.",
      })

      router.push("/dashboard/work-orders")
      router.refresh()
    } catch (error: unknown) {
      console.error("Error al eliminar la orden de trabajo:", error)
      const errorMessage = error instanceof Error ? error.message : "Ha ocurrido un error al eliminar la orden de trabajo."
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsDeleting(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-48">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!workOrder) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold">Orden de trabajo no encontrada</h2>
        <p className="text-muted-foreground mt-2">
          La orden de trabajo que estás buscando no existe o ha sido eliminada.
        </p>
        <Button asChild className="mt-4">
          <Link href="/dashboard/work-orders">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Volver a órdenes de trabajo
          </Link>
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <Button variant="outline" size="sm" asChild>
            <Link href="/dashboard/work-orders">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Volver
            </Link>
          </Button>
          <h2 className="text-3xl font-bold tracking-tight mt-2">{workOrder.title}</h2>
          <div className="flex items-center gap-2 mt-1">
            <Badge className={getStatusColor(workOrder.status)}>
              {getStatusText(workOrder.status)}
            </Badge>
            <Badge className={getPriorityColor(workOrder.priority)}>
              {getPriorityText(workOrder.priority)}
            </Badge>
            {workOrder.project && (
              <Badge variant="outline">{workOrder.project.name}</Badge>
            )}
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" asChild>
            <Link href={`/dashboard/work-orders/${params.id}/edit`}>
              <Pencil className="mr-2 h-4 w-4" />
              Editar
            </Link>
          </Button>
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="destructive">
                <Trash2 className="mr-2 h-4 w-4" />
                Eliminar
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>¿Estás seguro?</AlertDialogTitle>
                <AlertDialogDescription>
                  Esta acción no se puede deshacer. Se eliminará permanentemente la orden de trabajo.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancelar</AlertDialogCancel>
                <AlertDialogAction
                  onClick={handleDelete}
                  disabled={isDeleting}
                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                >
                  {isDeleting ? "Eliminando..." : "Eliminar"}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Detalles</CardTitle>
          </CardHeader>
          <CardContent>
            {workOrder.description ? (
              <div className="prose max-w-none">
                <p>{workOrder.description}</p>
              </div>
            ) : (
              <p className="text-muted-foreground">Sin descripción</p>
            )}
          </CardContent>
        </Card>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Información</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-start gap-2">
                <Calendar className="h-5 w-5 text-muted-foreground mt-0.5" />
                <div>
                  <p className="text-sm font-medium">Fecha de creación</p>
                  <p className="text-sm text-muted-foreground">
                    {format(new Date(workOrder.created_at), "PPP", { locale: es })}
                  </p>
                </div>
              </div>
              {workOrder.due_date && (
                <div className="flex items-start gap-2">
                  <Clock className="h-5 w-5 text-muted-foreground mt-0.5" />
                  <div>
                    <p className="text-sm font-medium">Fecha límite</p>
                    <p className="text-sm text-muted-foreground">
                      {format(new Date(workOrder.due_date), "PPP", { locale: es })}
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="h-5 w-5 mr-2" />
                Usuarios Asignados
              </CardTitle>
            </CardHeader>
            <CardContent>
              {assignedUsers.length > 0 ? (
                <div className="space-y-3">
                  {assignedUsers.map((item) => (
                    <div key={item.user_id} className="flex items-center justify-between">
                      <div className="flex flex-col">
                        <span className="font-medium">
                          {item.user.full_name || "Usuario sin nombre"}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {item.user.email}
                        </span>
                      </div>
                      <Badge variant="outline">{item.role}</Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground text-sm">
                  No hay usuarios asignados a esta orden de trabajo.
                </p>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
