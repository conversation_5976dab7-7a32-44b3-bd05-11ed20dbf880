'use client'

import { useState, useEffect, useCallback } from 'react'
import { useParams, useRouter } from 'next/navigation'
import { GitHubService, GitHubRepository } from '@/lib/services/github-service'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { FaGithub, FaSync, FaExternalLinkAlt, FaLock, FaUnlock } from 'react-icons/fa'
import { Loader2 } from 'lucide-react'
import { createClient } from '@/lib/supabase/client'

export default function GitHubSyncPage() {
  const params = useParams()
  const router = useRouter()
  const repositoryId = params.repositoryId as string
  const [repository, setRepository] = useState<GitHubRepository | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [projectName, setProjectName] = useState('')
  const [projectDescription, setProjectDescription] = useState('')
  const [syncEnabled, setSyncEnabled] = useState(true)
  const [autoSync, setAutoSync] = useState(false)
  const [syncInterval, setSyncInterval] = useState(60)
  const [syncBranch, setSyncBranch] = useState('')
  const [syncPath, setSyncPath] = useState('/')
  const [branches, setBranches] = useState<string[]>([])
  const githubService = new GitHubService()
  const supabase = createClient()

  const loadRepositoryData = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)

      // Obtener la conexión de GitHub
      const connection = await githubService.getUserConnection()

      if (!connection) {
        setError('No se encontró una conexión de GitHub activa')
        return
      }

      // Obtener el repositorio específico
      const { data, error } = await supabase
        .from('github_repositories')
        .select('*')
        .eq('id', repositoryId)
        .single()

      if (error) {
        console.error('Error al obtener repositorio:', error)
        setError('No se pudo cargar el repositorio')
        return
      }

      setRepository(data as GitHubRepository)
      setProjectName(data.repository_name)
      setProjectDescription(data.repository_description || '')
      setSyncBranch(data.default_branch)

      // Obtener las ramas del repositorio desde la API de GitHub
      const response = await fetch(`https://api.github.com/repos/${data.repository_full_name}/branches`, {
        headers: {
          'Authorization': `token ${connection.access_token}`,
          'Accept': 'application/vnd.github.v3+json'
        }
      })

      if (response.ok) {
        const branchesData = await response.json()
        setBranches(branchesData.map((branch: any) => branch.name))
      } else {
        console.error('Error al obtener ramas:', response.statusText)
      }
    } catch (e: unknown) {
      console.error('Error al cargar datos del repositorio:', e)
      const errorMessage = e instanceof Error ? e.message : 'Error al cargar datos del repositorio';
      setError(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }, [repositoryId, supabase])

  useEffect(() => {
    loadRepositoryData()
  }, [loadRepositoryData])

  const handleCreateProject = async () => {
    try {
      setIsSaving(true)
      setError(null)

      if (!repository) {
        setError('No se encontró el repositorio')
        return
      }

      // Crear el proyecto en la base de datos
      const { data: projectData, error: projectError } = await supabase
        .from('projects')
        .insert({
          name: projectName,
          description: projectDescription,
          root_path: syncPath,
          project_type: 'github',
          config: {
            github_repository_id: repository.id,
            github_repository_name: repository.repository_name,
            github_repository_url: repository.repository_url,
            sync_branch: syncBranch,
            auto_sync: autoSync,
            sync_interval_minutes: syncInterval
          }
        })
        .select('id')
        .single()

      if (projectError) {
        console.error('Error al crear proyecto:', projectError)
        setError('No se pudo crear el proyecto')
        return
      }

      // Crear la configuración de sincronización
      const { error: syncError } = await supabase
        .from('github_project_sync')
        .insert({
          project_id: projectData.id,
          repository_id: repository.id,
          sync_enabled: syncEnabled,
          auto_sync: autoSync,
          sync_interval_minutes: syncInterval,
          sync_branch: syncBranch,
          sync_path: syncPath
        })

      if (syncError) {
        console.error('Error al crear configuración de sincronización:', syncError)
        setError('No se pudo crear la configuración de sincronización')
        return
      }

      // Redirigir a la página del proyecto
      router.push(`/dashboard/projects/${projectData.id}`)
    } catch (e: unknown) {
      console.error('Error al crear proyecto:', e)
      const errorMessage = e instanceof Error ? e.message : 'Error al crear proyecto';
      setError(errorMessage)
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <div className="container mx-auto py-6 flex justify-center items-center h-40">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Cargando...</span>
      </div>
    )
  }

  if (!repository) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardHeader>
            <CardTitle>Error</CardTitle>
            <CardDescription>No se pudo cargar el repositorio</CardDescription>
          </CardHeader>
          <CardContent>
            <p>{error || 'No se encontró el repositorio especificado'}</p>
          </CardContent>
          <CardFooter>
            <Button onClick={() => router.push('/dashboard/github')}>
              Volver a GitHub
            </Button>
          </CardFooter>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-3xl font-bold mb-6">Sincronizar repositorio con proyecto</h1>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FaGithub className="mr-2 h-6 w-6" />
            {repository.repository_name}
          </CardTitle>
          <CardDescription>
            {repository.repository_description || 'Sin descripción'}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="projectName">Nombre del proyecto</Label>
            <Input
              id="projectName"
              value={projectName}
              onChange={(e) => setProjectName(e.target.value)}
              placeholder="Nombre del proyecto"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="projectDescription">Descripción del proyecto</Label>
            <Input
              id="projectDescription"
              value={projectDescription}
              onChange={(e) => setProjectDescription(e.target.value)}
              placeholder="Descripción del proyecto"
            />
          </div>

          <Separator className="my-4" />

          <h3 className="text-lg font-semibold">Configuración de sincronización</h3>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="syncEnabled">Sincronización habilitada</Label>
              <p className="text-sm text-muted-foreground">
                Habilitar la sincronización con GitHub
              </p>
            </div>
            <Switch
              id="syncEnabled"
              checked={syncEnabled}
              onCheckedChange={setSyncEnabled}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="autoSync">Sincronización automática</Label>
              <p className="text-sm text-muted-foreground">
                Sincronizar automáticamente en intervalos regulares
              </p>
            </div>
            <Switch
              id="autoSync"
              checked={autoSync}
              onCheckedChange={setAutoSync}
            />
          </div>

          {autoSync && (
            <div className="space-y-2">
              <Label htmlFor="syncInterval">Intervalo de sincronización (minutos)</Label>
              <Input
                id="syncInterval"
                type="number"
                min="5"
                value={syncInterval}
                onChange={(e) => setSyncInterval(parseInt(e.target.value))}
              />
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="syncBranch">Rama a sincronizar</Label>
            <Select value={syncBranch} onValueChange={setSyncBranch}>
              <SelectTrigger>
                <SelectValue placeholder="Selecciona una rama" />
              </SelectTrigger>
              <SelectContent>
                {branches.map((branch) => (
                  <SelectItem key={branch} value={branch}>
                    {branch}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="syncPath">Ruta del proyecto</Label>
            <Input
              id="syncPath"
              value={syncPath}
              onChange={(e) => setSyncPath(e.target.value)}
              placeholder="/"
            />
            <p className="text-sm text-muted-foreground">
              Ruta dentro del repositorio donde se encuentra el proyecto (/ para la raíz)
            </p>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={() => router.push('/dashboard/github')}>
            Cancelar
          </Button>
          <Button
            onClick={handleCreateProject}
            disabled={isSaving || !projectName}
          >
            {isSaving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Creando proyecto...
              </>
            ) : (
              'Crear proyecto'
            )}
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}
