export const dynamic = 'force-dynamic'

import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/client'
import { cookies } from 'next/headers'
import stringSimilarity from 'string-similarity'
import type { Database } from '@/lib/supabase/types'

export async function POST(request: Request) {
  try {
    // Obtener el cuerpo de la solicitud
    const requestData = await request.json()

    // Validar que se proporcionó un ID de documento
    if (!requestData.documentId) {
      return NextResponse.json(
        { error: 'Se requiere un ID de documento' },
        { status: 400 }
      )
    }

    // Crear cliente de Supabase (sin argumentos)
    const supabase = createClient()

    // Verificar autenticación
    const { data: { session } } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      )
    }

    // Verificar si el usuario es administrador
    const { data: profile, error: profileError } = await (supabase as any)
      .from('profiles')
      .select('role')
      .eq('id', session.user.id)
      .single()

    if (profileError || profile?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Acceso denegado. Se requieren permisos de administrador' },
        { status: 403 }
      )
    }

    // Obtener información del documento
    const { data: document, error: documentError } = await (supabase as any)
      .from('documents')
      .select('*')
      .eq('id', requestData.documentId)
      .single()

    if (documentError || !document) {
      return NextResponse.json(
        { error: 'Documento no encontrado' },
        { status: 404 }
      )
    }

    // Obtener URL firmada para el documento
    const { data: signedUrlData, error: signedUrlError } = await supabase
      .storage
      .from('documents')
      .createSignedUrl(document.file_path, 60)

    if (signedUrlError || !signedUrlData) {
      return NextResponse.json(
        { error: 'Error al generar URL firmada para el documento' },
        { status: 500 }
      )
    }

    const signedUrl = signedUrlData.signedUrl;

    // Extraer texto original del documento
    let originalText = ''
    try {
      // Descargar el documento para extraer texto
      const response = await fetch(signedUrl)
      const blob = await response.blob()

      // Si es un PDF, intentar extraer texto usando una biblioteca de extracción de texto
      if (document.filename.toLowerCase().endsWith('.pdf')) {
        try {
          // Aquí se implementaría la extracción real de texto del PDF
          // Por ahora, usamos un texto simulado más realista
          originalText = `PROPUESTA DE PROYECTO: ${document.filename.replace('.pdf', '')}\n\n` +
            `CLIENTE: Empresa ABC\n` +
            `FECHA: ${new Date().toLocaleDateString()}\n\n` +
            `1. INTRODUCCIÓN\n\n` +
            `Este documento presenta una propuesta para el desarrollo de un nuevo sistema de gestión para la Empresa ABC. ` +
            `El proyecto tiene como objetivo mejorar la eficiencia operativa y proporcionar herramientas analíticas avanzadas.\n\n` +
            `2. ALCANCE DEL PROYECTO\n\n` +
            `- Desarrollo de módulos de gestión de usuarios\n` +
            `- Implementación de dashboard analítico\n` +
            `- Integración con sistemas existentes\n` +
            `- Capacitación de usuarios finales\n\n` +
            `3. CRONOGRAMA\n\n` +
            `- Inicio: ${new Date().toLocaleDateString()}\n` +
            `- Duración: 6 meses\n` +
            `- Entrega final: ${new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000).toLocaleDateString()}\n\n` +
            `4. PRESUPUESTO\n\n` +
            `El costo total del proyecto es de $120,000 USD, distribuidos de la siguiente manera:\n\n` +
            `- Desarrollo: $80,000\n` +
            `- Implementación: $20,000\n` +
            `- Capacitación: $10,000\n` +
            `- Contingencia: $10,000\n\n` +
            `5. EQUIPO DE TRABAJO\n\n` +
            `- 1 Gerente de Proyecto\n` +
            `- 3 Desarrolladores Senior\n` +
            `- 2 Desarrolladores Junior\n` +
            `- 1 Especialista en UX/UI\n` +
            `- 1 Especialista en QA\n\n` +
            `6. CONTACTO\n\n` +
            `Para cualquier consulta, contactar a:\n` +
            `Juan Pérez\n` +
            `Director de Proyectos\n` +
            `Email: <EMAIL>\n` +
            `Tel: ****** 567 8900`
        } catch (pdfError) {
          console.error('Error al extraer texto del PDF:', pdfError)
          const errorMessage = pdfError instanceof Error ? pdfError.message : 'Error desconocido'
          originalText = `No se pudo extraer texto del documento ${document.filename}. Error: ${errorMessage}`
        }
      } else {
        // Para otros tipos de documentos
        originalText = `Contenido original del documento ${document.filename}.\n\nEste es un texto simulado para pruebas de comparación.\n\nEl documento real tendría el contenido extraído directamente del archivo.`
      }
    } catch (error) {
      console.error('Error al extraer texto original:', error)
      originalText = 'Error al extraer texto original del documento'
    }

    // Realizar solicitud a la función de análisis de documentos
    const { data: analysisData, error: analysisError } = await supabase.functions.invoke(
      'analyze-document',
      {
        body: {
          documentId: requestData.documentId,
          provider: requestData.provider || 'gemini',
          userId: session.user.id,
          config: requestData.config || {
            modelName: 'gemini-1.5-flash',
            maxTokens: 2000,
            temperature: 0.7
          },
          testMode: true, // Indicar que es una prueba
          extractTextOnly: true // Solicitar solo extracción de texto para comparación
        }
      }
    )

    if (analysisError) {
      return NextResponse.json(
        { error: 'Error al analizar el documento', details: analysisError },
        { status: 500 }
      )
    }

    // Extraer texto del documento usando OCR (desde la respuesta)
    const extractedText = analysisData.extractedText || 'No se pudo extraer texto del documento'

    // Realizar análisis completo del documento
    const { data: fullAnalysisData, error: fullAnalysisError } = await supabase.functions.invoke(
      'analyze-document',
      {
        body: {
          documentId: requestData.documentId,
          provider: requestData.provider || 'gemini',
          userId: session.user.id,
          config: requestData.config || {
            modelName: 'gemini-1.5-flash',
            maxTokens: 2000,
            temperature: 0.7
          },
          documentText: extractedText, // Usar el texto extraído para el análisis
          testMode: true // Indicar que es una prueba
        }
      }
    )

    if (fullAnalysisError) {
      return NextResponse.json(
        { error: 'Error al realizar análisis completo', details: fullAnalysisError },
        { status: 500 }
      )
    }

    // Calcular similitud entre texto original y extraído
    let similarityScore = undefined;
    try {
      if (originalText && extractedText) {
        // Calcular similitud usando string-similarity
        similarityScore = stringSimilarity.compareTwoStrings(originalText, extractedText);
        console.log(`Similitud calculada: ${similarityScore}`);
      }
    } catch (similarityError) {
      console.error('Error al calcular similitud:', similarityError);
    }

    // Calcular tiempo de procesamiento total
    const processingTime = fullAnalysisData.processingTime || 0;

    // Devolver resultados
    return NextResponse.json({
      success: true,
      documentId: requestData.documentId,
      originalText,
      extractedText,
      analysisResult: fullAnalysisData.result || null,
      processingTime,
      similarityScore,
      model: requestData.config?.modelName || 'gemini-1.5-flash'
    })

  } catch (error) {
    console.error('Error en API de prueba de análisis:', error)
    const message = error instanceof Error ? error.message : 'Error desconocido'
    return NextResponse.json(
      { error: 'Error interno del servidor', details: message },
      { status: 500 }
    )
  }
}
