import { <PERSON>ada<PERSON> } from "next"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Calendar, User, AlertCircle, Database, ShieldAlert, Info, Wrench } from "lucide-react"
import { Project, ProjectStatus, getStatusColor, getStatusText } from "@/types/projects"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { ProjectsTable } from "@/components/features/projects/projects-table"
import { ProjectsFixDialog } from "@/components/features/projects/projects-fix-dialog"
import { ReloadButton } from "@/components/features/projects/reload-button"
import { AuthReconnect } from "@/components/features/auth/auth-reconnect"
import { AuthDiagnostics } from "@/components/features/auth/auth-diagnostics"
import { getServerSideProjects } from "./projects-server"
import { verifyAndFixUserRoles } from "@/lib/supabase/fix-user-roles"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import Link from "next/link"



export const metadata: Metadata = {
  title: "Proyectos | AdminCore ",
  description: "Gestión de proyectos de ingeniería",
}

// Configurar revalidación cada 5 segundos
export const revalidate = 5

export default async function ProjectsPage() {
  try {
    // Intentar verificar y corregir roles de usuario
    await verifyAndFixUserRoles();

    // Obtener proyectos
    const projects = await getServerSideProjects();
    console.log('Proyectos cargados en la página:', projects.length);

    return (
      <div className="space-y-8">
        <div className="flex items-center justify-between">
          <h2 className="text-3xl font-bold tracking-tight">Proyectos</h2>
          <Button asChild>
            <a href="/dashboard/projects/new">Nuevo Proyecto</a>
          </Button>
        </div>

        <Tabs defaultValue="table" className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="table">Tabla</TabsTrigger>
            <TabsTrigger value="cards">Tarjetas</TabsTrigger>
          </TabsList>
          <TabsContent value="table" className="w-full">
            <ProjectsTable data={projects} />
          </TabsContent>
          <TabsContent value="cards">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {projects.map((project) => (
                <Card key={project.id} className="hover:bg-muted/50 transition-colors">
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span className="truncate">{project.name}</span>
                      <Badge variant={getStatusColor(project.status as ProjectStatus)}>
                        {getStatusText(project.status as ProjectStatus)}
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-sm text-muted-foreground line-clamp-2">
                      {project.description}
                    </p>
                    <div className="flex items-center space-x-4 text-sm">
                      <div className="flex items-center">
                        <Calendar className="mr-1 h-4 w-4 opacity-70" />
                        {new Date(project.created_at).toLocaleDateString()}
                      </div>
                      <div className="flex items-center">
                        <User className="mr-1 h-4 w-4 opacity-70" />
                        {project.users?.full_name || 'Sin asignar'}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>

        {projects.length === 0 && (
          <div className="space-y-6">
            <Alert>
              <Info className="h-4 w-4" />
              <AlertTitle>No hay proyectos disponibles</AlertTitle>
              <AlertDescription>
                No se encontraron proyectos para mostrar. Esto puede deberse a varias razones.
              </AlertDescription>
            </Alert>

            <Card>
              <CardHeader>
                <CardTitle>¿Qué puedo hacer?</CardTitle>
                <CardDescription>
                  Aquí hay algunas opciones para resolver este problema
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base flex items-center">
                        <Database className="mr-2 h-4 w-4" /> Crear un nuevo proyecto
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="text-sm">
                      Puede crear un nuevo proyecto manualmente haciendo clic en el botón "Nuevo Proyecto".
                    </CardContent>
                    <CardFooter>
                      <Button asChild size="sm">
                        <Link href="/dashboard/projects/new">Crear Proyecto</Link>
                      </Button>
                    </CardFooter>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base flex items-center">
                        <ShieldAlert className="mr-2 h-4 w-4" /> Ejecutar diagnóstico
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="text-sm">
                      Ejecute una herramienta de diagnóstico para identificar y resolver problemas con los proyectos.
                    </CardContent>
                    <CardFooter>
                      <div className="flex flex-col space-y-2">
                        <ProjectsFixDialog
                          trigger={
                            <Button size="sm">
                              <Wrench className="mr-2 h-4 w-4" />
                              Diagnosticar problema
                            </Button>
                          }
                        />
                        <Button asChild size="sm" variant="outline">
                          <Link href="/dashboard/projects/diagnostics/connection">Verificar Conexión</Link>
                        </Button>
                        <div className="mt-4">
                          <AuthDiagnostics />
                        </div>
                      </div>
                    </CardFooter>
                  </Card>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between border-t pt-4">
                <ReloadButton />
                <Button asChild variant="secondary" size="sm">
                  <Link href="/dashboard">Volver al Dashboard</Link>
                </Button>
              </CardFooter>
            </Card>
          </div>
        )}
      </div>
    );
  } catch (error) {
    console.error('Error al renderizar la página de proyectos:', error);

    // Determinar si es un error de autenticación
    const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
    const isAuthError = error instanceof Error &&
      (errorMessage.includes('auth') ||
       errorMessage.includes('401') ||
       errorMessage.includes('unauthorized') ||
       errorMessage.includes('Unauthorized') ||
       errorMessage.includes('token') ||
       errorMessage.includes('session'));

    return (
      <div className="space-y-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            {isAuthError
              ? "Se detectó un problema con tu sesión. Esto puede causar que los proyectos no se carguen correctamente."
              : "Ocurrió un error al cargar los proyectos. Esto puede deberse a problemas de autenticación o permisos."}
          </AlertDescription>
        </Alert>

        {isAuthError ? (
          <div className="py-6">
            <AuthReconnect />
          </div>
        ) : (
          <div className="text-center py-10">
            <p className="text-muted-foreground">No se pudieron cargar los proyectos.</p>
            <div className="flex justify-center gap-4 mt-4">
              <Button asChild>
                <a href="/dashboard">Volver al Dashboard</a>
              </Button>
              <Button variant="outline" onClick={() => window.location.reload()}>
                Reintentar
              </Button>
            </div>
          </div>
        )}
      </div>
    );
  }
}