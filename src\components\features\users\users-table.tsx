"use client"

import { useState } from "react"
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import { ArrowUpDown, ChevronDown, MoreHorizontal, Plus, Trash2 } from "lucide-react"
import Link from "next/link"
import { format } from "date-fns"
import { es } from "date-fns/locale"

import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { useToast } from "@/hooks/use-toast"
import { getBaseUrl } from "@/utils/get-base-url"

interface User {
  id: string
  email: string
  full_name?: string
  role: string[] | string
  status: string
  created_at: string
  last_sign_in_at?: string
}

// Mover la definición de columnas dentro del componente para acceder a las funciones
function createColumns(handleDeleteUser: (userId: string) => void, deletingUserId: string | null): ColumnDef<User>[] {
  return [
  {
    accessorKey: "full_name",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Nombre
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => {
      const email = row.original.email
      const fullName = row.getValue("full_name") as string

      return (
        <div className="flex items-center gap-2">
          <Avatar className="h-8 w-8">
            <AvatarFallback>
              {fullName
                ? fullName
                    .split(" ")
                    .map((n) => n[0])
                    .join("")
                    .toUpperCase()
                    .substring(0, 2)
                : email.substring(0, 2).toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div className="font-medium">
            <Link href={`/dashboard/users/${row.original.id}`} className="hover:underline">
              {fullName || "Sin nombre"}
            </Link>
            <div className="text-xs text-muted-foreground">{email}</div>
          </div>
        </div>
      )
    },
  },
  {
    accessorKey: "role",
    header: "Rol",
    cell: ({ row }) => {
      const role = row.getValue("role") as string[] | string

      const getRoleBadge = (role: string[] | string) => {
        if (Array.isArray(role)) {
          if (role.includes("admin")) {
            return <Badge className="bg-red-500">Administrador</Badge>
          } else if (role.includes("manager")) {
            return <Badge className="bg-blue-500">Gerente</Badge>
          } else if (role.includes("user")) {
            return <Badge className="bg-green-500">Usuario</Badge>
          } else {
            return <Badge>{role.join(", ")}</Badge>
          }
        } else {
          switch (role) {
            case "admin":
              return <Badge className="bg-red-500">Administrador</Badge>
            case "manager":
              return <Badge className="bg-blue-500">Gerente</Badge>
            case "user":
              return <Badge className="bg-green-500">Usuario</Badge>
            default:
              return <Badge>{role}</Badge>
          }
        }
      }

      return getRoleBadge(role)
    },
    filterFn: (row, id, value) => {
      const role = row.getValue(id) as string[] | string
      if (Array.isArray(role)) {
        return role.some(r => value.includes(r))
      }
      return value.includes(role)
    },
  },
  {
    accessorKey: "status",
    header: "Estado",
    cell: ({ row }) => {
      const status = row.getValue("status") as string

      const getStatusBadge = (status: string) => {
        switch (status) {
          case "active":
            return <Badge variant="outline" className="text-green-500 border-green-500">Activo</Badge>
          case "inactive":
            return <Badge variant="outline" className="text-yellow-500 border-yellow-500">Inactivo</Badge>
          case "suspended":
            return <Badge variant="outline" className="text-red-500 border-red-500">Suspendido</Badge>
          default:
            return <Badge variant="outline">{status}</Badge>
        }
      }

      return getStatusBadge(status)
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
  },
  {
    accessorKey: "created_at",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Creado
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => {
      return (
        <div className="text-muted-foreground">
          {format(new Date(row.getValue("created_at")), "dd/MM/yyyy", {
            locale: es,
          })}
        </div>
      )
    },
  },
  {
    accessorKey: "last_sign_in_at",
    header: "Último acceso",
    cell: ({ row }) => {
      const date = row.getValue("last_sign_in_at") as string | null
      return date ? (
        <div className="text-muted-foreground">
          {format(new Date(date), "dd/MM/yyyy", {
            locale: es,
          })}
        </div>
      ) : (
        <div className="text-muted-foreground">Nunca</div>
      )
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const user = row.original

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Abrir menú</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Acciones</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(user.id)}
            >
              Copiar ID
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              <Link href={`/dashboard/users/${user.id}`}>
                Ver detalles
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Link href={`/dashboard/users/${user.id}/edit`}>
                Editar
              </Link>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <DropdownMenuItem
                  className="text-red-600 cursor-pointer"
                  onSelect={(e) => e.preventDefault()}
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Eliminar
                </DropdownMenuItem>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>¿Estás seguro?</AlertDialogTitle>
                  <AlertDialogDescription>
                    Esta acción no se puede deshacer. Esto eliminará permanentemente el usuario
                    <strong> {user.full_name || user.email}</strong> y todos sus datos asociados.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancelar</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={() => handleDeleteUser(user.id)}
                    disabled={deletingUserId === user.id}
                    className="bg-red-600 hover:bg-red-700"
                  >
                    {deletingUserId === user.id ? "Eliminando..." : "Eliminar"}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    },
  },
  ]
}

interface UsersTableProps {
  data: User[]
}

export function UsersTable({ data }: UsersTableProps) {
  const { toast } = useToast()
  const [sorting, setSorting] = useState<SortingState>([])
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({})
  const [rowSelection, setRowSelection] = useState({})
  const [roleFilter, setRoleFilter] = useState<string[]>([])
  const [statusFilter, setStatusFilter] = useState<string[]>([])
  const [deletingUserId, setDeletingUserId] = useState<string | null>(null)

  const handleDeleteUser = async (userId: string) => {
    setDeletingUserId(userId)
    try {
      const response = await fetch(`${getBaseUrl()}/api/users/${userId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Error al eliminar el usuario')
      }

      toast({
        title: "Usuario eliminado",
        description: "El usuario se ha eliminado correctamente",
      })

      // Recargar la página para actualizar la lista
      window.location.reload()
    } catch (error: unknown) {
      console.error("Error al eliminar el usuario:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Ha ocurrido un error al eliminar el usuario",
        variant: "destructive",
      })
    } finally {
      setDeletingUserId(null)
    }
  }

  const columns = createColumns(handleDeleteUser, deletingUserId)

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  })

  // Aplicar filtro de rol
  const handleRoleFilterChange = (role: string) => {
    setRoleFilter((prev) => {
      if (prev.includes(role)) {
        return prev.filter((r) => r !== role)
      } else {
        return [...prev, role]
      }
    })
  }

  // Aplicar filtro de estado
  const handleStatusFilterChange = (status: string) => {
    setStatusFilter((prev) => {
      if (prev.includes(status)) {
        return prev.filter((s) => s !== status)
      } else {
        return [...prev, status]
      }
    })
  }

  // Actualizar los filtros de la tabla cuando cambien los filtros de rol y estado
  useState(() => {
    if (roleFilter.length > 0) {
      table.getColumn("role")?.setFilterValue(roleFilter)
    } else {
      table.getColumn("role")?.setFilterValue(undefined)
    }

    if (statusFilter.length > 0) {
      table.getColumn("status")?.setFilterValue(statusFilter)
    } else {
      table.getColumn("status")?.setFilterValue(undefined)
    }
  }, [roleFilter, statusFilter, table])

  return (
    <div className="w-full">
      <div className="flex flex-col md:flex-row items-center py-4 gap-2">
        <Input
          placeholder="Filtrar usuarios..."
          value={(table.getColumn("full_name")?.getFilterValue() as string) ?? ""}
          onChange={(event) =>
            table.getColumn("full_name")?.setFilterValue(event.target.value)
          }
          className="max-w-sm"
        />
        <div className="flex flex-wrap gap-2 ml-auto">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                Rol <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Filtrar por rol</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {["admin", "manager", "user"].map((role) => (
                <DropdownMenuCheckboxItem
                  key={role}
                  checked={roleFilter.includes(role)}
                  onCheckedChange={() => handleRoleFilterChange(role)}
                >
                  {role === "admin"
                    ? "Administrador"
                    : role === "manager"
                    ? "Gerente"
                    : "Usuario"}
                </DropdownMenuCheckboxItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                Estado <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Filtrar por estado</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {["active", "inactive", "suspended"].map((status) => (
                <DropdownMenuCheckboxItem
                  key={status}
                  checked={statusFilter.includes(status)}
                  onCheckedChange={() => handleStatusFilterChange(status)}
                >
                  {status === "active"
                    ? "Activo"
                    : status === "inactive"
                    ? "Inactivo"
                    : "Suspendido"}
                </DropdownMenuCheckboxItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                Columnas <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(!!value)
                      }
                    >
                      {column.id === "full_name"
                        ? "Nombre"
                        : column.id === "role"
                        ? "Rol"
                        : column.id === "status"
                        ? "Estado"
                        : column.id === "created_at"
                        ? "Creado"
                        : column.id === "last_sign_in_at"
                        ? "Último acceso"
                        : column.id}
                    </DropdownMenuCheckboxItem>
                  )
                })}
            </DropdownMenuContent>
          </DropdownMenu>
          <Button asChild>
            <Link href="/dashboard/users/new">
              <Plus className="mr-2 h-4 w-4" /> Nuevo Usuario
            </Link>
          </Button>
        </div>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No se encontraron resultados.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredRowModel().rows.length} usuario(s) encontrado(s).
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Anterior
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Siguiente
          </Button>
        </div>
      </div>
    </div>
  )
}
