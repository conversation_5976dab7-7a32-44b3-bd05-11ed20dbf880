import { NextResponse } from 'next/server'

export async function GET() {
  try {
    // Simulamos la obtención de modelos disponibles
    // En una implementación real, esto obtendría los modelos del servicio

    const models = [
      { id: "gemini-1.5-flash", name: "Gemini 1.5 Flash" },
      { id: "llama3-8b", name: "Llama 3 8B" },
      { id: "mistral-7b", name: "Mistral 7B" },
      { id: "deepseek-coder", name: "DeepSeek Coder" },
    ]

    return NextResponse.json({ models })
  } catch (error: unknown) {
    console.error('Error al obtener los modelos:', error)

    const errorMessage = error instanceof Error ? error.message : '<PERSON>rror desconocido';

    return NextResponse.json(
      {
        error: 'Error al obtener los modelos',
        message: errorMessage
      },
      { status: 500 }
    )
  }
}
