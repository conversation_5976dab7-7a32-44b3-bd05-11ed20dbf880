'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { Switch } from '@/components/ui/switch'
import {
  <PERSON><PERSON><PERSON>,
  Line<PERSON><PERSON>,
  Pie<PERSON>hart
} from '@/components/features/dashboard'
import {
  AlertCircle,
  AlertTriangle,
  ArrowDownIcon,
  ArrowUpIcon,
  CheckCircle2,
  Database,
  Key,
  RefreshCw,
  Save,
  Settings,
  Trash2,
  Upload
} from 'lucide-react'
import {
  rateLimiterService,
  OperationType,
  RateLimitConfig,
  UsageStats
} from '@/lib/services/rate-limiter-service'

// Componente para mostrar estadísticas de uso
const UsageStatsCard = ({ stats, type }: { stats: UsageStats, type: OperationType }) => {
  const counts = stats.counts[type]
  const errors = stats.errors[type]
  const config = rateLimiterService.getConfig(type)

  if (!config) return null

  // Calcular porcentajes de uso
  const minutePercentage = Math.min(100, (counts.lastMinute / config.operationsPerMinute) * 100)
  const hourPercentage = Math.min(100, (counts.lastHour / config.operationsPerHour) * 100)
  const dayPercentage = Math.min(100, (counts.lastDay / config.operationsPerDay) * 100)

  // Determinar el estado de alerta
  const getAlertStatus = (percentage: number) => {
    if (percentage >= 90) return 'danger'
    if (percentage >= 70) return 'warning'
    return 'normal'
  }

  const minuteStatus = getAlertStatus(minutePercentage)
  const hourStatus = getAlertStatus(hourPercentage)
  const dayStatus = getAlertStatus(dayPercentage)

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg">{type} Usage</CardTitle>
          <Badge variant={errors > 0 ? 'destructive' : 'outline'}>
            {errors > 0 ? `${errors} errors` : 'Healthy'}
          </Badge>
        </div>
        <CardDescription>
          Current usage statistics for {type} operations
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Per Minute</span>
            <span className={minuteStatus === 'danger' ? 'text-destructive font-medium' : ''}>
              {counts.lastMinute} / {config.operationsPerMinute}
            </span>
          </div>
          <Progress
            value={minutePercentage}
            className={
              minuteStatus === 'danger'
                ? 'bg-destructive/20'
                : minuteStatus === 'warning'
                  ? 'bg-warning/20'
                  : ''
            }
          />
        </div>

        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Per Hour</span>
            <span className={hourStatus === 'danger' ? 'text-destructive font-medium' : ''}>
              {counts.lastHour} / {config.operationsPerHour}
            </span>
          </div>
          <Progress
            value={hourPercentage}
            className={
              hourStatus === 'danger'
                ? 'bg-destructive/20'
                : hourStatus === 'warning'
                  ? 'bg-warning/20'
                  : ''
            }
          />
        </div>

        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Per Day</span>
            <span className={dayStatus === 'danger' ? 'text-destructive font-medium' : ''}>
              {counts.lastDay} / {config.operationsPerDay}
            </span>
          </div>
          <Progress
            value={dayPercentage}
            className={
              dayStatus === 'danger'
                ? 'bg-destructive/20'
                : dayStatus === 'warning'
                  ? 'bg-warning/20'
                  : ''
            }
          />
        </div>

        <div className="pt-2">
          <div className="flex justify-between items-center text-sm text-muted-foreground">
            <span>Total Operations</span>
            <span>{counts.total}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// Componente para configurar límites
const ConfigurationCard = ({
  type,
  config,
  onSave
}: {
  type: OperationType,
  config: RateLimitConfig,
  onSave: (type: OperationType, config: RateLimitConfig) => void
}) => {
  const [localConfig, setLocalConfig] = useState<RateLimitConfig>(config)
  const [hasChanges, setHasChanges] = useState(false)

  // Actualizar configuración local cuando cambia la configuración externa
  useEffect(() => {
    setLocalConfig(config)
    setHasChanges(false)
  }, [config])

  // Manejar cambios en los campos
  const handleChange = (field: keyof RateLimitConfig, value: number) => {
    setLocalConfig(prev => {
      const updated = { ...prev, [field]: value }
      setHasChanges(true)
      return updated
    })
  }

  // Guardar cambios
  const handleSave = () => {
    onSave(type, localConfig)
    setHasChanges(false)
  }

  // Restablecer cambios
  const handleReset = () => {
    setLocalConfig(config)
    setHasChanges(false)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">{type} Configuration</CardTitle>
        <CardDescription>
          Configure rate limits for {type} operations
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid gap-4">
          <div className="grid gap-2">
            <Label htmlFor={`${type}-per-minute`}>Operations Per Minute</Label>
            <Input
              id={`${type}-per-minute`}
              type="number"
              min="1"
              value={localConfig.operationsPerMinute}
              onChange={(e) => handleChange('operationsPerMinute', parseInt(e.target.value))}
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor={`${type}-per-hour`}>Operations Per Hour</Label>
            <Input
              id={`${type}-per-hour`}
              type="number"
              min="1"
              value={localConfig.operationsPerHour}
              onChange={(e) => handleChange('operationsPerHour', parseInt(e.target.value))}
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor={`${type}-per-day`}>Operations Per Day</Label>
            <Input
              id={`${type}-per-day`}
              type="number"
              min="1"
              value={localConfig.operationsPerDay}
              onChange={(e) => handleChange('operationsPerDay', parseInt(e.target.value))}
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor={`${type}-queue-size`}>Max Queue Size</Label>
            <Input
              id={`${type}-queue-size`}
              type="number"
              min="1"
              value={localConfig.maxQueueSize}
              onChange={(e) => handleChange('maxQueueSize', parseInt(e.target.value))}
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor={`${type}-throttle`}>Throttle (ms)</Label>
            <Input
              id={`${type}-throttle`}
              type="number"
              min="0"
              value={localConfig.throttleMs}
              onChange={(e) => handleChange('throttleMs', parseInt(e.target.value))}
            />
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={handleReset} disabled={!hasChanges}>
          Reset
        </Button>
        <Button onClick={handleSave} disabled={!hasChanges}>
          <Save className="mr-2 h-4 w-4" />
          Save Changes
        </Button>
      </CardFooter>
    </Card>
  )
}

// Componente para mostrar la cola de operaciones
const QueueStatusCard = ({ stats }: { stats: UsageStats }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Queue Status</CardTitle>
        <CardDescription>
          Current operation queue status
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <p className="text-sm font-medium leading-none">
              Queue Size
            </p>
            <p className="text-sm text-muted-foreground">
              Operations waiting to be processed
            </p>
          </div>
          <Badge variant={stats.queueSize > 20 ? 'destructive' : stats.queueSize > 5 ? 'secondary' : 'outline'}>
            {stats.queueSize} operations
          </Badge>
        </div>

        <Separator />

        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <p className="text-sm font-medium leading-none">
              Average Wait Time
            </p>
            <p className="text-sm text-muted-foreground">
              Time operations spend in queue
            </p>
          </div>
          <Badge variant="outline">
            {stats.averageWaitTime.toFixed(0)} ms
          </Badge>
        </div>
      </CardContent>
      <CardFooter>
        <Button
          variant="outline"
          className="w-full"
          onClick={() => rateLimiterService.clearQueue()}
          disabled={stats.queueSize === 0}
        >
          <Trash2 className="mr-2 h-4 w-4" />
          Clear Queue
        </Button>
      </CardFooter>
    </Card>
  )
}

// Componente principal del dashboard
export function RateLimiterDashboard() {
  const [stats, setStats] = useState<UsageStats | null>(null)
  const [activeTab, setActiveTab] = useState<string>('overview')
  const [isEnabled, setIsEnabled] = useState(true)

  // Cargar estadísticas iniciales y suscribirse a actualizaciones
  useEffect(() => {
    // Obtener estadísticas iniciales
    setStats(rateLimiterService.getUsageStats())

    // Suscribirse a actualizaciones
    const unsubscribe = rateLimiterService.addStatsListener((newStats) => {
      setStats(newStats)
    })

    // Limpiar suscripción al desmontar
    return () => {
      unsubscribe()
    }
  }, [])

  // Guardar configuración
  const handleSaveConfig = (type: OperationType, config: RateLimitConfig) => {
    rateLimiterService.updateConfig(type, config)
  }

  // Restablecer todas las configuraciones
  const handleResetAllConfigs = () => {
    rateLimiterService.resetConfig()
  }

  // Limpiar estadísticas
  const handleClearStats = () => {
    rateLimiterService.clearStats()
  }

  // Si no hay estadísticas, mostrar carga
  if (!stats) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex flex-col items-center space-y-4">
          <RefreshCw className="h-8 w-8 animate-spin text-primary" />
          <p className="text-sm text-muted-foreground">Loading rate limiter statistics...</p>
        </div>
      </div>
    )
  }

  // Preparar datos para gráficos
  const operationTypes = Object.keys(stats.counts) as OperationType[]

  const pieChartData = operationTypes.map(type => ({
    name: type,
    value: stats.counts[type].total
  }))

  const barChartData = operationTypes.map(type => ({
    name: type,
    minute: stats.counts[type].lastMinute,
    hour: stats.counts[type].lastHour,
    day: stats.counts[type].lastDay
  }))

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Rate Limiter Dashboard</h2>
          <p className="text-muted-foreground">
            Monitor and configure rate limits for Supabase operations
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Switch
            checked={isEnabled}
            onCheckedChange={setIsEnabled}
            id="rate-limiter-enabled"
          />
          <Label htmlFor="rate-limiter-enabled">Enable Rate Limiting</Label>
        </div>
      </div>

      {!isEnabled && (
        <Alert variant="warning" className="mb-6">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Rate limiting is disabled</AlertTitle>
          <AlertDescription>
            Your application is not protected against rate limits. This could lead to exceeding Supabase's free tier limits.
          </AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="configuration">Configuration</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {operationTypes.map(type => (
              <UsageStatsCard key={type} stats={stats} type={type} />
            ))}
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <QueueStatusCard stats={stats} />

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Actions</CardTitle>
                <CardDescription>
                  Manage rate limiter settings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">
                    Clear all usage statistics and reset counters
                  </p>
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={handleClearStats}
                  >
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Reset Statistics
                  </Button>
                </div>

                <Separator />

                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">
                    Reset all configuration to default values
                  </p>
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={handleResetAllConfigs}
                  >
                    <Settings className="mr-2 h-4 w-4" />
                    Reset All Configurations
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="configuration" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {operationTypes.map(type => {
              const config = rateLimiterService.getConfig(type)
              if (!config) return null
              return (
                <ConfigurationCard
                  key={type}
                  type={type}
                  config={config}
                  onSave={handleSaveConfig}
                />
              )
            })}
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Operation Distribution</CardTitle>
              <CardDescription>
                Distribution of operations by type
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <PieChart
                  title="Operation Distribution"
                  data={pieChartData}
                  dataKey="value"
                  nameKey="name"
                  colors={['#2563eb', '#16a34a', '#dc2626', '#ca8a04', '#9333ea']}
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Operation Counts</CardTitle>
              <CardDescription>
                Number of operations by time period
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <BarChart
                  title="Operation Counts"
                  data={barChartData}
                  dataKeys={['minute', 'hour', 'day']}
                  xAxisDataKey="name"
                  colors={['#2563eb', '#16a34a', '#dc2626']}
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Error Rates</CardTitle>
              <CardDescription>
                Errors by operation type
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <BarChart
                  title="Error Rates"
                  data={operationTypes.map(type => ({
                    name: type,
                    errors: stats.errors[type],
                    rate: stats.counts[type].total > 0
                      ? (stats.errors[type] / stats.counts[type].total) * 100
                      : 0
                  }))}
                  dataKeys={['errors', 'rate']}
                  xAxisDataKey="name"
                  colors={['#dc2626', '#ca8a04']}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
