'use client'

import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Loader2 } from 'lucide-react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { AlertCircle, CheckCircle2 } from 'lucide-react'

import { Suspense } from 'react'

export default function AuthCallbackPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <AuthCallbackPageContentInner />
    </Suspense>
  )
}

function AuthCallbackPageContentInner() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)
  const [success, setSuccess] = useState(false)
  const supabase = createClient()

  useEffect(() => {
    const handleCallback = async () => {
      try {
        setLoading(true)

        // Verificar si hay errores en los parámetros de URL
        const errorParam = searchParams.get('error')
        const errorDescription = searchParams.get('error_description')
        const redirectTo = searchParams.get('redirect_to') || localStorage.getItem('auth_redirect_after_login') || '/dashboard'

        if (errorParam) {
          console.error('Error en callback OAuth:', errorParam, errorDescription)
          setError(errorDescription || `Error de autenticación: ${errorParam}`)
          return
        }

        // Intentar procesar el código de autenticación en la URL
        console.log('Procesando callback de autenticación...')

        // Primero intentar intercambiar el código por una sesión
        try {
          const { data: exchangeData, error: exchangeError } = await supabase.auth.exchangeCodeForSession(
            window.location.href
          )

          if (exchangeError) {
            console.warn('Error al intercambiar código por sesión:', exchangeError.message)
            // Continuar con el siguiente método si este falla
          } else if (exchangeData?.session) {
            console.log('Código intercambiado por sesión correctamente')
            setSuccess(true)

            // Guardar timestamp de inicio de sesión
            localStorage.setItem('auth_signin_time', Date.now().toString())

            // Limpiar la URL de redirección almacenada
            localStorage.removeItem('auth_redirect_after_login')

            // Redirigir al destino después de un breve retraso
            setTimeout(() => {
              router.push(redirectTo)
            }, 1500)
            return
          }
        } catch (exchangeErr) {
          console.warn('Excepción al intercambiar código:', exchangeErr)
          // Continuar con el siguiente método si este falla
        }

        // Si el intercambio de código falló, verificar si ya hay una sesión activa
        const { data, error } = await supabase.auth.getSession()

        if (error) {
          const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

          console.error('Error al obtener sesión:', errorMessage)
          setError(errorMessage)
          return
        }

        if (data?.session) {
          console.log('Sesión existente encontrada:', data.session.user.email)
          setSuccess(true)

          // Guardar timestamp de inicio de sesión
          localStorage.setItem('auth_signin_time', Date.now().toString())

          // Limpiar la URL de redirección almacenada
          localStorage.removeItem('auth_redirect_after_login')

          // Redirigir al destino después de un breve retraso
          setTimeout(() => {
            router.push(redirectTo)
          }, 1500)
        } else {
          console.error('No se pudo obtener una sesión válida')
          setError('No se pudo completar la autenticación. Por favor, intente iniciar sesión nuevamente.')
        }
      } catch (err: unknown) {
        console.error('Error inesperado en callback:', err)

        const errorMessage = err instanceof Error
          ? err.message
          : 'Error inesperado durante la autenticación'

        setError(errorMessage)
      } finally {
        setLoading(false)
      }
    }

    // Ejecutar el manejador de callback
    handleCallback()
  }, [router, searchParams, supabase.auth])

  return (
    <div className="container flex h-screen w-screen flex-col items-center justify-center">
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px] md:w-[450px]">
        <Card>
          <CardHeader>
            <CardTitle>Autenticación</CardTitle>
            <CardDescription>
              {loading ? 'Procesando autenticación...' :
               success ? 'Autenticación exitosa' :
               'Error de autenticación'}
            </CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col items-center justify-center p-6">
            {loading ? (
              <div className="flex flex-col items-center space-y-4">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <p>Completando el proceso de autenticación...</p>
              </div>
            ) : success ? (
              <div className="flex flex-col items-center space-y-4">
                <CheckCircle2 className="h-8 w-8 text-green-500" />
                <p>Has iniciado sesión correctamente.</p>
                <p className="text-sm text-muted-foreground">
                  Serás redirigido al dashboard en unos segundos...
                </p>
              </div>
            ) : (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>
                  {error || 'Ocurrió un error durante la autenticación'}
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
