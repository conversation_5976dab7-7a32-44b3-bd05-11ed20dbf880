{"compilerOptions": {"strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "useUnknownInCatchVariables": true, "target": "esnext", "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "noEmit": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "incremental": true, "plugins": [{"name": "next"}], "downlevelIteration": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}