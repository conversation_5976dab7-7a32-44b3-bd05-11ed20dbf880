/**
 * @file Tests for project form validation and submission
 * @description Comprehensive tests for the project creation form system
 */

import React from 'react';
import { vi } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ProjectForm } from '@/components/features/projects/project-form';
import { errorHandler, ErrorType } from '@/lib/services/error-handler-service';

// Mock dependencies
vi.mock('@/lib/supabase/client', async (importOriginal) => {
  const mod = await importOriginal<typeof import('@/lib/supabase/client')>();
  return {
    ...mod,
    createBrowserSupabaseClient: vi.fn(() => mod.createMockClient()),
    createServerSupabaseClient: vi.fn(() => mod.createMockClient()),
  }
});

vi.mock('@/hooks/use-toast', () => ({
  toast: vi.fn()
}));

vi.mock('@/lib/services/data-validator-service', () => ({
  dataValidator: {
    validateRequiredFields: vi.fn(() => ({ isValid: true, errors: {} })),
    sanitizeObject: vi.fn((data: unknown) => data),
    sanitizeTextFields: vi.fn((data: unknown) => data),
    isValidUUID: vi.fn(() => true)
  }
}));

describe('Project Form Validation', () => {
  const mockOnSubmit = vi.fn();



beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Status Field Validation', () => {
    it('should accept all valid status values', async () => {
      const user = userEvent.setup();
      
      render(<ProjectForm onSubmit={mockOnSubmit} />);
      
      // Fill required fields
      await user.type(screen.getByLabelText(/nombre/i), 'Test Project');
      
      // Test each valid status
      const validStatuses = ['pending', 'planning', 'in_progress', 'completed', 'cancelled', 'on_hold'];
      
      for (const status of validStatuses) {
        // Open status dropdown
        await user.click(screen.getByRole('combobox', { name: /estado/i }));
        
        // Select status
        const statusOption = screen.getByText(getStatusDisplayText(status));
        await user.click(statusOption);
        
        // Verify selection
        expect(screen.getByDisplayValue(status)).toBeInTheDocument();
      }
    });

    it('should show validation error for empty status', async () => {
      const user = userEvent.setup();
      
      render(<ProjectForm onSubmit={mockOnSubmit} />);
      
      // Fill name but leave status empty
      await user.type(screen.getByLabelText(/nombre/i), 'Test Project');
      
      // Try to submit
      await user.click(screen.getByRole('button', { name: /crear proyecto/i }));
      
      // Should show validation error
      await waitFor(() => {
        expect(screen.getByText(/estado.*obligatorio/i)).toBeInTheDocument();
      });
    });
  });

  describe('Required Fields Validation', () => {
    it('should validate required name field', async () => {
      const user = userEvent.setup();
      
      render(<ProjectForm onSubmit={mockOnSubmit} />);
      
      // Try to submit without name
      await user.click(screen.getByRole('button', { name: /crear proyecto/i }));
      
      await waitFor(() => {
        expect(screen.getByText(/nombre.*obligatorio/i)).toBeInTheDocument();
      });
    });

    it('should validate minimum name length', async () => {
      const user = userEvent.setup();
      
      render(<ProjectForm onSubmit={mockOnSubmit} />);
      
      // Enter short name
      await user.type(screen.getByLabelText(/nombre/i), 'AB');
      await user.click(screen.getByRole('button', { name: /crear proyecto/i }));
      
      await waitFor(() => {
        expect(screen.getByText(/al menos 3 caracteres/i)).toBeInTheDocument();
      });
    });
  });

  describe('Form Submission', () => {
    it('should submit valid form data', async () => {
      const user = userEvent.setup();
      
      render(<ProjectForm onSubmit={mockOnSubmit} />);
      
      // Fill valid form data
      await user.type(screen.getByLabelText(/nombre/i), 'Test Project');
      await user.type(screen.getByLabelText(/descripción/i), 'Test Description');
      
      // Select status
      await user.click(screen.getByRole('combobox', { name: /estado/i }));
      await user.click(screen.getByText('Pendiente'));
      
      // Submit form
      await user.click(screen.getByRole('button', { name: /crear proyecto/i }));
      
      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith(
          expect.objectContaining({
            name: 'Test Project',
            description: 'Test Description',
            status: 'pending'
          })
        );
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle constraint violation errors', () => {
      const constraintError = {
        message: 'new row for relation "projects" violates check constraint "projects_status_check"',
        code: '23514'
      };
      
      const result = errorHandler.handleError(constraintError);
      
      expect(result.type).toBe(ErrorType.VALIDATION);
      expect(result.code).toBe('23514');
      expect(result.message).toContain('estado seleccionado no es válido');
    });

    it('should handle missing required field errors', () => {
      const nullError = {
        message: 'null value in column "root_path" violates not-null constraint',
        code: '23502'
      };
      
      const result = errorHandler.handleError(nullError);
      
      expect(result.type).toBe(ErrorType.REQUIRED_FIELD);
      expect(result.code).toBe('23502');
      expect(result.message).toContain('root_path es obligatorio');
    });
  });
});

// Helper function to get display text for status
function getStatusDisplayText(status: string): string {
  const statusMap: Record<string, string> = {
    'pending': 'Pendiente',
    'planning': 'Planificación',
    'in_progress': 'En Progreso',
    'completed': 'Completado',
    'cancelled': 'Cancelado',
    'on_hold': 'En Pausa'
  };
  
  return statusMap[status] || status;
}
