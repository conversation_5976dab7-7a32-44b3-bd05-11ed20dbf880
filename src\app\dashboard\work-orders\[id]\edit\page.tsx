"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { createClient } from "@/lib/supabase/client"
import { WorkOrderForm } from "@/components/features/work-orders/work-order-form"
import { Button } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import { toast } from "@/components/shared/ui/use-toast"
import Link from "next/link"

export default function EditWorkOrderPage({ params }: { params: { id: string } }) {
  const [workOrder, setWorkOrder] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const router = useRouter()
  const supabase = createClient()

  useEffect(() => {
    const fetchWorkOrder = async () => {
      setIsLoading(true)
      try {
        const { data, error } = await supabase
          .from("work_orders")
          .select("*")
          .eq("id", params.id)
          .single()

        if (error) throw error

        // Convertir la fecha a objeto Date para el formulario
        if (data.due_date) {
          data.due_date = new Date(data.due_date)
        }

        setWorkOrder(data)
      } catch (error) {
        console.error("Error al cargar la orden de trabajo:", error)
        toast({
          title: "Error",
          description: "No se pudo cargar la orden de trabajo.",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchWorkOrder()
  }, [params.id, supabase])

  const handleSubmit = async (data: unknown) => {
    setIsSaving(true)
    try {
      // Actualizar la orden de trabajo
      const { error: updateError } = await supabase
        .from("work_orders")
        .update({
          title: data.title,
          description: data.description,
          status: data.status,
          priority: data.priority,
          project_id: data.project_id,
          assigned_to: data.assigned_to,
          due_date: data.due_date,
        })
        .eq("id", params.id)

      if (updateError) throw updateError

      // Gestionar los usuarios asignados
      if (data.assigned_users && data.assigned_users.length > 0) {
        // Primero, eliminar todas las asignaciones actuales
        const { error: deleteError } = await supabase
          .from("work_order_users")
          .delete()
          .eq("work_order_id", params.id)

        if (deleteError) {
          console.error("Error al eliminar usuarios asignados:", deleteError)
        }

        // Luego, insertar las nuevas asignaciones
        const workOrderUsersData = data.assigned_users.map((userId: string) => ({
          work_order_id: params.id,
          user_id: userId,
          role: 'assignee'
        }))

        const { error: insertError } = await supabase
          .from("work_order_users")
          .insert(workOrderUsersData)

        if (insertError) {
          console.error("Error al asignar usuarios a la orden de trabajo:", insertError)
        }
      } else {
        // Si no hay usuarios asignados, eliminar todas las asignaciones
        const { error: deleteError } = await supabase
          .from("work_order_users")
          .delete()
          .eq("work_order_id", params.id)

        if (deleteError) {
          console.error("Error al eliminar usuarios asignados:", deleteError)
        }
      }

      toast({
        title: "Orden de trabajo actualizada",
        description: "La orden de trabajo se ha actualizado correctamente.",
      })

      router.push(`/dashboard/work-orders/${params.id}`)
      router.refresh()
    } catch (error: unknown) {
      console.error("Error al actualizar la orden de trabajo:", error)
      const errorMessage = error instanceof Error ? error.message : "Ha ocurrido un error al actualizar la orden de trabajo.";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-48">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!workOrder) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold">Orden de trabajo no encontrada</h2>
        <p className="text-muted-foreground mt-2">
          La orden de trabajo que estás buscando no existe o ha sido eliminada.
        </p>
        <Button asChild className="mt-4">
          <Link href="/dashboard/work-orders">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Volver a órdenes de trabajo
          </Link>
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <Button variant="outline" size="sm" asChild>
          <Link href={`/dashboard/work-orders/${params.id}`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Volver a la orden
          </Link>
        </Button>
        <h2 className="text-3xl font-bold tracking-tight mt-2">Editar Orden de Trabajo</h2>
        <p className="text-muted-foreground mt-2">
          Modifica los detalles de la orden de trabajo.
        </p>
      </div>

      <div className="rounded-md border p-6">
        <WorkOrderForm
          initialData={{
            id: workOrder.id,
            title: workOrder.title,
            description: workOrder.description || "",
            status: workOrder.status,
            priority: workOrder.priority,
            project_id: workOrder.project_id,
            assigned_to: workOrder.assigned_to,
            due_date: workOrder.due_date,
          }}
          onSubmit={handleSubmit}
          isLoading={isSaving}
        />
      </div>
    </div>
  )
}
