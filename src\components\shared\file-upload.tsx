/**
 * @ai-file-description: "File upload component for document uploads"
 * @ai-related-files: ["../features/projects/ai-document-upload.tsx"]
 * @ai-owner: "File-Based Projects"
 */

"use client";

import { useState, useRef } from "react";
import { createClient } from "@/lib/supabase/client";
import { Database } from "@/lib/supabase/types"; // Importar tipos de Supabase
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Progress } from "@/components/ui/progress"; // Re-importar Progress
import { Loader2, Upload, AlertCircle } from "@/components/ui/icons";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

// Ajustar tipos locales para coincidir con el uso actual,
// puede necesitar revisión si los tipos generados por Supabase se actualizan.
type DocumentInsert = {
  filename?: string | null;
  file_path?: string | null;
  file_size?: number | null;
  file_type?: string | null;
  description?: string | null;
  file_url?: string | null; // Cambiado de public_url
  uploaded_by?: string | null;
};
type DocumentRow = {
  id: string; // Asumiendo que la fila devuelta siempre tiene un ID
  filename?: string | null;
  file_path?: string | null;
  file_size?: number | null;
  file_type?: string | null;
  description?: string | null;
  file_url?: string | null; // Cambiado de public_url
  uploaded_by?: string | null;
  upload_date?: string | null; // Añadir campos que podrían estar en la fila
};

interface FileUploadProps {
  bucket: string;
  folder?: string;
  acceptedFileTypes?: string[];
  maxFileSizeMB?: number;
  onUploadComplete: (fileData: DocumentRow) => void; // Usar el tipo DocumentRow ajustado
  onUploadStart?: () => void;
  onUploadError?: (error: string) => void;
}

/**
 * File upload component for document uploads
 *
 * @ai-responsibility: "Handles file uploads to Supabase storage"
 */
export function FileUpload({
  bucket,
  folder = "",
  acceptedFileTypes = [],
  maxFileSizeMB = 5,
  onUploadComplete,
  onUploadStart,
  onUploadError,
}: FileUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const supabase = createClient();

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file type if acceptedFileTypes is provided
    if (acceptedFileTypes.length > 0) {
      const fileExtension = `.${file.name.split('.').pop()?.toLowerCase()}`;
      if (!acceptedFileTypes.includes(fileExtension)) {
        const errorMsg = `File type not supported. Please upload one of the following: ${acceptedFileTypes.join(", ")}`;
        setError(errorMsg);
        if (onUploadError) onUploadError(errorMsg);
        return;
      }
    }

    // Check file size
    const fileSizeInMB = file.size / (1024 * 1024);
    if (fileSizeInMB > maxFileSizeMB) {
      const errorMsg = `File size exceeds the maximum allowed size of ${maxFileSizeMB}MB`;
      setError(errorMsg);
      if (onUploadError) onUploadError(errorMsg);
      return;
    }

    // Clear previous errors
    setError(null);

    // Start upload
    setIsUploading(true);
    setUploadProgress(0);
    if (onUploadStart) onUploadStart();

    try {
      // Verificar si el usuario está autenticado
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated. Please log in again.');
      }

      // Create a unique file name
      const timestamp = new Date().getTime();
      const fileExtension = file.name.split('.').pop();
      const fileName = `${timestamp}_${Math.random().toString(36).substring(2, 15)}_${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;
      const filePath = folder ? `${folder}/${fileName}` : fileName;

      // Upload file to Supabase Storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from(bucket)
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false,
        });

      if (uploadError) {
        // Manejar errores específicos
        if (uploadError.message.includes('row level security')) {
          throw new Error('Permission denied. You do not have access to upload files to this bucket.');
        }
        throw uploadError;
      }

      // Get signed URL for the uploaded file (expires in 60 seconds)
      const { data: signedUrlData, error: signedUrlError } = await supabase.storage
        .from(bucket)
        .createSignedUrl(filePath, 60); // 60 seconds expiration

      if (signedUrlError) {
        console.error('Error creating signed URL:', signedUrlError);
        throw new Error('Failed to create secure link for the uploaded file.');
      }

      if (!signedUrlData?.signedUrl) {
        const errorMsg = 'Failed to get signed URL for the uploaded file.';
        console.error(errorMsg);
        setError(errorMsg);
        if (onUploadError) onUploadError(errorMsg);
        return; // Stop the process if signed URL is not available
      }

      const documentData: DocumentInsert = {
        filename: file.name,
        file_path: filePath,
        file_size: file.size,
        file_type: file.type,
        description: `Uploaded for AI analysis`,
        file_url: signedUrlData.signedUrl, // Use the signed URL
        uploaded_by: user.id,
      };

      // Insert record in documents table
      const { data: insertedData, error: documentError } = await supabase
        .from('documents')
        .insert(documentData)
        .select()
        .single();

      if (documentError) {
        console.error('Error inserting document record:', documentError);
        setError(documentError.message || 'Failed to save document information');
        if (onUploadError) onUploadError(documentError.message || 'Failed to save document information');
        return; // Stop the process if database insertion fails
      }

      // Call the onUploadComplete callback with the document data from the database
      // Asegurarse de que insertedData no sea nulo, no sea un error, y tenga un ID antes de llamar a onUploadComplete
      if (insertedData && typeof insertedData === 'object' && 'id' in insertedData && insertedData.id) {
        onUploadComplete(insertedData as DocumentRow); // Castear a DocumentRow ajustado
      } else {
        // Manejar el caso inesperado donde la inserción fue exitosa pero no se devolvieron datos válidos
        const errorMsg = 'Failed to retrieve valid document details after insertion.';
        console.error(errorMsg, insertedData);
        setError(errorMsg);
        if (onUploadError) onUploadError(errorMsg);
      }

    } catch (error: unknown) {
      console.error('Error uploading file:', error);
      const errorMessage = error instanceof Error ? error.message : 'An error occurred while uploading the file';

      setError(errorMessage);
      if (onUploadError) onUploadError(errorMessage);
    } finally {
      setIsUploading(false);
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // Handle drag and drop events
  const [isDragging, setIsDragging] = useState(false);

  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    if (isUploading) return;

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      // Create a synthetic event to pass to handleFileChange
      const event = {
        target: {
          files: files
        }
      } as React.ChangeEvent<HTMLInputElement>;

      handleFileChange(event);
    }
  };

  return (
    <div className="space-y-4">
      <div
        className="flex flex-col gap-2"
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
      >
        <Input
          ref={fileInputRef}
          type="file"
          onChange={handleFileChange}
          disabled={isUploading}
          accept={acceptedFileTypes.join(',')}
          className="hidden"
          id="file-upload"
        />
        <Button
          type="button"
          variant="outline"
          className={`w-full h-24 flex flex-col gap-2 justify-center items-center border-dashed ${isDragging ? 'border-primary border-2' : ''}`}
          disabled={isUploading}
          onClick={() => fileInputRef.current?.click()}
        >
          {isUploading ? (
            <>
              <Loader2 className="h-6 w-6 animate-spin" />
              <span>Uploading...</span>
            </>
          ) : (
            <>
              <Upload className="h-6 w-6" />
              <span>Click to upload or drag and drop</span>
              <span className="text-xs text-muted-foreground">
                {acceptedFileTypes.length > 0
                  ? `Supported formats: ${acceptedFileTypes.join(', ')}`
                  : 'All file types supported'}
              </span>
              <span className="text-xs text-muted-foreground">
                Max size: {maxFileSizeMB}MB
              </span>
            </>
          )}
        </Button>
      </div>

      {isUploading && (
        <div className="space-y-2">
          <Progress value={uploadProgress} className="h-2" /> {/* Re-utilizar Progress */}
          <p className="text-xs text-center text-muted-foreground">
            {uploadProgress}% uploaded
          </p>
        </div>
      )}

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Upload Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
    </div>
  );
}
