import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Divider,
  Chip,
  Alert,
  AlertTitle,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Tooltip,
  Paper
} from '@mui/material';
import { 
  AttachMoney as MoneyIcon,
  Description as DocumentIcon,
  Timer as TimerIcon,
  Info as InfoIcon,
  Warning as WarningIcon,
  Check as CheckIcon
} from '@mui/icons-material';
import { supabaseClient } from '@/lib/supabase';

interface CostEstimatorProps {
  documentId: string;
  documentSize?: number;
  provider: string;
  modelName: string;
  onEstimationComplete?: (estimation: unknown) => void;
}

interface CostEstimation {
  estimatedCost: number;
  currency: string;
  modelInfo: {
    inputCostPer1M: number;
    outputCostPer1M: number;
    thinkingCostPer1M?: number;
    freeTokensPerMinute?: number;
    hasThinking: boolean;
  } | null;
  estimatedTokens: number;
  details: string;
}

const CostEstimator: React.FC<CostEstimatorProps> = ({
  documentId,
  documentSize,
  provider,
  modelName,
  onEstimationComplete
}) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [estimation, setEstimation] = useState<CostEstimation | null>(null);
  const [documentInfo, setDocumentInfo] = useState<{
    size: number;
    name: string;
    type: string;
  } | null>(null);

  useEffect(() => {
    const fetchDocumentInfo = async () => {
      if (!documentId) return;

      try {
        // Obtener información del documento
        const { data: document, error: docError } = await supabaseClient
          .from('documents')
          .select('filename, file_type, file_size')
          .eq('id', documentId)
          .single();

        if (docError) {
          throw new Error(`Error al obtener información del documento: ${docError.message}`);
        }

        if (!document) {
          throw new Error('Documento no encontrado');
        }

        setDocumentInfo({
          size: document.file_size || 0,
          name: document.filename || 'Documento sin nombre',
          type: document.file_type || 'application/octet-stream'
        });

        // Si no se proporcionó un tamaño de documento, usar el tamaño del archivo
        const size = documentSize || document.file_size || 0;

        // Estimar el costo
        const { data: estimationData, error: estimationError } = await supabaseClient.functions.invoke(
          'analyze-document',
          {
            body: {
              estimateCost: true,
              documentSize: size,
              provider,
              config: {
                modelName
              }
            }
          }
        );

        if (estimationError) {
          throw new Error(`Error al estimar costo: ${estimationError.message}`);
        }

        if (!estimationData || !estimationData.costEstimation) {
          throw new Error('No se pudo obtener la estimación de costo');
        }

        setEstimation(estimationData.costEstimation);
        
        if (onEstimationComplete) {
          onEstimationComplete(estimationData.costEstimation);
        }
      } catch (err) {
        console.error('Error en estimación de costo:', err);
        setError(err instanceof Error ? err.message : 'Error desconocido');
      } finally {
        setLoading(false);
      }
    };

    fetchDocumentInfo();
  }, [documentId, documentSize, provider, modelName, onEstimationComplete]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" p={2}>
        <CircularProgress size={24} sx={{ mr: 1 }} />
        <Typography variant="body2">Calculando estimación de costo...</Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        <AlertTitle>Error en la estimación</AlertTitle>
        {error}
      </Alert>
    );
  }

  if (!estimation) {
    return (
      <Alert severity="warning" sx={{ mt: 2 }}>
        <AlertTitle>No hay estimación disponible</AlertTitle>
        No se pudo calcular el costo para este documento.
      </Alert>
    );
  }

  // Formatear detalles para mostrar
  const detailLines = estimation.details
    .split('\n')
    .map(line => line.trim())
    .filter(line => line.length > 0);

  return (
    <Card variant="outlined" sx={{ mt: 2, mb: 2 }}>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Estimación de Costo
        </Typography>
        
        <Box display="flex" alignItems="center" mb={2}>
          <DocumentIcon sx={{ mr: 1, color: 'text.secondary' }} />
          <Typography variant="body2">
            {documentInfo?.name || 'Documento'} ({documentInfo?.type || 'Desconocido'})
          </Typography>
        </Box>
        
        <Divider sx={{ mb: 2 }} />
        
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="subtitle1">Costo estimado:</Typography>
          <Chip
            icon={<MoneyIcon />}
            label={`${estimation.estimatedCost.toFixed(4)} ${estimation.currency}`}
            color={estimation.estimatedCost > 0.1 ? "primary" : "success"}
            variant="outlined"
          />
        </Box>
        
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="subtitle1">Tokens estimados:</Typography>
          <Chip
            icon={<TimerIcon />}
            label={`${estimation.estimatedTokens.toLocaleString()}`}
            color="default"
            variant="outlined"
          />
        </Box>
        
        <Divider sx={{ mb: 2 }} />
        
        <Typography variant="subtitle2" gutterBottom>
          Detalles del cálculo:
        </Typography>
        
        <Paper variant="outlined" sx={{ p: 1, bgcolor: 'background.default' }}>
          <List dense disablePadding>
            {detailLines.map((line, index) => (
              <ListItem key={index} disablePadding sx={{ py: 0.5 }}>
                <ListItemIcon sx={{ minWidth: 30 }}>
                  {line.includes('Tokens') ? (
                    <TimerIcon fontSize="small" color="info" />
                  ) : line.includes('Costo') ? (
                    <MoneyIcon fontSize="small" color="primary" />
                  ) : line.includes('Nota') ? (
                    <InfoIcon fontSize="small" color="warning" />
                  ) : (
                    <CheckIcon fontSize="small" color="success" />
                  )}
                </ListItemIcon>
                <ListItemText
                  primary={line}
                  primaryTypographyProps={{ variant: 'body2' }}
                />
              </ListItem>
            ))}
          </List>
        </Paper>
        
        {estimation.modelInfo?.freeTokensPerMinute && (
          <Tooltip title="Los tokens gratuitos pueden reducir el costo real si no se excede el límite">
            <Alert severity="info" sx={{ mt: 2 }}>
              <AlertTitle>Tokens gratuitos disponibles</AlertTitle>
              Este modelo ofrece aproximadamente {estimation.modelInfo.freeTokensPerMinute} solicitudes gratuitas por minuto.
            </Alert>
          </Tooltip>
        )}
        
        {estimation.estimatedCost === 0 && (
          <Alert severity="success" sx={{ mt: 2 }}>
            <AlertTitle>Análisis gratuito</AlertTitle>
            El análisis de este documento no tendrá costo.
          </Alert>
        )}
        
        {estimation.estimatedCost > 1 && (
          <Alert severity="warning" sx={{ mt: 2 }}>
            <AlertTitle>Costo elevado</AlertTitle>
            El análisis de este documento tiene un costo relativamente alto. Considere usar un modelo más económico.
          </Alert>
        )}
      </CardContent>
    </Card>
  );
};

export default CostEstimator;
