/**
 * Configuración para optimización de imágenes en Next.js
 * 
 * Este archivo proporciona configuraciones optimizadas para el componente Image de Next.js
 * y funciones de utilidad para trabajar con imágenes en la aplicación.
 */

import { ImageLoaderProps } from 'next/image';

/**
 * Loader personalizado para imágenes de Supabase Storage
 * 
 * @param params Parámetros del loader de imágenes
 * @returns URL optimizada para la imagen
 */
export function supabaseImageLoader({ src, width, quality }: ImageLoaderProps): string {
  // Si la URL ya es de Supabase Storage, añadir parámetros de transformación
  if (src.includes('storage.googleapis.com') || src.includes('supabase.co/storage/v1')) {
    const url = new URL(src);
    
    // Añadir parámetros de transformación si no existen
    if (!url.searchParams.has('width')) {
      url.searchParams.set('width', width.toString());
    }
    
    if (!url.searchParams.has('quality') && quality) {
      url.searchParams.set('quality', quality.toString());
    }
    
    // Añadir formato webp si el navegador lo soporta
    if (!url.searchParams.has('format')) {
      url.searchParams.set('format', 'webp');
    }
    
    return url.toString();
  }
  
  // Para otras imágenes, devolver la URL original
  return src;
}

/**
 * Configuración para el componente Image de Next.js
 */
export const imageConfig = {
  domains: ['xdboxokpjubowptrcytl.supabase.co'],
  formats: ['image/avif', 'image/webp'],
  minimumCacheTTL: 60 * 60 * 24 * 7, // 7 días
  dangerouslyAllowSVG: false,
  contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  remotePatterns: [
    {
      protocol: 'https',
      hostname: 'xdboxokpjubowptrcytl.supabase.co',
      pathname: '/storage/v1/object/public/**',
    },
  ],
};

/**
 * Genera una URL de placeholder para imágenes
 * 
 * @param width Ancho del placeholder
 * @param height Alto del placeholder
 * @param text Texto a mostrar en el placeholder
 * @returns URL del placeholder
 */
export function generatePlaceholder(width: number = 200, height: number = 200, text: string = 'Image'): string {
  return `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 ${width} ${height}'%3E%3Crect width='${width}' height='${height}' fill='%23f0f0f0'/%3E%3Ctext x='50%25' y='50%25' dominant-baseline='middle' text-anchor='middle' font-family='system-ui, sans-serif' font-size='${Math.floor(width/10)}px' fill='%23888888'%3E${text}%3C/text%3E%3C/svg%3E`;
}
