"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { createClient } from "@/lib/supabase/client"
import { InventoryItemForm } from "@/components/features/inventory/inventory-item-form"
import { Button } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import Link from "next/link"

export default function EditInventoryItemPage({ params }: { params: { id: string } }) {
  const [item, setItem] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const router = useRouter()
  const supabase = createClient()

  useEffect(() => {
    const fetchInventoryItem = async () => {
      setIsLoading(true)
      try {
        const { data, error } = await supabase
          .from("inventory_items")
          .select("*")
          .eq("id", params.id)
          .single()

        if (error) throw error
        setItem(data)
      } catch (error) {
        console.error("Error al cargar el ítem de inventario:", error)
        toast({
          title: "Error",
          description: "No se pudo cargar el ítem de inventario.",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchInventoryItem()
  }, [params.id, supabase])

  const handleSubmit = async (data: unknown) => {
    setIsSaving(true)
    try {
      const { error } = await supabase
        .from("inventory_items")
        .update({
          name: data.name,
          description: data.description,
          quantity_available: data.quantity_available,
          unit: data.unit,
          unit_cost: data.unit_cost,
          location: data.location,
          barcode: data.barcode,
        })
        .eq("id", params.id)

      if (error) throw error

      toast({
        title: "Ítem actualizado",
        description: "El ítem de inventario se ha actualizado correctamente.",
      })

      router.push(`/dashboard/inventory/${params.id}`)
      router.refresh()
    } catch (error: unknown) {
      console.error("Error al actualizar el ítem de inventario:", error)
      const errorMessage = error instanceof Error ? error.message : "Ha ocurrido un error al actualizar el ítem de inventario.";

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-48">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!item) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold">Ítem no encontrado</h2>
        <p className="text-muted-foreground mt-2">
          El ítem de inventario que estás buscando no existe o ha sido eliminado.
        </p>
        <Button asChild className="mt-4">
          <Link href="/dashboard/inventory">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Volver a inventario
          </Link>
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <Button variant="outline" size="sm" asChild>
          <Link href={`/dashboard/inventory/${params.id}`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Volver al ítem
          </Link>
        </Button>
        <h2 className="text-3xl font-bold tracking-tight mt-2">Editar Ítem de Inventario</h2>
        <p className="text-muted-foreground mt-2">
          Modifica los detalles del ítem de inventario.
        </p>
      </div>

      <div className="rounded-md border p-6">
        <InventoryItemForm
          initialData={item}
          onSubmit={handleSubmit}
          isLoading={isSaving}
        />
      </div>
    </div>
  )
}
