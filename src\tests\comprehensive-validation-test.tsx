/**
 * @ai-file-description: "Comprehensive validation test for the enhanced project form system"
 * @ai-related-files: ["../components/features/projects/project-form.tsx", "../app/api/projects/route.ts"]
 * @ai-owner: "Projects"
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ProjectForm } from '@/components/features/projects/project-form'
import { EnhancedFormValidationAlert, createValidationError } from '@/components/features/projects/enhanced-form-validation-alert'
import { projectValidators } from '@/components/features/projects/real-time-validation'

// Mock Supabase client
vi.mock('@/lib/supabase/client', () => ({
  createClient: () => ({
    from: () => ({
      select: () => ({
        data: [],
        error: null
      })
    })
  })
}))

// Mock i18n
vi.mock('@/i18n/i18n-context', () => ({
  useI18n: () => ({
    t: (key: string) => key
  })
}))

// Mock currency detection
vi.mock('@/lib/utils/currency-detection', () => ({
  detectUserCurrency: () => Promise.resolve('CLP')
}))

describe('Comprehensive Validation System', () => {
  describe('Project Validators', () => {
    it('should validate project name correctly', () => {
      // Test empty name
      expect(projectValidators.name('')).toEqual({
        isValid: false,
        message: 'El nombre es obligatorio',
        type: 'error'
      })

      // Test short name
      expect(projectValidators.name('ab')).toEqual({
        isValid: false,
        message: 'El nombre debe tener al menos 3 caracteres',
        type: 'error'
      })

      // Test valid name
      expect(projectValidators.name('Valid Project Name')).toEqual({
        isValid: true,
        message: 'Nombre válido',
        type: 'success'
      })

      // Test very long name
      expect(projectValidators.name('a'.repeat(101))).toEqual({
        isValid: false,
        message: 'El nombre no puede exceder 100 caracteres',
        type: 'warning'
      })
    })

    it('should validate budget correctly', () => {
      // Test empty budget (should be valid)
      expect(projectValidators.budget('')).toEqual({
        isValid: true,
        message: undefined
      })

      // Test invalid budget
      expect(projectValidators.budget('not-a-number')).toEqual({
        isValid: false,
        message: 'El presupuesto debe ser un número válido',
        type: 'error'
      })

      // Test negative budget
      expect(projectValidators.budget('-100')).toEqual({
        isValid: false,
        message: 'El presupuesto no puede ser negativo',
        type: 'error'
      })

      // Test valid budget
      expect(projectValidators.budget('1000')).toEqual({
        isValid: true,
        message: 'Presupuesto válido',
        type: 'success'
      })

      // Test very high budget
      expect(projectValidators.budget('9999999999')).toEqual({
        isValid: false,
        message: 'El presupuesto es demasiado alto',
        type: 'warning'
      })
    })

    it('should validate UUID correctly', () => {
      // Test empty UUID (should be valid)
      expect(projectValidators.uuid('')).toEqual({
        isValid: true,
        message: undefined
      })

      // Test invalid UUID
      expect(projectValidators.uuid('invalid-uuid')).toEqual({
        isValid: false,
        message: 'Formato de ID inválido',
        type: 'error'
      })

      // Test valid UUID
      const validUuid = '123e4567-e89b-12d3-a456-************'
      expect(projectValidators.uuid(validUuid)).toEqual({
        isValid: true,
        message: 'ID válido',
        type: 'success'
      })
    })
  })

  describe('Enhanced Form Validation Alert', () => {
    it('should render validation errors correctly', () => {
      const errors = [
        createValidationError('Test error message', 'name', 'error', 'Test suggestion'),
        'Simple string error'
      ]

      render(
        <EnhancedFormValidationAlert
          errors={errors}
          showSuggestions={true}
        />
      )

      expect(screen.getByText('Test error message')).toBeInTheDocument()
      expect(screen.getByText('💡 Test suggestion')).toBeInTheDocument()
      expect(screen.getByText('Simple string error')).toBeInTheDocument()
    })

    it('should categorize errors by type', () => {
      const errors = [
        createValidationError('Error message', 'field1', 'error'),
        createValidationError('Warning message', 'field2', 'warning'),
        createValidationError('Info message', 'field3', 'info')
      ]

      render(
        <EnhancedFormValidationAlert
          errors={errors}
          showSuggestions={true}
        />
      )

      expect(screen.getByText('1 errores')).toBeInTheDocument()
      expect(screen.getByText('1 advertencias')).toBeInTheDocument()
    })

    it('should handle progressive error disclosure', async () => {
      const user = userEvent.setup()
      const manyErrors = Array.from({ length: 10 }, (_, i) => 
        createValidationError(`Error ${i + 1}`, `field${i + 1}`, 'error')
      )

      render(
        <EnhancedFormValidationAlert
          errors={manyErrors}
          maxVisibleErrors={3}
        />
      )

      // Should show "Ver X errores más" button
      expect(screen.getByText(/Ver \d+ errores? más/)).toBeInTheDocument()

      // Click to expand
      await user.click(screen.getByText(/Ver \d+ errores? más/))

      // Should now show "Mostrar menos" button
      expect(screen.getByText('Mostrar menos')).toBeInTheDocument()
    })
  })

  describe('Project Form Integration', () => {
    let mockOnSubmit: ReturnType<typeof vi.fn>

    beforeEach(() => {
      mockOnSubmit = vi.fn()
    })

    it('should validate required fields on submission', async () => {
      const user = userEvent.setup()

      render(
        <ProjectForm
          onSubmit={mockOnSubmit}
          isLoading={false}
        />
      )

      // Try to submit without filling required fields
      const submitButton = screen.getByRole('button', { name: /crear proyecto/i })
      await user.click(submitButton)

      // Should not call onSubmit
      expect(mockOnSubmit).not.toHaveBeenCalled()
    })

    it('should validate form fields in real-time', async () => {
      const user = userEvent.setup()

      render(
        <ProjectForm
          onSubmit={mockOnSubmit}
          isLoading={false}
        />
      )

      const nameInput = screen.getByPlaceholderText(/nombre del proyecto/i)

      // Type invalid name (too short)
      await user.type(nameInput, 'ab')
      
      await waitFor(() => {
        expect(screen.getByText(/debe tener al menos 3 caracteres/i)).toBeInTheDocument()
      })

      // Clear and type valid name
      await user.clear(nameInput)
      await user.type(nameInput, 'Valid Project Name')

      await waitFor(() => {
        expect(screen.queryByText(/debe tener al menos 3 caracteres/i)).not.toBeInTheDocument()
      })
    })

    it('should handle budget validation', async () => {
      const user = userEvent.setup()

      render(
        <ProjectForm
          onSubmit={mockOnSubmit}
          isLoading={false}
        />
      )

      const budgetInput = screen.getByPlaceholderText(/presupuesto/i)

      // Type invalid budget
      await user.type(budgetInput, 'not-a-number')

      await waitFor(() => {
        expect(screen.getByText(/debe ser un número válido/i)).toBeInTheDocument()
      })

      // Clear and type valid budget
      await user.clear(budgetInput)
      await user.type(budgetInput, '1000')

      await waitFor(() => {
        expect(screen.queryByText(/debe ser un número válido/i)).not.toBeInTheDocument()
      })
    })

    it('should submit valid form data', async () => {
      const user = userEvent.setup()

      render(
        <ProjectForm
          onSubmit={mockOnSubmit}
          isLoading={false}
        />
      )

      // Fill required fields
      await user.type(screen.getByPlaceholderText(/nombre del proyecto/i), 'Test Project')
      
      // Select status
      await user.click(screen.getByRole('combobox'))
      await user.click(screen.getByText('Pendiente'))

      // Submit form
      await user.click(screen.getByRole('button', { name: /crear proyecto/i }))

      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith(
          expect.objectContaining({
            name: 'Test Project',
            status: 'pending'
          })
        )
      })
    })
  })

  describe('API Validation Schema Consistency', () => {
    it('should have consistent status values between frontend and backend', () => {
      const frontendStatuses = ["pending", "planning", "in_progress", "completed", "cancelled", "on_hold"]
      const backendStatuses = ["pending", "planning", "in_progress", "completed", "cancelled", "on_hold"]
      
      expect(frontendStatuses).toEqual(backendStatuses)
    })

    it('should have consistent currency values', () => {
      const frontendCurrencies = ["USD", "CLP"]
      const backendCurrencies = ["USD", "CLP"]
      
      expect(frontendCurrencies).toEqual(backendCurrencies)
    })
  })
})
