{"common": {"loading": "Cargando...", "error": "Error", "success": "Éxito", "save": "Guardar", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Eliminar", "edit": "<PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "back": "Volver", "next": "Siguient<PERSON>", "previous": "Anterior", "search": "Buscar", "filter": "Filtrar", "sort": "Ordenar", "view": "<PERSON>er", "download": "<PERSON><PERSON><PERSON>", "upload": "Subir", "yes": "Sí", "no": "No", "confirm": "Confirmar", "actions": "Acciones"}, "auth": {"signIn": "In<PERSON><PERSON>", "signOut": "<PERSON><PERSON><PERSON>", "signUp": "Registrarse", "forgotPassword": "¿Olvidaste tu contraseña?", "resetPassword": "Restablecer contraseña", "email": "Correo electrónico", "password": "Contraseña", "confirmPassword": "Confirmar con<PERSON>", "name": "Nombre", "username": "Nombre de usuario"}, "projects": {"title": "Proyectos", "newProject": "Nuevo Proyecto", "projectDetails": "Detalles del Proyecto", "projectName": "Nombre del proyecto", "description": "Descripción", "status": "Estado", "startDate": "Fecha de inicio", "endDate": "Fecha de finalización", "budget": "Presupuesto", "currency": "Moneda", "client": "Cliente", "team": "Equipo", "createFromDocument": "<PERSON><PERSON><PERSON>de Documento", "uploadDocument": "Subir Documento", "documentDescription": "Sube un documento (PDF, DOCX, TXT) y deja que la IA extraiga automáticamente los detalles del proyecto.", "projectCreated": "Proyecto creado", "projectCreatedDescription": "El proyecto ha sido creado exitosamente.", "errorCreatingProject": "Ocurrió un error al crear el proyecto.", "statuses": {"pending": "Pendiente", "inProgress": "En progreso", "completed": "Completado", "cancelled": "Cancelado"}}, "workOrders": {"title": "Órdenes de Trabajo", "newWorkOrder": "Nueva Orden de Trabajo", "workOrderDetails": "Detalles de la Orden de Trabajo", "workOrderTitle": "<PERSON><PERSON><PERSON><PERSON> la orden", "description": "Descripción", "status": "Estado", "priority": "Prioridad", "assignedTo": "Asignado a", "dueDate": "<PERSON><PERSON>nc<PERSON>o", "project": "Proyecto", "materials": "Materiales", "notes": "Notas"}, "documents": {"title": "Documentos", "newDocument": "Nuevo Documento", "documentDetails": "Detalles del Documento", "documentName": "Nombre del documento", "documentType": "Tipo de documento", "uploadDate": "<PERSON><PERSON>", "fileSize": "Tamaño del archivo", "project": "Proyecto", "workOrder": "Orden de Trabajo", "category": "Categoría", "tags": "Etiquetas"}, "users": {"title": "Usuarios", "newUser": "Nuevo Usuario", "userDetails": "Detalles del Usuario", "name": "Nombre", "email": "Correo electrónico", "role": "Rol", "status": "Estado", "lastLogin": "Último inicio de sesión", "projects": "Proyectos", "workOrders": "Órdenes de Trabajo"}, "navigation": {"dashboard": "Dashboard", "projects": "Proyectos", "workOrders": "<PERSON><PERSON><PERSON>", "inventory": "Inventario", "documents": "Documentos", "synapseAI": "SynapseAI", "users": "Usuarios", "settings": "Configuración", "administration": "Administración", "documentAnalysis": "Análisis de Documentos", "rateLimiter": "Limitador de <PERSON>", "localSync": "Sincronización Local", "aiProviders": "Proveedores de IA", "sessionDiagnostic": "Diagnóstico de Sesión", "debugging": "Depuración", "changeLanguage": "Cambiar idioma"}, "inventory": {"title": "Inventario", "newItem": "Nuevo Artículo", "itemDetails": "Detalles del Artículo", "name": "Nombre", "description": "Descripción", "category": "Categoría", "quantity": "Cantidad", "unit": "Unidad", "price": "Precio", "supplier": "<PERSON><PERSON><PERSON><PERSON>", "location": "Ubicación", "lastUpdated": "Última Actualización", "lowStock": "<PERSON>", "outOfStock": "Sin Stock", "inStock": "En Stock"}, "settings": {"title": "Configuración", "general": "General", "account": "C<PERSON><PERSON>", "notifications": "Notificaciones", "appearance": "Apariencia", "language": "Idioma", "theme": "<PERSON><PERSON>", "security": "Seguridad", "privacy": "Privacidad", "integrations": "Integraciones", "billing": "Facturación", "subscription": "Suscripción", "apiKeys": "Claves API", "webhooks": "Webhooks", "advanced": "<PERSON><PERSON><PERSON>", "dangerZone": "Zona de Peligro", "deleteAccount": "Eliminar Cuenta", "exportData": "Exportar Datos"}, "notifications": {"title": "Notificaciones", "markAllAsRead": "<PERSON>ar todas como leídas", "clearAll": "<PERSON><PERSON><PERSON> to<PERSON>", "noNotifications": "No tienes notificaciones", "newProject": "Nuevo proyecto creado", "newWorkOrder": "Nueva orden de trabajo asignada", "documentUploaded": "Documento subido", "projectUpdated": "Proyecto actualizado", "workOrderCompleted": "Orden de trabajo completada", "userMention": "Te han mencionado en un comentario"}}