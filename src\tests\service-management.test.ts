import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { ServiceManagementService } from '@/lib/services/service-management-service';

// Mock de Supabase
const mockRpc = vi.fn();
const mockFrom = vi.fn(() => ({
  insert: mockInsert,
  select: mockSelect,
  update: vi.fn(),
  delete: vi.fn(),
  eq: vi.fn(),
  order: vi.fn(),
  range: vi.fn(),
  single: vi.fn(),
}));
const mockInsert = vi.fn(() => ({ select: mockSelect }));
const mockSelect = vi.fn();

vi.mock('@/lib/supabase/client', () => ({
  createClient: () => ({
    rpc: mockRpc,
    from: mockFrom,
    insert: mockInsert,
    select: mockSelect,
    update: vi.fn(),
    delete: vi.fn(),
    eq: vi.fn(),
    order: vi.fn(),
    range: vi.fn(),
    single: vi.fn(),
  }),
}));
import { createClient } from '@/lib/supabase/client';

describe('ServiceManagementService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('getPendingServiceRequests', () => {
    it('should fetch pending service requests successfully', async () => {
      const supabaseClient = createClient();
      const mockData = [
        {
          id: '123',
          title: 'Solicitud de prueba',
          status: 'pending',
          priority: 'medium',
        },
      ];
      supabaseClient.rpc.mockResolvedValueOnce({ data: mockData, error: null });

      const result = await ServiceManagementService.getPendingServiceRequests();

      expect(supabaseClient.rpc).toHaveBeenCalledWith(
        'get_pending_service_requests',
        { p_limit: 100, p_offset: 0 }
      );
      expect(result).toEqual(mockData);
    });

    it('should handle errors when fetching pending service requests', async () => {
      const supabaseClient = createClient();
      supabaseClient.rpc.mockResolvedValueOnce({ data: null, error: { message: 'Error de prueba' } });

      await expect(ServiceManagementService.getPendingServiceRequests()).rejects.toEqual({
        message: 'Error de prueba',
      });
    });
  });

  describe('getServiceRequestById', () => {
    it('should fetch a service request by ID successfully', async () => {
      const supabaseClient = createClient();
      const mockData = [
        {
          id: '123',
          title: 'Solicitud de prueba',
          status: 'pending',
          priority: 'medium',
        },
      ];
      supabaseClient.rpc.mockResolvedValueOnce({ data: mockData, error: null });

      const result = await ServiceManagementService.getServiceRequestById('123');

      expect(supabaseClient.rpc).toHaveBeenCalledWith(
        'get_service_request_details',
        { p_service_request_id: '123' }
      );
      expect(result).toEqual(mockData[0]);
    });

    it('should handle errors when fetching a service request by ID', async () => {
      const supabaseClient = createClient();
      supabaseClient.rpc.mockResolvedValueOnce({ data: null, error: { message: 'Error de prueba' } });

      await expect(ServiceManagementService.getServiceRequestById('123')).rejects.toEqual({
        message: 'Error de prueba',
      });
    });

    it('should throw an error when no service request is found', async () => {
      const supabaseClient = createClient();
      supabaseClient.rpc.mockResolvedValueOnce({ data: [], error: null });

      await expect(ServiceManagementService.getServiceRequestById('123')).rejects.toThrow(
        'No se encontró la solicitud de servicio con ID 123'
      );
    });
  });

  describe('createServiceRequest', () => {
    it('should create a service request successfully', async () => {
      const supabaseClient = createClient();
      const mockData = [
        {
          id: '123',
          title: 'Nueva solicitud',
          status: 'pending',
          priority: 'medium',
        },
      ];
      supabaseClient.from.mockReturnThis();
      supabaseClient.insert.mockReturnThis();
      supabaseClient.select.mockResolvedValueOnce({ data: mockData, error: null });

      const serviceRequest = {
        title: 'Nueva solicitud',
        status: 'pending',
        priority: 'medium',
      };
      const result = await ServiceManagementService.createServiceRequest(serviceRequest);
      expect(supabaseClient.from).toHaveBeenCalledWith('service_requests');
      expect(supabaseClient.insert).toHaveBeenCalled();
      expect(supabaseClient.select).toHaveBeenCalled();
      expect(result).toEqual(mockData[0]);
    });

    it('should handle errors when creating a service request', async () => {
      const supabaseClient = createClient();
      supabaseClient.from.mockReturnThis();
      supabaseClient.insert.mockReturnThis();
      supabaseClient.select.mockResolvedValueOnce({ data: null, error: { message: 'Error de prueba' } });

      const serviceRequest = {
        title: 'Nueva solicitud',
        status: 'pending',
        priority: 'medium',
      };
      await expect(ServiceManagementService.createServiceRequest(serviceRequest)).rejects.toEqual({
        message: 'Error de prueba',
      });
    });
  });

  describe('getServiceManagementMetrics', () => {
    it('should fetch service management metrics successfully', async () => {
      const supabaseClient = createClient();
      const mockData = {
        service_requests_count: 10,
        pending_service_requests_count: 5,
        customer_equipment_count: 20,
        maintenance_schedules_count: 15,
        overdue_maintenance_count: 3,
        service_requests_trend: 5.2,
        maintenance_trend: -2.1,
        service_requests_by_status: [
          { name: 'pending', value: 5 },
          { name: 'completed', value: 5 },
        ],
        service_requests_by_priority: [
          { name: 'medium', value: 7 },
          { name: 'high', value: 3 },
        ],
        maintenance_by_type: [
          { name: 'preventive', value: 10 },
          { name: 'corrective', value: 5 },
        ],
        equipment_by_status: [
          { name: 'active', value: 15 },
          { name: 'inactive', value: 5 },
        ],
        recent_activities: [
          {
            id: '123',
            description: 'Actividad de prueba',
            activity_type: 'maintenance',
            status: 'completed',
            created_at: '2023-01-01T00:00:00Z',
            equipment_name: 'Equipo de prueba',
            client_name: 'Cliente de prueba',
          },
        ],
      };
      supabaseClient.rpc.mockResolvedValueOnce({ data: mockData, error: null });

      const result = await ServiceManagementService.getServiceManagementMetrics();
      expect(supabaseClient.rpc).toHaveBeenCalledWith('get_service_management_metrics');
      expect(result).toEqual(mockData);
    });

    it('should handle errors when fetching service management metrics', async () => {
      const supabaseClient = createClient();
      supabaseClient.rpc.mockResolvedValueOnce({ data: null, error: { message: 'Error de prueba' } });

      await expect(ServiceManagementService.getServiceManagementMetrics()).rejects.toEqual({
        message: 'Error de prueba',
      });
    });
  });
});
