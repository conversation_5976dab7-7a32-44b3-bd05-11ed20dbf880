# Google Gemini Integration

## Overview

This document provides detailed information about integrating Google's Gemini AI models for document processing in the AdminCore  project management system.

## Features

- Document text extraction and analysis
- Project information identification (scope, timeline, budget, etc.)
- Support for multiple document formats
- Confidence scoring for extracted information

## Prerequisites

- Google AI Studio account
- Gemini API key
- Appropriate permissions for the Gemini API

## API Key Setup

1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Store the API key securely in environment variables
4. Never expose the API key in client-side code

## Environment Variables

```
GEMINI_API_KEY=your_gemini_api_key
GEMINI_MODEL=gemini-pro
```

## Models

| Model | Description | Best For | Token Limit |
|-------|-------------|----------|-------------|
| gemini-pro | Text-only model | General document analysis | 32,000 tokens |
| gemini-pro-vision | Text and image model | Documents with visual elements | 16,000 tokens |

## Implementation Details

### Installation

```bash
npm install @google/generative-ai
```

### Basic Usage

```typescript
import { GoogleGenerativeAI } from '@google/generative-ai';

// Initialize the API
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || '');

// For text-only input
const model = genAI.getGenerativeModel({ model: "gemini-pro" });

// For text and image input
const visionModel = genAI.getGenerativeModel({ model: "gemini-pro-vision" });
```

### Document Processing Flow

1. Extract text from document (using appropriate library based on document type)
2. Send text to Gemini API with specific prompts
3. Process and structure the response
4. Map the structured data to project fields

### Example Prompt for Project Information Extraction

```typescript
const prompt = `
Extract the following information from this project document:
1. Project name
2. Project description
3. Start date
4. End date
5. Budget amount
6. Client name
7. Key deliverables
8. Project scope
9. Team requirements

Format the response as a JSON object with these fields. If information is not found, use null.

Document text:
${documentText}
`;

const result = await model.generateContent(prompt);
const response = await result.response;
const projectData = JSON.parse(response.text());
```

## Rate Limits and Quotas

- Free tier: Limited requests per minute
- Paid tier: Higher limits based on subscription
- [Check current limits](https://ai.google.dev/pricing)

## Error Handling

Common errors to handle:

- API key invalid or expired
- Rate limit exceeded
- Token limit exceeded
- Content policy violation
- Network errors

Example error handling:

```typescript
try {
  const result = await model.generateContent(prompt);
  // Process result
} catch (error) {
  if (error.message.includes('API key')) {
    // Handle authentication error
  } else if (error.message.includes('quota')) {
    // Handle rate limit error
  } else {
    // Handle other errors
  }
}
```

## Best Practices

1. **Optimize token usage**
   - Only include relevant parts of documents
   - Use concise prompts
   - Consider chunking large documents

2. **Improve accuracy**
   - Provide clear instructions in prompts
   - Include examples for complex extractions
   - Use structured output formats (JSON)

3. **Handle failures gracefully**
   - Implement retry logic with backoff
   - Provide meaningful error messages to users
   - Consider fallback to other providers

4. **Monitor usage**
   - Track API calls and costs
   - Set up alerts for unusual usage patterns
   - Implement rate limiting on your side

## Limitations

- Maximum token limits (32K for gemini-pro)
- May struggle with highly technical or domain-specific content
- Limited support for non-English languages compared to English
- Cannot process complex tables or charts directly

## Resources

- [Google Generative AI Documentation](https://ai.google.dev/docs)
- [Gemini API Reference](https://ai.google.dev/api/rest/v1beta/models)
- [Prompt Design Guide](https://ai.google.dev/docs/prompting)
- [Pricing Information](https://ai.google.dev/pricing)
