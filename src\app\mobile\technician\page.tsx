'use client'

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { TechnicianDashboard } from '@/components/mobile/technician-dashboard';
import { useToast } from '@/components/ui/use-toast';
import { createClient } from '@/lib/supabase/client';
import { Loader2, LogOut, Settings } from 'lucide-react';

export default function TechnicianMobilePage() {
  const [loading, setLoading] = useState(true);
  const [userId, setUserId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { toast } = useToast();

  useEffect(() => {
    const checkAuth = async () => {
      try {
        setLoading(true);
        const supabase = createClient();
        // No pasar parámetros a getUser() para compatibilidad con todas las versiones de Supabase
        const { data: { user } } = await supabase.auth.getUser();

        if (!user) {
          router.push('/login?redirect=/mobile/technician');
          return;
        }

        // Verificar si el usuario es un técnico
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('role')
          .eq('id', user.id)
          .single();

        if (profileError) {
          throw profileError;
        }

        if (profile?.role !== 'technician' && profile?.role !== 'admin') {
          setError('Acceso denegado. Solo los técnicos pueden acceder a esta página.');
          return;
        }

        setUserId(user.id);
      } catch (err) {
        console.error('Error al verificar autenticación:', err);
        setError('Error al verificar autenticación. Intente nuevamente.');
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, [router]);

  const handleLogout = async () => {
    try {
      const supabase = createClient();
      await supabase.auth.signOut();
      router.push('/login');
    } catch (err) {
      console.error('Error al cerrar sesión:', err);
      toast({
        title: 'Error',
        description: 'No se pudo cerrar sesión. Intente nuevamente.',
        variant: 'destructive',
      });
    }
  };

  const handleSettings = () => {
    router.push('/mobile/technician/settings');
  };

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="mt-4 text-muted-foreground">Cargando...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="bg-destructive/10 p-4 rounded-md text-destructive">
              {error}
            </div>
            <div className="mt-4 flex justify-between">
              <Button
                variant="outline"
                onClick={() => router.push('/login')}
              >
                Iniciar sesión
              </Button>
              <Button
                variant="default"
                onClick={() => router.push('/dashboard')}
              >
                Ir al dashboard
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen">
      {/* Encabezado */}
      <header className="sticky top-0 z-10 bg-background border-b p-4">
        <div className="flex justify-between items-center">
          <h1 className="text-xl font-bold">Técnico</h1>
          <div className="flex gap-2">
            <Button variant="outline" size="icon" onClick={handleSettings}>
              <Settings className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon" onClick={handleLogout}>
              <LogOut className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </header>

      {/* Contenido principal */}
      <main className="flex-1 p-4 pb-20">
        {userId && <TechnicianDashboard technicianId={userId} />}
      </main>

      {/* Barra de navegación inferior */}
      <nav className="fixed bottom-0 left-0 right-0 bg-background border-t p-2 flex justify-around">
        <Button
          variant="ghost"
          className="flex-1 flex flex-col items-center py-2"
          onClick={() => router.push('/mobile/technician')}
        >
          <span className="text-xs">Inicio</span>
        </Button>
        <Button
          variant="ghost"
          className="flex-1 flex flex-col items-center py-2"
          onClick={() => router.push('/mobile/technician/activities')}
        >
          <span className="text-xs">Actividades</span>
        </Button>
        <Button
          variant="ghost"
          className="flex-1 flex flex-col items-center py-2"
          onClick={() => router.push('/mobile/technician/equipment')}
        >
          <span className="text-xs">Equipos</span>
        </Button>
        <Button
          variant="ghost"
          className="flex-1 flex flex-col items-center py-2"
          onClick={() => router.push('/mobile/technician/profile')}
        >
          <span className="text-xs">Perfil</span>
        </Button>
      </nav>
    </div>
  );
}
