"use client"

import { useState, useEffect } from 'react'
import { createBrowserSupabaseClient } from '@/lib/supabase/client'

type Session = {
  user: {
    id: string
    email: string
    user_metadata?: {
      name?: string
      avatar_url?: string
    }
  }
}

export function useSupabaseSession() {
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const supabase = createBrowserSupabaseClient()
    
    // NOTE: In tests, supabase.auth.onAuthStateChange must be mocked to avoid TypeError
    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session as Session | null)
      setLoading(false)
    })

    // Cargar la sesión inicial
    const getInitialSession = async () => {
      try {
        const { data: { session: initialSession } } = await supabase.auth.getSession()
        setSession(initialSession as Session | null)
      } catch (error) {
        console.error('Error al obtener la sesión:', error)
      } finally {
        setLoading(false)
      }
    }
    
    getInitialSession()

    return () => {
      subscription?.unsubscribe()
    }
  }, [])

  return { session, loading }
}
