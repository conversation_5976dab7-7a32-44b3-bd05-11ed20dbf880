#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Upgrading to Next.js 15+ and fixing Vercel deployment...\n');

// Function to run commands safely
function runCommand(command, description) {
  console.log(`📦 ${description}...`);
  try {
    execSync(command, { stdio: 'inherit', cwd: process.cwd() });
    console.log(`✅ ${description} completed\n`);
  } catch (error) {
    console.error(`❌ Error during ${description}:`, error.message);
    process.exit(1);
  }
}

// Function to clean cache and lock files
function cleanProject() {
  console.log('🧹 Cleaning project...');
  
  const filesToRemove = [
    'package-lock.json',
    'yarn.lock',
    'pnpm-lock.yaml',
    '.next',
    'node_modules',
    '.turbo'
  ];

  filesToRemove.forEach(file => {
    const filePath = path.join(process.cwd(), file);
    if (fs.existsSync(filePath)) {
      console.log(`   Removing ${file}...`);
      fs.rmSync(filePath, { recursive: true, force: true });
    }
  });
  
  console.log('✅ Project cleaned\n');
}

// Main upgrade process
async function upgradeProject() {
  try {
    // Step 1: Clean project
    cleanProject();

    // Step 2: Install dependencies with legacy peer deps
    runCommand(
      'npm install --legacy-peer-deps --ignore-optional',
      'Installing dependencies'
    );

    // Step 3: Audit and fix vulnerabilities
    runCommand(
      'npm audit fix --force',
      'Fixing security vulnerabilities'
    );

    // Step 4: Generate Prisma client
    runCommand(
      'npx prisma generate',
      'Generating Prisma client'
    );

    // Step 5: Type check
    runCommand(
      'npx tsc --noEmit',
      'Type checking'
    );

    // Step 6: Lint
    runCommand(
      'npm run lint',
      'Linting code'
    );

    // Step 7: Test build
    runCommand(
      'npm run build:next',
      'Testing build'
    );

    console.log('🎉 Upgrade completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('1. Test the application locally: npm run dev');
    console.log('2. Deploy to Vercel: vercel --prod');
    console.log('3. Monitor the deployment logs for any issues');

  } catch (error) {
    console.error('❌ Upgrade failed:', error.message);
    process.exit(1);
  }
}

// Run the upgrade
upgradeProject();
