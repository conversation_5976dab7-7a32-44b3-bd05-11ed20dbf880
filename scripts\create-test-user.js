// Script para crear un usuario de prueba en Supabase
// Ejecutar con: node scripts/create-test-user.js

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Obtener variables de entorno
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Error: NEXT_PUBLIC_SUPABASE_URL y SUPABASE_SERVICE_ROLE_KEY deben estar definidos en el archivo .env.local');
  process.exit(1);
}

// Crear cliente de Supabase con service role
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createTestUser() {
  try {
    console.log('Creando usuario de prueba...');
    
    // Datos del usuario de prueba
    const testUser = {
      email: '<EMAIL>',
      password: 'AdminCore123!',
      email_confirm: true,
      user_metadata: {
        full_name: 'Admin AdminCore',
        role: 'admin'
      }
    };

    // Crear el usuario
    const { data, error } = await supabase.auth.admin.createUser({
      email: testUser.email,
      password: testUser.password,
      email_confirm: testUser.email_confirm,
      user_metadata: testUser.user_metadata
    });

    if (error) {
      if (error.message.includes('already registered')) {
        console.log('✅ El usuario ya existe:', testUser.email);
        
        // Intentar obtener el usuario existente
        const { data: users, error: listError } = await supabase.auth.admin.listUsers();
        if (!listError) {
          const existingUser = users.users.find(u => u.email === testUser.email);
          if (existingUser) {
            console.log('📋 Información del usuario existente:');
            console.log('   ID:', existingUser.id);
            console.log('   Email:', existingUser.email);
            console.log('   Confirmado:', existingUser.email_confirmed_at ? 'Sí' : 'No');
            console.log('   Rol:', existingUser.user_metadata?.role || 'No definido');
          }
        }
        return;
      } else {
        throw error;
      }
    }

    console.log('✅ Usuario de prueba creado exitosamente:');
    console.log('   ID:', data.user.id);
    console.log('   Email:', data.user.email);
    console.log('   Confirmado:', data.user.email_confirmed_at ? 'Sí' : 'No');
    
    // Crear registro en la tabla users si no existe
    try {
      const { error: insertError } = await supabase
        .from('users')
        .insert({
          id: data.user.id,
          email: data.user.email,
          full_name: testUser.user_metadata.full_name,
          role: testUser.user_metadata.role,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (insertError && !insertError.message.includes('duplicate key')) {
        console.warn('⚠️  Error al crear registro en tabla users:', insertError.message);
      } else {
        console.log('✅ Registro creado en tabla users');
      }
    } catch (userTableError) {
      console.warn('⚠️  Error al acceder a tabla users:', userTableError.message);
    }

    console.log('\n🎉 ¡Usuario de prueba listo!');
    console.log('📧 Email:', testUser.email);
    console.log('🔑 Contraseña:', testUser.password);
    console.log('\nPuedes usar estas credenciales para iniciar sesión en http://localhost:3001/auth/login');

  } catch (error) {
    console.error('❌ Error al crear usuario de prueba:', error.message);
    process.exit(1);
  }
}

// Ejecutar la función
createTestUser();
