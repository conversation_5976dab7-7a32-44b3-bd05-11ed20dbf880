/**
 * @file Servicio para validación y sanitización de datos
 * @description Proporciona funciones para validar y sanitizar datos antes de enviarlos a la base de datos
 */

import { validate as validateUUID } from 'uuid';

/**
 * Servicio para validación y sanitización de datos
 */
class DataValidatorService {
  private static instance: DataValidatorService;

  /**
   * Constructor privado para implementar el patrón Singleton
   */
  private constructor() {
    // Inicialización del servicio
  }

  /**
   * Obtiene la instancia única del servicio
   */
  public static getInstance(): DataValidatorService {
    if (!DataValidatorService.instance) {
      DataValidatorService.instance = new DataValidatorService();
    }
    return DataValidatorService.instance;
  }

  /**
   * Valida si un valor es un UUID válido
   * @param value Valor a validar
   * @returns true si es un UUID válido, false en caso contrario
   */
  public isValidUUID(value: unknown): boolean {
    if (!value) return false;
    if (typeof value !== 'string') return false;
    return validateUUID(value);
  }

  /**
   * Verifica si un valor está vacío (null, undefined, string vacío, o solo espacios)
   * @param value Valor a verificar
   * @returns true si el valor está vacío, false en caso contrario
   */
  public isEmpty(value: unknown): boolean {
    if (value === null || value === undefined) return true;
    if (typeof value === 'string' && value.trim() === '') return true;
    if (Array.isArray(value) && value.length === 0) return true;
    if (typeof value === 'object' && Object.keys(value).length === 0) return true;
    return false;
  }

  /**
   * Verifica si un campo es requerido y no está vacío
   * @param value Valor a verificar
   * @param isRequired Indica si el campo es requerido
   * @returns true si el campo es válido (no requerido o no vacío), false en caso contrario
   */
  public isValidRequired(value: unknown, isRequired: boolean = true): boolean {
    if (!isRequired) return true;
    return !this.isEmpty(value);
  }

  /**
   * Sanitiza un valor UUID
   * @param value Valor a sanitizar
   * @returns null si el valor no es un UUID válido, el valor original en caso contrario
   */
  public sanitizeUUID(value: unknown): string | null {
    if (!value) return null;
    if (typeof value === 'string' && value.trim() === '') return null;

    // Verificar si es un UUID válido usando la librería uuid
    try {
      if (typeof value === 'string' && validateUUID(value)) {
        return value;
      }
    } catch (error) {
      console.error('Error validando UUID:', error);
    }

    return null;
  }

  /**
   * Sanitiza un objeto completo, procesando todos los campos que podrían ser UUID
   * @param data Objeto a sanitizar
   * @param uuidFields Lista de campos que deben ser UUID
   * @returns Objeto sanitizado
   */
  public sanitizeObject<T extends Record<string, any>>(data: T, uuidFields: string[] = []): T {
    const sanitizedData = { ...data };

    // Procesar campos específicos de UUID
    for (const field of uuidFields) {
      if (field in sanitizedData) {
        sanitizedData[field] = this.sanitizeUUID(sanitizedData[field]);
      }
    }

    // Detectar automáticamente campos que podrían ser UUID por su nombre
    for (const [key, value] of Object.entries(sanitizedData)) {
      // Si el campo termina en _id y no está en la lista de campos UUID, verificarlo
      if (
        key.endsWith('_id') &&
        !uuidFields.includes(key) &&
        typeof value === 'string'
      ) {
        sanitizedData[key] = this.sanitizeUUID(value);
      }
    }

    return sanitizedData;
  }

  /**
   * Valida un objeto completo según reglas específicas
   * @param data Objeto a validar
   * @param rules Reglas de validación
   * @returns Resultado de la validación
   */
  public validateObject<T extends Record<string, any>>(
    data: T,
    rules: Record<string, (value: unknown) => boolean>
  ): { isValid: boolean; errors: Record<string, string> } {
    const errors: Record<string, string> = {};

    for (const [field, validator] of Object.entries(rules)) {
      if (field in data && !validator(data[field])) {
        errors[field] = `El campo ${field} no es válido`;
      }
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }

  /**
   * Valida campos requeridos en un objeto
   * @param data Objeto a validar
   * @param requiredFields Lista de campos requeridos
   * @returns Resultado de la validación
   */
  public validateRequiredFields<T extends Record<string, any>>(
    data: T,
    requiredFields: string[]
  ): { isValid: boolean; errors: Record<string, string> } {
    const errors: Record<string, string> = {};

    for (const field of requiredFields) {
      if (field in data && this.isEmpty(data[field])) {
        errors[field] = `El campo ${field} es obligatorio`;
      } else if (!(field in data)) {
        errors[field] = `El campo ${field} es obligatorio pero no está presente`;
      }
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }

  /**
   * Sanitiza campos de texto en un objeto, convirtiendo strings vacíos a null
   * @param data Objeto a sanitizar
   * @param textFields Lista de campos de texto a sanitizar
   * @returns Objeto sanitizado
   */
  public sanitizeTextFields<T extends Record<string, any>>(data: T, textFields: string[] = []): T {
    const sanitizedData = { ...data };

    for (const field of textFields) {
      if (field in sanitizedData && typeof sanitizedData[field] === 'string') {
        sanitizedData[field] = sanitizedData[field].trim() === '' ? null : sanitizedData[field].trim();
      }
    }

    return sanitizedData;
  }

  /**
   * Crea reglas de validación para campos UUID
   * @param fields Lista de campos que deben ser UUID
   * @returns Reglas de validación
   */
  public createUUIDValidationRules(fields: string[]): Record<string, (value: unknown) => boolean> {
    const rules: Record<string, (value: unknown) => boolean> = {};

    for (const field of fields) {
      rules[field] = (value: unknown) => {
        // Si el valor es null o vacío, se considera válido (no obligatorio)
        if (value === null || value === undefined || value === '') return true;
        // Si no, debe ser un UUID válido
        return this.isValidUUID(value);
      };
    }

    return rules;
  }

  /**
   * Crea reglas de validación para campos requeridos
   * @param fields Lista de campos requeridos
   * @returns Reglas de validación
   */
  public createRequiredValidationRules(fields: string[]): Record<string, (value: unknown) => boolean> {
    const rules: Record<string, (value: unknown) => boolean> = {};

    for (const field of fields) {
      rules[field] = (value: unknown) => !this.isEmpty(value);
    }

    return rules;
  }
}

// Exportar instancia única
export const dataValidator = DataValidatorService.getInstance();
