import { NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function DELETE(
  request: Request,
  context: { params: Promise<{ id: string; docId: string }> }
) {
  try {
    const params = await context.params;
    const { id, docId } = params;

    // Create client directly in the route handler
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.SUPABASE_SERVICE_ROLE_KEY || ''
    )

    // First, get the document to verify ownership and get file path
    const { data: document, error: fetchError } = await supabase
      .from('documents')
      .select('id, file_path, uploaded_by, filename')
      .eq('id', docId)
      .eq('uploaded_by', id) // Ensure the document belongs to this user
      .single()

    if (fetchError || !document) {
      return NextResponse.json({
        error: 'Documento no encontrado o no pertenece a este usuario'
      }, { status: 404 })
    }

    // Delete the file from storage
    if (document.file_path) {
      const { error: storageError } = await supabase.storage
        .from('documents')
        .remove([document.file_path])

      if (storageError) {
        console.error('Error deleting file from storage:', storageError)
        // Continue with database deletion even if storage deletion fails
      }
    }

    // Delete the document record from database
    const { error: deleteError } = await supabase
      .from('documents')
      .delete()
      .eq('id', docId)
      .eq('uploaded_by', id) // Double-check ownership

    if (deleteError) {
      console.error('Error deleting document from database:', deleteError)
      return NextResponse.json({ error: 'Error al eliminar el documento' }, { status: 500 })
    }

    return NextResponse.json({
      message: 'Documento eliminado correctamente',
      filename: document.filename
    })
  } catch (error: unknown) {
    console.error('Unexpected error deleting user document:', error)
    const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
    return NextResponse.json(
      { error: 'Error del servidor', message: errorMessage },
      { status: 500 }
    )
  }
}

export async function GET(
  request: Request,
  context: { params: Promise<{ id: string; docId: string }> }
) {
  try {
    const params = await context.params;
    const { id, docId } = params;

    // Create client directly in the route handler
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.SUPABASE_SERVICE_ROLE_KEY || ''
    )

    // Get the specific document
    const { data: document, error } = await supabase
      .from('documents')
      .select(`
        id,
        filename,
        file_url,
        file_type,
        file_size,
        description,
        category,
        created_at,
        uploaded_by
      `)
      .eq('id', docId)
      .eq('uploaded_by', id) // Ensure the document belongs to this user
      .single()

    if (error || !document) {
      return NextResponse.json({
        error: 'Documento no encontrado o no pertenece a este usuario'
      }, { status: 404 })
    }

    return NextResponse.json(document)
  } catch (error: unknown) {
    console.error('Unexpected error fetching user document:', error)
    const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
    return NextResponse.json(
      { error: 'Error del servidor', message: errorMessage },
      { status: 500 }
    )
  }
}
