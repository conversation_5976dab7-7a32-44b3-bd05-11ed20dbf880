'use client'

import { useEffect, useState } from 'react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { AlertCircle, Clock } from 'lucide-react'

export function RateLimitWarning() {
  const [isRateLimited, setIsRateLimited] = useState(false)
  const [waitMinutes, setWaitMinutes] = useState(0)
  const [timeLeft, setTimeLeft] = useState('')

  useEffect(() => {
    // Verificar si hay un bloqueo por rate limit al cargar
    const checkRateLimit = () => {
      const rateLimitUntil = localStorage.getItem('auth_rate_limit_until')
      const now = Date.now()
      
      if (rateLimitUntil && parseInt(rateLimitUntil) > now) {
        const waitMs = parseInt(rateLimitUntil) - now
        const minutes = Math.floor(waitMs / 1000 / 60)
        const seconds = Math.floor((waitMs / 1000) % 60)
        
        setIsRateLimited(true)
        setWaitMinutes(minutes)
        setTimeLeft(`${minutes}:${seconds < 10 ? '0' : ''}${seconds}`)
      } else {
        setIsRateLimited(false)
        setTimeLeft('')
      }
    }

    // Verificar inicialmente
    checkRateLimit()
    
    // Actualizar cada segundo para mostrar una cuenta regresiva
    const interval = setInterval(() => {
      checkRateLimit()
    }, 1000)
    
    // Escuchar eventos de rate limit
    const handleRateLimit = (event: CustomEvent) => {
      setIsRateLimited(true)
      const waitMs = event.detail.waitUntil - Date.now()
      setWaitMinutes(Math.floor(waitMs / 1000 / 60))
    }
    
    window.addEventListener('supabase:auth:rate_limited', handleRateLimit as EventListener)
    
    return () => {
      clearInterval(interval)
      window.removeEventListener('supabase:auth:rate_limited', handleRateLimit as EventListener)
    }
  }, [])
  
  if (!isRateLimited) return null
  
  return (
    <Alert variant="destructive" className="mb-4">
      <AlertCircle className="h-4 w-4" />
      <AlertTitle className="flex items-center gap-2">
        <Clock className="h-4 w-4" /> Límite de intentos alcanzado
      </AlertTitle>
      <AlertDescription>
        Se han realizado demasiados intentos de inicio de sesión. 
        Por favor, espera {timeLeft} minutos antes de intentar nuevamente.
      </AlertDescription>
    </Alert>
  )
}
