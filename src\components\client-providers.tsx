'use client'

import { ThemeProvider } from "@/components/theme-provider"
import { Toaster } from "@/components/ui/toaster"
import { AuthProvider } from "@/components/auth-provider"
import { AuthDebug } from "@/components/debug/auth-debug"
import { SessionPersistence } from "@/components/session-persistence"
import { DashboardInitializer } from "@/components/dashboard-initializer"
import { I18nProvider } from "@/i18n/i18n-context"
import { ReactQueryProvider } from "@/lib/react-query-provider"

/**
 * Componente que agrupa todos los providers del lado del cliente
 * Esto permite mantener el layout principal como un componente de servidor
 */
export function ClientProviders({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      <AuthProvider>
        <ReactQueryProvider>
          <I18nProvider>
            <SessionPersistence />
            <DashboardInitializer />
            {children}
            <Toaster />
            <AuthDebug />
          </I18nProvider>
        </ReactQueryProvider>
      </AuthProvider>
    </ThemeProvider>
  )
}
