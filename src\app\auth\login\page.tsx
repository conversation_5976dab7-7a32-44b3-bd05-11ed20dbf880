import { Metadata } from "next"
import Image from "next/image"
import Link from "next/link"
import { LoginForm } from "@/components/features/auth/login-form"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Suspense } from 'react';

export const metadata: Metadata = {
  title: "Iniciar Sesión | AdminCore ",
  description: "Inicia sesión en tu cuenta de AdminCore ",
}

export const dynamic = 'force-dynamic';

export default function LoginPage() {
  return (
    <div className="container flex h-screen w-screen flex-col items-center justify-center">
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px] md:w-[450px]">
        <div className="flex flex-col space-y-2 text-center">
          <Image
            src="/logo.svg"
            width={80}
            height={80}
            alt="AdminCore  Logo"
            className="mx-auto"
          />
          <h1 className="text-2xl font-semibold tracking-tight">
            Iniciar sesión
          </h1>
          <p className="text-sm text-muted-foreground">
            Ingresa tus credenciales para acceder a tu cuenta
          </p>
        </div>

        <Card>
          <CardHeader className="space-y-1">
            <CardTitle className="text-xl">Bienvenido de nuevo</CardTitle>
            <CardDescription>
              Ingresa tus datos para continuar
            </CardDescription>
          </CardHeader>
          <CardContent className="p-6">
            <LoginForm />
          </CardContent>
          <CardFooter className="flex justify-center border-t px-6 py-4">
            <div className="text-sm text-muted-foreground">
              ¿Necesitas ayuda?{" "}
              <Link href="/contact" className="text-primary hover:underline">
                Contacta con soporte
              </Link>
            </div>
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}