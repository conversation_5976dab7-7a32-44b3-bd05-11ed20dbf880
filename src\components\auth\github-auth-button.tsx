'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { createClient } from '@/lib/supabase/client'
import { validateOAuthConfig } from '@/lib/supabase/oauth-config'
import { FaGithub } from 'react-icons/fa'

interface GitHubAuthButtonProps {
  mode?: 'signin' | 'signup'
  className?: string
  fullWidth?: boolean
}

export function GitHubAuthButton({
  mode = 'signin',
  className = '',
  fullWidth = false,
}: GitHubAuthButtonProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const supabase = createClient()

  const handleGitHubAuth = async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      console.log('Iniciando autenticación con GitHub...')
      
      // Verificar configuración de OAuth
      const isConfigValid = validateOAuthConfig('github')
      if (!isConfigValid) {
        console.warn('La configuración de OAuth para GitHub no es válida, pero se intentará usar la configuración de Supabase')
      }
      
      // Iniciar flujo de autenticación con GitHub
      const { data, error: authError } = await supabase.auth.signInWithOAuth({
        provider: 'github',
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
          scopes: 'read:user user:email repo',
        },
      })
      
      if (authError) {
        console.error('Error en autenticación con GitHub:', authError)
        throw authError
      }
      
      if (!data?.url) {
        console.error('No se recibió URL de autorización')
        throw new Error('No se pudo iniciar la autenticación con GitHub')
      }
      
      console.log('Autenticación con GitHub iniciada correctamente')
      console.log('URL de autorización:', data.url)
      
      // Redirigir al usuario a la URL de autorización de GitHub
      window.location.href = data.url
      
    } catch (e: unknown) {
      console.error('Error en autenticación con GitHub:', e)
      const errorMessage = e instanceof Error ? e.message : 'Error al iniciar sesión con GitHub';

      setError(errorMessage)
      setIsLoading(false)
    }
  }

  return (
    <div className="w-full space-y-2">
      <Button
        variant="outline"
        onClick={handleGitHubAuth}
        disabled={isLoading}
        className={`relative ${fullWidth ? 'w-full' : ''} ${className}`}
      >
        <FaGithub className="mr-2 h-4 w-4" />
        {mode === 'signin' ? 'Iniciar sesión con GitHub' : 'Registrarse con GitHub'}
      </Button>
      {error && (
        <p className="text-sm text-red-500">{error}</p>
      )}
    </div>
  )
}
