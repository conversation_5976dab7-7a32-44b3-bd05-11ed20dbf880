# AI Document Processing API Implementation

## Overview

This guide details the implementation of API endpoints required for AI document processing integration in the AdminCore  project management system.

## API Endpoints Structure

```
/api/
└── ai/
    ├── providers/
    │   ├── route.ts                # GET, POST providers
    │   └── [id]/
    │       └── route.ts            # GET, PUT, DELETE specific provider
    ├── analyze-document/
    │   └── route.ts                # POST document for analysis
    ├── analysis/
    │   └── [id]/
    │       └── route.ts            # GET analysis results
    └── create-project-from-analysis/
        └── route.ts                # POST create project from analysis
```

## Implementation Steps

### 1. Provider Management API

Create endpoints to manage AI providers:

```typescript
// web/src/app/api/ai/providers/route.ts
import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server-client'

// Get all available AI providers
export async function GET() {
  try {
    const supabase = createClient()

    const { data, error } = await supabase
      .from('ai_provider_configs')
      .select('id, provider_name, model_name, is_active, priority')
      .eq('is_active', true)
      .order('priority', { ascending: true })

    if (error) {
      console.error('Error fetching AI providers:', error)
      return NextResponse.json(
        { error: 'Failed to fetch AI providers' },
        { status: 500 }
      )
    }

    // Map to a more user-friendly format
    const providers = data.map(provider => ({
      id: provider.provider_name,
      name: getProviderDisplayName(provider.provider_name),
      model: provider.model_name,
      priority: provider.priority
    }))

    return NextResponse.json(providers)
  } catch (error) {
    console.error('Unexpected error fetching AI providers:', error)
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}

// Add or update an AI provider
export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { provider_name, api_key, model_name, is_active, priority } = body

    // Validate required fields
    if (!provider_name || !api_key || !model_name) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    const supabase = createClient()

    // Check if provider already exists
    const { data: existingProvider } = await supabase
      .from('ai_provider_configs')
      .select('id')
      .eq('provider_name', provider_name)
      .maybeSingle()

    let result

    if (existingProvider) {
      // Update existing provider
      result = await supabase
        .from('ai_provider_configs')
        .update({
          api_key,
          model_name,
          is_active: is_active ?? true,
          priority: priority ?? 1,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingProvider.id)
        .select('id, provider_name, model_name, is_active, priority')
    } else {
      // Create new provider
      result = await supabase
        .from('ai_provider_configs')
        .insert({
          provider_name,
          api_key,
          model_name,
          is_active: is_active ?? true,
          priority: priority ?? 1
        })
        .select('id, provider_name, model_name, is_active, priority')
    }

    if (result.error) {
      console.error('Error saving AI provider:', result.error)
      return NextResponse.json(
        { error: 'Failed to save AI provider' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      id: result.data[0].provider_name,
      name: getProviderDisplayName(result.data[0].provider_name),
      model: result.data[0].model_name,
      priority: result.data[0].priority,
      is_active: result.data[0].is_active
    })
  } catch (error) {
    console.error('Unexpected error saving AI provider:', error)
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}

// Helper function to get display name
function getProviderDisplayName(providerName: string): string {
  const displayNames: Record<string, string> = {
    'gemini': 'Google Gemini',
    'openai': 'OpenAI',
    'deepseek': 'DeepSeek'
  }

  return displayNames[providerName] || providerName
}
```

### 2. Document Analysis API

Create an endpoint to analyze documents:

```typescript
// web/src/app/api/ai/analyze-document/route.ts
import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server-client'
import { analyzeDocument } from '@/lib/ai-providers/document-analyzer'

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { documentId, provider = 'gemini' } = body

    if (!documentId) {
      return NextResponse.json(
        { error: 'Document ID is required' },
        { status: 400 }
      )
    }

    const supabase = createClient()

    // Get document details
    const { data: document, error: documentError } = await supabase
      .from('documents')
      .select('id, filename, file_path, file_url, file_type')
      .eq('id', documentId)
      .single()

    if (documentError || !document) {
      console.error('Error fetching document:', documentError)
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      )
    }

    // Create analysis record
    const { data: analysis, error: analysisError } = await supabase
      .from('ai_document_analyses')
      .insert({
        document_id: documentId,
        provider,
        status: 'pending'
      })
      .select()

    if (analysisError) {
      console.error('Error creating analysis record:', analysisError)
      return NextResponse.json(
        { error: 'Failed to create analysis record' },
        { status: 500 }
      )
    }

    // Start analysis process
    try {
      // Update status to processing
      await supabase
        .from('ai_document_analyses')
        .update({ status: 'processing' })
        .eq('id', analysis[0].id)

      // Perform document analysis
      const analysisResult = await analyzeDocument(document, provider)

      // Update analysis record with results
      const { error: updateError } = await supabase
        .from('ai_document_analyses')
        .update({
          analysis_data: analysisResult,
          status: 'completed',
          completed_at: new Date().toISOString()
        })
        .eq('id', analysis[0].id)

      if (updateError) {
        console.error('Error updating analysis record:', updateError)
        throw new Error('Failed to update analysis record')
      }

      return NextResponse.json({
        id: analysis[0].id,
        status: 'completed',
        projectData: analysisResult
      })
    } catch (analysisError) {
      console.error('Error during document analysis:', analysisError)

      // Update analysis record with error
      await supabase
        .from('ai_document_analyses')
        .update({
          status: 'failed',
          error_message: analysisError.message || 'Unknown error during analysis'
        })
        .eq('id', analysis[0].id)

      return NextResponse.json(
        { error: 'Document analysis failed', details: analysisError.message },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Unexpected error in document analysis:', error)
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}
```

### 3. Get Analysis Results API

Create an endpoint to retrieve analysis results:

```typescript
// web/src/app/api/ai/analysis/[id]/route.ts
import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server-client'

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const analysisId = params.id

    if (!analysisId) {
      return NextResponse.json(
        { error: 'Analysis ID is required' },
        { status: 400 }
      )
    }

    const supabase = createClient()

    // Get analysis details
    const { data: analysis, error } = await supabase
      .from('ai_document_analyses')
      .select(`
        id,
        document_id,
        provider,
        status,
        analysis_data,
        created_at,
        completed_at,
        error_message,
        documents:document_id(id, filename, file_url)
      `)
      .eq('id', analysisId)
      .single()

    if (error) {
      console.error('Error fetching analysis:', error)
      return NextResponse.json(
        { error: 'Analysis not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      id: analysis.id,
      documentId: analysis.document_id,
      documentName: analysis.documents?.filename,
      documentUrl: analysis.documents?.file_url,
      provider: analysis.provider,
      status: analysis.status,
      createdAt: analysis.created_at,
      completedAt: analysis.completed_at,
      projectData: analysis.analysis_data,
      errorMessage: analysis.error_message
    })
  } catch (error) {
    console.error('Unexpected error fetching analysis:', error)
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}
```

### 4. Create Project from Analysis API

Create an endpoint to generate a project from analysis results:

```typescript
// web/src/app/api/ai/create-project-from-analysis/route.ts
import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server-client'

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { analysisId, projectData } = body

    if (!analysisId && !projectData) {
      return NextResponse.json(
        { error: 'Either analysis ID or project data is required' },
        { status: 400 }
      )
    }

    const supabase = createClient()
    let data = projectData

    // If analysis ID is provided, fetch the analysis data
    if (analysisId && !projectData) {
      const { data: analysis, error } = await supabase
        .from('ai_document_analyses')
        .select('analysis_data, document_id')
        .eq('id', analysisId)
        .single()

      if (error || !analysis) {
        console.error('Error fetching analysis:', error)
        return NextResponse.json(
          { error: 'Analysis not found' },
          { status: 404 }
        )
      }

      data = analysis.analysis_data

      // If no data was extracted, return error
      if (!data) {
        return NextResponse.json(
          { error: 'No project data found in analysis' },
          { status: 400 }
        )
      }
    }

    // Get current user
    const { data: userData } = await supabase.auth.getUser()

    if (!userData?.user) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      )
    }

    // Create project from analysis data
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .insert({
        name: data.project_name || data.name || 'New Project',
        description: data.description || data.project_description || null,
        start_date: data.start_date || null,
        end_date: data.end_date || null,
        status: 'planning',
        created_by: userData.user.id,
        owner_id: userData.user.id,
        budget: data.budget ? parseFloat(data.budget) : null,
        currency: data.currency || 'CLP',
        ai_generated: true,
        source_document_id: analysisId ? data.document_id : null,
        tags: data.tags || []
      })
      .select()

    if (projectError) {
      console.error('Error creating project:', projectError)
      return NextResponse.json(
        { error: 'Failed to create project' },
        { status: 500 }
      )
    }

    // If project stages were extracted, create them
    if (data.deliverables && Array.isArray(data.deliverables)) {
      const stages = data.deliverables.map((deliverable: string, index: number) => ({
        project_id: project[0].id,
        name: deliverable,
        description: `Deliverable extracted from document analysis`,
        stage_order: index + 1,
        completed: false
      }))

      const { error: stagesError } = await supabase
        .from('project_stages')
        .insert(stages)

      if (stagesError) {
        console.error('Error creating project stages:', stagesError)
        // Continue anyway, as the project was created successfully
      }
    }

    return NextResponse.json({
      id: project[0].id,
      name: project[0].name,
      message: 'Project created successfully from AI analysis'
    })
  } catch (error) {
    console.error('Unexpected error creating project from analysis:', error)
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}
```

## AI Provider Implementation

Create a provider-agnostic document analyzer:

```typescript
// web/src/lib/ai-providers/document-analyzer.ts
import { getProviderConfig } from './provider-config'
import { extractTextFromDocument } from './document-text-extractor'
import { GeminiProvider } from './providers/gemini-provider'
import { OpenAIProvider } from './providers/openai-provider'
import { DeepSeekProvider } from './providers/deepseek-provider'

// Interface for document analysis
export interface DocumentAnalysisResult {
  project_name: string
  description: string
  start_date: string | null
  end_date: string | null
  budget: string | null
  currency: string | null
  client_name: string | null
  deliverables: string[] | null
  scope: string | null
  team_requirements: string[] | null
  tags: string[] | null
  confidence_score: number
}

// Interface for document data
export interface DocumentData {
  id: string
  filename: string
  file_path: string
  file_url: string
  file_type: string
}

/**
 * Analyzes a document using the specified AI provider
 *
 * @param document Document data object
 * @param providerName Name of the AI provider to use
 * @returns Analysis result with project information
 */
export async function analyzeDocument(
  document: DocumentData,
  providerName: string = 'gemini'
): Promise<DocumentAnalysisResult> {
  try {
    // Get provider configuration
    const providerConfig = await getProviderConfig(providerName)

    if (!providerConfig) {
      throw new Error(`Provider ${providerName} not configured`)
    }

    // Extract text from document
    const documentText = await extractTextFromDocument(document)

    if (!documentText || documentText.trim() === '') {
      throw new Error('No text could be extracted from the document')
    }

    // Create provider instance based on provider name
    const provider = createProviderInstance(providerName, providerConfig)

    // Analyze document with provider
    const analysisResult = await provider.analyzeDocument(documentText)

    return analysisResult
  } catch (error) {
    console.error(`Error analyzing document with ${providerName}:`, error)
    throw error
  }
}

/**
 * Creates an instance of the appropriate AI provider
 */
function createProviderInstance(providerName: string, config: any) {
  switch (providerName.toLowerCase()) {
    case 'gemini':
      return new GeminiProvider(config)
    case 'openai':
      return new OpenAIProvider(config)
    case 'deepseek':
      return new DeepSeekProvider(config)
    default:
      throw new Error(`Unsupported provider: ${providerName}`)
  }
}
```

## Provider Interface

Create a common interface for all AI providers:

```typescript
// web/src/lib/ai-providers/provider-interface.ts
import { DocumentAnalysisResult } from './document-analyzer'

/**
 * Interface that all AI providers must implement
 */
export interface AIProvider {
  /**
   * Analyzes document text and extracts project information
   *
   * @param documentText The text content of the document
   * @returns Structured project information
   */
  analyzeDocument(documentText: string): Promise<DocumentAnalysisResult>

  /**
   * Validates the provider configuration
   *
   * @returns True if configuration is valid, false otherwise
   */
  validateConfig(): boolean

  /**
   * Gets the name of the provider
   *
   * @returns Provider name
   */
  getProviderName(): string
}
```

## Provider Configuration

Create a utility to manage provider configurations:

```typescript
// web/src/lib/ai-providers/provider-config.ts
import { createClient } from '@/lib/supabase/server-client'

/**
 * Gets the configuration for a specific AI provider
 *
 * @param providerName Name of the provider
 * @returns Provider configuration object
 */
export async function getProviderConfig(providerName: string) {
  try {
    const supabase = createClient()

    const { data, error } = await supabase
      .from('ai_provider_configs')
      .select('id, provider_name, api_key, model_name')
      .eq('provider_name', providerName)
      .eq('is_active', true)
      .single()

    if (error || !data) {
      console.error(`Error fetching ${providerName} configuration:`, error)
      return null
    }

    return {
      apiKey: data.api_key,
      modelName: data.model_name
    }
  } catch (error) {
    console.error(`Unexpected error fetching ${providerName} configuration:`, error)
    return null
  }
}

/**
 * Gets all available AI provider configurations
 *
 * @returns Array of provider configurations
 */
export async function getAllProviderConfigs() {
  try {
    const supabase = createClient()

    const { data, error } = await supabase
      .from('ai_provider_configs')
      .select('id, provider_name, model_name, is_active, priority')
      .eq('is_active', true)
      .order('priority', { ascending: true })

    if (error) {
      console.error('Error fetching AI provider configurations:', error)
      return []
    }

    return data
  } catch (error) {
    console.error('Unexpected error fetching AI provider configurations:', error)
    return []
  }
}
```

## Document Text Extraction

Create a utility to extract text from different document types:

```typescript
// web/src/lib/ai-providers/document-text-extractor.ts
import { createClient } from '@/lib/supabase/server-client'
import { DocumentData } from './document-analyzer'

/**
 * Extracts text content from a document
 *
 * @param document Document data object
 * @returns Extracted text content
 */
export async function extractTextFromDocument(document: DocumentData): Promise<string> {
  try {
    const fileType = document.file_type || document.filename.split('.').pop()?.toLowerCase()

    // Get document content from Supabase Storage
    const supabase = createClient()
    const { data: fileData, error: fileError } = await supabase.storage
      .from('documents')
      .download(document.file_path)

    if (fileError) {
      console.error('Error downloading document:', fileError)
      throw new Error('Failed to download document')
    }

    // Extract text based on file type
    let text = ''

    switch (fileType) {
      case 'application/pdf':
      case 'pdf':
        text = await extractTextFromPDF(fileData)
        break
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
      case 'docx':
        text = await extractTextFromDOCX(fileData)
        break
      case 'text/plain':
      case 'txt':
        text = await fileData.text()
        break
      default:
        throw new Error(`Unsupported file type: ${fileType}`)
    }

    return text
  } catch (error) {
    console.error('Error extracting text from document:', error)
    throw error
  }
}

/**
 * Extracts text from a PDF file
 *
 * @param fileData PDF file data
 * @returns Extracted text
 */
async function extractTextFromPDF(fileData: Blob): Promise<string> {
  // Use pdf.js or similar library to extract text
  // This is a simplified example
  const pdfjsLib = await import('pdfjs-dist')
  const pdf = await pdfjsLib.getDocument(await fileData.arrayBuffer()).promise

  let text = ''
  for (let i = 1; i <= pdf.numPages; i++) {
    const page = await pdf.getPage(i)
    const content = await page.getTextContent()
    const pageText = content.items.map((item: any) => item.str).join(' ')
    text += pageText + '\n'
  }

  return text
}

/**
 * Extracts text from a DOCX file
 *
 * @param fileData DOCX file data
 * @returns Extracted text
 */
async function extractTextFromDOCX(fileData: Blob): Promise<string> {
  // Use mammoth.js or similar library to extract text
  // This is a simplified example
  const mammoth = await import('mammoth')
  const result = await mammoth.extractRawText({
    arrayBuffer: await fileData.arrayBuffer()
  })

  return result.value
}
```

## Testing

Create tests for the API endpoints:

```typescript
// web/src/app/api/ai/__tests__/analyze-document.test.ts
import { POST } from '../analyze-document/route'
import { createClient } from '@/lib/supabase/server-client'
import { analyzeDocument } from '@/lib/ai-providers/document-analyzer'

// Mock dependencies
jest.mock('@/lib/supabase/server-client', () => ({
  createClient: jest.fn()
}))

jest.mock('@/lib/ai-providers/document-analyzer', () => ({
  analyzeDocument: jest.fn()
}))

describe('Document Analysis API', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should return 400 if document ID is missing', async () => {
    const request = new Request('http://localhost:3000/api/ai/analyze-document', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({})
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(400)
    expect(data.error).toBe('Document ID is required')
  })

  it('should analyze a document successfully', async () => {
    // Mock Supabase responses
    const mockSupabase = {
      from: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      single: jest.fn().mockResolvedValue({
        data: {
          id: 'doc-123',
          filename: 'test.pdf',
          file_path: 'documents/test.pdf',
          file_url: 'https://example.com/test.pdf',
          file_type: 'application/pdf'
        },
        error: null
      }),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockResolvedValue({ error: null })
    }

    ;(createClient as jest.Mock).mockReturnValue(mockSupabase)

    // Mock analysis result
    const mockAnalysisResult = {
      project_name: 'Test Project',
      description: 'A test project',
      start_date: '2023-08-01',
      end_date: '2023-12-31',
      budget: '10000',
      currency: 'USD',
      confidence_score: 0.85
    }

    ;(analyzeDocument as jest.Mock).mockResolvedValue(mockAnalysisResult)

    const request = new Request('http://localhost:3000/api/ai/analyze-document', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ documentId: 'doc-123', provider: 'gemini' })
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(200)
    expect(data.status).toBe('completed')
    expect(data.projectData).toEqual(mockAnalysisResult)
  })

  // Add more tests for error cases, different providers, etc.
})
```

## Usage Example

Example of how to use the API in a component:

```typescript
// Example component using the API
async function analyzeDocumentAndCreateProject(documentId: string, provider: string) {
  try {
    // Step 1: Submit document for analysis
    const analysisResponse = await fetch('/api/ai/analyze-document', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ documentId, provider })
    })

    if (!analysisResponse.ok) {
      const error = await analysisResponse.json()
      throw new Error(error.error || 'Analysis failed')
    }

    const analysisResult = await analysisResponse.json()

    // Step 2: Create project from analysis
    const projectResponse = await fetch('/api/ai/create-project-from-analysis', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ analysisId: analysisResult.id })
    })

    if (!projectResponse.ok) {
      const error = await projectResponse.json()
      throw new Error(error.error || 'Project creation failed')
    }

    const projectResult = await projectResponse.json()

    // Step 3: Navigate to the new project
    router.push(`/dashboard/projects/${projectResult.id}`)

    return projectResult
  } catch (error) {
    console.error('Error in document analysis workflow:', error)
    toast({
      title: 'Error',
      description: error.message || 'An unexpected error occurred',
      variant: 'destructive'
    })
    return null
  }
}
```

## Considerations

1. **Security**
   - Never expose API keys in client-side code
   - Validate all inputs on the server side
   - Implement proper authentication and authorization

2. **Error Handling**
   - Provide meaningful error messages
   - Log detailed errors on the server
   - Implement retry mechanisms for transient failures

3. **Performance**
   - Consider implementing background processing for long-running operations
   - Add caching for frequently accessed data
   - Monitor API usage and performance

4. **Scalability**
   - Design for potential high volume of document processing
   - Consider implementing a queue system for processing
   - Add rate limiting to prevent abuse

5. **Monitoring**
   - Log all API calls and errors
   - Track usage metrics by provider
   - Set up alerts for unusual patterns or errors
