{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "create": "Create", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "sort": "Sort", "view": "View", "download": "Download", "upload": "Upload", "yes": "Yes", "no": "No", "confirm": "Confirm", "actions": "Actions"}, "auth": {"signIn": "Sign In", "signOut": "Sign Out", "signUp": "Sign Up", "forgotPassword": "Forgot Password?", "resetPassword": "Reset Password", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "name": "Name", "username": "Username"}, "projects": {"title": "Projects", "newProject": "New Project", "projectDetails": "Project Details", "projectName": "Project Name", "description": "Description", "status": "Status", "startDate": "Start Date", "endDate": "End Date", "budget": "Budget", "currency": "<PERSON><PERSON><PERSON><PERSON>", "client": "Client", "team": "Team", "createFromDocument": "Create from Document", "uploadDocument": "Upload Document", "documentDescription": "Upload a document (PDF, DOCX, TXT) and let AI extract project details automatically.", "projectCreated": "Project Created", "projectCreatedDescription": "The project has been created successfully.", "errorCreatingProject": "An error occurred while creating the project.", "statuses": {"pending": "Pending", "inProgress": "In Progress", "completed": "Completed", "cancelled": "Cancelled"}}, "workOrders": {"title": "Work Orders", "newWorkOrder": "New Work Order", "workOrderDetails": "Work Order Details", "workOrderTitle": "Work Order Title", "description": "Description", "status": "Status", "priority": "Priority", "assignedTo": "Assigned To", "dueDate": "Due Date", "project": "Project", "materials": "Materials", "notes": "Notes"}, "documents": {"title": "Documents", "newDocument": "New Document", "documentDetails": "Document Details", "documentName": "Document Name", "documentType": "Document Type", "uploadDate": "Upload Date", "fileSize": "File Size", "project": "Project", "workOrder": "Work Order", "category": "Category", "tags": "Tags"}, "users": {"title": "Users", "newUser": "New User", "userDetails": "User Details", "name": "Name", "email": "Email", "role": "Role", "status": "Status", "lastLogin": "Last Login", "projects": "Projects", "workOrders": "Work Orders"}, "navigation": {"dashboard": "Dashboard", "projects": "Projects", "workOrders": "Work Orders", "serviceRequests": "Service Requests", "customerEquipment": "Customer Equipment", "maintenanceSchedules": "Maintenance", "inventory": "Inventory", "documents": "Documents", "synapseAI": "SynapseAI", "users": "Users", "settings": "Settings", "administration": "Administration", "documentAnalysis": "Document Analysis", "rateLimiter": "Rate Limiter", "localSync": "Local Synchronization", "aiProviders": "AI Providers", "sessionDiagnostic": "Session Diagnostic", "debugging": "Debugging", "changeLanguage": "Change Language"}, "inventory": {"title": "Inventory", "newItem": "New Item", "itemDetails": "<PERSON><PERSON>", "name": "Name", "description": "Description", "category": "Category", "quantity": "Quantity", "unit": "Unit", "price": "Price", "supplier": "Supplier", "location": "Location", "lastUpdated": "Last Updated", "lowStock": "Low Stock", "outOfStock": "Out of Stock", "inStock": "In Stock"}, "settings": {"title": "Settings", "general": "General", "account": "Account", "notifications": "Notifications", "appearance": "Appearance", "language": "Language", "theme": "Theme", "security": "Security", "privacy": "Privacy", "integrations": "Integrations", "billing": "Billing", "subscription": "Subscription", "apiKeys": "API Keys", "webhooks": "Webhooks", "advanced": "Advanced", "dangerZone": "Danger Zone", "deleteAccount": "Delete Account", "exportData": "Export Data"}, "notifications": {"title": "Notifications", "markAllAsRead": "Mark all as read", "clearAll": "Clear all", "noNotifications": "You have no notifications", "newProject": "New project created", "newWorkOrder": "New work order assigned", "documentUploaded": "Document uploaded", "projectUpdated": "Project updated", "workOrderCompleted": "Work order completed", "userMention": "You were mentioned in a comment", "newServiceRequest": "New service request assigned", "serviceRequestUpdated": "Service request updated", "maintenanceDue": "Maintenance due"}, "serviceManagement": {"serviceRequests": {"title": "Title", "newServiceRequest": "New Service Request", "serviceRequestDetails": "Service Request Details", "description": "Description", "client": "Client", "status": "Status", "priority": "Priority", "source": "Source", "assignedTo": "Assigned To", "dueDate": "Due Date", "location": "Location", "estimatedHours": "Estimated Hours", "actualHours": "Actual Hours", "billable": "Billable", "externalReference": "External Reference", "equipment": "Equipment", "convertToWorkOrder": "Convert to Work Order", "statuses": {"pending": "Pending", "assigned": "Assigned", "inProgress": "In Progress", "onHold": "On Hold", "resolved": "Resolved", "closed": "Closed", "cancelled": "Cancelled"}, "priorities": {"low": "Low", "medium": "Medium", "high": "High", "critical": "Critical"}, "sources": {"email": "Email", "phone": "Phone", "web": "Web", "app": "Mobile App", "inPerson": "In Person", "other": "Other"}}, "customerEquipment": {"title": "Customer Equipment", "newEquipment": "New Equipment", "equipmentDetails": "Equipment Details", "name": "Name", "model": "Model", "serialNumber": "Serial Number", "client": "Client", "location": "Location", "installationDate": "Installation Date", "warrantyStartDate": "Warranty Start Date", "warrantyEndDate": "Warranty End Date", "status": "Status", "notes": "Notes", "manufacturer": "Manufacturer", "purchaseDate": "Purchase Date", "purchasePrice": "Purchase Price", "expectedLifetime": "Expected Lifetime", "lastServiceDate": "Last Service Date", "generateQRCode": "Generate QR Code", "serviceHistory": "Service History", "statuses": {"active": "Active", "inactive": "Inactive", "underRepair": "Under Repair", "decommissioned": "Decommissioned", "inStorage": "In Storage"}, "warranty": {"active": "Active", "expired": "Expired", "expiringSoon": "Expiring Soon", "unknown": "Unknown", "daysRemaining": "Days Remaining"}}, "maintenance": {"title": "Maintenance Schedules", "newSchedule": "New Maintenance Schedule", "scheduleDetails": "Schedule Details", "equipment": "Equipment", "maintenanceType": "Maintenance Type", "frequency": "Frequency", "lastMaintenanceDate": "Last Maintenance Date", "nextMaintenanceDate": "Next Maintenance Date", "description": "Description", "active": "Active", "estimatedDuration": "Estimated Duration", "checklistTemplate": "Checklist Template", "notificationDaysBefore": "Notification Days Before", "repeatCount": "Repeat Count", "generateWorkOrders": "Generate Work Orders", "completeAndScheduleNext": "Complete and Schedule Next", "types": {"preventive": "Preventive", "corrective": "Corrective", "predictive": "Predictive", "conditionBased": "Condition-Based"}, "frequencies": {"daily": "Daily", "weekly": "Weekly", "biweekly": "Biweekly", "monthly": "Monthly", "quarterly": "Quarterly", "biannual": "Biannual", "annual": "Annual", "custom": "Custom"}}, "serviceActivities": {"title": "Service Activities", "newActivity": "New Activity", "activityDetails": "Activity Details", "serviceRequest": "Service Request", "equipment": "Equipment", "activityType": "Activity Type", "description": "Description", "startTime": "Start Time", "endTime": "End Time", "technician": "Technician", "status": "Status", "notes": "Notes", "location": "Location", "travelTime": "Travel Time", "remote": "Remote", "diagnosticResults": "Diagnostic Results", "resolutionCode": "Resolution Code", "followUpRequired": "Follow-Up Required", "partsUsed": "Parts Used", "captureSignature": "Capture Signature", "types": {"diagnosis": "Diagnosis", "repair": "Repair", "installation": "Installation", "maintenance": "Maintenance", "inspection": "Inspection", "training": "Training", "other": "Other"}, "statuses": {"pending": "Pending", "inProgress": "In Progress", "completed": "Completed", "cancelled": "Cancelled"}}, "checklists": {"title": "Service Checklists", "newChecklist": "New Checklist", "checklistDetails": "Checklist Details", "name": "Name", "description": "Description", "active": "Active", "equipmentType": "Equipment Type", "maintenanceType": "Maintenance Type", "items": "Checklist Items", "responses": "Responses", "itemTypes": {"checkbox": "Checkbox", "text": "Text", "number": "Number", "photo": "Photo", "signature": "Signature", "select": "Select"}}}}