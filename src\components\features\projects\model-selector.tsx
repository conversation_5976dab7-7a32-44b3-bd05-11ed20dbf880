import React, { useState, useEffect } from 'react';
import {
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Chip,
  CircularProgress,
  Alert,
  AlertTitle,
  FormHelperText,
  Divider,
  Paper,
  Tooltip,
  IconButton
} from '@mui/material';
import {
  Bolt as SpeedIcon,
  Psychology as ThinkingIcon,
  PriceCheck as PriceIcon,
  Info as InfoIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { supabaseClient } from '@/lib/supabase';

interface ModelSelectorProps {
  provider: string;
  value: string;
  onChange: (model: string) => void;
  apiKey?: string;
  disabled?: boolean;
}

interface AIModel {
  name: string;
  description: string;
  capabilities?: string[];
  pricing?: {
    input: number;
    output: number;
    thinking?: number;
  };
  recommended?: boolean;
}

const ModelSelector: React.FC<ModelSelectorProps> = ({
  provider,
  value,
  onChange,
  apiKey,
  disabled = false
}) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [models, setModels] = useState<AIModel[]>([]);
  const [connectionStatus, setConnectionStatus] = useState<string>('');
  const [apiKeyStatus, setApiKeyStatus] = useState<string>('');

  const fetchModels = async () => {
    setLoading(true);
    setError(null);

    try {
      const { data, error: invokeError } = await supabaseClient.functions.invoke(
        'analyze-document',
        {
          body: {
            listModels: true,
            provider,
            apiKey
          }
        }
      );

      if (invokeError) {
        throw new Error(`Error al listar modelos: ${invokeError.message}`);
      }

      if (!data || !data.models) {
        throw new Error('No se recibieron datos de modelos');
      }

      setModels(data.models);
      setConnectionStatus(data.connectionStatus || '');
      setApiKeyStatus(data.apiKeyStatus || '');

      // Si hay un modelo recomendado y no hay un modelo seleccionado, seleccionar el recomendado
      if (data.recommendedModel && (!value || value === 'default-model')) {
        onChange(data.recommendedModel);
      }
    } catch (err) {
      console.error('Error al obtener modelos:', err);
      setError(err instanceof Error ? err.message : 'Error desconocido');

      // Cargar modelos por defecto según el proveedor
      const defaultModels = getDefaultModels(provider);
      setModels(defaultModels);

      // Seleccionar el primer modelo por defecto si no hay uno seleccionado
      if ((!value || value === 'default-model') && defaultModels.length > 0) {
        onChange(defaultModels[0].name);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchModels();
  }, [fetchModels]);

  const getDefaultModels = (provider: string): AIModel[] => {
    switch (provider) {
      case 'gemini':
        return [
          {
            name: 'gemini-2.5-flash-preview-04-17',
            description: 'Gemini 2.5 Flash (recomendado)',
            capabilities: ['Análisis de documentos', 'Thinking', 'Rápido'],
            pricing: { input: 0.15, output: 0.60, thinking: 3.50 },
            recommended: true
          },
          {
            name: 'gemini-2.5-pro-preview-03-25',
            description: 'Gemini 2.5 Pro',
            capabilities: ['Análisis avanzado', 'Thinking', 'Alta precisión'],
            pricing: { input: 0.25, output: 0.75, thinking: 3.50 }
          },
          {
            name: 'gemini-1.5-flash',
            description: 'Gemini 1.5 Flash',
            capabilities: ['Análisis básico', 'Rápido', 'Económico'],
            pricing: { input: 0.10, output: 0.35 }
          }
        ];
      case 'openai':
        return [
          {
            name: 'gpt-4o',
            description: 'GPT-4o (recomendado)',
            capabilities: ['Análisis de documentos', 'Alta precisión'],
            pricing: { input: 5.00, output: 15.00 },
            recommended: true
          },
          {
            name: 'gpt-4-turbo',
            description: 'GPT-4 Turbo',
            capabilities: ['Análisis avanzado', 'Alta precisión'],
            pricing: { input: 10.00, output: 30.00 }
          },
          {
            name: 'gpt-3.5-turbo',
            description: 'GPT-3.5 Turbo',
            capabilities: ['Análisis básico', 'Económico'],
            pricing: { input: 0.50, output: 1.50 }
          }
        ];
      default:
        return [];
    }
  };

  const getModelChip = (model: AIModel) => {
    if (model.recommended) {
      return <Chip size="small" label="Recomendado" color="success" />;
    }

    if (model.name.includes('flash')) {
      return <Chip size="small" label="Rápido" color="primary" icon={<SpeedIcon />} />;
    }

    if (model.name.includes('pro')) {
      return <Chip size="small" label="Preciso" color="secondary" />;
    }

    return null;
  };

  const getProviderName = (provider: string): string => {
    switch (provider) {
      case 'gemini':
        return 'Google Gemini';
      case 'openai':
        return 'OpenAI';
      case 'deepseek':
        return 'DeepSeek';
      default:
        return provider.charAt(0).toUpperCase() + provider.slice(1);
    }
  };

  return (
    <Box>
      <Typography variant="subtitle1" gutterBottom>
        Modelo de {getProviderName(provider)}
        <Tooltip title="Actualizar lista de modelos">
          <IconButton
            size="small"
            onClick={fetchModels}
            disabled={loading}
            sx={{ ml: 1 }}
          >
            <RefreshIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </Typography>

      {loading ? (
        <Box display="flex" alignItems="center" my={2}>
          <CircularProgress size={20} sx={{ mr: 1 }} />
          <Typography variant="body2">Cargando modelos disponibles...</Typography>
        </Box>
      ) : error ? (
        <Alert severity="error" sx={{ mb: 2 }}>
          <AlertTitle>Error al cargar modelos</AlertTitle>
          {error}
        </Alert>
      ) : (
        <>
          {connectionStatus && (
            <Alert
              severity={connectionStatus.includes('Error') ? 'error' : 'info'}
              sx={{ mb: 2 }}
            >
              {connectionStatus}
            </Alert>
          )}

          {apiKeyStatus && (
            <Alert
              severity={apiKeyStatus.includes('Inválida') ? 'warning' : 'success'}
              sx={{ mb: 2 }}
            >
              {apiKeyStatus}
            </Alert>
          )}

          <FormControl fullWidth variant="outlined" disabled={disabled}>
            <InputLabel id="model-select-label">Modelo</InputLabel>
            <Select
              labelId="model-select-label"
              id="model-select"
              value={value || ''}
              onChange={(e) => onChange(e.target.value)}
              label="Modelo"
            >
              {models.map((model) => (
                <MenuItem key={model.name} value={model.name}>
                  <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
                    <Box display="flex" justifyContent="space-between" alignItems="center">
                      <Typography variant="body2">{model.description}</Typography>
                      {getModelChip(model)}
                    </Box>

                    {model.pricing && (
                      <Box display="flex" mt={0.5}>
                        <Tooltip title="Costo por millón de tokens de entrada">
                          <Typography variant="caption" color="text.secondary" sx={{ mr: 2 }}>
                            <PriceIcon fontSize="inherit" sx={{ verticalAlign: 'text-bottom', mr: 0.5 }} />
                            Entrada: ${model.pricing.input.toFixed(2)}/1M
                          </Typography>
                        </Tooltip>

                        <Tooltip title="Costo por millón de tokens de salida">
                          <Typography variant="caption" color="text.secondary">
                            <PriceIcon fontSize="inherit" sx={{ verticalAlign: 'text-bottom', mr: 0.5 }} />
                            Salida: ${model.pricing.output.toFixed(2)}/1M
                          </Typography>
                        </Tooltip>

                        {model.pricing.thinking && (
                          <Tooltip title="Costo por millón de tokens de thinking">
                            <Typography variant="caption" color="text.secondary" sx={{ ml: 2 }}>
                              <ThinkingIcon fontSize="inherit" sx={{ verticalAlign: 'text-bottom', mr: 0.5 }} />
                              Thinking: ${model.pricing.thinking.toFixed(2)}/1M
                            </Typography>
                          </Tooltip>
                        )}
                      </Box>
                    )}
                  </Box>
                </MenuItem>
              ))}
            </Select>
            <FormHelperText>
              Seleccione el modelo de IA para analizar el documento
            </FormHelperText>
          </FormControl>

          {models.length === 0 && (
            <Alert severity="warning" sx={{ mt: 2 }}>
              No hay modelos disponibles para este proveedor
            </Alert>
          )}
        </>
      )}
    </Box>
  );
};

export default ModelSelector;
