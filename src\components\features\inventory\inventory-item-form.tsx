"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Loader2 } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { createClient } from "@/lib/supabase/client"
import { dataValidator } from "@/lib/services/data-validator-service"
import { errorHandler } from "@/lib/services/error-handler-service"
import { FormValidationAlert } from "@/components/ui/form-validation-alert"

// Esquema de validación para el formulario
const inventoryItemFormSchema = z.object({
  name: z.string({
    required_error: "El nombre es obligatorio",
  }).min(3, {
    message: "El nombre debe tener al menos 3 caracteres.",
  }).refine(
    (val) => val && val.trim() !== '',
    {
      message: "El nombre no puede estar vacío."
    }
  ),
  description: z.string().optional()
    .transform(val => val === '' ? null : val), // Convertir string vacío a null
  quantity_available: z.coerce.number({
    required_error: "La cantidad es obligatoria",
    invalid_type_error: "La cantidad debe ser un número",
  }).min(0, {
    message: "La cantidad no puede ser negativa.",
  }),
  unit: z.string().optional()
    .transform(val => val === '' ? null : val), // Convertir string vacío a null
  unit_cost: z.coerce.number().min(0, {
    message: "El costo unitario no puede ser negativo.",
  }).optional()
    .transform(val => isNaN(val) ? null : val), // Convertir NaN a null
  location: z.string().optional()
    .transform(val => val === '' ? null : val), // Convertir string vacío a null
  barcode: z.string().optional()
    .transform(val => val === '' ? null : val), // Convertir string vacío a null
})

type InventoryItemFormValues = z.infer<typeof inventoryItemFormSchema>

// Valores por defecto para un nuevo ítem
const defaultValues: Partial<InventoryItemFormValues> = {
  name: "",
  description: "",
  quantity_available: 0,
  unit: "",
  unit_cost: undefined,
  location: "",
  barcode: "",
}

interface InventoryItemFormProps {
  initialData?: Partial<InventoryItemFormValues>
  onSubmit: (data: InventoryItemFormValues) => void
  isLoading?: boolean
}

export function InventoryItemForm({
  initialData,
  onSubmit,
  isLoading = false,
}: InventoryItemFormProps) {
  const supabase = createClient()
  const [validationErrors, setValidationErrors] = useState<string[]>([])

  const form = useForm<InventoryItemFormValues>({
    resolver: zodResolver(inventoryItemFormSchema),
    defaultValues: {
      ...defaultValues,
      ...initialData,
    },
  })

  const handleSubmit = (data: InventoryItemFormValues) => {
    // Limpiar errores previos
    setValidationErrors([])

    // Validar campos requeridos
    const requiredFields = ['name', 'quantity_available']
    const { isValid, errors } = dataValidator.validateRequiredFields(data, requiredFields)

    if (!isValid) {
      // Mostrar errores en los campos correspondientes
      Object.entries(errors).forEach(([field, message]) => {
        form.setError(field as any, {
          type: 'required',
          message
        })
      })

      // Agregar errores a la lista de validación para mostrar en el alert
      const errorMessages = Object.values(errors)
      setValidationErrors(errorMessages)
      return
    }

    // Sanitizar campos de texto para convertir strings vacíos a null
    const textFields = ['description', 'unit', 'location', 'barcode']
    const sanitizedData = dataValidator.sanitizeTextFields(data, textFields)

    // Continuar con el envío del formulario
    onSubmit(sanitizedData)
  }

  return (
    <Form {...form}>
      <FormValidationAlert errors={validationErrors} />
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <div className="text-sm text-muted-foreground mb-4">
          Los campos marcados con <span className="text-red-500">*</span> son obligatorios.
        </div>
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center">
                Nombre
                <span className="text-red-500 ml-1">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  placeholder="Nombre del ítem"
                  {...field}
                  className={form.formState.errors.name ? "border-red-500" : ""}
                  required
                />
              </FormControl>
              <FormDescription>
                Nombre descriptivo para identificar el ítem.
              </FormDescription>
              <FormMessage className="text-red-500" />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Descripción</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Descripción detallada del ítem"
                  className="min-h-[120px]"
                  {...field}
                  value={field.value || ""}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <FormField
            control={form.control}
            name="quantity_available"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center">
                  Cantidad Disponible
                  <span className="text-red-500 ml-1">*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    min="0"
                    step="1"
                    {...field}
                    className={form.formState.errors.quantity_available ? "border-red-500" : ""}
                    required
                  />
                </FormControl>
                <FormDescription>
                  Cantidad actual disponible en inventario.
                </FormDescription>
                <FormMessage className="text-red-500" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="unit"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Unidad</FormLabel>
                <FormControl>
                  <Input placeholder="Unidad de medida (ej. kg, m, unidad)" {...field} value={field.value || ""} />
                </FormControl>
                <FormDescription>
                  Unidad de medida del ítem.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="unit_cost"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Costo Unitario</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    min="0"
                    step="0.01"
                    placeholder="0.00"
                    {...field}
                    value={field.value === undefined ? "" : field.value}
                  />
                </FormControl>
                <FormDescription>
                  Costo por unidad del ítem.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="location"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Ubicación</FormLabel>
                <FormControl>
                  <Input placeholder="Ubicación física del ítem" {...field} value={field.value || ""} />
                </FormControl>
                <FormDescription>
                  Dónde se encuentra almacenado el ítem.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="barcode"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Código de Barras</FormLabel>
                <FormControl>
                  <Input placeholder="Código de barras o identificador único" {...field} value={field.value || ""} />
                </FormControl>
                <FormDescription>
                  Código de barras o identificador único del ítem.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex justify-end space-x-4">
          <Button variant="outline" type="button" onClick={() => window.history.back()}>
            Cancelar
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {initialData ? "Actualizar ítem" : "Crear ítem"}
          </Button>
        </div>
      </form>
    </Form>
  )
}
