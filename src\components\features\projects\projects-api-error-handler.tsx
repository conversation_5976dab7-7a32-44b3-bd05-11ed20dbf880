"use client"

import { useEffect, useState } from "react"
import { createClient } from "@/lib/supabase/client"
import { toast } from "@/hooks/use-toast"
import { AuthReconnect } from "@/components/features/auth/auth-reconnect"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"

/**
 * Componente que monitorea errores 401 en las solicitudes API y muestra un diálogo de reconexión
 */
export function ProjectsApiErrorHandler() {
  const [showReconnectDialog, setShowReconnectDialog] = useState(false)

  useEffect(() => {
    // Función para interceptar errores de red
    const handleFetchError = (event: PromiseRejectionEvent) => {
      // Verificar si es un error de fetch
      if (event.reason instanceof Error) {
        const error = event.reason;

        // Verificar si es un error de red o un error 401
        const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

        if (
          errorMessage.includes('401') ||
          errorMessage.includes('Unauthorized') ||
          errorMessage.includes('unauthorized')
        ) {
          console.log('Interceptado error 401 en solicitud API:', error);

          // Mostrar el diálogo de reconexión
          setShowReconnectDialog(true);

          // Prevenir que el error se propague a la consola
          event.preventDefault();
        }
      }
    };

    // Registrar el listener
    window.addEventListener('unhandledrejection', handleFetchError);

    // Verificar la sesión al montar el componente
    const checkSession = async () => {
      try {
        const supabase = createClient();
        const { data, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Error al verificar sesión:', error);
          setShowReconnectDialog(true);
          return;
        }

        if (!data?.session) {
          console.warn('No hay sesión activa');
          setShowReconnectDialog(true);
        }
      } catch (err) {
        console.error('Error inesperado al verificar sesión:', err);
      }
    };

    // Verificar la sesión solo si estamos en la página de proyectos
    if (window.location.pathname.includes('/projects')) {
      checkSession();
    }

    // Limpiar el listener al desmontar
    return () => {
      window.removeEventListener('unhandledrejection', handleFetchError);
    };
  }, []);

  // Función para intentar reconectar
  const handleReconnect = async () => {
    try {
      const supabase = createClient();
      const { data, error } = await supabase.auth.refreshSession();

      if (error) {
        console.error('Error al refrescar sesión:', error);
        toast({
          title: "Error de sesión",
          description: "No se pudo refrescar la sesión. Intente cerrar sesión y volver a iniciar.",
          variant: "destructive"
        });
        return;
      }

      if (data?.session) {
        console.log('Sesión refrescada exitosamente');
        toast({
          title: "Sesión restaurada",
          description: "La sesión ha sido refrescada correctamente"
        });

        // Cerrar el diálogo y recargar la página
        setShowReconnectDialog(false);
        window.location.reload();
      }
    } catch (err) {
      console.error('Error inesperado al reconectar:', err);
      toast({
        title: "Error",
        description: "Ocurrió un error inesperado",
        variant: "destructive"
      });
    }
  };

  return (
    <Dialog open={showReconnectDialog} onOpenChange={setShowReconnectDialog}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Problema de autenticación detectado</DialogTitle>
          <DialogDescription>
            Se ha detectado un problema con tu sesión que impide cargar los proyectos.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <AuthReconnect />
        </div>
      </DialogContent>
    </Dialog>
  );
}
