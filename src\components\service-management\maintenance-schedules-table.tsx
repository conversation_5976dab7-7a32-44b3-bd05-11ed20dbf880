import { useState, useEffect } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { MaintenanceSchedule } from '@/lib/types/service-management';
import { ServiceManagementService } from '@/lib/services/service-management-service';
import { formatDate } from '@/lib/utils';
import { useRouter } from 'next/navigation';
import { 
  Calendar, 
  AlertTriangle, 
  Clock, 
  CheckCircle2,
  Timer,
  Building,
  Wrench,
  RotateCw
} from 'lucide-react';

interface MaintenanceSchedulesTableProps {
  daysAhead?: number;
  includeOverdue?: boolean;
  limit?: number;
  clientId?: string;
  equipmentId?: string;
  onRowClick?: (schedule: MaintenanceSchedule) => void;
  onCreateServiceRequest?: (schedule: MaintenanceSchedule) => void;
}

export function MaintenanceSchedulesTable({
  daysAhead = 30,
  includeOverdue = true,
  limit = 10,
  clientId,
  equipmentId,
  onRowClick,
  onCreateServiceRequest,
}: MaintenanceSchedulesTableProps) {
  const [schedules, setSchedules] = useState<MaintenanceSchedule[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [processingId, setProcessingId] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    const fetchSchedules = async () => {
      try {
        setLoading(true);
        let data = await ServiceManagementService.getUpcomingMaintenanceSchedules(
          daysAhead,
          includeOverdue,
          limit,
          0
        );
        
        // Filtrar por cliente si se proporciona clientId
        if (clientId) {
          data = data.filter(schedule => schedule.client_id === clientId);
        }
        
        // Filtrar por equipo si se proporciona equipmentId
        if (equipmentId) {
          data = data.filter(schedule => schedule.equipment_id === equipmentId);
        }
        
        setSchedules(data);
        setError(null);
      } catch (err) {
        console.error('Error al cargar programas de mantenimiento:', err);
        setError('Error al cargar programas de mantenimiento. Intente nuevamente.');
      } finally {
        setLoading(false);
      }
    };

    fetchSchedules();
  }, [daysAhead, includeOverdue, limit, clientId, equipmentId]);

  const handleRowClick = (schedule: MaintenanceSchedule) => {
    if (onRowClick) {
      onRowClick(schedule);
    } else {
      router.push(`/dashboard/maintenance/${schedule.id}`);
    }
  };

  const handleCreateServiceRequest = async (schedule: MaintenanceSchedule, e: React.MouseEvent) => {
    e.stopPropagation(); // Evitar que se propague al hacer clic en la fila
    
    try {
      setProcessingId(schedule.id);
      const serviceRequestId = await ServiceManagementService.createServiceRequestFromMaintenance(schedule.id);
      
      if (onCreateServiceRequest) {
        onCreateServiceRequest(schedule);
      } else {
        router.push(`/dashboard/service-requests/${serviceRequestId}`);
      }
    } catch (err) {
      console.error('Error al crear solicitud de servicio:', err);
      alert('Error al crear solicitud de servicio. Intente nuevamente.');
    } finally {
      setProcessingId(null);
    }
  };

  const getMaintenanceTypeBadge = (type: string) => {
    switch (type) {
      case 'preventive':
        return (
          <Badge variant="outline" className="flex gap-1 items-center bg-blue-50">
            <CheckCircle2 className="h-3 w-3 text-blue-500" />
            <span className="text-blue-700">Preventivo</span>
          </Badge>
        );
      case 'corrective':
        return (
          <Badge variant="outline" className="flex gap-1 items-center bg-amber-50">
            <Wrench className="h-3 w-3 text-amber-500" />
            <span className="text-amber-700">Correctivo</span>
          </Badge>
        );
      case 'predictive':
        return (
          <Badge variant="outline" className="flex gap-1 items-center bg-purple-50">
            <RotateCw className="h-3 w-3 text-purple-500" />
            <span className="text-purple-700">Predictivo</span>
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            {type}
          </Badge>
        );
    }
  };

  const getFrequencyBadge = (frequency: string | null) => {
    if (!frequency) return null;
    
    switch (frequency) {
      case 'daily':
        return <Badge variant="outline">Diario</Badge>;
      case 'weekly':
        return <Badge variant="outline">Semanal</Badge>;
      case 'monthly':
        return <Badge variant="outline">Mensual</Badge>;
      case 'quarterly':
        return <Badge variant="outline">Trimestral</Badge>;
      case 'yearly':
        return <Badge variant="outline">Anual</Badge>;
      default:
        return <Badge variant="outline">{frequency}</Badge>;
    }
  };

  const getStatusBadge = (isOverdue?: boolean, daysToMaintenance?: number | null) => {
    if (isOverdue) {
      return (
        <Badge variant="destructive" className="flex gap-1 items-center">
          <AlertTriangle className="h-3 w-3" />
          Vencido
        </Badge>
      );
    } else if (daysToMaintenance !== undefined && daysToMaintenance !== null) {
      if (daysToMaintenance <= 7) {
        return (
          <Badge variant="warning" className="flex gap-1 items-center">
            <Clock className="h-3 w-3" />
            Próximo ({daysToMaintenance} días)
          </Badge>
        );
      } else {
        return (
          <Badge variant="outline" className="flex gap-1 items-center">
            <Calendar className="h-3 w-3" />
            Programado
          </Badge>
        );
      }
    } else {
      return (
        <Badge variant="outline">
          Sin programar
        </Badge>
      );
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Programas de Mantenimiento</CardTitle>
          <CardDescription>
            Mantenimientos programados para los próximos {daysAhead} días
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {Array(5).fill(0).map((_, i) => (
              <Skeleton key={i} className="h-12 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Programas de Mantenimiento</CardTitle>
          <CardDescription>
            Mantenimientos programados para los próximos {daysAhead} días
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-destructive/10 p-4 rounded-md text-destructive">
            {error}
          </div>
          <Button 
            variant="outline" 
            className="mt-4"
            onClick={() => window.location.reload()}
          >
            Reintentar
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Programas de Mantenimiento</CardTitle>
        <CardDescription>
          Mantenimientos programados para los próximos {daysAhead} días
          {includeOverdue && ' (incluye vencidos)'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {schedules.length === 0 ? (
          <div className="text-center py-6 text-muted-foreground">
            No hay programas de mantenimiento para mostrar.
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Equipo</TableHead>
                  <TableHead>Cliente</TableHead>
                  <TableHead>Tipo</TableHead>
                  <TableHead>Frecuencia</TableHead>
                  <TableHead>Próxima fecha</TableHead>
                  <TableHead>Estado</TableHead>
                  <TableHead>Acciones</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {schedules.map((schedule) => (
                  <TableRow 
                    key={schedule.id}
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleRowClick(schedule)}
                  >
                    <TableCell className="font-medium">
                      <div>
                        {schedule.equipment_name}
                        {schedule.equipment_model && (
                          <div className="text-xs text-muted-foreground">
                            Modelo: {schedule.equipment_model}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Building className="h-3 w-3" />
                        {schedule.client_name || 'Sin cliente'}
                      </div>
                    </TableCell>
                    <TableCell>{getMaintenanceTypeBadge(schedule.maintenance_type)}</TableCell>
                    <TableCell>{getFrequencyBadge(schedule.frequency)}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {schedule.next_maintenance_date 
                          ? formatDate(schedule.next_maintenance_date)
                          : 'Sin programar'
                        }
                      </div>
                      {schedule.estimated_duration_hours && (
                        <div className="text-xs text-muted-foreground mt-1 flex items-center gap-1">
                          <Timer className="h-3 w-3" />
                          {schedule.estimated_duration_hours} horas
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(schedule.is_overdue, schedule.days_to_maintenance)}
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => handleCreateServiceRequest(schedule, e)}
                        disabled={!!processingId}
                        className="whitespace-nowrap"
                      >
                        {processingId === schedule.id ? (
                          <>
                            <span className="animate-spin mr-1">
                              <RotateCw className="h-3 w-3" />
                            </span>
                            Procesando...
                          </>
                        ) : (
                          'Crear solicitud'
                        )}
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
        <div className="mt-4 flex justify-end">
          <Button 
            variant="outline" 
            onClick={() => router.push('/dashboard/maintenance')}
          >
            Ver todos
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
