"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Trash2, Loader2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import { toast } from "@/hooks/use-toast"
import { projectsService } from "@/lib/services/projects-service"
import { createClient } from "@/lib/supabase/client"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

interface ProjectDeleteButtonProps {
  projectId: string
  projectName: string
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
  size?: "default" | "sm" | "lg" | "icon"
  className?: string
  softDelete?: boolean
  cascadeDelete?: boolean
  children?: React.ReactNode
}

export function ProjectDeleteButton({
  projectId,
  projectName,
  variant = "destructive",
  size = "default",
  className = "",
  softDelete = false,
  cascadeDelete = true,
  children,
}: ProjectDeleteButtonProps) {
  const router = useRouter()
  const [isDeleting, setIsDeleting] = useState(false)
  const supabase = createClient()

  const handleDelete = async () => {
    setIsDeleting(true)
    try {
      console.log(`Deleting project with ID: ${projectId}`);

      // Verificar si el proyecto existe antes de intentar eliminarlo
      try {
        const { data: projectData, error: projectError } = await supabase
          .from('projects')
          .select('id, name')
          .eq('id', projectId)
          .maybeSingle(); // Usar maybeSingle en lugar de single para evitar errores

        if (projectError) {
          console.error("Error al verificar el proyecto:", projectError);
          throw new Error("Error al verificar el proyecto: " + projectError.message);
        }

        if (!projectData) {
          console.warn("El proyecto no existe o ya fue eliminado");
          toast({
            title: "Advertencia",
            description: "El proyecto no existe o ya fue eliminado.",
            variant: "warning",
          });
          setIsDeleting(false);

          // Redirigir a la lista de proyectos ya que el proyecto no existe
          router.push("/dashboard/projects");
          router.refresh();
          return;
        }
      } catch (verifyError) {
        console.error("Error al verificar el proyecto:", verifyError);
        toast({
          title: "Error",
          description: "No se pudo verificar el proyecto. Es posible que ya haya sido eliminado.",
          variant: "destructive",
        });
        setIsDeleting(false);
        return;
      }

      // Intentar eliminar el proyecto con opciones MCP
      try {
        // Intentar primero con la función MCP directamente
        const { data: mcpResult, error: mcpError } = await supabase
          .rpc('mcp_delete_project', {
            project_id: projectId,
            options: {
              cascade: cascadeDelete,
              soft_delete: softDelete
            }
          });

        if (mcpError) {
          console.log('Error al usar función MCP directamente, usando servicio de proyectos:', mcpError);
          // Si falla, usar el servicio de proyectos que tiene lógica de fallback
          await projectsService.deleteProject(projectId);
        } else if (!mcpResult.success) {
          // Si la función MCP devuelve un error en el resultado
          console.error(`MCP delete returned error: ${mcpResult.message}, code: ${mcpResult.code}`);

          // Manejar diferentes códigos de error
          if (mcpResult.code === 'PERMISSION_DENIED') {
            throw new Error('No tienes permisos para eliminar este proyecto. Contacta al administrador.');
          } else if (mcpResult.code === 'PROJECT_NOT_FOUND') {
            throw new Error('El proyecto no existe o ya ha sido eliminado.');
          } else {
            throw new Error(mcpResult.message || 'Error al eliminar el proyecto.');
          }
        } else {
          console.log(`Proyecto eliminado correctamente con MCP:`, mcpResult);
        }
      } catch (mcpError) {
        console.log('Error al usar MCP, usando servicio de proyectos:', mcpError);
        // Si falla, usar el servicio de proyectos que tiene lógica de fallback
        await projectsService.deleteProject(projectId);
      }

      toast({
        title: softDelete ? "Proyecto archivado" : "Proyecto eliminado",
        description: softDelete
          ? "El proyecto ha sido marcado como eliminado y se puede recuperar"
          : "El proyecto ha sido eliminado correctamente",
      });

      // Forzar actualización del localStorage para que otras páginas se actualicen
      localStorage.setItem('projects_last_updated', Date.now().toString());

      // Pequeña pausa para asegurar que la eliminación se complete
      await new Promise(resolve => setTimeout(resolve, 500));

      // Verificar si el proyecto fue realmente eliminado
      try {
        const { data: checkData, error: checkError } = await supabase
          .from('projects')
          .select('id')
          .eq('id', projectId)
          .maybeSingle(); // Usar maybeSingle en lugar de single para evitar errores

        if (!checkError && checkData) {
          // El proyecto todavía existe, mostrar advertencia
          console.warn("El proyecto no fue eliminado correctamente");
          toast({
            title: "Advertencia",
            description: "El proyecto parece seguir existiendo. Puede que no tengas permisos suficientes para eliminarlo.",
            variant: "destructive",
          });
          setIsDeleting(false);
          return;
        } else {
          // Si no hay datos, significa que el proyecto fue eliminado correctamente
          console.log("Proyecto eliminado correctamente verificado");
        }
      } catch (checkError) {
        // Si hay un error, consideramos que el proyecto fue eliminado
        // ya que el error más común es que el proyecto no existe
        console.log("Verificación de eliminación completada (con error, lo cual es esperado):", checkError);
        // No hacemos nada más, continuamos con la redirección
      }

      // Redirigir a la lista de proyectos
      router.push("/dashboard/projects");

      // Forzar actualización de la página
      router.refresh();

      // Recargar la página después de un breve retraso para asegurar que se actualice completamente
      setTimeout(() => {
        window.location.href = "/dashboard/projects";
      }, 1000);
    } catch (error) {
      console.error("Error al eliminar el proyecto:", error);

      // Mostrar mensaje de error específico si está disponible
      const errorMessage = error instanceof Error ? error.message : "No se pudo eliminar el proyecto. Intente nuevamente.";

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  }

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button variant={variant} size={size} className={className}>
          {children || (
            <>
              <Trash2 className="mr-2 h-4 w-4" /> {softDelete ? "Archivar Proyecto" : "Eliminar Proyecto"}
            </>
          )}
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{softDelete ? "¿Archivar este proyecto?" : "¿Eliminar este proyecto?"}</AlertDialogTitle>
          <AlertDialogDescription>
            {softDelete
              ? `El proyecto "${projectName}" será marcado como eliminado pero se podrá recuperar más tarde. ¿Deseas continuar?`
              : `Esta acción no se puede deshacer. ¿Estás seguro de que quieres eliminar el proyecto "${projectName}"?`
            }
            {!cascadeDelete && !softDelete && (
              <p className="mt-2 text-yellow-600 dark:text-yellow-400">
                Advertencia: No se eliminarán los datos relacionados (documentos, tareas, etc.).
              </p>
            )}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>Cancelar</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={isDeleting}
            className="bg-red-500 hover:bg-red-600"
          >
            {isDeleting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {softDelete ? "Archivando..." : "Eliminando..."}
              </>
            ) : (
              softDelete ? "Archivar" : "Eliminar"
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
