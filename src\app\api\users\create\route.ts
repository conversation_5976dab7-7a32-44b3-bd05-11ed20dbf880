import { NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function POST(request: Request) {
  try {
    const body = await request.json()

    // Validate required fields
    if (!body.email) {
      return NextResponse.json({ error: 'El email es obligatorio' }, { status: 400 })
    }

    // Create client directly in the route handler
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || ''
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || ''

    console.log('Supabase URL:', supabaseUrl)
    console.log('Service Role Key exists:', !!supabaseServiceKey)
    console.log('Service Role Key length:', supabaseServiceKey.length)

    // Check if we have the required configuration
    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json({ error: 'Configuración de Supabase incompleta' }, { status: 500 })
    }

    // Create the Supabase client with explicit options
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })

    // Test the connection
    try {
      const { data: testData, error: testError } = await supabase.from('users').select('count').limit(1)
      if (testError) {
        console.error('Test connection failed:', testError)
        return NextResponse.json({
          error: 'Error de conexión con Supabase',
          details: testError.message
        }, { status: 500 })
      }
      console.log('Test connection successful')
    } catch (testErr) {
      console.error('Exception testing connection:', testErr)
    }

    console.log('Creating user with data:', {
      email: body.email,
      password: body.password ? '(provided)' : '(not provided)',
      full_name: body.full_name,
      role: body.role
    })

    // Validate role
    if (!body.role) {
      return NextResponse.json({ error: 'El rol es obligatorio' }, { status: 400 })
    }

    // Ensure role is properly formatted
    const userRole = Array.isArray(body.role) ? body.role : [body.role]
    console.log('Role formatted as array:', userRole)

    // Create user with admin API
    console.log('Creating user with admin API...')
    let userData;
    let userError;

    try {
      const { data, error } = await supabase.auth.admin.createUser({
        email: body.email,
        password: body.password || undefined,
        email_confirm: true,
        user_metadata: {
          full_name: body.full_name,
          role: userRole,
        },
      })

      console.log('Admin API response:', { success: !!data, error: error?.message })

      // Store the response for later use
      userData = data;
      userError = error;

      if (error) {
        const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

        if (error.message.includes('Invalid API key') || errorMessage.includes('service_role')) {
          console.error('Service role key issue detected:', error)
          return NextResponse.json({
            error: 'Error de autenticación con Supabase',
            details: 'La clave de servicio (service_role) no es válida o no tiene los permisos necesarios. Por favor, actualice la clave en el archivo .env.local con la clave correcta de la consola de Supabase.',
            code: 'INVALID_SERVICE_ROLE_KEY'
          }, { status: 401 })
        }

        return NextResponse.json({
          error: errorMessage,
          details: 'Error al crear el usuario en Supabase'
        }, { status: 500 })
      }
    } catch (adminError) {
      console.error('Exception calling admin API:', adminError)
      const errorMessage = adminError instanceof Error ? adminError.message : 'Error desconocido'
      const errorStack = adminError instanceof Error ? adminError.stack : undefined
      return NextResponse.json({
        error: 'Error al crear el usuario',
        details: errorMessage,
        stack: errorStack
      }, { status: 500 })
    }

    // Also update the public.users table with the new user data
    const firstName = body.full_name ? body.full_name.split(' ')[0] : null
    const lastName = body.full_name ? body.full_name.split(' ').slice(1).join(' ') : null

    if (!userData.user) {
      return NextResponse.json({
        error: 'Error: usuario creado pero sin datos de usuario'
      }, { status: 500 })
    }

    console.log('Updating public.users table with data:', {
      id: userData.user.id,
      email: body.email,
      first_name: firstName,
      last_name: lastName,
      role: userRole
    })

    try {
      const { error: userError } = await supabase
        .from('users')
        .upsert({
          id: userData.user.id,
          email: body.email,
          first_name: firstName,
          last_name: lastName,
          role: userRole,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })

      if (userError) {
        console.error('Error updating public.users table:', userError)
        // We don't return an error here as the auth user was created successfully
      }
    } catch (dbError) {
      console.error('Exception updating public.users table:', dbError)
      // Continue execution even if there's an error
    }

    // Also update the profiles table
    console.log('Updating profiles table with data:', {
      id: userData.user.id,
      first_name: firstName,
      last_name: lastName,
      role: body.role // profiles.role is a string, not an array
    })

    try {
      const { error: profileError } = await supabase
        .from('profiles')
        .upsert({
          id: userData.user.id,
          first_name: firstName,
          last_name: lastName,
          role: body.role, // profiles.role is a string, not an array
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })

      if (profileError) {
        console.error('Error updating profiles table:', profileError)
        // We don't return an error here as the auth user was created successfully
      }
    } catch (profileDbError) {
      console.error('Exception updating profiles table:', profileDbError)
      // Continue execution even if there's an error
    }

    // If send_invite is true, you could implement invitation logic here
    if (body.send_invite) {
      // For now, we'll just return success
      console.log(`Invitation would be sent to ${body.email}`)
    }

    console.log('User created successfully:', userData.user.id)
    return NextResponse.json({
      success: true,
      user: userData.user,
      message: 'Usuario creado correctamente'
    })
  } catch (error: unknown) {
    console.error('Unexpected error creating user:', error)

    const errorMessage = error instanceof Error
      ? error.message
      : 'Error inesperado al crear el usuario';

    const errorStack = error instanceof Error
      ? error.stack
      : undefined;

    return NextResponse.json({
      error: errorMessage,
      details: errorStack
    }, { status: 500 })
  }
}
