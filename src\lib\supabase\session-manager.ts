import { createClient } from './client';
import { toast } from "@/hooks/use-toast";

/**
 * Servicio para gestionar sesiones de Supabase
 * Proporciona funciones para verificar y refrescar sesiones
 */
export const sessionManager = {
  /**
   * Refresca la sesión actual
   * @returns {Promise<boolean>} True si la sesión se refrescó correctamente
   */
  async refreshSession() {
    try {
      const supabase = createClient();
      const { data, error } = await supabase.auth.refreshSession();

      if (error) {
        console.error('Error refreshing session:', error);
        return false;
      }

      if (data?.session) {
        console.log('Session refreshed successfully');
        // Guardar timestamp de la sesión para caché
        localStorage.setItem('auth_session_timestamp', Date.now().toString());
        return true;
      }

      return false;
    } catch (error) {
      console.error('Unexpected error refreshing session:', error);
      return false;
    }
  },

  /**
   * Verifica si la sesión actual es válida y la refresca si es necesario
   * @returns {Promise<boolean>} True si la sesión es válida o se refrescó correctamente
   */
  async ensureValidSession() {
    try {
      const supabase = createClient();
      const { data, error } = await supabase.auth.getSession();

      if (error) {
        console.error('Error getting session:', error);
        return await this.refreshSession();
      }

      if (!data?.session) {
        console.warn('No active session found');
        return await this.refreshSession();
      }

      // Verificar si la sesión está a punto de expirar (menos de 5 minutos)
      const expiresAt = data.session.expires_at;
      const expiresInMs = expiresAt ? (expiresAt * 1000) - Date.now() : 0;

      if (expiresInMs < 5 * 60 * 1000) {
        console.log(`Session about to expire (${Math.round(expiresInMs/1000)} seconds), refreshing...`);
        return await this.refreshSession();
      }

      return true;
    } catch (error) {
      console.error('Unexpected error ensuring valid session:', error);
      return false;
    }
  },

  /**
   * Verifica el estado de la sesión y muestra un mensaje si hay problemas
   * @param {boolean} showToast Si es true, muestra un toast con el resultado
   * @returns {Promise<{valid: boolean, message: string}>} Estado de la sesión
   */
  async checkSessionStatus(showToast = false) {
    try {
      const supabase = createClient();
      const { data, error } = await supabase.auth.getSession();

      if (error) {
        const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

        const message = `Error de sesión: ${errorMessage}`;
        if (showToast) {
          toast({
            title: "Error de sesión",
            description: message,
            variant: "destructive"
          });
        }
        return { valid: false, message };
      }

      if (!data?.session) {
        const message = "No hay sesión activa";
        if (showToast) {
          toast({
            title: "Error de sesión",
            description: message,
            variant: "destructive"
          });
        }
        return { valid: false, message };
      }

      // Verificar si la sesión está a punto de expirar
      const expiresAt = data.session.expires_at;
      const expiresInMs = expiresAt ? (expiresAt * 1000) - Date.now() : 0;

      if (expiresInMs < 0) {
        const message = "La sesión ha expirado";
        if (showToast) {
          toast({
            title: "Sesión expirada",
            description: message,
            variant: "destructive"
          });
        }
        return { valid: false, message };
      }

      if (expiresInMs < 5 * 60 * 1000) {
        const message = `La sesión expirará pronto (en ${Math.round(expiresInMs/1000)} segundos)`;
        if (showToast) {
          toast({
            title: "Sesión a punto de expirar",
            description: message,
            variant: "destructive"
          });
        }
        return { valid: true, message };
      }

      return {
        valid: true,
        message: `Sesión válida (expira en ${Math.round(expiresInMs/60000)} minutos)`
      };
    } catch (error) {
      const message = `Error inesperado: ${error instanceof Error ? error.message : 'Desconocido'}`;
      if (showToast) {
        toast({
          title: "Error",
          description: message,
          variant: "destructive"
        });
      }
      return { valid: false, message };
    }
  }
};
