import { describe, it, expect, beforeAll, afterAll, vi } from 'vitest';
import { ServiceManagementService } from '@/lib/services/service-management-service';
import { ServiceContractService } from '@/lib/services/service-contract-service';
import { createClient } from '@/lib/supabase/client';

// Mock de Supabase
vi.mock('@/lib/supabase/client', () => ({
  createClient: () => ({
    rpc: vi.fn(),
    from: vi.fn().mockReturnThis(),
    select: vi.fn().mockReturnThis(),
    insert: vi.fn().mockReturnThis(),
    update: vi.fn().mockReturnThis(),
    delete: vi.fn().mockReturnThis(),
    eq: vi.fn().mockReturnThis(),
    order: vi.fn().mockReturnThis(),
    range: vi.fn().mockReturnThis(),
    single: vi.fn().mockReturnThis(),
    auth: {
      getUser: vi.fn().mockResolvedValue({
        data: { user: { id: 'test-user-id' } },
        error: null
      })
    }
  }),
}));

describe('Service Management Integration Tests', () => {
  // Datos de prueba
  const testServiceRequest = {
    id: 'test-request-id',
    title: 'Test Service Request',
    description: 'Test description',
    status: 'pending',
    priority: 'medium',
    client_id: 'test-client-id',
    client_name: 'Test Client',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  const testEquipment = {
    id: 'test-equipment-id',
    name: 'Test Equipment',
    model: 'Test Model',
    serial_number: 'SN12345',
    client_id: 'test-client-id',
    client_name: 'Test Client',
    status: 'active',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  const testServiceActivity = {
    id: 'test-activity-id',
    service_request_id: 'test-request-id',
    equipment_id: 'test-equipment-id',
    description: 'Test activity',
    activity_type: 'maintenance',
    status: 'pending',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  const testContract = {
    id: 'test-contract-id',
    title: 'Test Contract',
    contract_number: '2023-0001',
    client_id: 'test-client-id',
    client_name: 'Test Client',
    start_date: new Date().toISOString(),
    end_date: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
    status: 'active',
    contract_type: 'standard',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    is_active: true
  };

  beforeAll(() => {
    // Configurar mocks para las pruebas
    const mockSupabase = createClient();
    (mockSupabase.rpc as any).mockImplementation((functionName, params) => {
      if (functionName === 'get_pending_service_requests') {
        return Promise.resolve({
          data: [testServiceRequest],
          error: null
        });
      }

      if (functionName === 'get_service_request_details') {
        return Promise.resolve({
          data: [testServiceRequest],
          error: null
        });
      }

      if (functionName === 'get_client_equipment') {
        return Promise.resolve({
          data: [testEquipment],
          error: null
        });
      }

      if (functionName === 'get_service_activities') {
        return Promise.resolve({
          data: [testServiceActivity],
          error: null
        });
      }

      if (functionName === 'get_active_service_contracts') {
        return Promise.resolve({
          data: [testContract],
          error: null
        });
      }

      if (functionName === 'get_service_contract_details') {
        return Promise.resolve({
          data: [{
            ...testContract,
            items: [],
            recent_services: [],
            covered_equipment: [],
            billing_history: []
          }],
          error: null
        });
      }

      return Promise.resolve({
        data: null,
        error: { message: 'Function not mocked' }
      });
    });

    (mockSupabase.from as any).mockImplementation((table: string) => {
      return {
        select: () => ({
          eq: () => ({
            single: () => {
              if (table === 'profiles') {
                return Promise.resolve({
                  data: { role: 'technician' },
                  error: null
                });
              }
              return Promise.resolve({ data: null, error: null });
            }
          })
        }),
        insert: () => ({
          select: () => Promise.resolve({
            data: [{ id: 'new-id' }],
            error: null
          })
        }),
        update: () => ({
          eq: () => ({
            select: () => Promise.resolve({
              data: [{ id: 'updated-id' }],
              error: null
            })
          })
        }),
        delete: () => ({
          eq: () => Promise.resolve({
            data: null,
            error: null
          })
        })
      };
    });
  });

  afterAll(() => {
    vi.clearAllMocks();
  });

  describe('Service Request Integration', () => {
    it('should fetch pending service requests and get details', async () => {
      // Obtener solicitudes pendientes
      const pendingRequests = await ServiceManagementService.getPendingServiceRequests();
      expect(pendingRequests).toHaveLength(1);
      expect(pendingRequests[0].id).toBe('test-request-id');

      // Obtener detalles de una solicitud
      const requestDetails = await ServiceManagementService.getServiceRequestById('test-request-id');
      expect(requestDetails.id).toBe('test-request-id');
      expect(requestDetails.title).toBe('Test Service Request');
    });

    it('should create and update a service request', async () => {
      // Crear solicitud
      const newRequest = await ServiceManagementService.createServiceRequest({
        title: 'New Request',
        description: 'New description',
        status: 'pending',
        priority: 'medium',
        client_id: 'test-client-id'
      });

      expect(newRequest.id).toBe('new-id');

      // Actualizar solicitud
      const updatedRequest = await ServiceManagementService.updateServiceRequest('test-request-id', {
        status: 'in_progress'
      });

      expect(updatedRequest.id).toBe('updated-id');
    });
  });

  describe('Equipment Integration', () => {
    it('should fetch client equipment', async () => {
      const equipment = await ServiceManagementService.getClientEquipment('test-client-id');
      expect(equipment).toHaveLength(1);
      expect(equipment[0].id).toBe('test-equipment-id');
      expect(equipment[0].name).toBe('Test Equipment');
    });
  });

  describe('Service Activities Integration', () => {
    it('should fetch service activities', async () => {
      const activities = await ServiceManagementService.getServiceActivities(
        'test-request-id', null, null, null, 10, 0
      );

      expect(activities).toHaveLength(1);
      expect(activities[0].id).toBe('test-activity-id');
      expect(activities[0].service_request_id).toBe('test-request-id');
    });
  });

  describe('Service Contracts Integration', () => {
    it('should fetch active service contracts and get details', async () => {
      // Obtener contratos activos
      const activeContracts = await ServiceContractService.getActiveServiceContracts();
      expect(activeContracts).toHaveLength(1);
      expect(activeContracts[0].id).toBe('test-contract-id');

      // Obtener detalles de un contrato
      const contractDetails = await ServiceContractService.getServiceContractDetails('test-contract-id');
      expect(contractDetails.id).toBe('test-contract-id');
      expect(contractDetails.title).toBe('Test Contract');
      expect(contractDetails.items).toEqual([]);
    });

    it('should create a service contract', async () => {
      // Crear contrato
      const contractId = await ServiceContractService.createServiceContract({
        client_id: 'test-client-id',
        title: 'New Contract',
        start_date: new Date().toISOString(),
        end_date: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
        contract_type: 'standard'
      });

      expect(contractId).toBeDefined();
    });
  });

  describe('End-to-End Service Flow', () => {
    it('should handle a complete service flow', async () => {
      // 1. Crear solicitud de servicio
      const newRequest = await ServiceManagementService.createServiceRequest({
        title: 'E2E Test Request',
        description: 'End-to-end test',
        status: 'pending',
        priority: 'high',
        client_id: 'test-client-id'
      });

      expect(newRequest.id).toBeDefined();

      // 2. Asignar técnico
      const updatedRequest = await ServiceManagementService.updateServiceRequest(newRequest.id, {
        assigned_to: 'test-technician-id',
        status: 'assigned'
      });

      expect(updatedRequest.id).toBeDefined();

      // 3. Crear actividad de servicio
      const newActivity = await ServiceManagementService.createServiceActivity({
        service_request_id: newRequest.id,
        equipment_id: 'test-equipment-id',
        description: 'E2E Test Activity',
        activity_type: 'repair',
        status: 'scheduled',
        technician_id: 'test-technician-id'
      });

      expect(newActivity.id).toBeDefined();

      // 4. Verificar si está cubierto por contrato
      const coverage = await ServiceContractService.isServiceRequestCoveredByContract(newRequest.id);

      // 5. Completar actividad
      const completedActivity = await ServiceManagementService.updateServiceActivity(newActivity.id, {
        status: 'completed',
        notes: 'Activity completed successfully'
      });

      expect(completedActivity.id).toBeDefined();

      // 6. Completar solicitud
      const completedRequest = await ServiceManagementService.updateServiceRequest(newRequest.id, {
        status: 'completed',
        resolution_notes: 'Service request completed successfully',
        resolution_date: new Date().toISOString()
      });

      expect(completedRequest.id).toBeDefined();

      // 7. Si está cubierto por contrato, registrar servicio
      if (coverage.is_covered && coverage.contract_id) {
        const historyId = await ServiceContractService.recordContractService({
          contract_id: coverage.contract_id,
          service_request_id: newRequest.id,
          service_activity_id: newActivity.id,
          hours_used: 2,
          description: 'Service covered by contract'
        });

        expect(historyId).toBeDefined();
      }
    });
  });
});
