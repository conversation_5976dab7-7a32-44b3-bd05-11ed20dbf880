import { NextResponse } from 'next/server'

export async function POST() {
  try {
    // Simulamos la detención del servicio
    // En una implementación real, esto detendría el servicio de análisis de documentos

    // Simulamos un retraso para la detención del servicio
    await new Promise(resolve => setTimeout(resolve, 1000))

    return NextResponse.json({
      message: 'Servicio detenido correctamente',
      status: 'stopped'
    })
  } catch (error: unknown) {
    console.error('Error al detener el servicio:', error)

    const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

    return NextResponse.json(
      {
        error: 'Error al detener el servicio',
        message: errorMessage
      },
      { status: 500 }
    )
  }
}
