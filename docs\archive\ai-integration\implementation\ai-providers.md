# AI Provider Implementation Guide

## Overview

This guide details the implementation of AI provider integrations for document processing in the AdminCore  project management system. It covers the implementation of three AI providers: Google Gemini, OpenAI, and DeepSeek.

## Provider Interface

All AI providers must implement a common interface to ensure consistency and interchangeability:

```typescript
// web/src/lib/ai-providers/provider-interface.ts
import { DocumentAnalysisResult } from './document-analyzer'

/**
 * Interface that all AI providers must implement
 */
export interface AIProvider {
  /**
   * Analyzes document text and extracts project information
   *
   * @param documentText The text content of the document
   * @returns Structured project information
   */
  analyzeDocument(documentText: string): Promise<DocumentAnalysisResult>

  /**
   * Validates the provider configuration
   *
   * @returns True if configuration is valid, false otherwise
   */
  validateConfig(): boolean

  /**
   * Gets the name of the provider
   *
   * @returns Provider name
   */
  getProviderName(): string
}
```

## Base Provider Implementation

Create a base abstract class that implements common functionality:

```typescript
// web/src/lib/ai-providers/base-provider.ts
import { AIProvider } from './provider-interface'
import { DocumentAnalysisResult } from './document-analyzer'

/**
 * Base class for AI providers with common functionality
 */
export abstract class BaseAIProvider implements AIProvider {
  protected apiKey: string
  protected modelName: string

  constructor(config: { apiKey: string; modelName: string }) {
    this.apiKey = config.apiKey
    this.modelName = config.modelName
  }

  /**
   * Analyzes document text and extracts project information
   * Must be implemented by each provider
   */
  abstract analyzeDocument(documentText: string): Promise<DocumentAnalysisResult>

  /**
   * Validates the provider configuration
   */
  validateConfig(): boolean {
    return Boolean(this.apiKey) && Boolean(this.modelName)
  }

  /**
   * Gets the name of the provider
   * Must be implemented by each provider
   */
  abstract getProviderName(): string

  /**
   * Processes the raw AI response into a standardized format
   *
   * @param response Raw response from the AI provider
   * @returns Standardized document analysis result
   */
  protected processResponse(response: any): DocumentAnalysisResult {
    try {
      // If response is a string, try to parse it as JSON
      if (typeof response === 'string') {
        try {
          response = JSON.parse(response)
        } catch (e) {
          console.error('Failed to parse response as JSON:', e)
          // Continue with string response
        }
      }

      // If response is an object, map it to our standard format
      if (typeof response === 'object' && response !== null) {
        return {
          project_name: response.project_name || response.name || null,
          description: response.description || response.project_description || null,
          start_date: this.formatDate(response.start_date) || null,
          end_date: this.formatDate(response.end_date) || null,
          budget: response.budget?.toString() || null,
          currency: response.currency || 'CLP',
          client_name: response.client_name || response.client || null,
          deliverables: Array.isArray(response.deliverables) ? response.deliverables : null,
          scope: response.scope || response.project_scope || null,
          team_requirements: Array.isArray(response.team_requirements) ? response.team_requirements : null,
          tags: Array.isArray(response.tags) ? response.tags : null,
          confidence_score: response.confidence_score || 0.7
        }
      }

      // If we couldn't process the response, return a default object
      return {
        project_name: 'Untitled Project',
        description: null,
        start_date: null,
        end_date: null,
        budget: null,
        currency: 'CLP',
        client_name: null,
        deliverables: null,
        scope: null,
        team_requirements: null,
        tags: null,
        confidence_score: 0.5
      }
    } catch (error) {
      console.error('Error processing AI response:', error)
      throw new Error('Failed to process AI response')
    }
  }

  /**
   * Formats a date string to ISO format (YYYY-MM-DD)
   *
   * @param dateStr Date string in various formats
   * @returns ISO formatted date string or null if invalid
   */
  protected formatDate(dateStr: string | null | undefined): string | null {
    if (!dateStr) return null

    try {
      const date = new Date(dateStr)
      if (isNaN(date.getTime())) return null

      return date.toISOString().split('T')[0]
    } catch (e) {
      return null
    }
  }

  /**
   * Creates a standard system prompt for document analysis
   *
   * @returns System prompt string
   */
  protected getSystemPrompt(): string {
    return `
You are a specialized AI assistant for engineering project management. Your task is to analyze project documents and extract key information in a structured format.

Extract the following fields:
- project_name: The name or title of the project
- description: A summary of the project's purpose and goals
- start_date: The planned start date (format as YYYY-MM-DD)
- end_date: The planned end date (format as YYYY-MM-DD)
- budget: The total budget amount (numeric value only)
- currency: The currency of the budget (USD, EUR, CLP, etc.)
- client_name: The name of the client or customer
- deliverables: An array of key deliverables
- scope: A description of the project scope
- team_requirements: An array of required team roles or skills
- tags: An array of keywords or categories for the project

Format your response as a valid JSON object. If information is not found, use null for that field.
Assign a confidence_score between 0 and 1 for your overall confidence in the extracted information.
`
  }

  /**
   * Creates a standard user prompt for document analysis
   *
   * @param documentText The text content of the document
   * @returns User prompt string
   */
  protected getUserPrompt(documentText: string): string {
    return `Analyze this project document and extract the required information:

${documentText}

Remember to format your response as a valid JSON object with the fields specified in the instructions.`
  }
}
```

## Google Gemini Implementation

Implement the Gemini provider:

```typescript
// web/src/lib/ai-providers/providers/gemini-provider.ts
import { GoogleGenerativeAI } from '@google/generative-ai'
import { BaseAIProvider } from '../base-provider'
import { DocumentAnalysisResult } from '../document-analyzer'

/**
 * Google Gemini AI provider implementation
 */
export class GeminiProvider extends BaseAIProvider {
  private genAI: GoogleGenerativeAI

  constructor(config: { apiKey: string; modelName: string }) {
    super(config)
    this.genAI = new GoogleGenerativeAI(this.apiKey)
  }

  /**
   * Gets the name of the provider
   */
  getProviderName(): string {
    return 'gemini'
  }

  /**
   * Analyzes document text using Google Gemini
   *
   * @param documentText The text content of the document
   * @returns Structured project information
   */
  async analyzeDocument(documentText: string): Promise<DocumentAnalysisResult> {
    try {
      if (!this.validateConfig()) {
        throw new Error('Invalid Gemini configuration')
      }

      // Prepare the model
      const model = this.genAI.getGenerativeModel({
        model: this.modelName || 'gemini-pro'
      })

      // Prepare the prompt
      const systemPrompt = this.getSystemPrompt()
      const userPrompt = this.getUserPrompt(documentText)

      // Generate content
      const result = await model.generateContent({
        contents: [
          { role: 'user', parts: [{ text: systemPrompt }] },
          { role: 'model', parts: [{ text: 'I understand. I will analyze the document and extract the requested information in JSON format.' }] },
          { role: 'user', parts: [{ text: userPrompt }] }
        ],
        generationConfig: {
          temperature: 0.2,
          topP: 0.8,
          topK: 40,
          maxOutputTokens: 1024,
        }
      })

      const response = result.response
      const text = response.text()

      // Extract JSON from response
      let jsonStr = text

      // If the response contains markdown code blocks, extract the JSON
      if (text.includes('```json')) {
        const match = text.match(/```json\s*([\s\S]*?)\s*```/)
        if (match && match[1]) {
          jsonStr = match[1].trim()
        }
      } else if (text.includes('```')) {
        const match = text.match(/```\s*([\s\S]*?)\s*```/)
        if (match && match[1]) {
          jsonStr = match[1].trim()
        }
      }

      // Parse and process the response
      let parsedResponse
      try {
        parsedResponse = JSON.parse(jsonStr)
      } catch (e) {
        console.error('Failed to parse Gemini response as JSON:', e)
        console.log('Raw response:', text)
        throw new Error('Failed to parse Gemini response as JSON')
      }

      return this.processResponse(parsedResponse)
    } catch (error) {
      console.error('Error analyzing document with Gemini:', error)
      throw error
    }
  }
}
```

## OpenAI Implementation

Implement the OpenAI provider:

```typescript
// web/src/lib/ai-providers/providers/openai-provider.ts
import OpenAI from 'openai'
import { BaseAIProvider } from '../base-provider'
import { DocumentAnalysisResult } from '../document-analyzer'

/**
 * OpenAI provider implementation
 */
export class OpenAIProvider extends BaseAIProvider {
  private openai: OpenAI

  constructor(config: { apiKey: string; modelName: string }) {
    super(config)
    this.openai = new OpenAI({
      apiKey: this.apiKey
    })
  }

  /**
   * Gets the name of the provider
   */
  getProviderName(): string {
    return 'openai'
  }

  /**
   * Analyzes document text using OpenAI
   *
   * @param documentText The text content of the document
   * @returns Structured project information
   */
  async analyzeDocument(documentText: string): Promise<DocumentAnalysisResult> {
    try {
      if (!this.validateConfig()) {
        throw new Error('Invalid OpenAI configuration')
      }

      // Prepare the prompt
      const systemPrompt = this.getSystemPrompt()
      const userPrompt = this.getUserPrompt(documentText)

      // Call OpenAI API
      const response = await this.openai.chat.completions.create({
        model: this.modelName || 'gpt-4',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.2,
        response_format: { type: 'json_object' }
      })

      // Extract and parse the response
      const content = response.choices[0]?.message?.content

      if (!content) {
        throw new Error('Empty response from OpenAI')
      }

      let parsedResponse
      try {
        parsedResponse = JSON.parse(content)
      } catch (e) {
        console.error('Failed to parse OpenAI response as JSON:', e)
        console.log('Raw response:', content)
        throw new Error('Failed to parse OpenAI response as JSON')
      }

      return this.processResponse(parsedResponse)
    } catch (error) {
      console.error('Error analyzing document with OpenAI:', error)
      throw error
    }
  }
}
```

## DeepSeek Implementation

Implement the DeepSeek provider:

```typescript
// web/src/lib/ai-providers/providers/deepseek-provider.ts
import { BaseAIProvider } from '../base-provider'
import { DocumentAnalysisResult } from '../document-analyzer'

/**
 * DeepSeek provider implementation
 */
export class DeepSeekProvider extends BaseAIProvider {
  constructor(config: { apiKey: string; modelName: string }) {
    super(config)
  }

  /**
   * Gets the name of the provider
   */
  getProviderName(): string {
    return 'deepseek'
  }

  /**
   * Analyzes document text using DeepSeek
   *
   * @param documentText The text content of the document
   * @returns Structured project information
   */
  async analyzeDocument(documentText: string): Promise<DocumentAnalysisResult> {
    try {
      if (!this.validateConfig()) {
        throw new Error('Invalid DeepSeek configuration')
      }

      // Prepare the prompt
      const systemPrompt = this.getSystemPrompt()
      const userPrompt = this.getUserPrompt(documentText)

      // Call DeepSeek API
      const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model: this.modelName || 'deepseek-chat',
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
          ],
          temperature: 0.2,
          response_format: { type: 'json_object' }
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(`DeepSeek API error: ${errorData.error?.message || response.statusText}`)
      }

      const data = await response.json()
      const content = data.choices?.[0]?.message?.content

      if (!content) {
        throw new Error('Empty response from DeepSeek')
      }

      let parsedResponse
      try {
        parsedResponse = JSON.parse(content)
      } catch (e) {
        console.error('Failed to parse DeepSeek response as JSON:', e)
        console.log('Raw response:', content)
        throw new Error('Failed to parse DeepSeek response as JSON')
      }

      return this.processResponse(parsedResponse)
    } catch (error) {
      console.error('Error analyzing document with DeepSeek:', error)
      throw error
    }
  }
}
```

## Provider Factory

Create a factory to instantiate the appropriate provider:

```typescript
// web/src/lib/ai-providers/provider-factory.ts
import { AIProvider } from './provider-interface'
import { GeminiProvider } from './providers/gemini-provider'
import { OpenAIProvider } from './providers/openai-provider'
import { DeepSeekProvider } from './providers/deepseek-provider'

/**
 * Factory for creating AI provider instances
 */
export class ProviderFactory {
  /**
   * Creates an instance of the appropriate AI provider
   *
   * @param providerName Name of the provider
   * @param config Provider configuration
   * @returns AI provider instance
   */
  static createProvider(providerName: string, config: { apiKey: string; modelName: string }): AIProvider {
    switch (providerName.toLowerCase()) {
      case 'gemini':
        return new GeminiProvider(config)
      case 'openai':
        return new OpenAIProvider(config)
      case 'deepseek':
        return new DeepSeekProvider(config)
      default:
        throw new Error(`Unsupported provider: ${providerName}`)
    }
  }

  /**
   * Gets a list of supported provider names
   *
   * @returns Array of provider names
   */
  static getSupportedProviders(): string[] {
    return ['gemini', 'openai', 'deepseek']
  }
}
```

## Fallback Mechanism

Implement a fallback mechanism to try alternative providers if one fails:

```typescript
// web/src/lib/ai-providers/fallback-analyzer.ts
import { DocumentAnalysisResult, DocumentData } from './document-analyzer'
import { getProviderConfig, getAllProviderConfigs } from './provider-config'
import { extractTextFromDocument } from './document-text-extractor'
import { ProviderFactory } from './provider-factory'

/**
 * Analyzes a document with fallback to alternative providers
 *
 * @param document Document data object
 * @param preferredProvider Name of the preferred provider
 * @returns Analysis result with project information
 */
export async function analyzeDocumentWithFallback(
  document: DocumentData,
  preferredProvider: string = 'gemini'
): Promise<DocumentAnalysisResult> {
  // Extract text from document
  const documentText = await extractTextFromDocument(document)

  if (!documentText || documentText.trim() === '') {
    throw new Error('No text could be extracted from the document')
  }

  // Get all available providers
  const allProviders = await getAllProviderConfigs()

  // Sort providers: preferred provider first, then by priority
  const sortedProviders = allProviders.sort((a, b) => {
    if (a.provider_name === preferredProvider) return -1
    if (b.provider_name === preferredProvider) return 1
    return a.priority - b.priority
  })

  // Try each provider in order
  let lastError = null

  for (const provider of sortedProviders) {
    try {
      console.log(`Attempting analysis with ${provider.provider_name}...`)

      const providerConfig = {
        apiKey: provider.api_key,
        modelName: provider.model_name
      }

      const providerInstance = ProviderFactory.createProvider(
        provider.provider_name,
        providerConfig
      )

      const result = await providerInstance.analyzeDocument(documentText)

      console.log(`Analysis with ${provider.provider_name} successful`)
      return result
    } catch (error) {
      console.error(`Error with ${provider.provider_name}:`, error)
      lastError = error
      // Continue to next provider
    }
  }

  // If all providers failed, throw the last error
  throw lastError || new Error('All providers failed to analyze the document')
}
```

## Document Chunking

Implement document chunking for large documents:

```typescript
// web/src/lib/ai-providers/document-chunker.ts
/**
 * Splits a document into manageable chunks for processing
 *
 * @param text Full document text
 * @param maxChunkSize Maximum size of each chunk in characters
 * @param overlapSize Number of characters to overlap between chunks
 * @returns Array of text chunks
 */
export function chunkDocument(
  text: string,
  maxChunkSize: number = 8000,
  overlapSize: number = 200
): string[] {
  if (!text || text.length <= maxChunkSize) {
    return [text]
  }

  const chunks: string[] = []
  let startIndex = 0

  while (startIndex < text.length) {
    // Calculate end index for this chunk
    let endIndex = startIndex + maxChunkSize

    // If we're not at the end of the text, try to find a good break point
    if (endIndex < text.length) {
      // Look for paragraph breaks, then sentence breaks, then word breaks
      const paragraphBreak = text.lastIndexOf('\n\n', endIndex)
      const sentenceBreak = text.lastIndexOf('. ', endIndex)
      const wordBreak = text.lastIndexOf(' ', endIndex)

      // Use the closest break point that's not too far back
      if (paragraphBreak > startIndex && paragraphBreak > endIndex - 500) {
        endIndex = paragraphBreak + 2 // Include the paragraph break
      } else if (sentenceBreak > startIndex && sentenceBreak > endIndex - 300) {
        endIndex = sentenceBreak + 2 // Include the period and space
      } else if (wordBreak > startIndex && wordBreak > endIndex - 100) {
        endIndex = wordBreak + 1 // Include the space
      }
    }

    // Add this chunk to the result
    chunks.push(text.substring(startIndex, endIndex))

    // Move to the next chunk, accounting for overlap
    startIndex = endIndex - overlapSize

    // Make sure we're making progress
    if (startIndex >= text.length || endIndex <= startIndex) {
      break
    }
  }

  return chunks
}

/**
 * Processes document chunks and combines the results
 *
 * @param chunks Array of document text chunks
 * @param processChunk Function to process each chunk
 * @returns Combined result
 */
export async function processDocumentChunks<T>(
  chunks: string[],
  processChunk: (chunk: string) => Promise<T>
): Promise<T[]> {
  const results: T[] = []

  for (const chunk of chunks) {
    const result = await processChunk(chunk)
    results.push(result)
  }

  return results
}

/**
 * Merges analysis results from multiple chunks
 *
 * @param results Array of analysis results
 * @returns Merged analysis result
 */
export function mergeAnalysisResults(
  results: DocumentAnalysisResult[]
): DocumentAnalysisResult {
  if (results.length === 0) {
    throw new Error('No results to merge')
  }

  if (results.length === 1) {
    return results[0]
  }

  // Find the result with the highest confidence score for basic fields
  const highestConfidenceResult = results.reduce((prev, current) => {
    return (current.confidence_score > prev.confidence_score) ? current : prev
  }, results[0])

  // Merge array fields from all results
  const allDeliverables = results
    .flatMap(r => r.deliverables || [])
    .filter((value, index, self) => value && self.indexOf(value) === index)

  const allTeamRequirements = results
    .flatMap(r => r.team_requirements || [])
    .filter((value, index, self) => value && self.indexOf(value) === index)

  const allTags = results
    .flatMap(r => r.tags || [])
    .filter((value, index, self) => value && self.indexOf(value) === index)

  // Combine scope descriptions if they differ
  const uniqueScopes = results
    .map(r => r.scope)
    .filter((value, index, self) => value && self.indexOf(value) === index)

  const combinedScope = uniqueScopes.length > 1
    ? uniqueScopes.join('\n\n')
    : highestConfidenceResult.scope

  // Return merged result
  return {
    project_name: highestConfidenceResult.project_name,
    description: highestConfidenceResult.description,
    start_date: highestConfidenceResult.start_date,
    end_date: highestConfidenceResult.end_date,
    budget: highestConfidenceResult.budget,
    currency: highestConfidenceResult.currency,
    client_name: highestConfidenceResult.client_name,
    deliverables: allDeliverables.length > 0 ? allDeliverables : null,
    scope: combinedScope,
    team_requirements: allTeamRequirements.length > 0 ? allTeamRequirements : null,
    tags: allTags.length > 0 ? allTags : null,
    confidence_score: highestConfidenceResult.confidence_score
  }
}
```

## Testing

Create tests for the AI providers:

```typescript
// web/src/lib/ai-providers/__tests__/gemini-provider.test.ts
import { GeminiProvider } from '../providers/gemini-provider'
import { GoogleGenerativeAI } from '@google/generative-ai'

// Mock the Google Generative AI library
jest.mock('@google/generative-ai', () => {
  return {
    GoogleGenerativeAI: jest.fn().mockImplementation(() => {
      return {
        getGenerativeModel: jest.fn().mockImplementation(() => {
          return {
            generateContent: jest.fn().mockResolvedValue({
              response: {
                text: jest.fn().mockReturnValue(`{
                  "project_name": "Test Project",
                  "description": "A test project description",
                  "start_date": "2023-08-01",
                  "end_date": "2023-12-31",
                  "budget": "10000",
                  "currency": "USD",
                  "client_name": "Test Client",
                  "deliverables": ["Deliverable 1", "Deliverable 2"],
                  "scope": "Project scope description",
                  "team_requirements": ["Engineer", "Designer"],
                  "tags": ["test", "project"],
                  "confidence_score": 0.85
                }`)
              }
            })
          }
        })
      }
    })
  }
})

describe('GeminiProvider', () => {
  const config = {
    apiKey: 'test-api-key',
    modelName: 'gemini-pro'
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should initialize correctly', () => {
    const provider = new GeminiProvider(config)
    expect(provider).toBeDefined()
    expect(provider.getProviderName()).toBe('gemini')
    expect(GoogleGenerativeAI).toHaveBeenCalledWith('test-api-key')
  })

  it('should validate config correctly', () => {
    const provider = new GeminiProvider(config)
    expect(provider.validateConfig()).toBe(true)

    const invalidProvider = new GeminiProvider({ apiKey: '', modelName: '' })
    expect(invalidProvider.validateConfig()).toBe(false)
  })

  it('should analyze document text correctly', async () => {
    const provider = new GeminiProvider(config)
    const result = await provider.analyzeDocument('Test document content')

    expect(result).toEqual({
      project_name: 'Test Project',
      description: 'A test project description',
      start_date: '2023-08-01',
      end_date: '2023-12-31',
      budget: '10000',
      currency: 'USD',
      client_name: 'Test Client',
      deliverables: ['Deliverable 1', 'Deliverable 2'],
      scope: 'Project scope description',
      team_requirements: ['Engineer', 'Designer'],
      tags: ['test', 'project'],
      confidence_score: 0.85
    })
  })

  // Add more tests for error handling, edge cases, etc.
})
```

## Usage Example

Example of how to use the AI providers in a component:

```typescript
// Example component using the AI providers
import { useState } from 'react'
import { ProviderFactory } from '@/lib/ai-providers/provider-factory'
import { getProviderConfig } from '@/lib/ai-providers/provider-config'
import { extractTextFromDocument } from '@/lib/ai-providers/document-text-extractor'

async function analyzeDocumentWithProvider(document, providerName) {
  try {
    setIsProcessing(true)
    setError(null)

    // Extract text from document
    const documentText = await extractTextFromDocument(document)

    // Get provider configuration
    const providerConfig = await getProviderConfig(providerName)

    if (!providerConfig) {
      throw new Error(`Provider ${providerName} not configured`)
    }

    // Create provider instance
    const provider = ProviderFactory.createProvider(providerName, providerConfig)

    // Analyze document
    const result = await provider.analyzeDocument(documentText)

    setAnalysisResult(result)
    setIsProcessing(false)

    return result
  } catch (error) {
    console.error('Error analyzing document:', error)
    setError(error.message)
    setIsProcessing(false)
    throw error
  }
}
```

## Considerations

1. **Token Limits**
   - Different providers have different token limits
   - Implement chunking for large documents
   - Consider using different models based on document size

2. **Error Handling**
   - Implement robust error handling for each provider
   - Add retry logic for transient errors
   - Provide meaningful error messages to users

3. **Performance**
   - Monitor response times for each provider
   - Consider caching results for similar documents
   - Implement background processing for large documents

4. **Cost Management**
   - Track API usage for each provider
   - Implement rate limiting to prevent excessive costs
   - Consider using cheaper models for initial analysis

5. **Security**
   - Never expose API keys in client-side code
   - Validate all inputs before sending to AI providers
   - Consider data privacy implications when sending documents to external APIs

6. **Extensibility**
   - Design the system to easily add new AI providers
   - Use the provider interface for consistency
   - Implement feature detection for provider-specific capabilities
