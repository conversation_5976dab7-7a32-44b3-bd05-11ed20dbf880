"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Loader2, Plus, Trash2, ExternalLink } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { toast } from "@/components/ui/use-toast"
import { createClient } from "@/lib/supabase/client"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  <PERSON><PERSON><PERSON><PERSON>og<PERSON>oot<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>og<PERSON>rig<PERSON>,
} from "@/components/ui/alert-dialog"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"

// Esquema de validación para el formulario de nueva integración
const newIntegrationSchema = z.object({
  name: z.string().min(2, {
    message: "El nombre debe tener al menos 2 caracteres.",
  }),
  api_key: z.string().min(5, {
    message: "La clave API debe tener al menos 5 caracteres.",
  }),
  api_url: z.string().url({
    message: "Por favor, introduce una URL válida.",
  }),
  is_active: z.boolean().default(true),
})

type NewIntegrationValues = z.infer<typeof newIntegrationSchema>

interface Integration {
  id: string
  name: string
  api_key: string
  api_url: string
  is_active: boolean
  created_at: string
}

interface IntegrationSettingsProps {
  initialData?: Integration[]
  onAddIntegration: (data: NewIntegrationValues) => Promise<void>
  onDeleteIntegration: (id: string) => Promise<void>
  onToggleIntegration: (id: string, isActive: boolean) => Promise<void>
  isLoading?: boolean
}

export function IntegrationSettings({
  initialData = [],
  onAddIntegration,
  onDeleteIntegration,
  onToggleIntegration,
  isLoading = false,
}: IntegrationSettingsProps) {
  const [integrations, setIntegrations] = useState<Integration[]>(initialData)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const form = useForm<NewIntegrationValues>({
    resolver: zodResolver(newIntegrationSchema),
    defaultValues: {
      name: "",
      api_key: "",
      api_url: "",
      is_active: true,
    },
  })

  const handleSubmit = async (data: NewIntegrationValues) => {
    setIsSubmitting(true)
    try {
      await onAddIntegration(data)
      form.reset()
      setIsAddDialogOpen(false)
      toast({
        title: "Integración añadida",
        description: "La integración se ha añadido correctamente.",
      })
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : "Ha ocurrido un error al añadir la integración.";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleDelete = async (id: string) => {
    try {
      await onDeleteIntegration(id)
      setIntegrations(integrations.filter(integration => integration.id !== id))
      toast({
        title: "Integración eliminada",
        description: "La integración se ha eliminado correctamente.",
      })
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : "Ha ocurrido un error al eliminar la integración.";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    }
  }

  const handleToggle = async (id: string, isActive: boolean) => {
    try {
      await onToggleIntegration(id, isActive)
      setIntegrations(integrations.map(integration =>
        integration.id === id ? { ...integration, is_active: isActive } : integration
      ))
      toast({
        title: isActive ? "Integración activada" : "Integración desactivada",
        description: `La integración se ha ${isActive ? "activado" : "desactivado"} correctamente.`,
      })
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : `Ha ocurrido un error al ${isActive ? "activar" : "desactivar"} la integración.`;
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Integraciones de servicios</h3>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" /> Añadir integración
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Añadir nueva integración</DialogTitle>
              <DialogDescription>
                Configura una nueva integración con un servicio externo.
              </DialogDescription>
            </DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nombre</FormLabel>
                      <FormControl>
                        <Input placeholder="Nombre de la integración" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="api_url"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>URL de la API</FormLabel>
                      <FormControl>
                        <Input placeholder="https://api.ejemplo.com" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="api_key"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Clave API</FormLabel>
                      <FormControl>
                        <Input placeholder="Clave API" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="is_active"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Activa</FormLabel>
                        <FormDescription>
                          Activar esta integración inmediatamente.
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsAddDialogOpen(false)}
                  >
                    Cancelar
                  </Button>
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    Añadir
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="space-y-4">
        {integrations.length === 0 ? (
          <Card>
            <CardContent className="pt-6 text-center">
              <p className="text-muted-foreground">
                No hay integraciones configuradas.
              </p>
              <Button
                className="mt-4"
                onClick={() => setIsAddDialogOpen(true)}
                variant="outline"
              >
                <Plus className="mr-2 h-4 w-4" /> Añadir primera integración
              </Button>
            </CardContent>
          </Card>
        ) : (
          integrations.map((integration) => (
            <Card key={integration.id}>
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <CardTitle className="text-base">{integration.name}</CardTitle>
                    {integration.is_active ? (
                      <Badge className="bg-green-500">Activa</Badge>
                    ) : (
                      <Badge variant="outline" className="text-muted-foreground">Inactiva</Badge>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={integration.is_active}
                      onCheckedChange={(checked) => handleToggle(integration.id, checked)}
                      disabled={isLoading}
                    />
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-red-500 hover:text-red-600"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>¿Eliminar esta integración?</AlertDialogTitle>
                          <AlertDialogDescription>
                            Esta acción no se puede deshacer. ¿Estás seguro de que quieres eliminar esta integración?
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancelar</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleDelete(integration.id)}
                            className="bg-red-500 hover:bg-red-600"
                          >
                            Eliminar
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">URL de la API:</span>
                    <a
                      href={integration.api_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center text-blue-500 hover:underline"
                    >
                      {integration.api_url}
                      <ExternalLink className="ml-1 h-3 w-3" />
                    </a>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Clave API:</span>
                    <span className="font-mono">
                      {integration.api_key.substring(0, 4)}...{integration.api_key.substring(integration.api_key.length - 4)}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  )
}
