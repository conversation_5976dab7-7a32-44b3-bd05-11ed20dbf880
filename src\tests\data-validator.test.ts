/**
 * @file Tests for the DataValidatorService
 * @description Unit tests for the data validation and sanitization functions
 */

import { vi } from 'vitest';
import { dataValidator } from '@/lib/services/data-validator-service';
import { jest } from '@jest/globals';
import { validate as validateUUID } from 'uuid';

// Mock the uuid validation function
vi.mock('uuid', () => ({
  validate: vi.fn(),
}));

describe('DataValidatorService', () => {
  beforeEach(() => {
    // Reset the mock before each test
    (validateUUID as jest.Mock).mockReset();
  });

  describe('isValidUUID', () => {
    it('should return false for null or undefined values', () => {
      expect(dataValidator.isValidUUID(null)).toBe(false);
      expect(dataValidator.isValidUUID(undefined)).toBe(false);
    });

    it('should return false for non-string values', () => {
      expect(dataValidator.isValidUUID(123)).toBe(false);
      expect(dataValidator.isValidUUID({})).toBe(false);
      expect(dataValidator.isValidUUID([])).toBe(false);
      expect(dataValidator.isValidUUID(true)).toBe(false);
    });

    it('should call the uuid validate function for string values', () => {
      (validateUUID as jest.Mock).mockReturnValue(true);
      expect(dataValidator.isValidUUID('valid-uuid')).toBe(true);
      expect(validateUUID).toHaveBeenCalledWith('valid-uuid');

      (validateUUID as jest.Mock).mockReturnValue(false);
      expect(dataValidator.isValidUUID('invalid-uuid')).toBe(false);
      expect(validateUUID).toHaveBeenCalledWith('invalid-uuid');
    });
  });

  describe('isEmpty', () => {
    it('should return true for null or undefined values', () => {
      expect(dataValidator.isEmpty(null)).toBe(true);
      expect(dataValidator.isEmpty(undefined)).toBe(true);
    });

    it('should return true for empty strings or strings with only whitespace', () => {
      expect(dataValidator.isEmpty('')).toBe(true);
      expect(dataValidator.isEmpty('   ')).toBe(true);
      expect(dataValidator.isEmpty('\t\n')).toBe(true);
    });

    it('should return true for empty arrays', () => {
      expect(dataValidator.isEmpty([])).toBe(true);
    });

    it('should return true for empty objects', () => {
      expect(dataValidator.isEmpty({})).toBe(true);
    });

    it('should return false for non-empty values', () => {
      expect(dataValidator.isEmpty('text')).toBe(false);
      expect(dataValidator.isEmpty([1, 2, 3])).toBe(false);
      expect(dataValidator.isEmpty({ key: 'value' })).toBe(false);
      expect(dataValidator.isEmpty(0)).toBe(false);
      expect(dataValidator.isEmpty(false)).toBe(false);
    });
  });

  describe('sanitizeUUID', () => {
    it('should return null for null, undefined, or empty string values', () => {
      expect(dataValidator.sanitizeUUID(null)).toBeNull();
      expect(dataValidator.sanitizeUUID(undefined)).toBeNull();
      expect(dataValidator.sanitizeUUID('')).toBeNull();
      expect(dataValidator.sanitizeUUID('   ')).toBeNull();
    });

    it('should return null for invalid UUIDs', () => {
      (validateUUID as jest.Mock).mockReturnValue(false);
      expect(dataValidator.sanitizeUUID('invalid-uuid')).toBeNull();
    });

    it('should return the original value for valid UUIDs', () => {
      const validUUID = '123e4567-e89b-12d3-a456-************';
      (validateUUID as jest.Mock).mockReturnValue(true);
      expect(dataValidator.sanitizeUUID(validUUID)).toBe(validUUID);
    });
  });

  describe('sanitizeObject', () => {
    it('should sanitize specified UUID fields', () => {
      (validateUUID as jest.Mock).mockImplementation((value: unknown) => {
        return value === 'valid-uuid';
      });

      const data = {
        id: 'valid-uuid',
        invalid_id: 'invalid-uuid',
        name: 'Test',
      };

      const sanitized = dataValidator.sanitizeObject(data, ['id', 'invalid_id']);
      
      expect(sanitized.id).toBe('valid-uuid');
      expect(sanitized.invalid_id).toBeNull();
      expect(sanitized.name).toBe('Test');
    });

    it('should automatically detect and sanitize fields ending with _id', () => {
      (validateUUID as jest.Mock).mockImplementation((value: unknown) => {
        return value === 'valid-uuid';
      });

      const data = {
        user_id: 'valid-uuid',
        project_id: 'invalid-uuid',
        name: 'Test',
      };

      const sanitized = dataValidator.sanitizeObject(data);
      
      expect(sanitized.user_id).toBe('valid-uuid');
      expect(sanitized.project_id).toBeNull();
      expect(sanitized.name).toBe('Test');
    });
  });

  describe('validateRequiredFields', () => {
    it('should validate required fields correctly', () => {
      const data = {
        name: 'Test',
        email: '',
        description: null,
      };

      const result = dataValidator.validateRequiredFields(data, ['name', 'email']);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveProperty('email');
      expect(result.errors).not.toHaveProperty('name');
    });

    it('should handle missing fields', () => {
      const data = {
        name: 'Test',
      };

      const result = dataValidator.validateRequiredFields(data, ['name', 'email']);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveProperty('email');
      expect(result.errors).not.toHaveProperty('name');
    });
  });
});
