-- Función para eliminar un proyecto y sus datos relacionados
CREATE OR REPLACE FUNCTION public.delete_project(project_id UUID)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSONB;
  deleted_count INTEGER := 0;
  document_count INTEGER := 0;
  task_count INTEGER := 0;
  order_count INTEGER := 0;
  storage_count INTEGER := 0;
  project_exists BOOLEAN;
BEGIN
  -- Verificar si el proyecto existe
  SELECT EXISTS (
    SELECT 1 FROM public.projects WHERE id = project_id
  ) INTO project_exists;
  
  IF NOT project_exists THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'El proyecto no existe',
      'project_id', project_id
    );
  END IF;
  
  -- Eliminar documentos asociados al proyecto
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'documents') THEN
    WITH deleted AS (
      DELETE FROM public.documents
      WHERE project_id = project_id
      RETURNING id
    )
    SELECT COUNT(*) INTO document_count FROM deleted;
  END IF;
  
  -- Eliminar tareas asociadas al proyecto a través de órdenes de trabajo
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'work_order_tasks') AND
     EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'work_orders') THEN
    WITH deleted AS (
      DELETE FROM public.work_order_tasks
      WHERE work_order_id IN (
        SELECT id FROM public.work_orders WHERE project_id = project_id
      )
      RETURNING id
    )
    SELECT COUNT(*) INTO task_count FROM deleted;
  END IF;
  
  -- Eliminar órdenes de trabajo asociadas al proyecto
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'work_orders') THEN
    WITH deleted AS (
      DELETE FROM public.work_orders
      WHERE project_id = project_id
      RETURNING id
    )
    SELECT COUNT(*) INTO order_count FROM deleted;
  END IF;
  
  -- Eliminar archivos de almacenamiento asociados al proyecto
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'storage' AND table_name = 'objects') THEN
    WITH deleted AS (
      DELETE FROM storage.objects
      WHERE bucket_id = 'projects' AND path LIKE project_id || '/%'
      RETURNING id
    )
    SELECT COUNT(*) INTO storage_count FROM deleted;
  END IF;
  
  -- Finalmente, eliminar el proyecto
  DELETE FROM public.projects
  WHERE id = project_id;
  
  -- Incrementar el contador de eliminados si se eliminó el proyecto
  IF FOUND THEN
    deleted_count := 1;
  END IF;
  
  -- Construir el resultado
  result := jsonb_build_object(
    'success', true,
    'message', 'Proyecto eliminado correctamente',
    'project_id', project_id,
    'deleted_count', deleted_count,
    'related_data', jsonb_build_object(
      'documents', document_count,
      'tasks', task_count,
      'orders', order_count,
      'storage_files', storage_count
    )
  );
  
  RETURN result;
END;
$$;
