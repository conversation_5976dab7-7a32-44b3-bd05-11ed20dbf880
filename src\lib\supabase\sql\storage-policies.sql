-- Función para configurar políticas de seguridad para buckets de almacenamiento
CREATE OR REPLACE FUNCTION configure_storage_policies(bucket_name TEXT)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSONB;
BEGIN
  -- Verificar si el bucket existe
  IF NOT EXISTS (
    SELECT 1 FROM storage.buckets WHERE name = bucket_name
  ) THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Bucket does not exist: ' || bucket_name
    );
  END IF;

  -- Eliminar políticas existentes para el bucket
  DROP POLICY IF EXISTS "Allow authenticated users to read from bucket" ON storage.objects;
  DROP POLICY IF EXISTS "Allow authenticated users to upload to bucket" ON storage.objects;
  DROP POLICY IF EXISTS "Allow authenticated users to update own objects" ON storage.objects;
  DROP POLICY IF EXISTS "Allow authenticated users to delete own objects" ON storage.objects;
  DROP POLICY IF EXISTS "Allow admins full access" ON storage.objects;

  -- Crear política para permitir a usuarios autenticados leer del bucket
  CREATE POLICY "Allow authenticated users to read from bucket"
    ON storage.objects FOR SELECT
    USING (
      auth.role() = 'authenticated' AND
      bucket_id = (SELECT id FROM storage.buckets WHERE name = bucket_name)
    );

  -- Crear política para permitir a usuarios autenticados subir al bucket
  CREATE POLICY "Allow authenticated users to upload to bucket"
    ON storage.objects FOR INSERT
    WITH CHECK (
      auth.role() = 'authenticated' AND
      bucket_id = (SELECT id FROM storage.buckets WHERE name = bucket_name)
    );

  -- Crear política para permitir a usuarios autenticados actualizar sus propios objetos
  CREATE POLICY "Allow authenticated users to update own objects"
    ON storage.objects FOR UPDATE
    USING (
      auth.role() = 'authenticated' AND
      bucket_id = (SELECT id FROM storage.buckets WHERE name = bucket_name) AND
      (owner = auth.uid() OR EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid() AND role = 'admin'
      ))
    );

  -- Crear política para permitir a usuarios autenticados eliminar sus propios objetos
  CREATE POLICY "Allow authenticated users to delete own objects"
    ON storage.objects FOR DELETE
    USING (
      auth.role() = 'authenticated' AND
      bucket_id = (SELECT id FROM storage.buckets WHERE name = bucket_name) AND
      (owner = auth.uid() OR EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid() AND role = 'admin'
      ))
    );

  -- Crear política para permitir a administradores acceso completo
  CREATE POLICY "Allow admins full access"
    ON storage.objects
    USING (
      auth.role() = 'authenticated' AND
      EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid() AND role = 'admin'
      )
    );

  -- Habilitar RLS en la tabla de objetos si no está habilitado
  ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

  RETURN jsonb_build_object(
    'success', true,
    'message', 'Storage policies configured successfully for bucket: ' || bucket_name
  );
END;
$$;

-- Función para crear un bucket si no existe
CREATE OR REPLACE FUNCTION create_storage_bucket_if_not_exists(
  bucket_name TEXT,
  is_public BOOLEAN DEFAULT false,
  file_size_limit BIGINT DEFAULT NULL,
  allowed_mime_types TEXT[] DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  bucket_id UUID;
  result JSONB;
BEGIN
  -- Verificar si el bucket ya existe
  SELECT id INTO bucket_id FROM storage.buckets WHERE name = bucket_name;
  
  IF bucket_id IS NULL THEN
    -- Crear el bucket si no existe
    INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
    VALUES (gen_random_uuid(), bucket_name, is_public, file_size_limit, allowed_mime_types)
    RETURNING id INTO bucket_id;
    
    result := jsonb_build_object(
      'success', true,
      'message', 'Bucket created successfully: ' || bucket_name,
      'bucket_id', bucket_id
    );
  ELSE
    result := jsonb_build_object(
      'success', true,
      'message', 'Bucket already exists: ' || bucket_name,
      'bucket_id', bucket_id
    );
  END IF;
  
  -- Configurar políticas de seguridad para el bucket
  PERFORM configure_storage_policies(bucket_name);
  
  RETURN result;
END;
$$;
