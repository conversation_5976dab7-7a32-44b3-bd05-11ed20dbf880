# AI Document Processing Integration Rules

This document outlines the rules and guidelines for implementing and maintaining the AI document processing integration in the AdminCore  project.

## 🔄 Project Awareness & Context

- **Always read `PLANNING.md`** at the start of a new conversation to understand the project's architecture, goals, style, and constraints.
- **Check `TASK.md`** before starting a new task. If the task isn't listed, add it with a brief description and today's date.
- **Use consistent naming conventions, file structure, and architecture patterns** as described in `PLANNING.md`.
- **Refer to the main project documentation** for overall project guidelines and standards.

## 🧱 Code Structure & Modularity

- **Never create a file longer than 500 lines of code.** If a file approaches this limit, refactor by splitting it into modules or helper files.
- **Organize code into clearly separated modules**, grouped by feature or responsibility:
  - `/lib/ai-providers` - Provider-specific implementations
  - `/components/features/ai-document-processing` - UI components
  - `/api/ai` - API routes for AI functionality
- **Use clear, consistent imports** (prefer relative imports within packages).
- **Create provider-agnostic interfaces** that can be implemented by different AI providers.

## 🧪 Testing & Reliability

- **Always create Jest unit tests for new features** (functions, components, API routes, etc).
- **After updating any logic**, check whether existing unit tests need to be updated. If so, do it.
- **Tests should live in a `__tests__` folder** adjacent to the files being tested.
  - Include at least:
    - 1 test for expected use
    - 1 edge case
    - 1 failure case
- **Mock AI provider APIs** in tests to avoid actual API calls.

## ✅ Task Completion

- **Mark completed tasks in `TASK.md`** immediately after finishing them.
- Add new sub-tasks or TODOs discovered during development to `TASK.md` under a "Discovered During Work" section.
- **Update documentation** when completing significant features or changes.

## 📎 Style & Conventions

- **Use TypeScript** for all new code.
- **Follow the project's existing style conventions**:
  - Use functional components with hooks for React
  - Use the Shadcn/UI component library for UI elements
  - Follow the existing project structure
- **Document all functions and components** with JSDoc comments:
  ```typescript
  /**
   * Analyzes a document using the specified AI provider
   *
   * @param documentId - The ID of the document to analyze
   * @param providerId - The ID of the AI provider to use
   * @returns Analysis result object
   */
  ```
- **Use meaningful variable and function names** that clearly indicate purpose.

## 📚 Documentation & Explainability

- **Update documentation** when adding new features or making significant changes.
- **Comment non-obvious code** and ensure everything is understandable to a mid-level developer.
- When writing complex logic, **add an inline `// Reason:` comment** explaining the why, not just the what.
- **Document AI provider-specific details** including rate limits, token limits, and cost considerations.

## 🔒 Security & Best Practices

- **Never expose API keys in client-side code**.
- **Always validate and sanitize user inputs** before processing.
- **Implement proper error handling** for all API calls and document processing.
- **Follow least privilege principle** for database operations and API access.
- **Implement timeout handling** for long-running AI operations.

## 🧠 AI Provider Integration Rules

- **Create a common interface** that all AI providers must implement.
- **Handle provider-specific errors** and translate them to common error types.
- **Implement fallback mechanisms** to try alternative providers if one fails.
- **Monitor and log usage** to track costs and performance.
- **Cache results when appropriate** to reduce API calls and costs.

## 🚀 Performance Considerations

- **Optimize document processing** for large files.
- **Implement background processing** for time-consuming operations.
- **Use streaming responses** when appropriate for better user experience.
- **Consider implementing a queue system** for handling multiple document processing requests.

## 🌐 Internationalization

- **Support multiple languages** in AI prompts and responses.
- **Consider language-specific processing requirements** when extracting information.
- **Document language limitations** for each AI provider.
