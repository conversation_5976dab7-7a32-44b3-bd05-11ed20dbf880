/**
 * @ai-file-description: "API endpoint for creating projects from AI document analysis"
 * @ai-related-files: ["../analyze-document/route.ts"]
 * @ai-owner: "File-Based Projects"
 */

import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server-client';
import { cookies } from 'next/headers';
import { Database } from '@/lib/supabase/types';

// Define Json type locally as it's not directly exported by @supabase/supabase-js
export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

/**
 * POST handler for creating projects from analysis
 *
 * @ai-responsibility: "Creates projects based on AI document analysis results"
 */
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { analysisId, projectData } = body;

    if (!analysisId && !projectData) {
      return NextResponse.json(
        { error: 'Either analysis ID or project data is required' },
        { status: 400 }
      );
    }

    // Create Supabase client
    const cookieStore = await cookies();
    const supabase = createClient(cookieStore);

    // Get user data for authorization
    // No pasar parámetros a getUser() para compatibilidad con todas las versiones de Supabase
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError || !userData.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    let data: unknown;

    if (analysisId) {
      // Get analysis data
      const { data: analysisData, error: analysisError } = await supabase
        .from('ai_document_analyses')
        .select('*, documents:document_id(*)')
        .eq('id', analysisId)
        .single<Database['public']['Tables']['ai_document_analyses']['Row'] & { documents: Database['public']['Tables']['documents']['Row'] | null }>();

      if (analysisError) {
        console.error('Error fetching ai_document_analyses:', analysisError);
        return NextResponse.json(
          { error: 'Failed to retrieve analysis details.', details: analysisError.message },
          { status: 500 }
        );
      }

      if (!analysisData) {
        return NextResponse.json(
          { error: 'Analysis not found with the provided ID.' },
          { status: 404 }
        );
      }

      if (typeof analysisData.status !== 'string' || analysisData.status !== 'completed') {
        console.error('Invalid or incomplete analysis status:', analysisData.status);
        return NextResponse.json(
          { error: `Analysis status is invalid or not complete. Current status: ${analysisData.status || 'unknown'}` },
          { status: 400 }
        );
      }

      if (!analysisData.analysis_data) {
        console.error('Analysis data (analysis_data property) is missing.');
        return NextResponse.json(
          { error: 'Critical analysis information is missing.' },
          { status: 400 }
        );
      }
      data = analysisData.analysis_data as Json; // Explicitly cast after check

      // Check if user has access to the document
      if (!analysisData.documents || typeof analysisData.documents.uploaded_by !== 'string') {
        console.error('Analysis documents object or uploaded_by is missing/invalid:', analysisData.documents);
        return NextResponse.json(
          { error: 'Invalid document data, cannot verify ownership.' },
          { status: 400 }
        );
      }

      if (analysisData.documents && analysisData.documents.uploaded_by !== userData.user.id) {
        // User is not the uploader, check if they have access via project_users
        if (analysisData.documents && analysisData.documents.project_id && typeof analysisData.documents.project_id === 'string') {
          const { data: projectUserAccess, error: projectUserError } = await supabase
            .from('project_users')
            .select('id', { count: 'exact' }) // Just need to know if a record exists
            .eq('project_id', analysisData.documents.project_id as any)
            .eq('user_id', userData.user.id as any)
            .maybeSingle(); // Use maybeSingle to handle 0 or 1 record gracefully

          if (projectUserError) {
            console.error('Error checking project_users access:', projectUserError);
            return NextResponse.json({ error: 'Failed to verify document access.', details: projectUserError.message }, { status: 500 });
          }

          if (!projectUserAccess) {
            return NextResponse.json(
              { error: 'You do not have access to this document (not uploader, no project access).' },
              { status: 403 }
            );
          }
          // User has access via project_users, proceed.
        } else {
          // No project_id associated with the document, and user is not the uploader
          return NextResponse.json(
            { error: 'You do not have access to this document (not uploader, no project link).' },
            { status: 403 }
          );
        }
      }
      // If we reach here, user is either the uploader or has access via project_users
    } else {
      // Use provided project data
      data = projectData;
    }

    // Crear un proyecto básico primero
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .insert(data as any)
      .select()
      .single<Database['public']['Tables']['projects']['Row']>();

    if (projectError) {
      console.error('Error creating project:', projectError);
      return NextResponse.json(
        { error: 'Failed to create project', details: projectError.message },
        { status: 500 }
      );
    }

    if (!project) {
      console.error('Project data is null after insert, despite no error reported from Supabase.');
      return NextResponse.json(
        { error: 'Failed to create project (internal data error).' },
        { status: 500 }
      );
    }

    // Add current user as project owner
    const { error: userRoleError } = await supabase
      .from('project_users')
      .insert({ project_id: project.id, user_id: userData.user.id, role: 'owner' } as any);

    if (userRoleError) {
      console.error('Error adding user to project:', userRoleError);
      // Continue anyway, as the project was created successfully
    }

    // Guardar los datos completos del análisis en la tabla ai_projects
    const { error: aiProjectError } = await supabase
      .from('ai_projects')
      .insert(data as any);

    if (aiProjectError) {
      console.error('Error saving AI project data:', aiProjectError);
      // Continue anyway, as the basic project was created successfully
    }

    // Create project stages if deliverables are available
    // Add type assertion to ensure TypeScript recognizes the structure
    const typedData = data as { deliverables?: string[] };
    if (typedData.deliverables && Array.isArray(typedData.deliverables) && typedData.deliverables.length > 0) {
      const stages = typedData.deliverables.map((deliverable: string, index: number) => ({
        project_id: project.id,
        name: deliverable,
        description: `Stage ${index + 1}: ${deliverable}`,
        stage_order: index,
        completed: false
      }));

      const { error: stagesError } = await supabase
        .from('project_stages')
        .insert(stages as any);

      if (stagesError) {
        console.error('Error creating project stages:', stagesError);
        // Continue anyway, as the project was created successfully
      }
    }

    return NextResponse.json({
      id: project!.id, // Assert project is not null
      name: project!.name, // Assert project is not null
      message: 'Project created successfully'
    });
  } catch (error) {
    console.error('Unexpected error creating project from analysis:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
