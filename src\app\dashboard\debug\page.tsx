import { DashboardShell } from "@/components/shared/layout/dashboard-shell"
import { SessionDebugger } from "@/components/debug/session-debugger"
import { Separator } from "@/components/ui/separator"

export const metadata = {
  title: "Depuración | AdminCore ",
  description: "Herramientas de depuración para AdminCore ",
}

export default function DebugPage() {
  return (
    <DashboardShell
      heading="Herramientas de Depuración"
      description="Herramientas para diagnosticar problemas en la aplicación"
    >
      <div className="space-y-6">
        <div>
          <h2 className="text-xl font-semibold">Depuración de Sesión</h2>
          <p className="text-muted-foreground">
            Verifica el estado de la sesión y la autenticación
          </p>
        </div>
        <Separator />
        <SessionDebugger />
      </div>
    </DashboardShell>
  )
}
