"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, CheckCircle2, RefreshCw, Database, ShieldAlert, FileText, User, Clock } from "lucide-react"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { projectsFixService } from "@/lib/services/fix-projects-listing"
import { toast } from "@/hooks/use-toast"
import { useRouter } from "next/navigation"
import { createClient } from "@/lib/supabase/client"

interface ProjectsFixDialogProps {
  trigger?: React.ReactNode
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

/**
 * Componente para diagnosticar y corregir problemas de listado de proyectos
 */
export function ProjectsFixDialog({
  trigger,
  open: controlledOpen,
  onOpenChange: setControlledOpen
}: ProjectsFixDialogProps) {
  const [open, setOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [diagnosisResult, setDiagnosisResult] = useState<any>(null)
  const [fixApplied, setFixApplied] = useState(false)
  const [sessionData, setSessionData] = useState<any>(null)
  const router = useRouter()

  // Manejar estado controlado vs no controlado
  const isControlled = controlledOpen !== undefined && setControlledOpen !== undefined
  const isOpen = isControlled ? controlledOpen : open
  const setIsOpen = isControlled ? setControlledOpen : setOpen

  // Realizar diagnóstico al abrir el diálogo
  useEffect(() => {
    if (isOpen && !diagnosisResult) {
      runDiagnosis()
    }
  }, [isOpen, diagnosisResult])

  // Función para ejecutar el diagnóstico
  const runDiagnosis = async () => {
    setIsLoading(true)
    try {
      // Verificar la sesión primero
      const supabase = createClient();
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      setSessionData(sessionData);

      if (sessionError) {
        console.error("Error al verificar sesión:", sessionError);
        setDiagnosisResult({
          success: false,
          message: "Error al verificar la sesión. Esto puede causar problemas al cargar proyectos.",
          details: {
            projectsInDb: 0,
            projectsVisible: 0,
            missingFields: [],
            roleIssues: true,
            accessIssues: true,
            structuralIssues: false
          }
        });
        return;
      }

      if (!sessionData?.session) {
        console.warn("No hay sesión activa");
        setDiagnosisResult({
          success: false,
          message: "No hay una sesión activa. Inicie sesión nuevamente para ver los proyectos.",
          details: {
            projectsInDb: 0,
            projectsVisible: 0,
            missingFields: [],
            roleIssues: false,
            accessIssues: true,
            structuralIssues: false
          }
        });
        return;
      }

      // Si la sesión es válida, continuar con el diagnóstico normal
      const result = await projectsFixService.diagnoseProjectsListing();
      setDiagnosisResult(result);
    } catch (error) {
      console.error("Error al ejecutar diagnóstico:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "No se pudo completar el diagnóstico"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Función para aplicar la corrección
  const applyFix = async () => {
    setIsLoading(true)
    try {
      // Verificar si hay problemas de sesión
      if (diagnosisResult?.details?.hasValidSession === false) {
        // Intentar refrescar la sesión
        const supabase = createClient();

        try {
          // Intentar refrescar la sesión
          const { data, error } = await supabase.auth.refreshSession();

          if (error) {
            console.error("Error al refrescar sesión:", error);
            toast({
              variant: "destructive",
              title: "Error de sesión",
              description: "No se pudo refrescar la sesión. Intente cerrar sesión y volver a iniciarla."
            });

            // Actualizar el diagnóstico con el error
            setDiagnosisResult({
              ...diagnosisResult,
              message: "Error al refrescar la sesión: " + (error instanceof Error ? error.message : 'Error desconocido'),
              fixApplied: true
            });

            setFixApplied(true);
            return;
          }

          if (data?.session) {
            console.log("Sesión refrescada exitosamente");
            toast({
              title: "Sesión restaurada",
              description: "La sesión ha sido refrescada correctamente"
            });

            // Actualizar el diagnóstico
            setDiagnosisResult({
              success: true,
              message: "Sesión refrescada exitosamente. Recargue la página para ver los proyectos.",
              details: {
                ...diagnosisResult.details,
                hasValidSession: true,
                sessionExpiry: data.session.expires_at ?
                  new Date(data.session.expires_at * 1000).toLocaleString() : 'Desconocido'
              },
              fixApplied: true
            });

            setFixApplied(true);
            return;
          }
        } catch (refreshError) {
          console.error("Error inesperado al refrescar sesión:", refreshError);
        }
      }

      // Continuar con la corrección normal de proyectos
      const result = await projectsFixService.fixProjectsListing();
      setDiagnosisResult(result);
      setFixApplied(true);

      toast({
        title: "Corrección aplicada",
        description: "Se han corregido los problemas detectados"
      });
    } catch (error) {
      console.error("Error al aplicar corrección:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "No se pudo aplicar la corrección"
      });
    } finally {
      setIsLoading(false);
    }
  }

  // Función para recargar la página
  const reloadPage = () => {
    router.refresh()
    setIsOpen(false)
    setDiagnosisResult(null)
    setFixApplied(false)
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Diagnóstico de proyectos</DialogTitle>
          <DialogDescription>
            Herramienta para diagnosticar y corregir problemas con el listado de proyectos
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="py-6 flex flex-col items-center justify-center">
            <RefreshCw className="h-8 w-8 animate-spin text-primary mb-4" />
            <p className="text-center text-sm text-muted-foreground">
              {fixApplied ? "Aplicando correcciones..." : "Analizando proyectos..."}
            </p>
          </div>
        ) : diagnosisResult ? (
          <div className="space-y-4">
            <Alert variant={diagnosisResult.success ? "default" : "destructive"}>
              {diagnosisResult.success ? (
                <CheckCircle2 className="h-4 w-4" />
              ) : (
                <AlertCircle className="h-4 w-4" />
              )}
              <AlertTitle>
                {diagnosisResult.success ? "Diagnóstico completado" : "Error en diagnóstico"}
              </AlertTitle>
              <AlertDescription>{diagnosisResult.message}</AlertDescription>
            </Alert>

            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Proyectos en base de datos:</span>
                <Badge variant="outline">{diagnosisResult.details.projectsInDb}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Proyectos visibles:</span>
                <Badge variant="outline">{diagnosisResult.details.projectsVisible}</Badge>
              </div>

              <Separator className="my-2" />

              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium mb-2">Estado de la sesión:</h4>
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        Sesión válida: {sessionData?.session ? '✅ Sí' : '❌ No'}
                      </span>
                    </div>
                    {sessionData?.session?.expires_at && (
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">
                          Expiración: {new Date(sessionData.session.expires_at * 1000).toLocaleString()}
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium mb-2">Problemas detectados:</h4>
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <Database className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        Campos faltantes: {diagnosisResult.details.missingFields?.length > 0 ?
                          diagnosisResult.details.missingFields.join(', ') : 'Ninguno'}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <ShieldAlert className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        Problemas de roles: {diagnosisResult.details.roleIssues ? 'Sí' : 'No'}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <AlertCircle className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        Problemas de acceso: {diagnosisResult.details.accessIssues ? 'Sí' : 'No'}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        Problemas estructurales: {diagnosisResult.details.structuralIssues ? 'Sí' : 'No'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="py-6 text-center text-muted-foreground">
            No hay resultados de diagnóstico disponibles
          </div>
        )}

        <DialogFooter className="flex flex-col sm:flex-row gap-2">
          {diagnosisResult?.fixApplied ? (
            <Button onClick={reloadPage} className="w-full">
              Recargar página
            </Button>
          ) : (
            <>
              <Button variant="outline" onClick={runDiagnosis} disabled={isLoading} className="w-full sm:w-auto">
                <RefreshCw className="mr-2 h-4 w-4" />
                Actualizar diagnóstico
              </Button>
              <Button
                onClick={applyFix}
                disabled={isLoading || !diagnosisResult?.success}
                className="w-full sm:w-auto"
              >
                Aplicar corrección
              </Button>
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
