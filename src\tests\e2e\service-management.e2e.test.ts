import { test, expect } from '@playwright/test';

// Datos de prueba
const testUser = {
  email: '<EMAIL>',
  password: 'password123'
};

test.describe('Service Management E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navegar a la página de inicio de sesión
    await page.goto('/login');
    
    // Iniciar sesión
    await page.fill('input[name="email"]', testUser.email);
    await page.fill('input[name="password"]', testUser.password);
    await page.click('button[type="submit"]');
    
    // Esperar a que se redirija al dashboard
    await page.waitForURL('/dashboard');
  });

  test('should navigate to service requests page', async ({ page }) => {
    // Navegar a la página de solicitudes de servicio
    await page.click('a[href="/dashboard/service-requests"]');
    
    // Verificar que se cargó la página correcta
    await expect(page).toHaveURL('/dashboard/service-requests');
    await expect(page.locator('h1')).toContainText('Solicitudes de Servicio');
  });

  test('should create a new service request', async ({ page }) => {
    // Navegar a la página de solicitudes de servicio
    await page.goto('/dashboard/service-requests');
    
    // Hacer clic en el botón "Nueva Solicitud"
    await page.click('button:has-text("Nueva Solicitud")');
    
    // Verificar que se cargó el formulario
    await expect(page).toHaveURL('/dashboard/service-requests/new');
    
    // Llenar el formulario
    await page.fill('input[name="title"]', 'E2E Test Service Request');
    await page.fill('textarea[name="description"]', 'This is an E2E test service request');
    await page.selectOption('select[name="client_id"]', { label: 'Test Client' });
    await page.selectOption('select[name="priority"]', { value: 'medium' });
    
    // Enviar el formulario
    await page.click('button[type="submit"]');
    
    // Verificar que se creó la solicitud y se redirigió a la página de detalles
    await page.waitForURL(/\/dashboard\/service-requests\/[a-zA-Z0-9-]+/);
    await expect(page.locator('h1')).toContainText('E2E Test Service Request');
  });

  test('should view service request details', async ({ page }) => {
    // Navegar a la página de solicitudes de servicio
    await page.goto('/dashboard/service-requests');
    
    // Hacer clic en la primera solicitud de la lista
    await page.click('table tbody tr:first-child');
    
    // Verificar que se cargó la página de detalles
    await page.waitForURL(/\/dashboard\/service-requests\/[a-zA-Z0-9-]+/);
    
    // Verificar que se muestran los detalles
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('text=Detalles de la Solicitud')).toBeVisible();
  });

  test('should update service request status', async ({ page }) => {
    // Navegar a la página de solicitudes de servicio
    await page.goto('/dashboard/service-requests');
    
    // Hacer clic en la primera solicitud de la lista
    await page.click('table tbody tr:first-child');
    
    // Verificar que se cargó la página de detalles
    await page.waitForURL(/\/dashboard\/service-requests\/[a-zA-Z0-9-]+/);
    
    // Hacer clic en el botón "Editar"
    await page.click('button:has-text("Editar")');
    
    // Verificar que se cargó el formulario de edición
    await expect(page.locator('text=Editar Solicitud de Servicio')).toBeVisible();
    
    // Cambiar el estado
    await page.selectOption('select[name="status"]', { value: 'in_progress' });
    
    // Guardar cambios
    await page.click('button[type="submit"]');
    
    // Verificar que se actualizó el estado
    await expect(page.locator('text=En progreso')).toBeVisible();
  });

  test('should add a service activity', async ({ page }) => {
    // Navegar a la página de solicitudes de servicio
    await page.goto('/dashboard/service-requests');
    
    // Hacer clic en la primera solicitud de la lista
    await page.click('table tbody tr:first-child');
    
    // Verificar que se cargó la página de detalles
    await page.waitForURL(/\/dashboard\/service-requests\/[a-zA-Z0-9-]+/);
    
    // Hacer clic en el botón "Agregar actividad"
    await page.click('button:has-text("Agregar actividad")');
    
    // Verificar que se cargó el formulario
    await expect(page.locator('text=Nueva Actividad de Servicio')).toBeVisible();
    
    // Llenar el formulario
    await page.fill('input[name="description"]', 'E2E Test Activity');
    await page.selectOption('select[name="activity_type"]', { value: 'maintenance' });
    
    // Enviar el formulario
    await page.click('button[type="submit"]');
    
    // Verificar que se creó la actividad
    await expect(page.locator('text=E2E Test Activity')).toBeVisible();
  });

  test('should navigate to customer equipment page', async ({ page }) => {
    // Navegar a la página de equipos de clientes
    await page.click('a[href="/dashboard/customer-equipment"]');
    
    // Verificar que se cargó la página correcta
    await expect(page).toHaveURL('/dashboard/customer-equipment');
    await expect(page.locator('h1')).toContainText('Equipos de Clientes');
  });

  test('should create a new customer equipment', async ({ page }) => {
    // Navegar a la página de equipos de clientes
    await page.goto('/dashboard/customer-equipment');
    
    // Hacer clic en el botón "Nuevo Equipo"
    await page.click('button:has-text("Nuevo Equipo")');
    
    // Verificar que se cargó el formulario
    await expect(page).toHaveURL('/dashboard/customer-equipment/new');
    
    // Llenar el formulario
    await page.fill('input[name="name"]', 'E2E Test Equipment');
    await page.fill('input[name="model"]', 'Test Model');
    await page.fill('input[name="serial_number"]', 'SN-E2E-TEST');
    await page.selectOption('select[name="client_id"]', { label: 'Test Client' });
    
    // Enviar el formulario
    await page.click('button[type="submit"]');
    
    // Verificar que se creó el equipo y se redirigió a la página de detalles
    await page.waitForURL(/\/dashboard\/customer-equipment\/[a-zA-Z0-9-]+/);
    await expect(page.locator('h1')).toContainText('E2E Test Equipment');
  });

  test('should navigate to service contracts page', async ({ page }) => {
    // Navegar a la página de contratos de servicio
    await page.click('a[href="/dashboard/service-contracts"]');
    
    // Verificar que se cargó la página correcta
    await expect(page).toHaveURL('/dashboard/service-contracts');
    await expect(page.locator('h1')).toContainText('Contratos de Servicio');
  });

  test('should create a new service contract', async ({ page }) => {
    // Navegar a la página de contratos de servicio
    await page.goto('/dashboard/service-contracts');
    
    // Hacer clic en el botón "Nuevo Contrato"
    await page.click('button:has-text("Nuevo Contrato")');
    
    // Verificar que se cargó el formulario
    await expect(page).toHaveURL('/dashboard/service-contracts/new');
    
    // Llenar el formulario
    await page.fill('input[name="title"]', 'E2E Test Contract');
    await page.selectOption('select[name="client_id"]', { label: 'Test Client' });
    await page.selectOption('select[name="contract_type"]', { value: 'standard' });
    await page.fill('input[name="start_date"]', new Date().toISOString().split('T')[0]);
    
    // Calcular fecha de fin (1 año después)
    const endDate = new Date();
    endDate.setFullYear(endDate.getFullYear() + 1);
    await page.fill('input[name="end_date"]', endDate.toISOString().split('T')[0]);
    
    // Enviar el formulario
    await page.click('button[type="submit"]');
    
    // Verificar que se creó el contrato y se redirigió a la página de detalles
    await page.waitForURL(/\/dashboard\/service-contracts\/[a-zA-Z0-9-]+/);
    await expect(page.locator('h1')).toContainText('E2E Test Contract');
  });

  test('should add an item to a service contract', async ({ page }) => {
    // Navegar a la página de contratos de servicio
    await page.goto('/dashboard/service-contracts');
    
    // Hacer clic en el primer contrato de la lista
    await page.click('table tbody tr:first-child');
    
    // Verificar que se cargó la página de detalles
    await page.waitForURL(/\/dashboard\/service-contracts\/[a-zA-Z0-9-]+/);
    
    // Hacer clic en el botón "Agregar elemento"
    await page.click('button:has-text("Agregar elemento")');
    
    // Verificar que se cargó el formulario
    await expect(page.locator('text=Nuevo Elemento de Contrato')).toBeVisible();
    
    // Llenar el formulario
    await page.selectOption('select[name="item_type"]', { value: 'service' });
    await page.fill('input[name="description"]', 'E2E Test Contract Item');
    
    // Enviar el formulario
    await page.click('button[type="submit"]');
    
    // Verificar que se creó el elemento
    await expect(page.locator('text=E2E Test Contract Item')).toBeVisible();
  });

  test('should check if a service request is covered by a contract', async ({ page }) => {
    // Navegar a la página de solicitudes de servicio
    await page.goto('/dashboard/service-requests');
    
    // Hacer clic en la primera solicitud de la lista
    await page.click('table tbody tr:first-child');
    
    // Verificar que se cargó la página de detalles
    await page.waitForURL(/\/dashboard\/service-requests\/[a-zA-Z0-9-]+/);
    
    // Hacer clic en el botón "Verificar cobertura"
    await page.click('button:has-text("Verificar cobertura")');
    
    // Verificar que se muestra la información de cobertura
    await expect(page.locator('text=Información de Cobertura')).toBeVisible();
  });

  test('should navigate to mobile technician view', async ({ page }) => {
    // Navegar a la vista móvil del técnico
    await page.goto('/mobile/technician');
    
    // Verificar que se cargó la página correcta
    await expect(page.locator('text=Panel del Técnico')).toBeVisible();
    
    // Verificar que se muestran las pestañas
    await expect(page.locator('button:has-text("Hoy")')).toBeVisible();
    await expect(page.locator('button:has-text("Asignadas")')).toBeVisible();
  });

  test('should complete a service activity in mobile view', async ({ page }) => {
    // Navegar a la vista móvil del técnico
    await page.goto('/mobile/technician');
    
    // Hacer clic en la pestaña "Hoy"
    await page.click('button:has-text("Hoy")');
    
    // Si hay actividades, hacer clic en el botón "Iniciar" de la primera
    const startButton = page.locator('button:has-text("Iniciar")').first();
    if (await startButton.isVisible()) {
      await startButton.click();
      
      // Verificar que se cargó la página de inicio de actividad
      await expect(page.locator('text=Iniciar Actividad')).toBeVisible();
      
      // Llenar el formulario
      await page.fill('textarea[name="notes"]', 'E2E Test Activity Start');
      
      // Enviar el formulario
      await page.click('button[type="submit"]');
      
      // Verificar que se actualizó el estado
      await expect(page.locator('text=En progreso')).toBeVisible();
      
      // Hacer clic en el botón "Completar"
      await page.click('button:has-text("Completar")');
      
      // Verificar que se cargó la página de completar actividad
      await expect(page.locator('text=Completar Actividad')).toBeVisible();
      
      // Llenar el formulario
      await page.fill('textarea[name="notes"]', 'E2E Test Activity Completion');
      
      // Enviar el formulario
      await page.click('button[type="submit"]');
      
      // Verificar que se actualizó el estado
      await expect(page.locator('text=Completada')).toBeVisible();
    }
  });
});
