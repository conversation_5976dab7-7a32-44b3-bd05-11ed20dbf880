export type ProjectStatus = 'pending' | 'in_progress' | 'completed' | 'cancelled'

export type Currency = 'USD' | 'CLP';

export interface Project {
  id: string
  name: string
  description: string | null
  status: ProjectStatus
  start_date?: string | null
  end_date?: string | null
  created_at: string
  updated_at: string
  owner_id: string
  created_by?: string | null
  contract_id?: string | null
  progress_percent?: number
  budget_utilization?: number
  client?: string | null
  client_id?: string | null
  budget?: number | null
  currency?: Currency
  priority?: string
  estimated_hours?: number
  actual_hours?: number
  tags?: string[]
  ai_generated?: boolean | null
  source_document_id?: string | null
  ai_provider?: string | null
  root_path?: string | null
  project_type?: string | null
  // Relaciones
  project_stages?: ProjectStage[]
  project_users?: ProjectUser[]
  work_orders?: WorkOrder[]
  documents?: Document[]
}

export function getStatusColor(status: ProjectStatus) {
  switch (status) {
    case 'pending':
      return 'warning'
    case 'in_progress':
      return 'info'
    case 'completed':
      return 'success'
    case 'cancelled':
      return 'destructive'
    default:
      return 'secondary'
  }
}

export function getStatusText(status: ProjectStatus) {
  switch (status) {
    case 'pending':
      return 'Pendiente'
    case 'in_progress':
      return 'En Progreso'
    case 'completed':
      return 'Completado'
    case 'cancelled':
      return 'Cancelado'
    default:
      return status
  }
}

export interface ProjectStage {
  id: string
  project_id: string
  name: string
  description?: string | null
  stage_order: number
  completed: boolean
  created_at?: string
  updated_at?: string
}

export interface ProjectUser {
  project_id: string
  user_id: string
  role: string
  user: {
    id: string
    email: string
    first_name?: string | null
    last_name?: string | null
  }
}

export interface WorkOrder {
  id: string
  project_id?: string | null
  title: string
  description?: string | null
  priority?: string | null
  status?: string | null
  assigned_to?: string | null
  due_date?: string | null
  created_at?: string
  updated_at?: string
}

export interface Document {
  id: string
  filename: string
  file_url: string
  upload_date?: string
  uploaded_by?: string | null
  project_id?: string | null
  work_order_id?: string | null
  description?: string | null
  version_chain?: string | null
  version_date?: string
  current_version_id?: string | null
}

export interface Client {
  id: string
  name: string
  contact_name?: string | null
  contact_email?: string | null
  contact_phone?: string | null
  address?: string | null
  notes?: string | null
  created_at?: string
  updated_at?: string
}