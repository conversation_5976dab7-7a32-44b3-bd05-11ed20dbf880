import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ServiceRequest, ServiceActivity } from '@/lib/types/service-management';
import { ServiceManagementService } from '@/lib/services/service-management-service';
import { formatDate, formatTime } from '@/lib/utils';
import { useRouter } from 'next/navigation';
import { 
  Clock, 
  Calendar, 
  MapPin, 
  CheckCircle2,
  XCircle,
  AlertTriangle,
  ArrowUpRight,
  User,
  Building,
  Wrench,
  ClipboardList,
  RefreshCw,
  Wifi,
  WifiOff
} from 'lucide-react';
import { useOfflineStorage } from '@/lib/hooks/use-offline-storage';

interface TechnicianDashboardProps {
  technicianId: string;
}

export function TechnicianDashboard({ technicianId }: TechnicianDashboardProps) {
  const [assignedRequests, setAssignedRequests] = useState<ServiceRequest[]>([]);
  const [todayActivities, setTodayActivities] = useState<ServiceActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isOnline, setIsOnline] = useState<boolean>(navigator.onLine);
  const router = useRouter();
  const { saveOfflineData, getOfflineData, hasOfflineData, syncOfflineData } = useOfflineStorage();

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Si está offline, intentar cargar datos guardados
        if (!isOnline && hasOfflineData('technicianDashboard')) {
          const offlineData = getOfflineData('technicianDashboard');
          if (offlineData) {
            setAssignedRequests(offlineData.assignedRequests || []);
            setTodayActivities(offlineData.todayActivities || []);
            setError(null);
            setLoading(false);
            return;
          }
        }
        
        // Si está online o no hay datos offline, cargar desde la API
        const [requestsData, activitiesData] = await Promise.all([
          ServiceManagementService.getAllServiceRequests(100, 0),
          ServiceManagementService.getServiceActivities(null, null, technicianId, null, 100, 0)
        ]);
        
        // Filtrar solicitudes asignadas al técnico
        const filteredRequests = requestsData.filter(req => req.assigned_to === technicianId);
        setAssignedRequests(filteredRequests);
        
        // Filtrar actividades para hoy
        const today = new Date().toISOString().split('T')[0];
        const filteredActivities = activitiesData.filter(act => {
          if (!act.start_time) return false;
          return act.start_time.split('T')[0] === today;
        });
        setTodayActivities(filteredActivities);
        
        // Guardar datos para uso offline
        saveOfflineData('technicianDashboard', {
          assignedRequests: filteredRequests,
          todayActivities: filteredActivities,
          timestamp: new Date().toISOString()
        });
        
        setError(null);
      } catch (err) {
        console.error('Error al cargar datos del técnico:', err);
        setError('Error al cargar datos. Intente nuevamente.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [technicianId, isOnline, saveOfflineData, getOfflineData, hasOfflineData]);

  const handleSync = async () => {
    if (!isOnline) {
      alert('No hay conexión a internet. Conéctese para sincronizar datos.');
      return;
    }
    
    try {
      setLoading(true);
      await syncOfflineData();
      // Recargar datos después de sincronizar
      const [requestsData, activitiesData] = await Promise.all([
        ServiceManagementService.getAllServiceRequests(100, 0),
        ServiceManagementService.getServiceActivities(null, null, technicianId, null, 100, 0)
      ]);
      
      const filteredRequests = requestsData.filter(req => req.assigned_to === technicianId);
      setAssignedRequests(filteredRequests);
      
      const today = new Date().toISOString().split('T')[0];
      const filteredActivities = activitiesData.filter(act => {
        if (!act.start_time) return false;
        return act.start_time.split('T')[0] === today;
      });
      setTodayActivities(filteredActivities);
      
      alert('Datos sincronizados correctamente');
    } catch (err) {
      console.error('Error al sincronizar datos:', err);
      alert('Error al sincronizar datos. Intente nuevamente.');
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string, isOverdue?: boolean) => {
    switch (status) {
      case 'pending':
        return (
          <Badge variant={isOverdue ? "destructive" : "outline"} className="flex gap-1 items-center">
            {isOverdue ? <AlertTriangle className="h-3 w-3" /> : <Clock className="h-3 w-3" />}
            {isOverdue ? 'Vencida' : 'Pendiente'}
          </Badge>
        );
      case 'assigned':
        return (
          <Badge variant="secondary" className="flex gap-1 items-center">
            <User className="h-3 w-3" />
            Asignada
          </Badge>
        );
      case 'in_progress':
        return (
          <Badge variant="default" className="flex gap-1 items-center">
            <ArrowUpRight className="h-3 w-3" />
            En progreso
          </Badge>
        );
      case 'completed':
        return (
          <Badge variant="success" className="flex gap-1 items-center">
            <CheckCircle2 className="h-3 w-3" />
            Completada
          </Badge>
        );
      case 'cancelled':
        return (
          <Badge variant="destructive" className="flex gap-1 items-center">
            <XCircle className="h-3 w-3" />
            Cancelada
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            {status}
          </Badge>
        );
    }
  };

  const getActivityStatusBadge = (status: string) => {
    switch (status) {
      case 'scheduled':
        return (
          <Badge variant="outline" className="flex gap-1 items-center">
            <Calendar className="h-3 w-3" />
            Programada
          </Badge>
        );
      case 'in_progress':
        return (
          <Badge variant="default" className="flex gap-1 items-center">
            <ArrowUpRight className="h-3 w-3" />
            En progreso
          </Badge>
        );
      case 'completed':
        return (
          <Badge variant="success" className="flex gap-1 items-center">
            <CheckCircle2 className="h-3 w-3" />
            Completada
          </Badge>
        );
      case 'cancelled':
        return (
          <Badge variant="destructive" className="flex gap-1 items-center">
            <XCircle className="h-3 w-3" />
            Cancelada
          </Badge>
        );
      case 'pending':
        return (
          <Badge variant="secondary" className="flex gap-1 items-center">
            <Clock className="h-3 w-3" />
            Pendiente
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            {status}
          </Badge>
        );
    }
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-bold">Panel del Técnico</h2>
          <Badge variant={isOnline ? "success" : "destructive"} className="flex gap-1 items-center">
            {isOnline ? <Wifi className="h-3 w-3" /> : <WifiOff className="h-3 w-3" />}
            {isOnline ? 'En línea' : 'Sin conexión'}
          </Badge>
        </div>
        <div className="space-y-2">
          {Array(3).fill(0).map((_, i) => (
            <Skeleton key={i} className="h-32 w-full" />
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-bold">Panel del Técnico</h2>
          <Badge variant={isOnline ? "success" : "destructive"} className="flex gap-1 items-center">
            {isOnline ? <Wifi className="h-3 w-3" /> : <WifiOff className="h-3 w-3" />}
            {isOnline ? 'En línea' : 'Sin conexión'}
          </Badge>
        </div>
        <Card>
          <CardContent className="pt-6">
            <div className="bg-destructive/10 p-4 rounded-md text-destructive">
              {error}
            </div>
            <Button 
              variant="outline" 
              className="mt-4 w-full"
              onClick={() => window.location.reload()}
            >
              Reintentar
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold">Panel del Técnico</h2>
        <div className="flex gap-2 items-center">
          <Badge variant={isOnline ? "success" : "destructive"} className="flex gap-1 items-center">
            {isOnline ? <Wifi className="h-3 w-3" /> : <WifiOff className="h-3 w-3" />}
            {isOnline ? 'En línea' : 'Sin conexión'}
          </Badge>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleSync}
            disabled={!isOnline}
          >
            <RefreshCw className="h-3 w-3 mr-1" />
            Sincronizar
          </Button>
        </div>
      </div>

      <Tabs defaultValue="today" className="w-full">
        <TabsList className="mb-4 w-full">
          <TabsTrigger value="today" className="flex-1">Hoy</TabsTrigger>
          <TabsTrigger value="assigned" className="flex-1">Asignadas</TabsTrigger>
        </TabsList>
        
        <TabsContent value="today" className="mt-0 space-y-4">
          <h3 className="text-lg font-medium">Actividades para hoy</h3>
          
          {todayActivities.length === 0 ? (
            <Card>
              <CardContent className="pt-6 text-center text-muted-foreground">
                No hay actividades programadas para hoy.
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {todayActivities.map((activity) => (
                <Card key={activity.id} className="overflow-hidden">
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-base">{activity.description}</CardTitle>
                      {getActivityStatusBadge(activity.status)}
                    </div>
                    <CardDescription>
                      {activity.activity_type} - {activity.service_request_title}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pb-2">
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      {activity.equipment_name && (
                        <div className="flex items-center gap-1">
                          <ClipboardList className="h-3 w-3 text-muted-foreground" />
                          <span>{activity.equipment_name}</span>
                        </div>
                      )}
                      {activity.client_name && (
                        <div className="flex items-center gap-1">
                          <Building className="h-3 w-3 text-muted-foreground" />
                          <span>{activity.client_name}</span>
                        </div>
                      )}
                      {activity.start_time && (
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3 text-muted-foreground" />
                          <span>{formatTime(activity.start_time)}</span>
                        </div>
                      )}
                      {activity.location_coordinates && (
                        <div className="flex items-center gap-1">
                          <MapPin className="h-3 w-3 text-muted-foreground" />
                          <span>Ver ubicación</span>
                        </div>
                      )}
                    </div>
                  </CardContent>
                  <CardFooter className="pt-2 flex justify-between">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => router.push(`/dashboard/service-activities/${activity.id}`)}
                    >
                      Ver detalles
                    </Button>
                    {activity.status === 'scheduled' && (
                      <Button 
                        variant="default" 
                        size="sm"
                        onClick={() => router.push(`/dashboard/service-activities/${activity.id}/start`)}
                      >
                        Iniciar
                      </Button>
                    )}
                    {activity.status === 'in_progress' && (
                      <Button 
                        variant="default" 
                        size="sm"
                        onClick={() => router.push(`/dashboard/service-activities/${activity.id}/complete`)}
                      >
                        Completar
                      </Button>
                    )}
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
          
          <Button 
            variant="outline" 
            className="w-full"
            onClick={() => router.push('/dashboard/service-activities')}
          >
            Ver todas las actividades
          </Button>
        </TabsContent>
        
        <TabsContent value="assigned" className="mt-0 space-y-4">
          <h3 className="text-lg font-medium">Solicitudes asignadas</h3>
          
          {assignedRequests.length === 0 ? (
            <Card>
              <CardContent className="pt-6 text-center text-muted-foreground">
                No hay solicitudes asignadas.
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {assignedRequests.map((request) => (
                <Card key={request.id} className="overflow-hidden">
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-base">{request.title}</CardTitle>
                      {getStatusBadge(request.status, request.is_overdue)}
                    </div>
                    <CardDescription>
                      {request.client_name}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pb-2">
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      {request.due_date && (
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3 text-muted-foreground" />
                          <span>{formatDate(request.due_date)}</span>
                        </div>
                      )}
                      {request.location && (
                        <div className="flex items-center gap-1">
                          <MapPin className="h-3 w-3 text-muted-foreground" />
                          <span>{request.location}</span>
                        </div>
                      )}
                      <div className="flex items-center gap-1">
                        <Wrench className="h-3 w-3 text-muted-foreground" />
                        <span>Prioridad: {request.priority}</span>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="pt-2">
                    <Button 
                      variant="outline" 
                      className="w-full"
                      onClick={() => router.push(`/dashboard/service-requests/${request.id}`)}
                    >
                      Ver detalles
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
          
          <Button 
            variant="outline" 
            className="w-full"
            onClick={() => router.push('/dashboard/service-requests')}
          >
            Ver todas las solicitudes
          </Button>
        </TabsContent>
      </Tabs>
    </div>
  );
}
