"use client"

import { useState, useEffect } from "react"
import { Download, ExternalLink, FileText, Image, File } from "lucide-react"
import { But<PERSON> } from "@/components/shared/ui/button"
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "@/components/shared/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/shared/ui/tabs"
import { toast } from "@/components/shared/ui/use-toast"

interface DocumentPreviewProps {
  document: {
    id: string
    filename: string
    description?: string
    file_type: string
    public_url?: string
  }
  height?: number
}

export function DocumentPreview({ document, height = 500 }: DocumentPreviewProps) {
  const [activeTab, setActiveTab] = useState<string>("preview")
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const isImage = document.file_type.startsWith("image/")
  const isPdf = document.file_type === "application/pdf"
  const isOffice = [
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "application/vnd.openxmlformats-officedocument.presentationml.presentation",
    "application/msword",
    "application/vnd.ms-excel",
    "application/vnd.ms-powerpoint",
  ].includes(document.file_type)

  const handleDownload = () => {
    if (document.public_url) {
      const link = document.createElement("a")
      link.href = document.public_url
      link.download = document.filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    } else {
      toast({
        title: "Error",
        description: "No se puede descargar este documento",
        variant: "destructive",
      })
    }
  }

  const handleOpenExternal = () => {
    if (document.public_url) {
      window.open(document.public_url, "_blank")
    }
  }

  useEffect(() => {
    setIsLoading(true)
    setError(null)
    
    // Simular carga
    const timer = setTimeout(() => {
      setIsLoading(false)
      
      // Simular error para tipos no soportados
      if (!isImage && !isPdf && !isOffice) {
        setError("Este tipo de documento no se puede previsualizar")
      }
    }, 1000)
    
    return () => clearTimeout(timer)
  }, [document.id, isImage, isPdf, isOffice])

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-lg">{document.filename}</CardTitle>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleDownload}
          >
            <Download className="mr-2 h-4 w-4" />
            Descargar
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleOpenExternal}
          >
            <ExternalLink className="mr-2 h-4 w-4" />
            Abrir
          </Button>
        </div>
      </CardHeader>
      <Tabs defaultValue="preview" value={activeTab} onValueChange={setActiveTab}>
        <div className="px-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="preview">Previsualización</TabsTrigger>
            <TabsTrigger value="details">Detalles</TabsTrigger>
          </TabsList>
        </div>
        <TabsContent value="preview" className="p-0">
          <CardContent className="p-6">
            <div
              className="w-full overflow-hidden rounded-md border"
              style={{ height: `${height}px` }}
            >
              {isLoading ? (
                <div className="flex h-full items-center justify-center">
                  <div className="flex flex-col items-center">
                    <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
                    <p className="mt-2 text-sm text-muted-foreground">Cargando documento...</p>
                  </div>
                </div>
              ) : error ? (
                <div className="flex h-full flex-col items-center justify-center">
                  <File className="h-16 w-16 text-muted-foreground" />
                  <p className="mt-4 text-center text-muted-foreground">{error}</p>
                  <Button
                    variant="outline"
                    className="mt-4"
                    onClick={handleOpenExternal}
                  >
                    Abrir en nueva pestaña
                  </Button>
                </div>
              ) : isImage ? (
                <div className="flex h-full items-center justify-center bg-muted/20">
                  <img
                    src={document.public_url}
                    alt={document.filename}
                    className="max-h-full max-w-full object-contain"
                    onError={() => setError("Error al cargar la imagen")}
                  />
                </div>
              ) : isPdf ? (
                <iframe
                  src={`${document.public_url}#toolbar=0`}
                  className="h-full w-full"
                  title={document.filename}
                  onError={() => setError("Error al cargar el PDF")}
                />
              ) : isOffice ? (
                <iframe
                  src={`https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(document.public_url || "")}`}
                  className="h-full w-full"
                  title={document.filename}
                  onError={() => setError("Error al cargar el documento de Office")}
                />
              ) : (
                <div className="flex h-full flex-col items-center justify-center">
                  <FileText className="h-16 w-16 text-muted-foreground" />
                  <p className="mt-4 text-center text-muted-foreground">
                    Este tipo de documento no se puede previsualizar
                  </p>
                  <Button
                    variant="outline"
                    className="mt-4"
                    onClick={handleOpenExternal}
                  >
                    Abrir en nueva pestaña
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </TabsContent>
        <TabsContent value="details">
          <CardContent className="p-6">
            <div className="space-y-4">
              <div>
                <h3 className="font-medium">Nombre del archivo</h3>
                <p className="text-sm text-muted-foreground">{document.filename}</p>
              </div>
              {document.description && (
                <div>
                  <h3 className="font-medium">Descripción</h3>
                  <p className="text-sm text-muted-foreground">{document.description}</p>
                </div>
              )}
              <div>
                <h3 className="font-medium">Tipo de archivo</h3>
                <p className="text-sm text-muted-foreground">{document.file_type}</p>
              </div>
              <div>
                <h3 className="font-medium">URL</h3>
                <p className="text-sm text-muted-foreground break-all">
                  {document.public_url || "No disponible"}
                </p>
              </div>
            </div>
          </CardContent>
        </TabsContent>
      </Tabs>
      <CardFooter className="border-t bg-muted/50 px-6 py-3">
        <div className="flex items-center text-sm text-muted-foreground">
          {isImage ? (
            <Image className="mr-2 h-4 w-4" />
          ) : isPdf ? (
            <FileText className="mr-2 h-4 w-4" />
          ) : (
            <File className="mr-2 h-4 w-4" />
          )}
          <span>
            {isImage
              ? "Imagen"
              : isPdf
              ? "Documento PDF"
              : isOffice
              ? "Documento de Office"
              : "Archivo"}
          </span>
        </div>
      </CardFooter>
    </Card>
  )
}
