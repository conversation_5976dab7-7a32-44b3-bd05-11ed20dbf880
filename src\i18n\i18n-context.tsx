'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import esTranslations from './locales/es.json';
import enTranslations from './locales/en.json';

// Definir los idiomas disponibles
export type Locale = 'es' | 'en';

// Definir el tipo para las traducciones
type Translations = typeof esTranslations;

// Definir el contexto
interface I18nContextType {
  locale: Locale;
  translations: Translations;
  setLocale: (locale: Locale) => void;
  t: (key: string) => string;
}

// Crear el contexto
const I18nContext = createContext<I18nContextType | undefined>(undefined);

// Función para obtener traducciones anidadas usando una cadena de puntos
const getNestedTranslation = (obj: unknown, path: string): string => {
  const keys = path.split('.');
  let result = obj;

  for (const key of keys) {
    if (result && typeof result === 'object' && key in result) {
      result = result[key];
    } else {
      return path; // Devolver la clave si no se encuentra la traducción
    }
  }

  return typeof result === 'string' ? result : path;
};

// Proveedor del contexto
export function I18nProvider({ children }: { children: ReactNode }) {
  // Obtener el idioma del navegador o usar español por defecto
  const getBrowserLocale = (): Locale => {
    if (typeof window !== 'undefined') {
      const browserLang = navigator.language.split('-')[0];
      return browserLang === 'en' ? 'en' : 'es';
    }
    return 'es'; // Español por defecto
  };

  // Estado para el idioma actual
  const [locale, setLocale] = useState<Locale>('es');
  
  // Estado para las traducciones
  const [translations, setTranslations] = useState<Translations>(esTranslations);

  // Efecto para inicializar el idioma
  useEffect(() => {
    const savedLocale = localStorage.getItem('locale') as Locale;
    const initialLocale = savedLocale || getBrowserLocale();
    setLocale(initialLocale);
  }, []);

  // Efecto para actualizar las traducciones cuando cambia el idioma
  useEffect(() => {
    // Guardar el idioma en localStorage
    localStorage.setItem('locale', locale);
    
    // Actualizar las traducciones
    setTranslations(locale === 'en' ? enTranslations : esTranslations);
    
    // Actualizar el atributo lang del HTML
    if (typeof document !== 'undefined') {
      document.documentElement.lang = locale;
    }
  }, [locale]);

  // Función para obtener una traducción
  const t = (key: string): string => {
    return getNestedTranslation(translations, key);
  };

  return (
    <I18nContext.Provider value={{ locale, translations, setLocale, t }}>
      {children}
    </I18nContext.Provider>
  );
}

// Hook personalizado para usar el contexto
export function useI18n() {
  const context = useContext(I18nContext);
  if (context === undefined) {
    throw new Error('useI18n must be used within an I18nProvider');
  }
  return context;
}

// Componente para cambiar el idioma
export function LanguageSwitcher() {
  const { locale, setLocale } = useI18n();

  return (
    <button
      onClick={() => setLocale(locale === 'es' ? 'en' : 'es')}
      className="flex items-center space-x-1 text-sm"
    >
      <span>{locale === 'es' ? '🇪🇸' : '🇺🇸'}</span>
      <span>{locale === 'es' ? 'ES' : 'EN'}</span>
    </button>
  );
}
