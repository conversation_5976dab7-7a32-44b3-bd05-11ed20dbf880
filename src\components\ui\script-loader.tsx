"use client"

import { useEffect, useState } from 'react'
import Script from 'next/script'

interface ScriptLoaderProps {
  src: string
  id?: string
  strategy?: 'beforeInteractive' | 'afterInteractive' | 'lazyOnload'
  onLoad?: () => void
  defer?: boolean
  async?: boolean
}

/**
 * Componente para cargar scripts externos de forma optimizada
 * 
 * Características:
 * - Carga scripts con diferentes estrategias
 * - Evita cargar el mismo script múltiples veces
 * - Proporciona callback cuando el script se ha cargado
 * - Soporta atributos defer y async
 * 
 * @param props Propiedades del componente
 * @returns Componente de carga de scripts
 */
export function ScriptLoader({
  src,
  id,
  strategy = 'lazyOnload',
  onLoad,
  defer = true,
  async = true,
}: ScriptLoaderProps) {
  const [loaded, setLoaded] = useState(false)
  const scriptId = id || `script-${src.replace(/[^a-zA-Z0-9]/g, '-')}`

  useEffect(() => {
    // Verificar si el script ya está cargado
    const existingScript = document.getElementById(scriptId)
    if (existingScript) {
      setLoaded(true)
      if (onLoad) onLoad()
      return
    }
  }, [scriptId, onLoad])

  const handleLoad = () => {
    setLoaded(true)
    if (onLoad) onLoad()
  }

  return (
    <>
      {!loaded && (
        <Script
          id={scriptId}
          src={src}
          strategy={strategy}
          onLoad={handleLoad}
          defer={defer}
          async={async}
        />
      )}
    </>
  )
}
