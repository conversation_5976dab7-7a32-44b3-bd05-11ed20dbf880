"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Loader2, ChevronsUpDown, Check } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { createClient } from "@/lib/supabase/client"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command"
import { cn } from "@/lib/utils"
import { dataValidator } from "@/lib/services/data-validator-service"
import { errorHandler } from "@/lib/services/error-handler-service"
import { FormValidationAlert } from "@/components/ui/form-validation-alert"

// Esquema de validación para el formulario
const transactionFormSchema = z.object({
  quantity: z.coerce.number({
    required_error: "La cantidad es obligatoria",
    invalid_type_error: "La cantidad debe ser un número",
  }).positive({
    message: "La cantidad debe ser mayor que cero.",
  }),
  notes: z.string().optional()
    .transform(val => val === '' ? null : val), // Convertir string vacío a null
  project_id: z.string().optional()
    .transform(val => val === '' ? null : val), // Convertir string vacío a null
  work_order_id: z.string().optional()
    .transform(val => val === '' ? null : val), // Convertir string vacío a null
})

type TransactionFormValues = z.infer<typeof transactionFormSchema>

// Valores por defecto
const defaultValues: Partial<TransactionFormValues> = {
  quantity: 1,
  notes: "",
  project_id: undefined,
  work_order_id: undefined,
}

interface Project {
  id: string
  name: string
}

interface WorkOrder {
  id: string
  title: string
}

interface TransactionFormProps {
  onSubmit: (data: TransactionFormValues) => void
  onCancel: () => void
  isLoading?: boolean
  transactionType: "add" | "remove"
  maxQuantity?: number
  unit?: string
}

export function InventoryTransactionForm({
  onSubmit,
  onCancel,
  isLoading = false,
  transactionType,
  maxQuantity,
  unit,
}: TransactionFormProps) {
  const [projects, setProjects] = useState<Project[]>([])
  const [workOrders, setWorkOrders] = useState<WorkOrder[]>([])
  const [isLoadingProjects, setIsLoadingProjects] = useState(false)
  const [isLoadingWorkOrders, setIsLoadingWorkOrders] = useState(false)
  const [openProjectSelect, setOpenProjectSelect] = useState(false)
  const [openWorkOrderSelect, setOpenWorkOrderSelect] = useState(false)
  const [validationErrors, setValidationErrors] = useState<string[]>([])
  const supabase = createClient()

  // Crear un esquema con validación condicional para maxQuantity
  const schema = transactionType === "remove" && maxQuantity !== undefined
    ? transactionFormSchema.refine(
        data => data.quantity <= maxQuantity,
        {
          message: `No puedes retirar más de ${maxQuantity} ${unit || "unidades"} disponibles.`,
          path: ["quantity"],
        }
      )
    : transactionFormSchema;

  const form = useForm<TransactionFormValues>({
    resolver: zodResolver(schema),
    defaultValues,
  })

  // Cargar proyectos
  useEffect(() => {
    const loadProjects = async () => {
      setIsLoadingProjects(true)
      try {
        const { data, error } = await supabase
          .from("projects")
          .select("id, name")
          .order("name")

        if (error) throw error
        setProjects(data || [])
      } catch (error) {
        console.error("Error al cargar proyectos:", error)
      } finally {
        setIsLoadingProjects(false)
      }
    }

    loadProjects()
  }, [supabase])

  // Cargar órdenes de trabajo
  useEffect(() => {
    const loadWorkOrders = async () => {
      setIsLoadingWorkOrders(true)
      try {
        const { data, error } = await supabase
          .from("work_orders")
          .select("id, title")
          .order("created_at", { ascending: false })

        if (error) throw error
        setWorkOrders(data || [])
      } catch (error) {
        console.error("Error al cargar órdenes de trabajo:", error)
      } finally {
        setIsLoadingWorkOrders(false)
      }
    }

    loadWorkOrders()
  }, [supabase])

  const handleSubmit = (data: TransactionFormValues) => {
    // Limpiar errores previos
    setValidationErrors([])

    // Validar campos requeridos
    const requiredFields = ['quantity']
    const { isValid, errors } = dataValidator.validateRequiredFields(data, requiredFields)

    if (!isValid) {
      // Mostrar errores en los campos correspondientes
      Object.entries(errors).forEach(([field, message]) => {
        form.setError(field as any, {
          type: 'required',
          message
        })
      })

      // Agregar errores a la lista de validación para mostrar en el alert
      const errorMessages = Object.values(errors)
      setValidationErrors(errorMessages)
      return
    }

    // Sanitizar campos de texto para convertir strings vacíos a null
    const textFields = ['notes']
    const sanitizedData = dataValidator.sanitizeTextFields(data, textFields)

    // Continuar con el envío del formulario
    onSubmit(sanitizedData)
  }

  return (
    <Form {...form}>
      <FormValidationAlert errors={validationErrors} />
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <div className="text-sm text-muted-foreground mb-4">
          Los campos marcados con <span className="text-red-500">*</span> son obligatorios.
        </div>
        <FormField
          control={form.control}
          name="quantity"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center">
                Cantidad
                <span className="text-red-500 ml-1">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  type="number"
                  min="1"
                  step="1"
                  {...field}
                  className={form.formState.errors.quantity ? "border-red-500" : ""}
                  required
                  onChange={(e) => {
                    const value = parseInt(e.target.value);
                    if (!isNaN(value) && value > 0) {
                      field.onChange(value);
                    }
                  }}
                />
              </FormControl>
              <FormDescription>
                Cantidad a {transactionType === "add" ? "agregar" : "retirar"} del inventario.
                {transactionType === "remove" && maxQuantity !== undefined && (
                  <span className="text-muted-foreground"> Disponible: {maxQuantity} {unit || "unidades"}</span>
                )}
              </FormDescription>
              <FormMessage className="text-red-500" />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="project_id"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Proyecto (opcional)</FormLabel>
              <Popover
                open={openProjectSelect}
                onOpenChange={setOpenProjectSelect}
              >
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant="outline"
                      role="combobox"
                      className={cn(
                        "w-full justify-between",
                        !field.value && "text-muted-foreground"
                      )}
                    >
                      {field.value
                        ? projects.find(
                            (project) => project.id === field.value
                          )?.name || "Seleccionar proyecto"
                        : "Seleccionar proyecto"}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-[300px] p-0">
                  <Command>
                    <CommandInput placeholder="Buscar proyecto..." />
                    <CommandEmpty>No se encontraron proyectos.</CommandEmpty>
                    <CommandGroup>
                      {projects.map((project) => (
                        <CommandItem
                          key={project.id}
                          value={project.id}
                          onSelect={(value) => {
                            form.setValue("project_id", value)
                            // Si se selecciona un proyecto, limpiar la orden de trabajo
                            form.setValue("work_order_id", undefined)
                            setOpenProjectSelect(false)
                          }}
                        >
                          <Check
                            className={cn(
                              "mr-2 h-4 w-4",
                              project.id === field.value
                                ? "opacity-100"
                                : "opacity-0"
                            )}
                          />
                          {project.name}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </Command>
                </PopoverContent>
              </Popover>
              <FormDescription>
                Asociar esta transacción a un proyecto.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="work_order_id"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Orden de Trabajo (opcional)</FormLabel>
              <Popover
                open={openWorkOrderSelect}
                onOpenChange={setOpenWorkOrderSelect}
              >
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant="outline"
                      role="combobox"
                      className={cn(
                        "w-full justify-between",
                        !field.value && "text-muted-foreground"
                      )}
                    >
                      {field.value
                        ? workOrders.find(
                            (order) => order.id === field.value
                          )?.title || "Seleccionar orden"
                        : "Seleccionar orden"}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-[300px] p-0">
                  <Command>
                    <CommandInput placeholder="Buscar orden..." />
                    <CommandEmpty>No se encontraron órdenes.</CommandEmpty>
                    <CommandGroup>
                      {workOrders.map((order) => (
                        <CommandItem
                          key={order.id}
                          value={order.id}
                          onSelect={(value) => {
                            form.setValue("work_order_id", value)
                            // Si se selecciona una orden, limpiar el proyecto
                            form.setValue("project_id", undefined)
                            setOpenWorkOrderSelect(false)
                          }}
                        >
                          <Check
                            className={cn(
                              "mr-2 h-4 w-4",
                              order.id === field.value
                                ? "opacity-100"
                                : "opacity-0"
                            )}
                          />
                          {order.title}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </Command>
                </PopoverContent>
              </Popover>
              <FormDescription>
                Asociar esta transacción a una orden de trabajo.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Notas (opcional)</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Notas adicionales sobre esta transacción"
                  className="min-h-[80px]"
                  {...field}
                  value={field.value || ""}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end space-x-4 pt-2">
          <Button variant="outline" type="button" onClick={onCancel}>
            Cancelar
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {transactionType === "add" ? "Agregar Stock" : "Retirar Stock"}
          </Button>
        </div>
      </form>
    </Form>
  )
}
