import { useState, useEffect } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { ServiceContract } from '@/lib/types/service-contracts';
import { ServiceContractService } from '@/lib/services/service-contract-service';
import { formatDate, formatCurrency } from '@/lib/utils';
import { useRouter } from 'next/navigation';
import { 
  Calendar, 
  AlertTriangle, 
  CheckCircle2, 
  Clock, 
  Building,
  FileText,
  RefreshCw,
  Tag
} from 'lucide-react';

interface ServiceContractsTableProps {
  clientId?: string;
  includeExpired?: boolean;
  limit?: number;
  onRowClick?: (contract: ServiceContract) => void;
}

export function ServiceContractsTable({
  clientId,
  includeExpired = false,
  limit = 10,
  onRowClick,
}: ServiceContractsTableProps) {
  const [contracts, setContracts] = useState<ServiceContract[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    const fetchContracts = async () => {
      try {
        setLoading(true);
        const data = await ServiceContractService.getActiveServiceContracts(
          clientId || null,
          includeExpired,
          limit,
          0
        );
        setContracts(data);
        setError(null);
      } catch (err) {
        console.error('Error al cargar contratos de servicio:', err);
        setError('Error al cargar contratos de servicio. Intente nuevamente.');
      } finally {
        setLoading(false);
      }
    };

    fetchContracts();
  }, [clientId, includeExpired, limit]);

  const handleRowClick = (contract: ServiceContract) => {
    if (onRowClick) {
      onRowClick(contract);
    } else {
      router.push(`/dashboard/service-contracts/${contract.id}`);
    }
  };

  const getStatusBadge = (status: string, isExpired?: boolean) => {
    if (isExpired) {
      return (
        <Badge variant="destructive" className="flex gap-1 items-center">
          <AlertTriangle className="h-3 w-3" />
          Vencido
        </Badge>
      );
    }

    switch (status) {
      case 'active':
        return (
          <Badge variant="success" className="flex gap-1 items-center">
            <CheckCircle2 className="h-3 w-3" />
            Activo
          </Badge>
        );
      case 'pending':
        return (
          <Badge variant="secondary" className="flex gap-1 items-center">
            <Clock className="h-3 w-3" />
            Pendiente
          </Badge>
        );
      case 'cancelled':
        return (
          <Badge variant="destructive" className="flex gap-1 items-center">
            <AlertTriangle className="h-3 w-3" />
            Cancelado
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            {status}
          </Badge>
        );
    }
  };

  const getContractTypeBadge = (type: string) => {
    switch (type) {
      case 'basic':
        return (
          <Badge variant="outline" className="flex gap-1 items-center bg-blue-50">
            <Tag className="h-3 w-3 text-blue-500" />
            <span className="text-blue-700">Básico</span>
          </Badge>
        );
      case 'standard':
        return (
          <Badge variant="outline" className="flex gap-1 items-center bg-green-50">
            <Tag className="h-3 w-3 text-green-500" />
            <span className="text-green-700">Estándar</span>
          </Badge>
        );
      case 'premium':
        return (
          <Badge variant="outline" className="flex gap-1 items-center bg-purple-50">
            <Tag className="h-3 w-3 text-purple-500" />
            <span className="text-purple-700">Premium</span>
          </Badge>
        );
      case 'custom':
        return (
          <Badge variant="outline" className="flex gap-1 items-center bg-amber-50">
            <Tag className="h-3 w-3 text-amber-500" />
            <span className="text-amber-700">Personalizado</span>
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            {type}
          </Badge>
        );
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Contratos de Servicio</CardTitle>
          <CardDescription>
            Contratos de servicio activos
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {Array(5).fill(0).map((_, i) => (
              <Skeleton key={i} className="h-12 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Contratos de Servicio</CardTitle>
          <CardDescription>
            Contratos de servicio activos
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-destructive/10 p-4 rounded-md text-destructive">
            {error}
          </div>
          <Button 
            variant="outline" 
            className="mt-4"
            onClick={() => window.location.reload()}
          >
            Reintentar
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Contratos de Servicio</CardTitle>
        <CardDescription>
          {clientId ? 'Contratos del cliente seleccionado' : 'Todos los contratos activos'}
          {includeExpired && ' (incluye vencidos)'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {contracts.length === 0 ? (
          <div className="text-center py-6 text-muted-foreground">
            No hay contratos de servicio para mostrar.
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Contrato</TableHead>
                  <TableHead>Cliente</TableHead>
                  <TableHead>Tipo</TableHead>
                  <TableHead>Vigencia</TableHead>
                  <TableHead>Estado</TableHead>
                  <TableHead>Facturación</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {contracts.map((contract) => (
                  <TableRow 
                    key={contract.id}
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleRowClick(contract)}
                  >
                    <TableCell className="font-medium">
                      <div>
                        {contract.title}
                        {contract.contract_number && (
                          <div className="text-xs text-muted-foreground flex items-center gap-1">
                            <FileText className="h-3 w-3" />
                            {contract.contract_number}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Building className="h-3 w-3" />
                        {contract.client_name || 'Sin cliente'}
                      </div>
                    </TableCell>
                    <TableCell>{getContractTypeBadge(contract.contract_type)}</TableCell>
                    <TableCell>
                      <div className="flex flex-col gap-1">
                        <div className="text-xs text-muted-foreground flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {formatDate(contract.start_date)} - {formatDate(contract.end_date)}
                        </div>
                        {contract.days_until_expiration !== undefined && (
                          <div className="text-xs">
                            {contract.is_expired ? (
                              <span className="text-destructive">Vencido</span>
                            ) : (
                              <>
                                {contract.days_until_expiration <= 30 ? (
                                  <span className="text-amber-600">Vence en {contract.days_until_expiration} días</span>
                                ) : (
                                  <span>Vence en {contract.days_until_expiration} días</span>
                                )}
                              </>
                            )}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{getStatusBadge(contract.status, contract.is_expired)}</TableCell>
                    <TableCell>
                      <div className="flex flex-col gap-1">
                        {contract.billing_amount && (
                          <div className="font-medium">
                            {formatCurrency(contract.billing_amount, contract.currency)}
                          </div>
                        )}
                        {contract.billing_cycle && (
                          <div className="text-xs text-muted-foreground flex items-center gap-1">
                            <RefreshCw className="h-3 w-3" />
                            {contract.billing_cycle === 'monthly' && 'Mensual'}
                            {contract.billing_cycle === 'quarterly' && 'Trimestral'}
                            {contract.billing_cycle === 'yearly' && 'Anual'}
                            {contract.billing_cycle === 'one-time' && 'Único pago'}
                          </div>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
        <div className="mt-4 flex justify-end">
          <Button 
            variant="outline" 
            onClick={() => router.push('/dashboard/service-contracts')}
          >
            Ver todos
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
