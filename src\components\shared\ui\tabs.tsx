// Componente de tabs mínimo para evitar errores de importación
import React from "react";

// Stubs de Tabs para evitar errores de importación y facilitar reemplazo posterior
export function Tabs({ children }: { children: React.ReactNode }) {
  return <div>{children}</div>;
}
export function TabsList({ children }: { children: React.ReactNode }) {
  return <div>{children}</div>;
}
export function TabsTrigger({ children, ...props }: { children: React.ReactNode }) {
  return <button type="button" {...props}>{children}</button>;
}
export function TabsContent({ children }: { children: React.ReactNode }) {
  return <div>{children}</div>;
}
// Compatibilidad legacy
export const TabPanel = TabsContent;
