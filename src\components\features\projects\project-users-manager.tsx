"use client"

import { useState, useEffect } from "react"
import { <PERSON>ader2, Trash2, UserP<PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { toast } from "@/hooks/use-toast"
import { createClient } from "@/lib/supabase/client"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Check, ChevronsUpDown } from "lucide-react"
import { cn } from "@/lib/utils"

interface UserSummary {
  id: string;
  email: string;
  first_name?: string | null;
  last_name?: string | null;
  full_name?: string;
}

interface ProjectUserInProject {
  id: string;
  user_id: string;
  project_id: string;
  role: string;
  users?: Array<UserSummary | null> | (UserSummary | null);
  user?: UserSummary | null;
}

interface User {
  id: string
  email: string
  first_name?: string | null
  last_name?: string | null
  full_name?: string
}

interface ProjectUser {
  id: string
  user_id: string
  project_id: string
  role: string
  user: User | null
}

interface ProjectUsersManagerProps {
  projectId: string
  initialUsers?: ProjectUser[]
}

// Robust transformer for project users array
function transformProjectUsers(raw: unknown[]): ProjectUser[] {
  return Array.isArray(raw)
    ? raw.map((puRaw: unknown) => {
        const pu = puRaw as ProjectUserInProject;

        let userData: UserSummary | null | undefined;

        if (Array.isArray(pu.users)) {
          userData = pu.users[0] || null;
        } else if (pu.users !== undefined) {
          userData = pu.users;
        } else {
          userData = pu.user;
        }

        const user: User | null = userData
          ? {
              id: userData.id,
              email: userData.email,
              first_name: userData.first_name,
              last_name: userData.last_name,
              full_name: userData.full_name || ((userData.first_name || "") + " " + (userData.last_name || "")).trim() || undefined,
            }
          : null;

        return {
          id: pu.id,
          user_id: pu.user_id,
          project_id: pu.project_id,
          role: pu.role,
          user: user,
        };
      })
    : [];
}

export function ProjectUsersManager({
  projectId,
  initialUsers = [],
}: ProjectUsersManagerProps) {
  const [projectUsers, setProjectUsers] = useState<ProjectUser[]>(transformProjectUsers(initialUsers))
  const [availableUsers, setAvailableUsers] = useState<User[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [selectedUserId, setSelectedUserId] = useState<string>("")
  const [selectedRole, setSelectedRole] = useState<string>("member")
  const [open, setOpen] = useState(false)
  const supabase = createClient()

  const loadProjectUsers = async () => {
    setIsLoading(true)
    try {
      const { data, error } = await supabase
        .from("project_users")
        .select("*, user:user_id(id, email, full_name)")
        .eq("project_id", projectId)

      if (error) throw error
      setProjectUsers(transformProjectUsers(data || []))
    } catch (error) {
      console.error("Error al cargar los usuarios del proyecto:", error)
      toast({
        title: "Error",
        description: "No se pudieron cargar los usuarios del proyecto",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const loadAvailableUsers = async () => {
    try {
      // Obtener todos los usuarios
      const { data: allUsers, error: usersError } = await supabase
        .from("users")
        .select("id, email, first_name, last_name")

      if (usersError) throw usersError

      // Filtrar los usuarios que ya están asignados al proyecto
      const assignedUserIds = projectUsers.map((pu) => pu.user_id)
      const filteredUsers = (allUsers || []).filter(
        (user) => !assignedUserIds.includes(user.id)
      ).map(user => ({
        ...user,
        full_name: `${user.first_name || ''} ${user.last_name || ''}`.trim() || undefined
      }))

      setAvailableUsers(filteredUsers)
    } catch (error) {
      console.error("Error al cargar usuarios disponibles:", error)
      toast({
        title: "Error",
        description: "No se pudieron cargar los usuarios disponibles",
        variant: "destructive",
      })
    }
  }

  const addUserToProject = async () => {
    if (!selectedUserId) {
      toast({
        title: "Error",
        description: "Debes seleccionar un usuario",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)
    try {
      const { data, error } = await supabase
        .from("project_users")
        .insert({
          project_id: projectId,
          user_id: selectedUserId,
          role: selectedRole,
        })
        .select("*, user:user_id(id, email, full_name)")

      if (error) throw error

      setProjectUsers([...projectUsers, data[0]])
      setSelectedUserId("")
      setSelectedRole("member")
      setIsAddDialogOpen(false)

      toast({
        title: "Usuario añadido",
        description: "El usuario ha sido añadido al proyecto correctamente",
      })
    } catch (error) {
      console.error("Error al añadir usuario al proyecto:", error)
      toast({
        title: "Error",
        description: "No se pudo añadir el usuario al proyecto",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const removeUserFromProject = async (projectUserId: string) => {
    setIsLoading(true)
    try {
      const { error } = await supabase
        .from("project_users")
        .delete()
        .eq("id", projectUserId)

      if (error) throw error

      setProjectUsers(projectUsers.filter((pu) => pu.id !== projectUserId))

      toast({
        title: "Usuario eliminado",
        description: "El usuario ha sido eliminado del proyecto correctamente",
      })
    } catch (error) {
      console.error("Error al eliminar usuario del proyecto:", error)
      toast({
        title: "Error",
        description: "No se pudo eliminar el usuario del proyecto",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const updateUserRole = async (projectUserId: string, newRole: string) => {
    setIsLoading(true)
    try {
      const { error } = await supabase
        .from("project_users")
        .update({ role: newRole })
        .eq("id", projectUserId)

      if (error) throw error

      setProjectUsers(
        projectUsers.map((pu) =>
          pu.id === projectUserId ? { ...pu, role: newRole } : pu
        )
      )

      toast({
        title: "Rol actualizado",
        description: "El rol del usuario ha sido actualizado correctamente",
      })
    } catch (error) {
      console.error("Error al actualizar el rol del usuario:", error)
      toast({
        title: "Error",
        description: "No se pudo actualizar el rol del usuario",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Cargar usuarios del proyecto si no se proporcionaron inicialmente
  useEffect(() => {
    if (initialUsers.length === 0) {
      loadProjectUsers()
    }
  }, [initialUsers, projectId])

  // Cargar usuarios disponibles cuando se abre el diálogo
  useEffect(() => {
    if (isAddDialogOpen) {
      loadAvailableUsers()
    }
  }, [isAddDialogOpen, projectUsers])

  useEffect(() => {
    loadProjectUsers();
  }, [loadProjectUsers]);

  useEffect(() => {
    loadAvailableUsers();
  }, [loadAvailableUsers]);

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case "admin":
        return "bg-red-500"
      case "manager":
        return "bg-blue-500"
      case "member":
        return "bg-green-500"
      default:
        return "bg-gray-500"
    }
  }

  const getRoleText = (role: string) => {
    switch (role) {
      case "admin":
        return "Administrador"
      case "manager":
        return "Gerente"
      case "member":
        return "Miembro"
      default:
        return role
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Equipo del Proyecto</h3>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button size="sm">
              <UserPlus className="mr-2 h-4 w-4" /> Añadir Usuario
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Añadir Usuario al Proyecto</DialogTitle>
              <DialogDescription>
                Selecciona un usuario y asígnale un rol en el proyecto.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <label htmlFor="user" className="text-sm font-medium">
                  Usuario
                </label>
                <Popover open={open} onOpenChange={setOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={open}
                      className="justify-between"
                    >
                      {selectedUserId
                        ? availableUsers.find(
                            (user) => user.id === selectedUserId
                          )?.full_name ||
                          availableUsers.find(
                            (user) => user.id === selectedUserId
                          )?.email ||
                          "Seleccionar usuario"
                        : "Seleccionar usuario"}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-[300px] p-0">
                    <Command>
                      <CommandInput placeholder="Buscar usuario..." />
                      <CommandEmpty>No se encontraron usuarios.</CommandEmpty>
                      <CommandGroup>
                        {availableUsers.map((user) => (
                          <CommandItem
                            key={user.id}
                            value={user.id}
                            onSelect={(currentValue) => {
                              setSelectedUserId(
                                currentValue === selectedUserId
                                  ? ""
                                  : currentValue
                              )
                              setOpen(false)
                            }}
                          >
                            <Check
                              className={cn(
                                "mr-2 h-4 w-4",
                                selectedUserId === user.id
                                  ? "opacity-100"
                                  : "opacity-0"
                              )}
                            />
                            <div className="flex flex-col">
                              <span>
                                {user.full_name || "Usuario sin nombre"}
                              </span>
                              <span className="text-xs text-muted-foreground">
                                {user.email}
                              </span>
                            </div>
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>
              <div className="grid gap-2">
                <label htmlFor="role" className="text-sm font-medium">
                  Rol
                </label>
                <Select
                  value={selectedRole}
                  onValueChange={setSelectedRole}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Seleccionar rol" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="admin">Administrador</SelectItem>
                    <SelectItem value="manager">Gerente</SelectItem>
                    <SelectItem value="member">Miembro</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsAddDialogOpen(false)}
              >
                Cancelar
              </Button>
              <Button onClick={addUserToProject} disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Añadir
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {isLoading && projectUsers.length === 0 ? (
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      ) : projectUsers.length === 0 ? (
        <Card>
          <CardContent className="pt-6 text-center">
            <p className="text-muted-foreground">
              No hay usuarios asignados a este proyecto.
            </p>
            <Button
              className="mt-4"
              onClick={() => setIsAddDialogOpen(true)}
              variant="outline"
            >
              <UserPlus className="mr-2 h-4 w-4" /> Añadir Usuario
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-3">
          {projectUsers.map((projectUser) => (
            <Card key={projectUser.id} className="relative">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <Avatar>
                      <AvatarFallback>
                        {projectUser.user?.full_name
                          ? projectUser.user.full_name
                              .split(" ")
                              .map((n) => n[0])
                              .join("")
                              .toUpperCase()
                              .substring(0, 2)
                          : projectUser.user?.email?.substring(0, 2).toUpperCase() || "??"}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium">
                        {projectUser.user?.full_name || "Usuario sin nombre"}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {projectUser.user?.email}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Select
                      value={projectUser.role}
                      onValueChange={(value) =>
                        updateUserRole(projectUser.id, value)
                      }
                      disabled={isLoading}
                    >
                      <SelectTrigger className="w-[130px]">
                        <SelectValue>
                          <Badge
                            className={cn(
                              "mr-2",
                              getRoleBadgeColor(projectUser.role)
                            )}
                          >
                            {getRoleText(projectUser.role)}
                          </Badge>
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="admin">Administrador</SelectItem>
                        <SelectItem value="manager">Gerente</SelectItem>
                        <SelectItem value="member">Miembro</SelectItem>
                      </SelectContent>
                    </Select>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-red-500 hover:text-red-600"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>
                            ¿Eliminar este usuario?
                          </AlertDialogTitle>
                          <AlertDialogDescription>
                            Esta acción eliminará al usuario del proyecto. ¿Estás
                            seguro?
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancelar</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() =>
                              removeUserFromProject(projectUser.id)
                            }
                            className="bg-red-500 hover:bg-red-600"
                          >
                            Eliminar
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
