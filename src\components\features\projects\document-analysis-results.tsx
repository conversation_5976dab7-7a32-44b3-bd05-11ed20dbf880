'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/lib/supabase/client';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, CheckCircle, XCircle, AlertTriangle } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

interface DocumentAnalysisResultsProps {
  analysisId: string;
  analysisData: unknown;
  documentData: unknown;
}

export function DocumentAnalysisResults({ analysisId, analysisData, documentData }: DocumentAnalysisResultsProps) {
  const [isCreating, setIsCreating] = useState(false);
  const [isRejecting, setIsRejecting] = useState(false);
  const router = useRouter();
  const supabase = createClient();

  // Extraer datos del análisis, manejando la estructura anidada
  const extractedData = analysisData?.analysis_data || analysisData;

  // Verificar si el documento es válido para crear un proyecto
  const isValid = extractedData?.is_valid_for_project !== false;
  const validationDetails = extractedData?.validation_details || extractedData?.reason || '';

  const handleCreateProject = async () => {
    setIsCreating(true);
    try {
      // Redirigir a la página de creación de proyecto con los datos del análisis
      router.push(`/dashboard/projects/new/from-analysis/${analysisId}`);
    } catch (error) {
      console.error('Error al crear proyecto:', error);
      toast({
        title: "Error",
        description: "No se pudo crear el proyecto",
        variant: "destructive",
      });
      setIsCreating(false);
    }
  };

  const handleRejectAnalysis = async () => {
    setIsRejecting(true);
    try {
      // Actualizar el estado del análisis a rechazado
      const { error } = await supabase
        .from('ai_document_analyses')
        .update({
          status: 'rejected',
          updated_at: new Date().toISOString()
        })
        .eq('id', analysisId);

      if (error) throw error;

      toast({
        title: "Análisis rechazado",
        description: "El documento ha sido descartado",
      });

      // Redirigir a la página de creación de proyectos
      router.push('/dashboard/projects/new');
    } catch (error) {
      console.error('Error al rechazar análisis:', error);
      toast({
        title: "Error",
        description: "No se pudo rechazar el análisis",
        variant: "destructive",
      });
    } finally {
      setIsRejecting(false);
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Resultados del Análisis</CardTitle>
            <CardDescription>
              Documento: {documentData?.filename || 'Documento sin nombre'}
            </CardDescription>
          </div>
          <Badge variant={isValid ? "success" : "destructive"}>
            {isValid ? (
              <><CheckCircle className="h-4 w-4 mr-1" /> Válido</>
            ) : (
              <><XCircle className="h-4 w-4 mr-1" /> No Válido</>
            )}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {!isValid && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Este documento no es adecuado para crear un proyecto</AlertTitle>
            <AlertDescription className="whitespace-pre-line">
              {validationDetails}
            </AlertDescription>
          </Alert>
        )}

        {isValid && (
          <Alert variant="success">
            <CheckCircle className="h-4 w-4" />
            <AlertTitle>Documento válido para crear un proyecto</AlertTitle>
            <AlertDescription>
              {validationDetails}
            </AlertDescription>
          </Alert>
        )}

        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold">Información Extraída</h3>
            <Separator className="my-2" />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-sm text-muted-foreground">Nombre del Proyecto</h4>
              <p className="text-base">{extractedData?.project_name || 'No disponible'}</p>
            </div>

            <div>
              <h4 className="font-medium text-sm text-muted-foreground">Cliente</h4>
              <p className="text-base">{extractedData?.client_name || 'No especificado'}</p>
            </div>

            <div className="col-span-1 md:col-span-2">
              <h4 className="font-medium text-sm text-muted-foreground">Descripción</h4>
              <p className="text-base">{extractedData?.description || 'No disponible'}</p>
            </div>

            <div>
              <h4 className="font-medium text-sm text-muted-foreground">Fecha de Inicio</h4>
              <p className="text-base">{extractedData?.start_date || 'No especificada'}</p>
            </div>

            <div>
              <h4 className="font-medium text-sm text-muted-foreground">Fecha de Finalización</h4>
              <p className="text-base">{extractedData?.end_date || 'No especificada'}</p>
            </div>

            <div>
              <h4 className="font-medium text-sm text-muted-foreground">Presupuesto</h4>
              <p className="text-base">
                {extractedData?.budget
                  ? `${extractedData.budget} ${extractedData.currency || 'CLP'}`
                  : 'No especificado'}
              </p>
            </div>

            <div className="col-span-1 md:col-span-2">
              <h4 className="font-medium text-sm text-muted-foreground">Alcance</h4>
              <p className="text-base">{extractedData?.scope || 'No disponible'}</p>
            </div>

            <div className="col-span-1 md:col-span-2">
              <h4 className="font-medium text-sm text-muted-foreground">Entregables</h4>
              {extractedData?.deliverables && extractedData.deliverables.length > 0 ? (
                <ul className="list-disc pl-5">
                  {extractedData.deliverables.map((item: string, index: number) => (
                    <li key={index}>{item}</li>
                  ))}
                </ul>
              ) : (
                <p className="text-base">No se especificaron entregables</p>
              )}
            </div>

            <div className="col-span-1 md:col-span-2">
              <h4 className="font-medium text-sm text-muted-foreground">Requerimientos de Equipo</h4>
              {extractedData?.team_requirements && extractedData.team_requirements.length > 0 ? (
                <ul className="list-disc pl-5">
                  {extractedData.team_requirements.map((item: string, index: number) => (
                    <li key={index}>{item}</li>
                  ))}
                </ul>
              ) : (
                <p className="text-base">No se especificaron requerimientos de equipo</p>
              )}
            </div>

            {extractedData?.tags && extractedData.tags.length > 0 && (
              <div className="col-span-1 md:col-span-2">
                <h4 className="font-medium text-sm text-muted-foreground">Etiquetas</h4>
                <div className="flex flex-wrap gap-2 mt-1">
                  {extractedData.tags.map((tag: string, index: number) => (
                    <Badge key={index} variant="outline">{tag}</Badge>
                  ))}
                </div>
              </div>
            )}

            <div className="col-span-1 md:col-span-2">
              <h4 className="font-medium text-sm text-muted-foreground">Confianza del Análisis</h4>
              <div className="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                <div
                  className="bg-blue-600 h-2.5 rounded-full"
                  style={{ width: `${(extractedData?.confidence_score || 0) * 100}%` }}
                ></div>
              </div>
              <p className="text-xs text-right mt-1">
                {Math.round((extractedData?.confidence_score || 0) * 100)}%
              </p>
            </div>
          </div>
        </div>
      </CardContent>

      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          onClick={handleRejectAnalysis}
          disabled={isCreating || isRejecting}
        >
          {isRejecting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Descartando...
            </>
          ) : (
            "Descartar"
          )}
        </Button>

        <Button
          onClick={handleCreateProject}
          disabled={!isValid || isCreating || isRejecting}
          className={isValid ? "bg-green-600 hover:bg-green-700" : ""}
        >
          {isCreating ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Creando Proyecto...
            </>
          ) : (
            "Crear Proyecto"
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
