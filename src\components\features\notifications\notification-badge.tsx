"use client"

import { useState, useEffect } from 'react'
import { Bell } from 'lucide-react'
import { getUnreadNotificationsCount } from '@/lib/services/notification-service'
import { Button } from '@/components/ui/button'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { NotificationList } from './notification-list'

interface NotificationBadgeProps {
  className?: string
}

export function NotificationBadge({ className = '' }: NotificationBadgeProps) {
  const [count, setCount] = useState(0)
  const [loading, setLoading] = useState(true)
  const [open, setOpen] = useState(false)

  // Cargar conteo de notificaciones
  useEffect(() => {
    async function loadCount() {
      try {
        setLoading(true)
        const unreadCount = await getUnreadNotificationsCount()
        setCount(unreadCount)
      } catch (err) {
        console.error('Error al cargar conteo de notificaciones:', err)
      } finally {
        setLoading(false)
      }
    }

    loadCount()

    // Actualizar el conteo cada minuto
    const interval = setInterval(loadCount, 60000)
    return () => clearInterval(interval)
  }, [])

  // Actualizar el conteo cuando se cierra el popover
  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen)
    
    // Si se cierra el popover, actualizar el conteo
    if (!newOpen) {
      setTimeout(async () => {
        const unreadCount = await getUnreadNotificationsCount()
        setCount(unreadCount)
      }, 500)
    }
  }

  return (
    <Popover open={open} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="icon" className={className}>
          <Bell className="h-5 w-5" />
          {count > 0 && !loading && (
            <span className="absolute top-1 right-1 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-[10px] font-medium text-white">
              {count > 9 ? '9+' : count}
            </span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="end">
        <NotificationList 
          limit={5} 
          showHeader={true} 
          showFooter={true} 
          className="border-0 shadow-none"
        />
      </PopoverContent>
    </Popover>
  )
}
