import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { Clock, AlertCircle } from "lucide-react";

export interface Task {
  id: string;
  title: string;
  completed: boolean;
  dueDate?: string | Date;
  priority: "low" | "medium" | "high";
  project?: string;
  assignedTo?: {
    name: string;
    avatar?: string;
  };
}

interface TaskListProps {
  tasks: Task[];
  className?: string;
  title?: string;
  limit?: number;
  onTaskComplete?: (taskId: string, completed: boolean) => void;
}

export function TaskList({
  tasks,
  className,
  title = "Tareas Pendientes",
  limit = 5,
  onTaskComplete,
}: TaskListProps) {
  const displayTasks = tasks.slice(0, limit);

  // Función para formatear la fecha
  const formatDate = (date?: string | Date) => {
    if (!date) return "";

    const d = new Date(date);
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // Si es hoy
    if (d.toDateString() === now.toDateString()) {
      return "Hoy";
    }

    // Si es mañana
    if (d.toDateString() === tomorrow.toDateString()) {
      return "Mañana";
    }

    // Formato de fecha normal
    return d.toLocaleDateString();
  };

  // Función para verificar si una tarea está vencida
  const isOverdue = (date?: string | Date) => {
    if (!date) return false;
    const d = new Date(date);
    const now = new Date();
    return d < now && d.toDateString() !== now.toDateString();
  };

  // Función para obtener el color del badge según la prioridad
  const getPriorityColor = (priority: Task["priority"]) => {
    switch (priority) {
      case "high":
        return "bg-red-500 hover:bg-red-600";
      case "medium":
        return "bg-amber-500 hover:bg-amber-600";
      case "low":
        return "bg-green-500 hover:bg-green-600";
      default:
        return "bg-gray-500 hover:bg-gray-600";
    }
  };

  // Función para obtener el texto de la prioridad
  const getPriorityText = (priority: Task["priority"]) => {
    switch (priority) {
      case "high":
        return "Alta";
      case "medium":
        return "Media";
      case "low":
        return "Baja";
      default:
        return "Normal";
    }
  };

  const handleTaskChange = (taskId: string, checked: boolean) => {
    if (onTaskComplete) {
      onTaskComplete(taskId, checked);
    }
  };

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>{title}</CardTitle>
        <Button variant="outline" size="sm">
          Ver todas
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {displayTasks.length > 0 ? (
            displayTasks.map((task) => (
              <div
                key={task.id}
                className={cn(
                  "flex items-start space-x-4 rounded-md border p-3",
                  task.completed && "bg-muted/50"
                )}
              >
                <Checkbox
                  checked={task.completed}
                  onCheckedChange={(checked) => handleTaskChange(task.id, !!checked)}
                  className="mt-1"
                />
                <div className="flex-1 space-y-1">
                  <p
                    className={cn(
                      "font-medium",
                      task.completed && "line-through text-muted-foreground"
                    )}
                  >
                    {task.title}
                  </p>
                  {task.project && (
                    <p className="text-xs text-muted-foreground">
                      Proyecto: {task.project}
                    </p>
                  )}
                  <div className="flex flex-wrap items-center gap-2 pt-1">
                    <Badge
                      className={cn(
                        "px-1 py-0 text-xs",
                        getPriorityColor(task.priority)
                      )}
                    >
                      {getPriorityText(task.priority)}
                    </Badge>
                    {task.dueDate && (
                      <div
                        className={cn(
                          "flex items-center text-xs",
                          isOverdue(task.dueDate) && !task.completed
                            ? "text-red-500"
                            : "text-muted-foreground"
                        )}
                      >
                        {isOverdue(task.dueDate) && !task.completed ? (
                          <AlertCircle className="mr-1 h-3 w-3" />
                        ) : (
                          <Clock className="mr-1 h-3 w-3" />
                        )}
                        {isOverdue(task.dueDate) && !task.completed
                          ? "Vencida: "
                          : "Vence: "}
                        {formatDate(task.dueDate)}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))
          ) : (
            <p className="text-center text-muted-foreground">No hay tareas pendientes</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
