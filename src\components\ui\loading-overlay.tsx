'use client';

import { useState, useEffect } from 'react';
import { Loader2 } from '@/components/ui/icons';

interface LoadingOverlayProps {
  isLoading: boolean;
  message?: string;
  progress?: number;
  progressStatus?: string;
}

export function LoadingOverlay({
  isLoading,
  message = 'Cargando...',
  progress = 0,
  progressStatus = ''
}: LoadingOverlayProps) {
  const [dots, setDots] = useState('');

  // Efecto para animar los puntos suspensivos
  useEffect(() => {
    if (!isLoading) return;

    const interval = setInterval(() => {
      setDots(prev => {
        if (prev.length >= 3) return '';
        return prev + '.';
      });
    }, 500);

    return () => clearInterval(interval);
  }, [isLoading]);

  if (!isLoading) return null;

  return (
    <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex flex-col items-center justify-center">
      <div className="bg-card rounded-lg shadow-lg p-6 max-w-md w-full mx-4 border">
        <div className="flex flex-col items-center text-center space-y-4">
          <Loader2 className="h-12 w-12 animate-spin text-primary" />
          <h3 className="text-lg font-medium">
            {message}{dots}
          </h3>

          {progressStatus && (
            <p className="text-sm text-muted-foreground">{progressStatus}</p>
          )}

          {progress > 0 && (
            <div className="w-full space-y-2">
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>Progreso</span>
                <span>{Math.round(progress)}%</span>
              </div>
              <div className="w-full bg-secondary rounded-full h-2">
                <div
                  className="bg-primary h-2 rounded-full transition-all duration-300 ease-in-out"
                  style={{ width: `${progress}%` }}
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
