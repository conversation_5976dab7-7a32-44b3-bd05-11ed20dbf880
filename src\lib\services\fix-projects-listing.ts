/**
 * @file Servicio para corregir problemas de listado de proyectos
 * @description Proporciona funciones para diagnosticar y corregir problemas con el listado de proyectos
 */

import { createClient } from '@/lib/supabase/client';
import { localDb } from '@/lib/db/local-database';


/**
 * Interfaz para los resultados del diagnóstico
 */
interface DiagnosticResult {
  success: boolean;
  message: string;
  details: {
    projectsInDb: number;
    projectsVisible: number;
    missingFields: string[];
    roleIssues: boolean;
    accessIssues: boolean;
    structuralIssues: boolean;
  };
  fixApplied?: boolean;
}

/**
 * Servicio para diagnosticar y corregir problemas de listado de proyectos
 */
export class ProjectsFixService {
  private static instance: ProjectsFixService;
  private supabase = createClient();

  /**
   * Constructor privado para implementar el patrón Singleton
   */
  private constructor() {}

  /**
   * Obtiene la instancia única del servicio
   */
  public static getInstance(): ProjectsFixService {
    if (!ProjectsFixService.instance) {
      ProjectsFixService.instance = new ProjectsFixService();
    }
    return ProjectsFixService.instance;
  }

  /**
   * Diagnostica problemas con el listado de proyectos
   */
  public async diagnoseProjectsListing(): Promise<DiagnosticResult> {
    try {
      console.log('Iniciando diagnóstico de listado de proyectos...');

      // 1. Verificar proyectos en la base de datos
      const { data: projectsData, error: projectsError } = await this.supabase
        .from('projects')
        .select('id, name, status')
        .order('updated_at', { ascending: false });

      if (projectsError) {
        console.error('Error al obtener proyectos:', projectsError);
        return {
          success: false,
          message: 'Error al obtener proyectos de la base de datos',
          details: {
            projectsInDb: 0,
            projectsVisible: 0,
            missingFields: [],
            roleIssues: false,
            accessIssues: true,
            structuralIssues: false
          }
        };
      }

      // 2. Verificar campos obligatorios
      const missingFields: string[] = [];
      const projectsWithMissingFields = projectsData?.filter(project => {
        const missing: string[] = [];

        if (!project.name) missing.push('name');
        if (!project.status) missing.push('status');

        if (missing.length > 0) {
          missingFields.push(...missing);
          return true;
        }
        return false;
      }) || [];

      // 3. Verificar problemas de roles y acceso
      const { data: sessionData } = await this.supabase.auth.getSession();
      const userId = sessionData?.session?.user?.id;

      // Verificar si el usuario tiene acceso a los proyectos
      const { data: userData, error: userError } = await this.supabase
        .from('users')
        .select('role')
        .eq('id', userId || '')
        .single();

      const roleIssuesBool = typeof userError === 'boolean' ? userError : !!userError || !userData || !userData.role;

      // 4. Verificar proyectos en la caché local
      const localProjects = await localDb.projects.toArray();

      return {
        success: true,
        message: 'Diagnóstico completado',
        details: {
          projectsInDb: projectsData?.length || 0,
          projectsVisible: localProjects.length,
          missingFields: [...new Set(missingFields)], // Eliminar duplicados
          roleIssues: roleIssuesBool,
          accessIssues: false,
          structuralIssues: projectsWithMissingFields.length > 0
        }
      };
    } catch (error) {
      console.error('Error en diagnóstico:', error);
      return {
        success: false,
        message: 'Error al realizar diagnóstico',
        details: {
          projectsInDb: 0,
          projectsVisible: 0,
          missingFields: [],
          roleIssues: false,
          accessIssues: false,
          structuralIssues: false
        }
      };
    }
  }

  /**
   * Corrige problemas con el listado de proyectos
   */
  public async fixProjectsListing(): Promise<DiagnosticResult> {
    try {
      console.log('Iniciando corrección de listado de proyectos...');

      // 1. Diagnosticar primero
      const diagnosis = await this.diagnoseProjectsListing();

      if (!diagnosis.success) {
        return {
          ...diagnosis,
          message: 'No se pudo completar el diagnóstico previo a la corrección'
        };
      }

      // 2. Corregir campos faltantes en proyectos
      if (diagnosis.details.structuralIssues) {
        console.log('Corrigiendo campos faltantes en proyectos...');

        // Obtener todos los proyectos
        const { data: projects, error: projectsError } = await this.supabase
          .from('projects')
          .select('id, name, status');

        if (projectsError) {
          console.error('Error al obtener proyectos para corrección:', projectsError);
          return {
            ...diagnosis,
            message: 'Error al obtener proyectos para corrección'
          };
        }

        // Corregir cada proyecto con campos faltantes
        for (const project of projects || []) {
          const updates: Record<string, unknown> = {};

          if (!project.status) {
            updates.status = 'pending';
          }

          // Solo actualizar si hay cambios
          if (Object.keys(updates).length > 0) {
            const { error: updateError } = await this.supabase
              .from('projects')
              .update(updates)
              .eq('id', project.id);

            if (updateError) {
              console.error(`Error al actualizar proyecto ${project.id}:`, updateError);
            } else {
              console.log(`Proyecto ${project.id} actualizado correctamente`);
            }
          }
        }
      }

      // 3. Limpiar caché local para forzar recarga
      console.log('Limpiando caché local...');
      await localDb.projects.clear();

      // 4. Realizar diagnóstico final
      const finalDiagnosis = await this.diagnoseProjectsListing();

      return {
        ...finalDiagnosis,
        message: 'Corrección aplicada. Por favor, recargue la página para ver los cambios.',
        fixApplied: true
      };
    } catch (error: unknown) {
      console.error('Error en corrección:', error);

      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

      return {
        success: false,
        message: `Error al realizar corrección: ${errorMessage}`,
        details: {
          projectsInDb: 0,
          projectsVisible: 0,
          missingFields: [],
          roleIssues: false,
          accessIssues: false,
          structuralIssues: false
        }
      };
    }
  }
}

// Exportar instancia única
export const projectsFixService = ProjectsFixService.getInstance();
