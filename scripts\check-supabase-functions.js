// Script para verificar si las funciones SQL necesarias existen en Supabase
// Ejecutar con: node scripts/check-supabase-functions.js

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Obtener variables de entorno
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

console.log('Verificando variables de entorno...');
console.log('NEXT_PUBLIC_SUPABASE_URL:', supabaseUrl ? 'Configurado' : 'No configurado');
console.log('SUPABASE_SERVICE_ROLE_KEY:', supabaseServiceKey ? 'Configurado' : 'No configurado');

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Error: NEXT_PUBLIC_SUPABASE_URL y SUPABASE_SERVICE_ROLE_KEY deben estar definidos en el archivo .env');
  process.exit(1);
}

// Crear cliente de Supabase
console.log('Creando cliente de Supabase...');
const supabase = createClient(supabaseUrl, supabaseServiceKey);
console.log('Cliente de Supabase creado');

/**
 * Verifica si una función existe en la base de datos
 * @param {string} functionName - Nombre de la función a verificar
 */
async function checkFunctionExists(functionName) {
  try {
    console.log(`Verificando si la función ${functionName} existe...`);
    
    // Intentar ejecutar una consulta simple
    const { data, error } = await supabase
      .from('_pgsql')
      .select('*')
      .limit(1);
    
    if (error) {
      console.log('Error al ejecutar consulta simple:', error);
    } else {
      console.log('Consulta simple ejecutada correctamente:', data);
    }
    
    // Intentar ejecutar una consulta directa a pg_proc
    const { data: pgProcData, error: pgProcError } = await supabase
      .from('pg_proc')
      .select('proname')
      .eq('proname', functionName)
      .limit(1);
    
    if (pgProcError) {
      console.log(`Error al verificar función ${functionName} en pg_proc:`, pgProcError);
    } else {
      console.log(`Resultado de verificar función ${functionName} en pg_proc:`, pgProcData);
      return pgProcData && pgProcData.length > 0;
    }
    
    return false;
  } catch (error) {
    console.error(`Error al verificar función ${functionName}:`, error);
    return false;
  }
}

/**
 * Función principal
 */
async function main() {
  try {
    console.log('Verificando funciones SQL en Supabase...');
    
    // Lista de funciones a verificar
    const functions = ['pgsql', 'function_exists', 'exec_sql'];
    
    // Verificar cada función
    for (const func of functions) {
      const exists = await checkFunctionExists(func);
      console.log(`Función ${func} existe: ${exists}`);
    }
    
    console.log('Verificación completada');
    process.exit(0);
  } catch (error) {
    console.error('Error inesperado:', error);
    process.exit(1);
  }
}

// Ejecutar la función principal
main();
