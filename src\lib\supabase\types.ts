export type Database = {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          updated_at: string | null
          username: string | null
          full_name: string | null
          avatar_url: string | null
          role: 'admin' | 'user' | null
        }
        Insert: {
          id: string
          updated_at?: string | null
          username?: string | null
          full_name?: string | null
          avatar_url?: string | null
          role?: 'admin' | 'user' | null
        }
        Update: {
          id?: string
          updated_at?: string | null
          username?: string | null
          full_name?: string | null
          avatar_url?: string | null
          role?: 'admin' | 'user' | null
        }
      },
      users: {
        Row: {
          id: string
          email: string
          first_name: string | null
          last_name: string | null
          avatar_url: string | null
          role: string[] | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id: string
          email: string
          first_name?: string | null
          last_name?: string | null
          avatar_url?: string | null
          role?: string[] | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          email?: string
          first_name?: string | null
          last_name?: string | null
          avatar_url?: string | null
          role?: string[] | null
          created_at?: string | null
          updated_at?: string | null
        }
      },
      projects: {
        Row: {
          id: string
          name: string
          description: string | null
          start_date: string | null
          end_date: string | null
          status: string | null
          created_by: string | null
          created_at: string | null
          updated_at: string | null
          owner_id: string | null
          contract_id: string | null
          progress_percent: number | null
          budget_utilization: number | null
          client_id: string | null
          budget: number | null
          priority: string | null
          estimated_hours: number | null
          actual_hours: number | null
          tags: string[] | null
          currency: string | null
          ai_generated: boolean | null
          source_document_id: string | null
          ai_provider: string | null
          root_path: string | null
          project_type: string | null
          last_scanned_at: string | null
          scan_count: number | null
          config: unknown | null
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          start_date?: string | null
          end_date?: string | null
          status?: string | null
          created_by?: string | null
          created_at?: string | null
          updated_at?: string | null
          owner_id?: string | null
          contract_id?: string | null
          progress_percent?: number | null
          budget_utilization?: number | null
          client_id?: string | null
          budget?: number | null
          priority?: string | null
          estimated_hours?: number | null
          actual_hours?: number | null
          tags?: string[] | null
          currency?: string | null
          ai_generated?: boolean | null
          source_document_id?: string | null
          ai_provider?: string | null
          root_path?: string | null
          project_type?: string | null
          last_scanned_at?: string | null
          scan_count?: number | null
          config?: unknown | null
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          start_date?: string | null
          end_date?: string | null
          status?: string | null
          created_by?: string | null
          created_at?: string | null
          updated_at?: string | null
          owner_id?: string | null
          contract_id?: string | null
          progress_percent?: number | null
          budget_utilization?: number | null
          client_id?: string | null
          budget?: number | null
          priority?: string | null
          estimated_hours?: number | null
          actual_hours?: number | null
          tags?: string[] | null
          currency?: string | null
          ai_generated?: boolean | null
          source_document_id?: string | null
          ai_provider?: string | null
          root_path?: string | null
          project_type?: string | null
          last_scanned_at?: string | null
          scan_count?: number | null
          config?: unknown | null
        }
      },
      project_users: {
        Row: {
          id: string
          project_id: string
          user_id: string
          role: string
          created_at: string
          updated_at: string
        }
        Insert: {
          project_id: string
          user_id: string
          role: string
        }
        Update: {
          project_id?: string
          user_id?: string
          role?: string
        }
      },
      documents: {
        Row: {
          id: string
          filename: string
          file_path: string
          file_size: number
          file_type: string
          description: string | null
          category: string | null
          project_id: string | null
          work_order_id: string | null
          created_at: string
          file_url: string
          uploaded_by: string | null
        }
        Insert: {
          filename: string
          file_path: string
          file_size: number
          file_type: string
          description?: string | null
          category?: string | null
          project_id?: string | null
          work_order_id?: string | null
          file_url: string
          uploaded_by?: string | null
        }
        Update: {
          filename?: string
          file_path?: string
          file_size?: number
          file_type?: string
          description?: string | null
          category?: string | null
          project_id?: string | null
          work_order_id?: string | null
          file_url?: string
          uploaded_by?: string | null
        }
      },
      work_orders: {
        Row: {
          id: string
          title: string | null
          status: string | null
          priority: string | null
          assigned_to: string | null
          project_id: string | null
        }
        Insert: {
          id?: string
          title?: string | null
          status?: string | null
          priority?: string | null
          assigned_to?: string | null
          project_id?: string | null
        }
        Update: {
          id?: string
          title?: string | null
          status?: string | null
          priority?: string | null
          assigned_to?: string | null
          project_id?: string | null
        }
      },
      clients: {
        Row: {
          id: string
          name: string | null
          contact_name: string | null
          contact_email: string | null
          contact_phone: string | null
        }
        Insert: {
          id?: string
          name?: string | null
          contact_name?: string | null
          contact_email?: string | null
          contact_phone?: string | null
        }
        Update: {
          id?: string
          name?: string | null
          contact_name?: string | null
          contact_email?: string | null
          contact_phone?: string | null
        }
      },
      work_order_tasks: {
        Row: {
          id: string
          work_order_id: string
          description: string
          is_completed: boolean | null
          created_at: string | null
          completed_at: string | null
          title: string
        }
        Insert: {
          id?: string
          work_order_id: string
          description: string
          is_completed?: boolean | null
          created_at?: string | null
          completed_at?: string | null
          title: string
        }
        Update: {
          id?: string
          work_order_id?: string
          description?: string
          is_completed?: boolean | null
          created_at?: string | null
          completed_at?: string | null
          title?: string
        }
      },
      notifications: {
        Row: {
          id: string
          user_id: string
          title: string
          description: string | null
          type: string | null
          read: boolean | null
          link: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          description?: string | null
          type?: string | null
          read?: boolean | null
          link?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          description?: string | null
          type?: string | null
          read?: boolean | null
          link?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
      },
      ai_document_analyses: {
        Row: {
          id: string
          document_id: string
          provider: string
          analysis_data: unknown
          created_at: string
          status: string
          error_message: string | null
          confidence_score: number | null
          completed_at: string | null
        }
        Insert: {
          document_id: string
          provider: string
          analysis_data?: unknown
          status?: string
          error_message?: string | null
          confidence_score?: number | null
          completed_at?: string | null
        }
        Update: {
          document_id?: string
          provider?: string
          analysis_data?: unknown
          status?: string
          error_message?: string | null
          confidence_score?: number | null
          completed_at?: string | null
        }
      },
      ai_projects: {
        Row: {
          id: string
          project_id: string
          document_id: string | null
          ai_provider: string
          analysis_data: unknown
          confidence_score: number
          deliverables: string[]
          team_requirements: string[]
          scope: string | null
          client_name: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          project_id: string
          document_id?: string | null
          ai_provider: string
          analysis_data: unknown
          confidence_score?: number
          deliverables?: string[]
          team_requirements?: string[]
          scope?: string | null
          client_name?: string | null
        }
        Update: {
          project_id?: string
          document_id?: string | null
          ai_provider?: string
          analysis_data?: unknown
          confidence_score?: number
          deliverables?: string[]
          team_requirements?: string[]
          scope?: string | null
          client_name?: string | null
        }
      },
      ai_provider_configs: {
        Row: {
          id: string
          provider_name: string
          api_key: string
          model_name: string
          is_active: boolean
          priority: number
          created_at: string
          updated_at: string
        }
        Insert: {
          provider_name: string
          api_key: string
          model_name: string
          is_active?: boolean
          priority?: number
        }
        Update: {
          provider_name?: string
          api_key?: string
          model_name?: string
          is_active?: boolean
          priority?: number
          updated_at?: string
        }
      },
      project_stages: {
        Row: {
          id: string
          project_id: string
          name: string
          description: string | null
          stage_order: number
          completed: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          project_id: string
          name: string
          description?: string | null
          stage_order: number
          completed?: boolean
        }
        Update: {
          project_id?: string
          name?: string
          description?: string | null
          stage_order?: number
          completed?: boolean
        }
      }
      // Añade aquí más tablas según sea necesario
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_project_distribution: {
        Returns: {
          name: string
          value: number
        }[]
      }
    }
    Enums: {
      [_ in never]: never
    }
  }
}