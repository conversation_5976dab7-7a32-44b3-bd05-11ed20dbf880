import { <PERSON>ada<PERSON> } from "next"
import { createClient } from "@/lib/supabase/server"
import { ServiceRequestTable } from "@/components/features/service-requests/service-request-table"
import { Button } from "@/components/ui/button"
import Link from "next/link"
import { Plus } from "lucide-react"

export const metadata: Metadata = {
  title: "Service Requests | AdminCore",
  description: "Manage technical service requests",
}

async function getServiceRequests() {
  try {
    const supabase = createClient()

    // Get service requests with related data
    const { data: serviceRequests, error } = await supabase
      .from('service_requests')
      .select(`
        *,
        client:client_id(*),
        assigned_user:assigned_to(id, email, full_name),
        work_order:work_order_id(id, title, status)
      `)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error loading service requests:', error)
      return []
    }

    return serviceRequests || []
  } catch (error) {
    console.error('Error in getServiceRequests:', error)
    return []
  }
}

export default async function ServiceRequestsPage() {
  const serviceRequests = await getServiceRequests()

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Service Requests</h2>
          <p className="text-muted-foreground mt-2">
            Manage technical service requests, maintenance, and repairs.
          </p>
        </div>
        <Button asChild>
          <Link href="/dashboard/service-requests/new">
            <Plus className="mr-2 h-4 w-4" /> New Service Request
          </Link>
        </Button>
      </div>

      <ServiceRequestTable data={serviceRequests} />
    </div>
  )
}
