import * as React from "react"
import { CalendarIcon } from "lucide-react"
import { addDays, format } from "date-fns"
import { es } from "date-fns/locale"
import { DateRange } from "react-day-picker"

import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

interface DateRangePickerProps {
  className?: string
  onChange: (range: DateRange | undefined) => void
}

export function DateRangePicker({ className, onChange }: DateRangePickerProps) {
  const [date, setDate] = React.useState<DateRange | undefined>({
    from: new Date(new Date().setDate(1)), // Primer día del mes actual
    to: new Date(),
  })

  // Presets de rangos de fechas
  const presets = [
    {
      label: "Hoy",
      value: "today",
      dateRange: {
        from: new Date(),
        to: new Date(),
      },
    },
    {
      label: "Ayer",
      value: "yesterday",
      dateRange: {
        from: addDays(new Date(), -1),
        to: addDays(new Date(), -1),
      },
    },
    {
      label: "Últimos 7 días",
      value: "last-7",
      dateRange: {
        from: addDays(new Date(), -6),
        to: new Date(),
      },
    },
    {
      label: "Últimos 30 días",
      value: "last-30",
      dateRange: {
        from: addDays(new Date(), -29),
        to: new Date(),
      },
    },
    {
      label: "Este mes",
      value: "this-month",
      dateRange: {
        from: new Date(new Date().setDate(1)),
        to: new Date(),
      },
    },
  ]

  // Manejar cambio de preset
  const handlePresetChange = (value: string) => {
    const preset = presets.find((p) => p.value === value)
    if (preset) {
      setDate(preset.dateRange)
      onChange(preset.dateRange)
    }
  }

  // Manejar cambio de rango de fechas
  const handleDateChange = (range: DateRange | undefined) => {
    setDate(range)
    onChange(range)
  }

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={"outline"}
            className={cn(
              "w-[300px] justify-start text-left font-normal",
              !date && "text-muted-foreground"
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {date?.from ? (
              date.to ? (
                <>
                  {format(date.from, "LLL dd, y", { locale: es })} -{" "}
                  {format(date.to, "LLL dd, y", { locale: es })}
                </>
              ) : (
                format(date.from, "LLL dd, y", { locale: es })
              )
            ) : (
              <span>Seleccionar fechas</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <div className="flex items-center border-b px-3 py-2">
            <div className="flex-1 text-sm font-medium">Rango de fechas</div>
            <Select
              onValueChange={handlePresetChange}
              defaultValue="this-month"
            >
              <SelectTrigger className="h-8 w-[150px]">
                <SelectValue placeholder="Seleccionar" />
              </SelectTrigger>
              <SelectContent>
                {presets.map((preset) => (
                  <SelectItem key={preset.value} value={preset.value}>
                    {preset.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <Calendar
            initialFocus
            mode="range"
            defaultMonth={date?.from}
            selected={date}
            onSelect={handleDateChange}
            numberOfMonths={2}
            locale={es}
          />
        </PopoverContent>
      </Popover>
    </div>
  )
}
