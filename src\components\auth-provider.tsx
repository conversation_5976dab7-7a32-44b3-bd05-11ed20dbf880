"use client"

import { createContext, useContext, useEffect, useState } from "react"
import { createBrowserClient } from "@supabase/ssr"
import { useRouter } from "next/navigation"
import type { User } from "@supabase/supabase-js"

type AuthContextType = {
  user: User | null
  loading: boolean
  error: string | null
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: true,
  error: null,
})

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()
  // Verificar si estamos en el navegador
  const isBrowser = typeof window !== 'undefined';

  // Obtener el protocolo actual para configurar 'secure' correctamente
  const isSecure = isBrowser ? window.location.protocol === 'https:' : false;

  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        persistSession: true,
        autoRefreshToken: true,
        detectSessionInUrl: true,
        // Configuración de cookies para desarrollo
        cookieOptions: {
          // Tiempo de vida de la cookie en segundos (30 días)
          lifetime: 60 * 60 * 24 * 30,
          // Configuración de seguridad de la cookie
          sameSite: 'lax',
          secure: false, // Desactivar secure para desarrollo local en http
          path: '/',
          domain: '', // Dominio vacío para usar el dominio actual
        },
        storageKey: 'supabase.auth.token', // Clave consistente para almacenamiento
        storage: {
          getItem: (key) => {
            if (isBrowser) {
              const storedItem = localStorage.getItem(key);
              console.log(`[Auth Provider] Getting item: ${key}`, storedItem ? 'Found' : 'Not found');
              return storedItem;
            }
            return null;
          },
          setItem: (key, value) => {
            if (isBrowser) {
              console.log(`[Auth Provider] Setting item: ${key}`);
              localStorage.setItem(key, value);
            }
          },
          removeItem: (key) => {
            if (isBrowser) {
              console.log(`[Auth Provider] Removing item: ${key}`);
              localStorage.removeItem(key);
            }
          }
        }
      },
      global: {
        // Headers para evitar problemas de caché
        headers: {
          'Cache-Control': 'no-store, max-age=0',
        },
      }
    }
  )

  useEffect(() => {
    // Verificar la sesión actual al cargar el componente
    const checkSession = async () => {
      try {
        console.log('Verificando sesión...');

        // Intentar obtener la sesión actual
        const { data, error } = await supabase.auth.getSession();

        if (error) {
          const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

          console.error('Error al obtener la sesión:', errorMessage);
          setError(errorMessage);
          setLoading(false);
          return;
        }

        // Si hay una sesión activa, usarla directamente sin refrescar
        // El refresco se maneja en el hook useSessionPersistence
        if (data?.session?.user) {
          console.log('Sesión encontrada para:', data.session.user.email);
          setUser(data.session.user);

          // Verificar si la sesión está cerca de expirar
          const expiresAt = data.session.expires_at;
          const expiresInMs = expiresAt ? (expiresAt * 1000) - Date.now() : 0;
          console.log(`La sesión expira en ${Math.round(expiresInMs/1000/60)} minutos`);
        } else {
          console.log('No se encontró sesión activa');
          setUser(null);
          // No redirigimos aquí, dejamos que el middleware maneje la redirección
        }
      } catch (err) {
        console.error('Error inesperado al verificar la sesión:', err);
        setError(err instanceof Error ? err.message : 'Error desconocido');
      } finally {
        setLoading(false);
      }
    };

    // Verificar sesión al montar el componente
    checkSession();

    // Suscribirse a cambios en el estado de autenticación
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((event, session) => {
      console.log('Evento de autenticación:', event);

      // Manejar diferentes eventos de autenticación
      switch (event) {
        case 'SIGNED_IN':
          if (session?.user) {
            console.log('Usuario autenticado:', session.user.email);
            setUser(session.user);
            // Almacenar timestamp de inicio de sesión
            localStorage.setItem('auth_signin_time', Date.now().toString());
          }
          break;

        case 'SIGNED_OUT':
          console.log('Usuario cerró sesión');
          setUser(null);
          // Limpiar datos de sesión en localStorage
          localStorage.removeItem('last_session_refresh');
          localStorage.removeItem('auth_signin_time');
          break;

        case 'TOKEN_REFRESHED':
          if (session?.user) {
            console.log('Token refrescado para usuario:', session.user.email);
            setUser(session.user);
          }
          break;

        case 'USER_UPDATED':
          if (session?.user) {
            console.log('Datos de usuario actualizados:', session.user.email);
            setUser(session.user);
          }
          break;

        default:
          // Para otros eventos, actualizar el estado del usuario si hay sesión
          if (session?.user) {
            setUser(session.user);
          } else {
            setUser(null);
          }
      }

      setLoading(false);
    })

    // Limpiar suscripción al desmontar
    return () => {
      subscription.unsubscribe();
    }
  }, [router, supabase])

  return (
    <AuthContext.Provider value={{ user, loading, error }}>
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => {
  return useContext(AuthContext)
}