import React, { useState, useEffect } from "react"
import { <PERSON> } from "lucide-react"
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@/components/ui/popover"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import {
  Notification,
  NotificationTitle,
  NotificationDescription
} from "@/components/ui/notification"
import { cn } from "@/lib/utils"
import { useI18n } from "@/i18n/i18n-context"
import {
  getUserNotifications,
  markNotificationAsRead,
  getUnreadNotificationsCount,
  type Notification as NotificationData
} from "@/lib/services/notification-service"
import Link from "next/link"
import { formatRelativeTime } from '@/lib/utils'

export interface NotificationItem {
  id: string
  title: string
  description?: string
  timestamp: Date | string
  read: boolean
  type?: string
  link?: string
}

interface NotificationCenterProps {
  className?: string
}

export function NotificationCenter({ className }: NotificationCenterProps) {
  const [notifications, setNotifications] = useState<NotificationItem[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const [open, setOpen] = useState(false)
  const [loading, setLoading] = useState(true)
  const { t, locale } = useI18n()

  // Cargar notificaciones
  useEffect(() => {
    const loadNotifications = async () => {
      try {
        setLoading(true)
        // Obtener notificaciones usando el servicio
        const notificationsData = await getUserNotifications(10)

        if (notificationsData && notificationsData.length > 0) {
          // Convertir a formato de notificaciones para el componente
          const formattedNotifications: NotificationItem[] = notificationsData.map(notification => ({
            id: notification.id,
            title: notification.title || 'Notificación',
            description: notification.description || '',
            timestamp: notification.created_at,
            read: notification.read || false,
            type: notification.type || 'info',
            link: notification.link
          }))

          setNotifications(formattedNotifications)
        } else {
          setNotifications([])
        }

        // Obtener conteo de no leídas
        const count = await getUnreadNotificationsCount()
        setUnreadCount(count)
      } catch (error) {
        console.error('Error al cargar notificaciones:', error)
      } finally {
        setLoading(false)
      }
    }

    loadNotifications()

    // Configurar un intervalo para verificar nuevas notificaciones cada minuto
    const interval = setInterval(() => {
      loadNotifications()
    }, 60000)

    return () => clearInterval(interval)
  }, [])

  // Actualizar cuando se abre/cierra el popover
  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen)

    // Si se cierra el popover, actualizar el conteo
    if (!newOpen) {
      setTimeout(async () => {
        const count = await getUnreadNotificationsCount()
        setUnreadCount(count)
      }, 500)
    }
  }

  // Marcar notificación como leída
  const handleMarkAsRead = async (id: string) => {
    try {
      const success = await markNotificationAsRead(id)

      if (success) {
        // Actualizar el estado local
        setNotifications(prev =>
          prev.map(notification =>
            notification.id === id
              ? { ...notification, read: true }
              : notification
          )
        )
        setUnreadCount(prev => Math.max(0, prev - 1))
      }
    } catch (error) {
      console.error('Error al marcar notificación como leída:', error)
    }
  }

  // Marcar todas como leídas
  const markAllAsRead = async () => {
    try {
      // Importar la función de marcar todas como leídas
      const { markAllNotificationsAsRead } = await import('@/lib/services/notification-service')

      // Marcar todas como leídas en la base de datos
      const count = await markAllNotificationsAsRead()

      if (count > 0) {
        console.log(`${count} notificaciones marcadas como leídas`)

        // Actualizar el estado local
        setNotifications(prev =>
          prev.map(notification => ({ ...notification, read: true }))
        )
        setUnreadCount(0)
      }
    } catch (error) {
      console.error('Error al marcar todas las notificaciones como leídas:', error)

      // Fallback: marcar cada notificación individualmente
      const unreadNotifications = notifications.filter(n => !n.read)

      for (const notification of unreadNotifications) {
        await markNotificationAsRead(notification.id)
      }

      // Actualizar el estado local
      setNotifications(prev =>
        prev.map(notification => ({ ...notification, read: true }))
      )
      setUnreadCount(0)
    }
  }

  // Usar la utilidad centralizada para formatear tiempo relativo
  return (
    <div className={className}>
      <Popover open={open} onOpenChange={handleOpenChange}>
        <PopoverTrigger asChild>
          <Button variant="outline" size="icon" className="relative">
            <Bell className="h-5 w-5" />
            {unreadCount > 0 && (
              <Badge
                className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center bg-red-500 text-white"
              >
                {unreadCount}
              </Badge>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-0" align="end">
          <div className="flex items-center justify-between border-b px-3 py-2">
            <h4 className="font-medium">{t('notifications.title')}</h4>
            {unreadCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={markAllAsRead}
                className="h-auto py-1 px-2 text-xs"
              >
                {t('notifications.markAllAsRead')}
              </Button>
            )}
          </div>
          <Tabs defaultValue="all" className="w-full">
            <TabsList className="w-full grid grid-cols-2">
              <TabsTrigger value="all">{locale === 'es' ? 'Todas' : 'All'}</TabsTrigger>
              <TabsTrigger value="unread">
                {locale === 'es' ? 'No leídas' : 'Unread'} {unreadCount > 0 && `(${unreadCount})`}
              </TabsTrigger>
            </TabsList>
            <TabsContent value="all" className="max-h-[300px] overflow-y-auto">
              {loading ? (
                <div className="flex h-32 items-center justify-center">
                  <p className="text-sm text-muted-foreground">
                    {locale === 'es' ? 'Cargando...' : 'Loading...'}
                  </p>
                </div>
              ) : notifications.length > 0 ? (
                <div className="space-y-2 p-2">
                  {notifications.map((notification) => (
                    <Notification
                      key={notification.id}
                      variant={notification.type as any}
                      className={cn(
                        "cursor-pointer transition-colors hover:bg-muted/50",
                        !notification.read && "border-l-4"
                      )}
                      onClick={() => {
                        if (!notification.read) handleMarkAsRead(notification.id)
                        if (notification.link) {
                          window.location.href = notification.link
                        }
                        setOpen(false)
                      }}
                    >
                      <NotificationTitle>{notification.title}</NotificationTitle>
                      {notification.description && (
                        <NotificationDescription>
                          {notification.description}
                        </NotificationDescription>
                      )}
                      <div className="mt-1 text-xs text-muted-foreground">
                        {formatRelativeTime(notification.timestamp)}
                      </div>
                    </Notification>
                  ))}
                </div>
              ) : (
                <div className="flex h-32 items-center justify-center">
                  <p className="text-sm text-muted-foreground">
                    {t('notifications.noNotifications')}
                  </p>
                </div>
              )}
            </TabsContent>
            <TabsContent value="unread" className="max-h-[300px] overflow-y-auto">
              {loading ? (
                <div className="flex h-32 items-center justify-center">
                  <p className="text-sm text-muted-foreground">
                    {locale === 'es' ? 'Cargando...' : 'Loading...'}
                  </p>
                </div>
              ) : notifications.filter(n => !n.read).length > 0 ? (
                <div className="space-y-2 p-2">
                  {notifications
                    .filter(n => !n.read)
                    .map((notification) => (
                      <Notification
                        key={notification.id}
                        variant={notification.type as any}
                        className="cursor-pointer border-l-4 transition-colors hover:bg-muted/50"
                        onClick={() => {
                          handleMarkAsRead(notification.id)
                          if (notification.link) {
                            window.location.href = notification.link
                          }
                          setOpen(false)
                        }}
                      >
                        <NotificationTitle>{notification.title}</NotificationTitle>
                        {notification.description && (
                          <NotificationDescription>
                            {notification.description}
                          </NotificationDescription>
                        )}
                        <div className="mt-1 text-xs text-muted-foreground">
                          {formatRelativeTime(notification.timestamp)}
                        </div>
                      </Notification>
                    ))}
                </div>
              ) : (
                <div className="flex h-32 items-center justify-center">
                  <p className="text-sm text-muted-foreground">
                    {locale === 'es' ? 'No hay notificaciones sin leer' : 'No unread notifications'}
                  </p>
                </div>
              )}
            </TabsContent>
          </Tabs>
          <div className="border-t p-2">
            <Link href="/dashboard/notifications">
              <Button
                variant="outline"
                size="sm"
                className="w-full"
                onClick={() => setOpen(false)}
              >
                {locale === 'es' ? 'Ver todas las notificaciones' : 'View all notifications'}
              </Button>
            </Link>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}
