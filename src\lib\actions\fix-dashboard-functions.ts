"use server";

import { createClient } from "@/lib/supabase/server";
import { revalidatePath } from "next/cache";

/**
 * Acción del servidor para corregir las funciones del dashboard
 * Ejecuta el script SQL que corrige los errores en las funciones y crea las tablas necesarias
 */
export async function fixDashboardFunctions() {
  try {
    const supabase = createClient();

    // En Vercel, no podemos leer archivos del sistema de archivos en tiempo de ejecución
    // En su lugar, incluimos el contenido SQL directamente en el código
    // Esto es más compatible con entornos serverless
    const sqlContent = `
-- Función para verificar si existe una función en la base de datos
CREATE OR REPLACE FUNCTION public.function_exists(function_name TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  func_exists BOOLEAN;
BEGIN
  -- Verificar si la función existe
  SELECT EXISTS (
    SELECT 1
    FROM pg_proc p
    JOIN pg_namespace n ON p.pronamespace = n.oid
    WHERE n.nspname = 'public'
    AND p.proname = function_name
  ) INTO func_exists;

  RETURN func_exists;
END;
$$;

-- Función para ejecutar SQL dinámico
CREATE OR REPLACE FUNCTION public.exec_sql(sql TEXT)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  EXECUTE sql;
END;
$$;

-- Crear tabla de métricas de rendimiento si no existe
CREATE TABLE IF NOT EXISTS public.performance_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  metric_name TEXT NOT NULL,
  metric_value NUMERIC NOT NULL,
  metric_date TIMESTAMP WITH TIME ZONE DEFAULT now(),
  category TEXT,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

-- Crear tabla de asignación de recursos si no existe
CREATE TABLE IF NOT EXISTS public.resource_allocation (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  resource_type TEXT NOT NULL,
  allocated_amount NUMERIC NOT NULL,
  allocation_date TIMESTAMP WITH TIME ZONE DEFAULT now(),
  project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

-- Función para obtener métricas de rendimiento
CREATE OR REPLACE FUNCTION public.get_performance_metrics(
  time_period TEXT DEFAULT 'week'
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSONB;
  date_filter TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Determinar el filtro de fecha basado en el período de tiempo
  CASE time_period
    WHEN 'day' THEN date_filter := now() - INTERVAL '1 day';
    WHEN 'week' THEN date_filter := now() - INTERVAL '1 week';
    WHEN 'month' THEN date_filter := now() - INTERVAL '1 month';
    WHEN 'quarter' THEN date_filter := now() - INTERVAL '3 month';
    WHEN 'year' THEN date_filter := now() - INTERVAL '1 year';
    ELSE date_filter := now() - INTERVAL '1 week';
  END CASE;

  -- Obtener métricas agregadas
  SELECT jsonb_build_object(
    'metrics', jsonb_agg(
      jsonb_build_object(
        'name', metric_name,
        'value', metric_value,
        'category', category,
        'date', metric_date
      )
    ),
    'summary', jsonb_build_object(
      'total_metrics', COUNT(*),
      'average_value', AVG(metric_value),
      'max_value', MAX(metric_value),
      'min_value', MIN(metric_value)
    )
  )
  INTO result
  FROM public.performance_metrics
  WHERE metric_date >= date_filter;

  RETURN COALESCE(result, '{}'::JSONB);
END;
$$;

-- Función para obtener distribución de proyectos
CREATE OR REPLACE FUNCTION public.get_project_distribution()
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSONB;
BEGIN
  -- Obtener distribución de proyectos por estado
  SELECT jsonb_build_object(
    'by_status', (
      SELECT jsonb_object_agg(status, count)
      FROM (
        SELECT status, COUNT(*) as count
        FROM public.projects
        GROUP BY status
      ) as status_counts
    ),
    'by_client', (
      SELECT jsonb_object_agg(client_name, count)
      FROM (
        SELECT c.name as client_name, COUNT(*) as count
        FROM public.projects p
        JOIN public.clients c ON p.client_id = c.id
        GROUP BY c.name
        ORDER BY count DESC
        LIMIT 5
      ) as client_counts
    ),
    'total_projects', (SELECT COUNT(*) FROM public.projects)
  )
  INTO result;

  RETURN COALESCE(result, '{}'::JSONB);
END;
$$;`;

    // Ejecutar el script SQL
    const { error } = await supabase.rpc("exec_sql", { sql: sqlContent });

    if (error) {
      console.error("Error al ejecutar el script SQL:", error);
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

      return { success: false, error: errorMessage };
    }

    // Revalidar las rutas del dashboard para que se actualicen los datos
    revalidatePath("/dashboard");

    return {
      success: true,
      message: "Funciones del dashboard corregidas correctamente"
    };
  } catch (error) {
    console.error("Error al corregir las funciones del dashboard:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Error desconocido"
    };
  }
}

/**
 * Acción del servidor para verificar si las tablas y funciones existen
 * Devuelve un objeto con el estado de cada tabla y función
 */
export async function checkDashboardDependencies() {
  try {
    const supabase = createClient();

    // Verificar si existen las tablas
    const { data: tables, error: tablesError } = await supabase
      .from("information_schema.tables")
      .select("table_name")
      .eq("table_schema", "public")
      .in("table_name", ["performance_metrics", "resource_allocation"]);

    if (tablesError) {
      console.error("Error al verificar tablas:", tablesError);
      return { success: false, error: tablesError.message };
    }

    // Verificar si existen las funciones
    const { data: functions, error: functionsError } = await supabase
      .rpc("function_exists", { function_name: "get_performance_metrics" });

    const { data: projectDistribution, error: projectDistributionError } = await supabase
      .rpc("function_exists", { function_name: "get_project_distribution" });

    if (functionsError || projectDistributionError) {
      console.error("Error al verificar funciones:", functionsError || projectDistributionError);
      return { success: false, error: (functionsError || projectDistributionError)?.message };
    }

    // Construir resultado
    const existingTables = tables?.map(t => t.table_name) || [];

    return {
      success: true,
      data: {
        tables: {
          performance_metrics: existingTables.includes("performance_metrics"),
          resource_allocation: existingTables.includes("resource_allocation")
        },
        functions: {
          get_performance_metrics: functions,
          get_project_distribution: projectDistribution
        }
      }
    };
  } catch (error) {
    console.error("Error al verificar dependencias del dashboard:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Error desconocido"
    };
  }
}
