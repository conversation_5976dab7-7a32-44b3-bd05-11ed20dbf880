/**
 * @file Servicio para gestionar proyectos con caché local
 * @description Implementa un servicio para gestionar proyectos utilizando IndexedDB como caché
 */

import { createClient } from '@/lib/supabase/client';
import { localDb, CachedProject } from '@/lib/db/local-database';
import { syncService } from '@/lib/services/sync-service';
import { rateLimiterService, OperationType, OperationPriority } from '@/lib/services/rate-limiter-service';
import { v4 as uuidv4 } from 'uuid';
import { errorHandler } from '@/lib/services/error-handler-service';
import { dataValidator } from '@/lib/services/data-validator-service';
import { clientCache } from '@/lib/services/client-cache-service';
import { toast } from "@/hooks/use-toast";

// Interfaces para tipos de datos
interface RawProjectUser {
  id: string;
  project_id: string;
  user_id: string;
  role?: string;
  users?: RawUserData | RawUserData[];
  user?: RawUserData;
}

interface RawUserData {
  id: string;
  email: string;
  full_name?: string;
  first_name?: string;
  last_name?: string;
}

interface RawProjectData {
  id: string;
  name: string;
  description?: string;
  status?: string;
  created_at: string;
  updated_at: string;
  project_users?: RawProjectUser[];
  project_stages?: unknown[];
  work_orders?: unknown[];
  documents?: unknown[];
  [key: string]: unknown;
}

// Función robusta para transformar relaciones de proyectos
function transformProjectRelations(raw: RawProjectData): ProjectWithRelations {
  // project_users
  const projectUsers = Array.isArray(raw.project_users)
    ? raw.project_users.map((pu: RawProjectUser) => {
        const userData = Array.isArray(pu.users) ? pu.users[0] : pu.users || pu.user;
        return {
          ...pu,
          users: undefined, // Limpiar campo redundante
          user: userData
            ? {
                id: userData.id,
                email: userData.email,
                full_name: userData.full_name || (userData.first_name ? (userData.first_name + ' ' + (userData.last_name || '')) : null),
              }
            : null,
        };
      })
    : [];

  // project_stages
  const projectStages = Array.isArray(raw.project_stages) ? raw.project_stages : [];
  // work_orders
  const workOrders = Array.isArray(raw.work_orders) ? raw.work_orders : [];
  // documents
  const documents = Array.isArray(raw.documents) ? raw.documents : [];

  return {
    ...raw,
    project_users: projectUsers,
    project_stages: projectStages,
    work_orders: workOrders,
    documents: documents,
  };
}

// Tipo para proyectos con relaciones
export interface ProjectWithRelations extends CachedProject {
  users?: unknown;
  project_stages?: unknown[];
  project_users?: unknown[];
  work_orders?: unknown[];
  documents?: unknown[];
  clients?: unknown;
  root_path?: string;
}

/**
 * Clase que implementa el servicio de proyectos
 */
export class ProjectsService {
  private static instance: ProjectsService;
  private supabase = createClient();

  /**
   * Constructor privado para implementar el patrón Singleton
   */
  private constructor() {}

  /**
   * Obtiene la instancia única del servicio (patrón Singleton)
   */
  public static getInstance(): ProjectsService {
    if (!ProjectsService.instance) {
      ProjectsService.instance = new ProjectsService();
    }
    return ProjectsService.instance;
  }

  /**
   * Obtiene todos los proyectos con caché local
   */
  public async getProjects(options: {
    userId?: string;
    limit?: number;
    forceRefresh?: boolean;
  } = {}): Promise<CachedProject[]> {
    const { userId, limit = 50, forceRefresh = false } = options;

    try {
      // Generar clave de caché
      const cacheKey = `projects:list:${userId || 'all'}:${limit}`;

      // Verificar si hay datos en caché del cliente
      if (!forceRefresh) {
        const cachedData = clientCache.get<CachedProject[]>(cacheKey);
        if (cachedData && cachedData.length > 0) {
          console.log(`Using client cache for projects list (${cachedData.length} items)`);
          return cachedData;
        }
      }

      // Verificar si estamos en línea
      const isOnline = typeof navigator !== 'undefined' ? navigator.onLine : true;

      // Obtener datos de la base de datos local
      const localProjects = await this.getProjectsFromLocalDb(userId, limit);

      // Si estamos offline o se solicita forzar datos locales, devolver datos locales
      if (!isOnline || forceRefresh === false) {
        // Guardar en caché del cliente
        clientCache.set(cacheKey, localProjects, { ttl: 60 * 5 }); // 5 minutos
        return localProjects;
      }

      try {
        // Intentar obtener datos actualizados de Supabase
        const projects = await this.fetchProjectsFromSupabaseDb(userId, limit);

        // Guardar en caché del cliente
        clientCache.set(cacheKey, projects, { ttl: 60 * 5 }); // 5 minutos

        // Iniciar actualización en segundo plano
        void this.refreshProjectsInBackground(userId);

        return projects;
      } catch (error) {
        console.error('Error fetching projects from Supabase:', error);

        // En caso de error, devolver datos locales
        const localProjects = await this.getProjectsFromLocalDb(userId, limit);

        // Guardar en caché del cliente
        clientCache.set(cacheKey, localProjects, { ttl: 60 * 10 }); // 10 minutos (más tiempo por el error)

        return localProjects;
      }
    } catch (error) {
      console.error('Error getting projects:', error);
      return [];
    }
  }

  /**
   * Obtiene un proyecto por su ID con caché local
   */
  public async getProjectById(id: string, options: { forceRefresh?: boolean } = {}): Promise<ProjectWithRelations | null> {
    const { forceRefresh = false } = options;

    try {
      // Generar clave de caché
      const cacheKey = `projects:detail:${id}`;

      // Verificar si hay datos en caché del cliente
      if (!forceRefresh) {
        const cachedData = clientCache.get<ProjectWithRelations>(cacheKey);
        if (cachedData) {
          console.log(`Returning project ${id} from client cache`);
          return cachedData;
        }
      }

      // Verificar si estamos en línea
      const isOnline = typeof navigator !== 'undefined' ? navigator.onLine : true;

      // Obtener proyecto de la base de datos local
      const localProject = await this.getProjectByIdFromLocalDb(id);

      // Si estamos offline o se solicita forzar datos locales, devolver datos locales
      if (!isOnline || forceRefresh === false) {
        // Guardar en caché del cliente
        if (localProject) {
          clientCache.set(cacheKey, localProject, { ttl: 60 * 10 }); // 10 minutos
        }
        return localProject;
      }

      try {
        // Intentar obtener proyecto actualizado de Supabase
        const project = await this.fetchProjectFromSupabaseDb(id);

        // Guardar en caché del cliente
        if (project) {
          clientCache.set(cacheKey, project, { ttl: 60 * 5 }); // 5 minutos
          
          // Iniciar actualización en segundo plano para mantener datos frescos
          void this.refreshProjectInBackground(id);
        }

        return project;
      } catch (error) {
        console.error(`Error fetching project ${id} from Supabase:`, error);

        // En caso de error, devolver datos locales
        const localProject = await this.getProjectByIdFromLocalDb(id);

        // Guardar en caché del cliente
        if (localProject) {
          clientCache.set(cacheKey, localProject, { ttl: 60 * 10 }); // 10 minutos (más tiempo por el error)
        }

        return localProject;
      }
    } catch (error) {
      console.error(`Error getting project ${id}:`, error);
      return null;
    }
  }

  /**
   * Crea un nuevo proyecto
   */
  public async createProject(projectData: Omit<CachedProject, 'id' | 'created_at' | 'updated_at'>): Promise<CachedProject> {
    try {
      // Generar ID y timestamps
      const now = new Date().toISOString();

      // Validar y sanitizar los campos UUID usando el servicio de validación
      const uuidFields = ['client_id', 'owner_id'];
      const sanitizedData = dataValidator.sanitizeObject(projectData, uuidFields);

      // Validar que los datos sean correctos
      const validationRules = dataValidator.createUUIDValidationRules(uuidFields);
      const validation = dataValidator.validateObject(sanitizedData, validationRules);

      if (!validation.isValid) {
        // Si hay errores de validación, mostrar un mensaje y lanzar un error
        const errorMessage = Object.values(validation.errors).join('. ');
        const errorDetails = errorHandler.handleError(
          new Error(errorMessage),
          { action: 'create_project' }
        );
        errorHandler.showErrorToast(errorDetails);
        throw new Error(errorMessage);
      }

      const newProject: CachedProject = {
        id: uuidv4(),
        ...sanitizedData,
        created_at: now,
        updated_at: now
      };

      // Verificar si estamos en línea
      const isOnline = typeof navigator !== 'undefined' ? navigator.onLine : true;

      if (isOnline) {
        // Si estamos en línea, intentar crear en Supabase primero
        try {
          // Usar el limitador de tasa
          const { data, error } = await rateLimiterService.enqueue(
            OperationType.DATABASE,
            async () => {
              return this.supabase
                .from('projects')
                .insert(newProject)
                .select()
                .single();
            },
            { priority: OperationPriority.HIGH }
          );

          if (error) {
            // Verificar si el error está relacionado con campos obligatorios
            const errorDetails = errorHandler.handleError(error, {
              action: 'create_project_supabase'
            });

            // Mostrar un toast con el error
            errorHandler.showErrorToast(errorDetails);
            throw error;
          }

          if (data) {
            // Guardar en la base de datos local
            await localDb.projects.put({
              ...data,
              _synced: true,
              _local_updated_at: Date.now()
            });

            // Mostrar mensaje de éxito
            toast({
              title: "Proyecto creado",
              description: "El proyecto se ha creado correctamente",
              variant: "default",
            });

            console.log('Project created successfully in Supabase and local DB');
            return data;
          }
        } catch (error) {
          console.error('Error creating project in Supabase:', error);
          console.log('Falling back to local creation with sync queue');
        }
      }

      // Si estamos offline o falló la creación en Supabase, crear localmente y encolar
      console.log('Creating project locally and queueing for sync');
      await syncService.addRecord('projects', newProject, { priority: 4 });

      return newProject;
    } catch (error) {
      console.error('Error creating project:', error);
      throw error;
    }
  }

  /**
   * Actualiza un proyecto existente
   */
  public async updateProject(id: string, projectData: Partial<CachedProject>): Promise<CachedProject> {
    try {
      // Obtener proyecto actual
      const currentProject = await this.getProjectByIdFromLocalDb(id);

      if (!currentProject) {
        const errorDetails = errorHandler.handleError(
          new Error(`Proyecto no encontrado: ${id}`),
          { action: 'update_project' }
        );
        errorHandler.showErrorToast(errorDetails);
        throw new Error(`Project not found: ${id}`);
      }

      // Preparar datos actualizados
      const now = new Date().toISOString();

      // Validar y sanitizar los campos UUID usando el servicio de validación
      const uuidFields = ['client_id', 'owner_id'];
      const sanitizedData = dataValidator.sanitizeObject(projectData, uuidFields);

      // Validar que los datos sean correctos
      const validationRules = dataValidator.createUUIDValidationRules(uuidFields);
      const validation = dataValidator.validateObject(sanitizedData, validationRules);

      if (!validation.isValid) {
        // Si hay errores de validación, mostrar un mensaje y lanzar un error
        const errorMessage = Object.values(validation.errors).join('. ');
        const errorDetails = errorHandler.handleError(
          new Error(errorMessage),
          { action: 'update_project' }
        );
        errorHandler.showErrorToast(errorDetails);
        throw new Error(errorMessage);
      }

      const updatedProject: Partial<CachedProject> = {
        ...sanitizedData,
        updated_at: now
      };

      // Verificar si estamos en línea
      const isOnline = typeof navigator !== 'undefined' ? navigator.onLine : true;

      if (isOnline) {
        // Si estamos en línea, intentar actualizar en Supabase primero
        try {
          // Usar el limitador de tasa
          const { data, error } = await rateLimiterService.enqueue(
            OperationType.DATABASE,
            async () => {
              return this.supabase
                .from('projects')
                .update(updatedProject)
                .eq('id', id)
                .select()
                .single();
            },
            { priority: OperationPriority.HIGH }
          );

          if (error) {
            // Verificar si el error está relacionado con campos obligatorios
            const errorDetails = errorHandler.handleError(error, {
              action: 'update_project_supabase'
            });
            errorHandler.showErrorToast(errorDetails);
            throw error;
          }

          if (data) {
            // Actualizar en la base de datos local
            await localDb.projects.put({
              ...data,
              _synced: true,
              _local_updated_at: Date.now()
            });

            // Devolver el proyecto actualizado
            return data;
          }
        } catch (error) {
          console.error('Error updating project in Supabase:', error);
          console.log('Falling back to local update with sync queue');
        }
      }

      // Si estamos offline o falló la actualización en Supabase, actualizar localmente y encolar
      console.log('Updating project locally and queueing for sync');
      await syncService.addRecord('projects', { id, ...updatedProject }, { priority: 4 });

      // Devolver el proyecto actualizado
      const finalProject = await this.getProjectByIdFromLocalDb(id);
      if (!finalProject) {
        throw new Error(`Project not found after update: ${id}`);
      }
      return finalProject as CachedProject;
    } catch (error) {
      console.error('Error updating project:', error);
      throw error;
    }
  }

  /**
   * Obtiene proyectos de la base de datos local
   */
  private async getProjectsFromLocalDb(userId?: string, limit: number = 50): Promise<CachedProject[]> {
    try {
      // Use type-safe approach to query
      let projects: CachedProject[];
      
      // Filter by userId if provided
      if (userId) {
        projects = await localDb.projects.where('owner_id').equals(userId).toArray();
      } else {
        projects = await localDb.projects.toArray();
      }
      
      return projects.slice(0, limit);
    } catch (error) {
      console.error('Error getting projects from local DB:', error);
      return [];
    }
  }

  /**
   * Obtiene un proyecto de la base de datos local por su ID
   */
  private async getProjectByIdFromLocalDb(id: string): Promise<ProjectWithRelations | null> {
    try {
      const project = await localDb.projects.get(id);
      if (!project) {
        return null;
      }
      return project as ProjectWithRelations;
    } catch (error) {
      console.error(`Error getting project ${id} from local DB:`, error);
      return null;
    }
  }

  /**
   * Obtiene proyectos de Supabase y los almacena en la caché local
   */
  private async fetchProjectsFromSupabaseDb(userId?: string, limit: number = 50): Promise<CachedProject[]> {
    try {
      // Verificar si estamos en línea
      const isOnline = typeof navigator !== 'undefined' ? navigator.onLine : true;

      if (!isOnline) {
        return [];
      }

      // Obtener proyectos de Supabase
      let query = this.supabase
        .from('projects')
        .select(`
          id, name, description, status, created_at, updated_at,
          owner_id, progress_percent, client_id, budget, currency,
          start_date, end_date, root_path,
          project_users(user_id, role)
        `)
        .order('updated_at', { ascending: false })
        .limit(limit);
        
      // Filtrar por usuario si se proporciona
      if (userId) {
        query = query.or(`owner_id.eq.${userId},project_users.user_id.eq.${userId}`);
      }
      
      const { data, error } = await query;

      if (error) {
        throw error;
      }

      if (!data || data.length === 0) {
        return [];
      }

      // Verificar y corregir campos obligatorios
      const projectsWithDefaults = data.map((project: any) => {
        // Asegurar que root_path tenga un valor por defecto
        if (!project.root_path) {
          console.log(`Project ${project.id} missing root_path, setting default value`);
          project.root_path = '/';
        }
        return project;
      });

      // Guardar en la base de datos local
      const projectsWithMeta = projectsWithDefaults.map(project => ({
        ...project,
        _synced: true,
        _local_updated_at: Date.now()
      }));

      try {
        // Guardar los proyectos actualizados (sin borrar los existentes)
        await localDb.projects.bulkPut(projectsWithMeta);

        // Actualizar timestamp de última sincronización
        localStorage.setItem('last_projects_sync', Date.now().toString());

        console.log(`Updated ${data.length} projects in background`);
      } catch (dbError) {
        console.error('Error al guardar proyectos en la base de datos local:', dbError);
      }

      return projectsWithDefaults;
    } catch (error) {
      console.error('Error fetching projects from Supabase:', error);
      throw error;
    }
  }

  /**
   * Obtiene un proyecto de Supabase por su ID
   */
  private async fetchProjectFromSupabaseDb(id: string): Promise<ProjectWithRelations | null> {
    try {
      // Verificar si estamos en línea
      const isOnline = typeof navigator !== 'undefined' ? navigator.onLine : true;

      if (!isOnline) {
        return null;
      }

      // Obtener proyecto de Supabase
      const { data, error } = await this.supabase
        .from('projects')
        .select(`
          *,
          users:owner_id(id, full_name, email),
          project_stages(id, name, description, stage_order, completed),
          project_users(id, user_id, role, user:user_id(id, email, full_name)),
          work_orders(id, title, status, priority, assigned_to),
          documents(id, filename, upload_date, uploaded_by)
        `)
        .eq('id', id)
        .single();

      if (error) {
        throw error;
      }

      if (!data) {
        return null;
      }

      // Transformar relaciones del proyecto
      const transformed = transformProjectRelations(data);

      // Guardar en la base de datos local
      await localDb.projects.put({
        ...transformed,
        _synced: true,
        _local_updated_at: Date.now()
      });

      return transformed as ProjectWithRelations;
    } catch (error) {
      console.error(`Error fetching project ${id} from Supabase:`, error);
      throw error;
    }
  }

  /**
   * Actualiza proyectos en segundo plano
   * @param userId ID de usuario opcional para filtrar proyectos
   */
  private async refreshProjectsInBackground(userId?: string): Promise<void> {
    try {
      // Verificar si estamos en línea
      const isOnline = typeof navigator !== 'undefined' ? navigator.onLine : true;
      
      if (!isOnline) {
        return;
      }
      
      console.log(`Refreshing projects in background for user ${userId || 'all'}`);

      // Usar el limitador de tasa con baja prioridad
      await rateLimiterService.enqueue(
        OperationType.DATABASE,
        async () => {
          let query = this.supabase
            .from('projects')
            .select(`
              id, name, description, status, created_at, updated_at,
              owner_id, progress_percent, client_id, budget, currency,
              start_date, end_date, root_path,
              project_users(user_id, role)
            `)
            .order('updated_at', { ascending: false })
            .limit(100);

          // Filtrar por usuario si se proporciona
          if (userId) {
            // Buscar proyectos donde el usuario es propietario o está asignado
            query = query.or(`owner_id.eq.${userId},project_users.user_id.eq.${userId}`);
          }

          const { data, error } = await query;

          if (error) {
            throw error;
          }

          if (!data || data.length === 0) {
            return;
          }

          // Verificar y corregir campos obligatorios
          const projectsWithDefaults = data.map((project: any) => {
            // Asegurar que root_path tenga un valor por defecto
            if (!project.root_path) {
              console.log(`Project ${project.id} missing root_path, setting default value`);
              project.root_path = '/';
            }
            return project;
          });

          // Guardar en la base de datos local
          const projectsWithMeta = projectsWithDefaults.map(project => ({
            ...project,
            _synced: true,
            _local_updated_at: Date.now()
          }));

          try {
            // Guardar los proyectos actualizados (sin borrar los existentes)
            await localDb.projects.bulkPut(projectsWithMeta);

            // Actualizar timestamp de última sincronización
            localStorage.setItem('last_projects_sync', Date.now().toString());

            console.log(`Updated ${data.length} projects in background`);
          } catch (dbError) {
            console.error('Error al guardar proyectos en la base de datos local:', dbError);
          }
        },
        { priority: OperationPriority.LOW }
      );
    } catch (error) {
      console.error('Error refreshing projects in background:', error);
    }
  }
  
  /**
   * Actualiza un proyecto específico en segundo plano
   * @param id ID del proyecto a actualizar
   */
  private async refreshProjectInBackground(id: string): Promise<void> {
    try {
      // Verificar si estamos en línea
      const isOnline = typeof navigator !== 'undefined' ? navigator.onLine : true;

      if (!isOnline) {
        return;
      }

      console.log(`Refreshing project ${id} in background`);

      // Usar el limitador de tasa con baja prioridad
      await rateLimiterService.enqueue(
        OperationType.DATABASE,
        async () => {
          const { data, error } = await this.supabase
            .from('projects')
            .select(`
              *,
              users:owner_id(id, full_name, email),
              project_stages(id, name, description, stage_order, completed),
              project_users(id, user_id, role, user:user_id(id, email, full_name)),
              work_orders(id, title, status, priority, assigned_to),
              documents(id, filename, upload_date, uploaded_by)
            `)
            .eq('id', id)
            .single();

          if (error) {
            throw error;
          }

          if (!data) {
            return;
          }

          // Guardar en la base de datos local (solo los datos principales, no las relaciones)
          await localDb.projects.put({
            ...data,
            _synced: true,
            _local_updated_at: Date.now()
          });

          console.log(`Updated project ${id} in background`);
        },
        { priority: OperationPriority.LOW }
      );
    } catch (error) {
      console.error(`Error refreshing project ${id} in background:`, error);
    }
  }
}

// Exportar una instancia única del servicio
export const projectsService = ProjectsService.getInstance();
