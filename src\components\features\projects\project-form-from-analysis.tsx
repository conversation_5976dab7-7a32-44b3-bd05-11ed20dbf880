/**
 * @ai-file-description: "Project form component pre-filled with AI analysis data"
 * @ai-related-files: ["project-form.tsx", "ai-document-upload.tsx"]
 * @ai-owner: "File-Based Projects"
 */

"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { createClient } from "@/lib/supabase/client";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { DatePicker } from "@/components/ui/date-picker";
import { <PERSON><PERSON>2, FileText, AlertCircle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { toast } from "@/hooks/use-toast";
import { Badge } from "@/components/ui/badge";

// Form schema
const projectFormSchema = z.object({
  name: z.string().min(3, "Project name must be at least 3 characters"),

  description: z.string().optional(),
  start_date: z.date().optional(),
  end_date: z.date().optional(),
  budget: z.string().optional(),
  currency: z.string().optional(),
  client_name: z.string().optional(),
  scope: z.string().optional(),
});

type ProjectFormValues = z.infer<typeof projectFormSchema>;

interface ProjectFormFromAnalysisProps {
  analysis: unknown;
}

/**
 * Project form component pre-filled with AI analysis data
 *
 * @ai-responsibility: "Displays and allows editing of AI-extracted project information"
 */
export function ProjectFormFromAnalysis({ analysis }: ProjectFormFromAnalysisProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();
  const supabase = createClient();

  // Extract analysis data, handling nested structure
  const rawAnalysisData = analysis.analysis_data || {};
  const extractedData = rawAnalysisData.analysis_data || rawAnalysisData;
  const document = analysis.document || {};
  const confidenceScore = analysis.confidence_score || extractedData.confidence_score || 0;

  // Format dates from ISO strings to Date objects
  const startDate = extractedData.start_date ? new Date(extractedData.start_date) : undefined;
  const endDate = extractedData.end_date ? new Date(extractedData.end_date) : undefined;

  // Default form values from analysis
  const defaultValues: Partial<ProjectFormValues> = {
    name: extractedData.name || "",
    description: extractedData.description || "",
    start_date: startDate,
    end_date: endDate,
    budget: extractedData.budget || "",
    currency: extractedData.currency || "USD",
    client_name: extractedData.client_name || "",
    scope: extractedData.scope || "",
  };

  // Initialize form
  const form = useForm<ProjectFormValues>({
    resolver: zodResolver(projectFormSchema),
    defaultValues,
  });

  // Handle form submission
  const onSubmit = async (data: ProjectFormValues) => {
    setIsSubmitting(true);

    try {
      // Create project
      const { data: project, error: projectError } = await supabase
        .from("projects")
        .insert({
          name: data.name,

          description: data.description || null,
          start_date: data.start_date?.toISOString() || null,
          end_date: data.end_date?.toISOString() || null,
          budget: data.budget || null,
          currency: data.currency || "USD",
          status: "active",
          ai_generated: true,
          source_document_id: document.id ? document.id : null, // Asegurarse de que un string vacío se convierta en null
          ai_provider: analysis.provider || null
        })
        .select()
        .single();

      if (projectError) {
        throw projectError;
      }

      // Link document to project
      if (document.id) {
        await supabase
          .from("documents")
          .update({ project_id: project.id })
          .eq("id", document.id);
      }

      // Create record in ai_projects table
      await supabase
        .from("ai_projects")
        .insert({
          project_id: project.id,
          document_id: document.id || null,
          ai_provider: analysis.provider || null,
          analysis_data: analysis.analysis_data || {},
          confidence_score: analysis.confidence_score || extractedData.confidence_score || 0,
          client_name: data.client_name || null,
          scope: data.scope || null,
          deliverables: extractedData.deliverables || [],
          team_requirements: extractedData.team_requirements || []
        });

      // Create project files for deliverables if available
      if (extractedData.deliverables && Array.isArray(extractedData.deliverables)) {
        for (const deliverable of extractedData.deliverables) {
          if (deliverable) {
            await supabase
              .from("project_files")
              .insert({
                project_id: project.id,
                name: deliverable,
                file_type: "deliverable",
                status: "pending",
              });
          }
        }
      }

      // Create PLANNING.md file
      await supabase
        .from("project_files")
        .insert({
          project_id: project.id,
          name: "PLANNING.md",
          file_type: "documentation",
          status: "active",
          content: generatePlanningMd(data, extractedData),
        });

      // Create TASKS.md file
      await supabase
        .from("project_files")
        .insert({
          project_id: project.id,
          name: "TASKS.md",
          file_type: "documentation",
          status: "active",
          content: generateTasksMd(data, extractedData),
        });

      // Add tags if available
      if (extractedData.tags && Array.isArray(extractedData.tags)) {
        for (const tag of extractedData.tags) {
          if (tag) {
            await supabase
              .from("project_tags")
              .insert({
                project_id: project.id,
                tag_name: tag,
              });
          }
        }
      }

      toast({
        title: "Project Created",
        description: "Project has been created successfully",
      });

      // Redirect to project page
      router.push(`/dashboard/projects/${project.id}`);
    } catch (error) {
      console.error("Error creating project:", error);
      toast({
        title: "Error",
        description: "Failed to create project. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <div className="grid gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Project Information</CardTitle>
              <CardDescription>
                Review and edit the information extracted from your document
              </CardDescription>
              <div className="flex items-center mt-2">
                <Badge variant={confidenceScore > 0.7 ? "default" : "outline"} className="mr-2">
                  {confidenceScore > 0.7 ? "High Confidence" : confidenceScore > 0.4 ? "Medium Confidence" : "Low Confidence"}
                </Badge>
                {document.filename && (
                  <div className="flex items-center text-sm text-muted-foreground">
                    <FileText className="h-4 w-4 mr-1" />
                    <span>Source: {document.filename}</span>
                  </div>
                )}
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Project Name</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="root_path"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Root Path</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="/" />
                    </FormControl>
                    <FormDescription>
                      Base path where the project is located
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="start_date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Start Date</FormLabel>
                      <FormControl>
                        <DatePicker
                          date={field.value}
                          setDate={field.onChange}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="end_date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>End Date</FormLabel>
                      <FormControl>
                        <DatePicker
                          date={field.value}
                          setDate={field.onChange}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="budget"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Budget</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="currency"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Currency</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="client_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Client</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="scope"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Project Scope</FormLabel>
                    <FormControl>
                      <Textarea {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {extractedData.deliverables && extractedData.deliverables.length > 0 && (
                <div>
                  <FormLabel>Deliverables</FormLabel>
                  <ul className="list-disc pl-5 mt-2 space-y-1">
                    {extractedData.deliverables.map((deliverable: string, index: number) => (
                      <li key={index} className="text-sm">{deliverable}</li>
                    ))}
                  </ul>
                  <FormDescription>
                    These deliverables will be added as project files
                  </FormDescription>
                </div>
              )}

              {extractedData.team_requirements && extractedData.team_requirements.length > 0 && (
                <div>
                  <FormLabel>Team Requirements</FormLabel>
                  <ul className="list-disc pl-5 mt-2 space-y-1">
                    {extractedData.team_requirements.map((requirement: string, index: number) => (
                      <li key={index} className="text-sm">{requirement}</li>
                    ))}
                  </ul>
                </div>
              )}

              {extractedData.tags && extractedData.tags.length > 0 && (
                <div>
                  <FormLabel>Tags</FormLabel>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {extractedData.tags.map((tag: string, index: number) => (
                      <Badge key={index} variant="secondary">{tag}</Badge>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push("/dashboard/projects/new")}
                disabled={isSubmitting}
              >
                Cancelar
              </Button>
              <Button type="submit" disabled={isSubmitting} className="bg-green-600 hover:bg-green-700">
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creando Proyecto...
                  </>
                ) : (
                  "Crear Proyecto"
                )}
              </Button>
            </CardFooter>
          </Card>
        </div>
      </form>
    </Form>
  );
}

/**
 * Generate PLANNING.md content
 */
function generatePlanningMd(formData: ProjectFormValues, analysisData: unknown): string {
  return `# Project Planning: ${formData.name}

## Overview
${formData.description || "No description provided."}

## Timeline
- **Start Date:** ${formData.start_date ? formData.start_date.toLocaleDateString() : "Not specified"}
- **End Date:** ${formData.end_date ? formData.end_date.toLocaleDateString() : "Not specified"}

## Budget
- **Amount:** ${formData.budget || "Not specified"} ${formData.currency || ""}
- **Client:** ${formData.client_name || "Not specified"}

## Scope
${formData.scope || "No scope defined."}

## Deliverables
${analysisData.deliverables && analysisData.deliverables.length > 0
  ? analysisData.deliverables.map((d: string) => `- ${d}`).join("\n")
  : "No deliverables specified."
}

## Team Requirements
${analysisData.team_requirements && analysisData.team_requirements.length > 0
  ? analysisData.team_requirements.map((r: string) => `- ${r}`).join("\n")
  : "No team requirements specified."
}

## Tags
${analysisData.tags && analysisData.tags.length > 0
  ? analysisData.tags.map((t: string) => `- ${t}`).join("\n")
  : "No tags specified."
}

---
This document was automatically generated from document analysis.
`;
}

/**
 * Generate TASKS.md content
 */
function generateTasksMd(formData: ProjectFormValues, analysisData: unknown): string {
  // Generate tasks based on deliverables if available
  const tasksList = analysisData.deliverables && analysisData.deliverables.length > 0
    ? analysisData.deliverables.map((deliverable: string) => `- [ ] Complete "${deliverable}"`).join("\n")
    : "- [ ] Define project tasks";

  return `# Project Tasks: ${formData.name}

## To Do
${tasksList}

## In Progress

## Completed

---
This document was automatically generated from document analysis.
`;
}
