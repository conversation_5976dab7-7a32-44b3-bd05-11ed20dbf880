import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { ContractMetrics, ChartData, MonthlyRevenue, TopClient } from '@/lib/types/service-contracts';
import { ServiceContractService } from '@/lib/services/service-contract-service';
import { MetricCard } from '@/components/dashboard/metric-card';
import { PieChart } from '@/components/charts/pie-chart';
import { Bar<PERSON>hart } from '@/components/charts/bar-chart';
import { LineChart } from '@/components/charts/line-chart';
import { formatCurrency } from '@/lib/utils';
import { 
  FileText, 
  AlertTriangle, 
  Calendar, 
  DollarSign,
  Building,
  Tag
} from 'lucide-react';

export function ContractMetricsDashboard() {
  const [metrics, setMetrics] = useState<ContractMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchMetrics = async () => {
      try {
        setLoading(true);
        const data = await ServiceContractService.getContractMetrics();
        setMetrics(data);
        setError(null);
      } catch (err) {
        console.error('Error al cargar métricas de contratos:', err);
        setError('Error al cargar métricas de contratos. Intente nuevamente.');
      } finally {
        setLoading(false);
      }
    };

    fetchMetrics();
  }, []);

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {Array(4).fill(0).map((_, i) => (
            <Skeleton key={i} className="h-32 w-full" />
          ))}
        </div>
        <div className="grid gap-4 md:grid-cols-2">
          {Array(2).fill(0).map((_, i) => (
            <Skeleton key={i} className="h-80 w-full" />
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="bg-destructive/10 p-4 rounded-md text-destructive">
            {error}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!metrics) {
    return null;
  }

  // Preparar datos para el gráfico de ingresos mensuales
  const revenueChartData = metrics.monthly_revenue.map(month => ({
    name: month.month,
    total: month.total,
    paid: month.paid,
    pending: month.pending,
    overdue: month.overdue
  }));

  return (
    <div className="space-y-4">
      {/* Métricas principales */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <MetricCard
          title="Contratos Activos"
          value={metrics.active_contracts}
          icon={FileText}
          iconColor="text-green-500"
          description={`${metrics.expiring_contracts} por vencer en 30 días`}
          href="/dashboard/service-contracts"
        />
        <MetricCard
          title="Contratos Vencidos"
          value={metrics.expired_contracts}
          icon={AlertTriangle}
          iconColor="text-red-500"
          href="/dashboard/service-contracts?tab=expired"
        />
        <MetricCard
          title="Valor Total de Contratos"
          value={formatCurrency(metrics.total_contract_value)}
          icon={DollarSign}
          iconColor="text-blue-600"
          href="/dashboard/service-contracts"
        />
        <MetricCard
          title="Clientes con Contratos"
          value={metrics.top_clients?.length || 0}
          icon={Building}
          iconColor="text-purple-500"
          href="/dashboard/clients"
        />
      </div>

      {/* Gráficos y análisis */}
      <div className="grid gap-4 md:grid-cols-2">
        <Tabs defaultValue="type" className="w-full">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>Contratos de Servicio</CardTitle>
              <CardDescription>
                Distribución por tipo y estado
              </CardDescription>
              <TabsList className="mt-2">
                <TabsTrigger value="type">Por Tipo</TabsTrigger>
                <TabsTrigger value="status">Por Estado</TabsTrigger>
              </TabsList>
            </CardHeader>
            <CardContent>
              <TabsContent value="type" className="mt-0 pt-0">
                <div className="h-60">
                  <PieChart 
                    data={metrics.contracts_by_type || []} 
                    colors={[
                      '#3b82f6', // blue-500 - basic
                      '#10b981', // emerald-500 - standard
                      '#a855f7', // purple-500 - premium
                      '#f97316', // orange-500 - custom
                    ]}
                  />
                </div>
              </TabsContent>
              <TabsContent value="status" className="mt-0 pt-0">
                <div className="h-60">
                  <PieChart 
                    data={metrics.contracts_by_status || []} 
                    colors={[
                      '#22c55e', // green-500 - active
                      '#94a3b8', // slate-400 - pending
                      '#ef4444', // red-500 - expired
                      '#f97316', // orange-500 - cancelled
                    ]}
                  />
                </div>
              </TabsContent>
            </CardContent>
          </Card>
        </Tabs>

        <Card>
          <CardHeader>
            <CardTitle>Ingresos Mensuales</CardTitle>
            <CardDescription>
              Ingresos por contratos en los últimos 12 meses
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-60">
              <LineChart 
                data={revenueChartData} 
                categories={['total', 'paid', 'pending', 'overdue']}
                colors={[
                  '#3b82f6', // blue-500 - total
                  '#22c55e', // green-500 - paid
                  '#f97316', // orange-500 - pending
                  '#ef4444', // red-500 - overdue
                ]}
                index="name"
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Clientes principales */}
      <Card>
        <CardHeader>
          <CardTitle>Clientes Principales</CardTitle>
          <CardDescription>
            Clientes con mayor valor en contratos
          </CardDescription>
        </CardHeader>
        <CardContent>
          {metrics.top_clients && metrics.top_clients.length > 0 ? (
            <div className="space-y-4">
              {metrics.top_clients.map((client, index) => (
                <div 
                  key={index} 
                  className="flex items-start gap-4 p-3 rounded-lg border border-border hover:bg-muted/50 transition-colors"
                >
                  <div className="bg-primary/10 p-2 rounded-full">
                    <Building className="h-4 w-4 text-primary" />
                  </div>
                  <div className="flex-1 space-y-1">
                    <div className="flex items-center justify-between">
                      <p className="font-medium">{client.client_name}</p>
                      <span className="font-medium">
                        {formatCurrency(client.total_value)}
                      </span>
                    </div>
                    <div className="flex flex-wrap gap-2 text-sm text-muted-foreground">
                      <span className="flex items-center gap-1">
                        <FileText className="h-3 w-3" />
                        {client.contracts_count} contratos
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-6 text-muted-foreground">
              No hay datos de clientes para mostrar.
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
