/**
 * @file Tests for fixed project form validation
 * @description Tests to verify the validation fixes are working correctly
 */

import React from 'react'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ProjectForm } from '@/components/features/projects/project-form'
import { createValidationError } from '@/components/features/projects/enhanced-form-validation-alert'


// Mock Supabase client
vi.mock('@/lib/supabase/client', async (importOriginal) => {
  const actual = await importOriginal() as any;
  return {
    ...actual,
    createBrowserSupabaseClient: () => actual.createMockClient(),
    createClient: () => actual.createMockClient(),
  };
});

// Mock useSupabaseSession
vi.mock('@/hooks/use-supabase-session', () => ({
  useSupabaseSession: () => ({
    session: { 
      user: { 
        id: 'test-user-id', 
        email: '<EMAIL>',
        user_metadata: { name: 'Test User' }
      }
    },
    loading: false,
  }),
}));

// Mock our UI Select wrapper as native <select> with <option>
vi.mock('@/components/ui/select', () => {
  const React = require('react')
  return {
    __esModule: true,
    Select: ({ children, name, defaultValue, onValueChange, ...props }: any) => (
      <select name={name} defaultValue={defaultValue} onChange={e => onValueChange?.(e.target.value)} {...props}>
        {React.Children.toArray(children)
          .filter((c: any) => c.props?.value)
          .map((c: any) => <option key={c.props.value} value={c.props.value}>{c.props.children}</option>)}
      </select>
    ),
    SelectTrigger: ({ children }: any) => <>{children}</>,
    SelectContent: ({ children }: any) => <>{children}</>,
    SelectItem: ({ children, value, ...props }: any) => <option value={value} {...props}>{children}</option>,
    SelectGroup: ({ children }: any) => <>{children}</>,
    SelectValue: ({ children }: any) => <>{children}</>,
    SelectLabel: ({ children, htmlFor }: any) => <label htmlFor={htmlFor}>{children}</label>,
    SelectSeparator: () => null,
    SelectScrollUpButton: () => null,
    SelectScrollDownButton: () => null,
  }
})

// Custom render with mocks
const renderWithMocks = (ui: React.ReactElement) => {
  return render(ui);
};

// Mock currency detection
vi.mock('@/lib/currency', () => ({
  detectUserCurrency: () => Promise.resolve('CLP'),
  formatCurrency: (value: number, currency: string) => `${currency} ${value}`
}))

// Mock data validator
vi.mock('@/lib/services/data-validator-service', () => ({
  dataValidator: {
    validateRequiredFields: (_data: unknown, _fields: string[]) => ({
      isValid: true,
      errors: {}
    }),
    sanitizeObject: (data: unknown) => data,
    sanitizeTextFields: (data: unknown) => data,
    isValidUUID: (value: string) => /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(value)
  }
}))

describe('ProjectForm - Fixed Validation', () => {
  const mockOnSubmit = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render form with required field indicators', async () => {
    renderWithMocks(<ProjectForm onSubmit={mockOnSubmit} />)

    // Check that required fields are marked with asterisk
    expect(await screen.findByText('Nombre del proyecto')).toBeInTheDocument()
    expect(await screen.findByText('*')).toBeInTheDocument()
    expect(await screen.findByText('Estado')).toBeInTheDocument()
  })

  it('should show validation errors for empty required fields', async () => {
    const user = userEvent.setup()
    renderWithMocks(<ProjectForm onSubmit={mockOnSubmit} />)

    // Try to submit without filling required fields
    const submitButton = await screen.findByRole('button', { name: /crear proyecto/i })
    await user.click(submitButton)

    // Should show validation errors
    await waitFor(() => {
      expect(screen.getByText(/obligatorio/i)).toBeInTheDocument()
    })
  })

  it('should validate project name length', async () => {
    const user = userEvent.setup()
    renderWithMocks(<ProjectForm onSubmit={mockOnSubmit} />)

    // Enter a name that's too short
    const nameInput = await screen.findByPlaceholderText(/ingrese el nombre del proyecto/i)
    await user.type(nameInput, 'ab')

    // Select a status
    const statusSelect = await screen.findByLabelText(/estado/i)
    await user.selectOptions(statusSelect, 'pending')

    // Try to submit
    const submitButton = await screen.findByRole('button', { name: /crear proyecto/i })
    await user.click(submitButton)

    // Should show validation error for short name
    await waitFor(() => {
      expect(screen.getByText(/al menos 3 caracteres/i)).toBeInTheDocument()
    })
  })

  it('should only show valid status options', async () => {
    renderWithMocks(<ProjectForm onSubmit={mockOnSubmit} />)

    const statusSelect = await screen.findByLabelText(/estado/i)
    const options = screen.getAllByRole('option')
    const optionValues = options.map(opt => opt.textContent)
    expect(optionValues).toEqual([
      'Pendiente',
      'En Progreso',
      'Completado',
      'Cancelado',
    ])
    // Should NOT show invalid statuses
    expect(optionValues).not.toContain('Planificación')
    expect(optionValues).not.toContain('En Pausa')
  })

  it('should submit valid form data', async () => {
    const user = userEvent.setup()
    renderWithMocks(<ProjectForm onSubmit={mockOnSubmit} />)

    // Fill in required fields
    const nameInput = await screen.findByPlaceholderText(/ingrese el nombre del proyecto/i)
    await user.type(nameInput, 'Test Project Name')

    // Select a status
    const statusSelect = await screen.findByLabelText(/estado/i)
    await user.selectOptions(statusSelect, 'pending')

    // Submit the form
    const submitButton = await screen.findByRole('button', { name: /crear proyecto/i })
    await user.click(submitButton)

    // Should call onSubmit with valid data
    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'Test Project Name',
          status: 'pending'
        })
      )
    })
  })

  it('should show enhanced validation alerts with suggestions', async () => {
    const user = userEvent.setup()
    renderWithMocks(<ProjectForm onSubmit={mockOnSubmit} />)

    // Try to submit without filling required fields
    const submitButton = await screen.findByRole('button', { name: /crear proyecto/i })
    await user.click(submitButton)

    // Should show enhanced validation alert
    await waitFor(() => {
      expect(screen.getByText(/obligatorio/i)).toBeInTheDocument()
      expect(screen.getByText(/descriptivo/i)).toBeInTheDocument()
    })
  })
})

describe('createValidationError utility', () => {
  it('should create validation error objects correctly', () => {
    const error = createValidationError(
      'Test error message',
      'test_field',
      'error',
      'Test suggestion'
    )

    expect(error).toEqual({
      message: 'Test error message',
      field: 'test_field',
      type: 'error',
      suggestion: 'Test suggestion'
    })
  })

  it('should handle optional parameters', () => {
    const error = createValidationError('Test message')

    expect(error).toEqual({
      message: 'Test message',
      field: undefined,
      type: 'error',
      suggestion: undefined
    })
  })
})
