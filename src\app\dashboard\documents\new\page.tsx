import { Suspense } from "react"
import { <PERSON><PERSON>ef<PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import Link from "next/link"
import { NewDocumentContent } from "@/components/features/documents/new-document-content"

export default function NewDocumentPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <Button variant="outline" size="sm" asChild>
            <Link href="/dashboard/documents">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Volver
            </Link>
          </Button>
          <h2 className="text-3xl font-bold tracking-tight mt-2">Subir Documento</h2>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Nuevo Documento</CardTitle>
          <CardDescription>
            Sube un nuevo documento al sistema
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<div className="p-8 text-center">Cargando componente de subida...</div>}>
            <NewDocumentContent />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  )
}
