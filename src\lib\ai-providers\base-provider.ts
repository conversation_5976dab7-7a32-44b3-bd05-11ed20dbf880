/**
 * @ai-file-description: "Base abstract class for AI providers with common functionality"
 * @ai-related-files: ["provider-interface.ts", "providers/gemini-provider.ts", "providers/openai-provider.ts", "providers/deepseek-provider.ts"]
 * @ai-owner: "File-Based Projects"
 */

import { AIProvider, AIProviderConfig, DocumentAnalysisResult } from './provider-interface';

/**
 * Base class for AI providers with common functionality
 * 
 * @ai-responsibility: "Provides common implementation for all AI providers"
 */
export abstract class BaseAIProvider implements AIProvider {
  protected apiKey: string;
  protected modelName: string;
  protected maxTokens?: number;
  protected temperature?: number;
  
  constructor(config: AIProviderConfig) {
    this.apiKey = config.apiKey;
    this.modelName = config.modelName;
    this.maxTokens = config.maxTokens;
    this.temperature = config.temperature ?? 0.2; // Default temperature
  }
  
  /**
   * Analyzes document text and extracts project information
   * Must be implemented by each provider
   */
  abstract analyzeDocument(documentText: string): Promise<DocumentAnalysisResult>;
  
  /**
   * Validates the provider configuration
   */
  validateConfig(): boolean {
    return Boolean(this.apiKey) && Boolean(this.modelName);
  }
  
  /**
   * Gets the name of the provider
   * Must be implemented by each provider
   */
  abstract getProviderName(): string;
  
  /**
   * Gets the display name of the provider for UI
   * Can be overridden by each provider
   */
  getProviderDisplayName(): string {
    // Default implementation capitalizes the provider name
    const name = this.getProviderName();
    return name.charAt(0).toUpperCase() + name.slice(1);
  }
  
  /**
   * Gets the system prompt for document analysis
   * Can be overridden by each provider for customization
   */
  protected getSystemPrompt(): string {
    return `
You are a specialized AI assistant for engineering project management. Your task is to analyze project documents and extract key information in a structured format.

Extract the following information from the document:
1. Project name
2. Project description
3. Start date (in ISO format YYYY-MM-DD)
4. End date (in ISO format YYYY-MM-DD)
5. Budget amount (as a string)
6. Currency (USD, CLP, etc.)
7. Client name
8. Key deliverables (as an array)
9. Project scope
10. Team requirements (as an array)
11. Tags (as an array of keywords)

Format your response as a JSON object with these fields. If information is not found, use null.
Provide a confidence_score between 0 and 1 indicating your confidence in the extracted information.
`;
  }
  
  /**
   * Gets the user prompt for document analysis
   * Can be overridden by each provider for customization
   * 
   * @param documentText The text content of the document
   */
  protected getUserPrompt(documentText: string): string {
    return `
Analyze the following document and extract project information:

${documentText}

Respond with a JSON object containing the extracted information.
`;
  }
  
  /**
   * Processes the raw response from the AI provider
   * 
   * @param response The raw response from the AI provider
   * @returns Structured document analysis result
   */
  protected processResponse(response: unknown): DocumentAnalysisResult {
    // Default implementation assumes response is already in the correct format
    // Providers can override this method if they need custom processing
    
    // Ensure all required fields are present
    const result: DocumentAnalysisResult = {
      project_name: response.project_name || 'Untitled Project',
      description: response.description || null,
      start_date: response.start_date || null,
      end_date: response.end_date || null,
      budget: response.budget || null,
      currency: response.currency || 'CLP', // Default to CLP
      client_name: response.client_name || null,
      deliverables: response.deliverables || null,
      scope: response.scope || null,
      team_requirements: response.team_requirements || null,
      tags: response.tags || null,
      confidence_score: response.confidence_score || 0.5, // Default confidence
    };
    
    return result;
  }
}
