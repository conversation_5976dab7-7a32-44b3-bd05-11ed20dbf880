/**
 * @file Utilidades para detectar el entorno de ejecución
 * @description Proporciona funciones para detectar si el código se está ejecutando en el navegador o en el servidor
 */

/**
 * Verifica si el código se está ejecutando en un navegador
 * @returns true si se está ejecutando en un navegador, false si es en el servidor
 */
export const isBrowser = (): boolean => {
  return typeof window !== 'undefined';
};

/**
 * Verifica si el código se está ejecutando en el servidor
 * @returns true si se está ejecutando en el servidor, false si es en un navegador
 */
export const isServer = (): boolean => {
  return typeof window === 'undefined';
};

/**
 * Ejecuta una función solo si estamos en el navegador
 * @param fn Función a ejecutar
 * @param fallbackValue Valor a devolver si estamos en el servidor
 * @returns El resultado de la función o el valor de fallback
 */
export function runOnlyInBrowser<T>(fn: () => T, fallbackValue: T): T {
  if (isBrowser()) {
    return fn();
  }
  return fallbackValue;
}

/**
 * Ejecuta una función asíncrona solo si estamos en el navegador
 * @param fn Función asíncrona a ejecutar
 * @param fallbackValue Valor a devolver si estamos en el servidor
 * @returns Una promesa con el resultado de la función o el valor de fallback
 */
export async function runAsyncOnlyInBrowser<T>(fn: () => Promise<T>, fallbackValue: T): Promise<T> {
  if (isBrowser()) {
    return await fn();
  }
  return fallbackValue;
}
