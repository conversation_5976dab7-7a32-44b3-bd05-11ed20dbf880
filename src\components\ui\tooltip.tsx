"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

interface TooltipProps {
  children: React.ReactNode
  content?: React.ReactNode
  delayDuration?: number
  side?: "top" | "right" | "bottom" | "left"
  sideOffset?: number
  align?: "start" | "center" | "end"
  className?: string
}

const TooltipProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return <>{children}</>
}

const Tooltip: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return <>{children}</>
}

const TooltipTrigger: React.FC<{ asChild?: boolean; className?: string; children: React.ReactNode }> = ({
  children,
  className,
  asChild = false
}) => {
  return (
    <div className={cn("inline-block", className)}>
      {children}
    </div>
  )
}

const TooltipContent: React.FC<{
  children: React.ReactNode
  className?: string
  sideOffset?: number
}> = ({ children, className }) => {
  return (
    <div
      className={cn(
        "absolute z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md",
        className
      )}
    >
      {children}
    </div>
  )
}

export { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }
