import { Metada<PERSON> } from "next"
import { DashboardShell } from "@/components/shared/layout/dashboard-shell"
import { RateLimiterDashboard } from "@/components/features/admin/rate-limiter-dashboard"

export const metadata: Metadata = {
  title: "Rate Limiter | AdminCore ",
  description: "Gestión de límites de tasa para peticiones a Supabase",
}

export default function RateLimiterPage() {
  return (
    <DashboardShell heading="Rate Limiter">
      <RateLimiterDashboard />
    </DashboardShell>
  )
}
