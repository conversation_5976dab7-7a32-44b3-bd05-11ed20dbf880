"use client"

import { redirect } from "next/navigation"
import { createClient } from "@/lib/supabase/client"
import { Sidebar } from "@/components/shared/layout/sidebar"
import { UserNav } from "@/components/shared/layout/user-nav"
import { MobileNav } from "@/components/shared/layout/mobile-nav"
import { NotificationCenter } from "@/components/features/dashboard/notification-center"
import { SupabaseInitializer } from "@/components/supabase-initializer"
import { ProjectsFixInitializer } from "@/components/features/projects/projects-fix-initializer"
import { ProjectsApiErrorHandler } from "@/components/features/projects/projects-api-error-handler"
import { useState, useEffect } from "react"
import { LanguageSwitcher } from "@/components/language-switcher"
import { ConnectionStatus } from "@/components/ui/connection-status-fixed"
import { Loader2 } from "lucide-react"
import type { Session } from "@supabase/supabase-js"

export function DashboardClient({
  children,
}: {
  children: React.ReactNode
}) {
  // Estados para manejar la sesión y la carga
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Verificar si ya tenemos una sesión en caché
    const checkCachedSession = () => {
      try {
        // Verificar si hay una sesión en localStorage
        const cachedSession = localStorage.getItem('supabase.auth.token')
        const sessionTimestamp = localStorage.getItem('auth_session_timestamp')

        if (cachedSession && sessionTimestamp) {
          // Verificar si la caché es reciente (menos de 1 minuto)
          const cacheAge = Date.now() - parseInt(sessionTimestamp)
          if (cacheAge < 60 * 1000) { // 1 minuto
            console.log('Usando sesión en caché (edad: ' + Math.round(cacheAge/1000) + ' segundos)')
            // Mostrar UI inmediatamente con la sesión en caché
            setLoading(false)
            return true
          }
        }
        return false
      } catch (e) {
        console.warn('Error al verificar sesión en caché:', e)
        return false
      }
    }

    // Función para verificar y refrescar la sesión
    const checkSession = async () => {
      // Verificar primero si tenemos una sesión en caché para mostrar UI más rápido
      const hasCachedSession = checkCachedSession()

      try {
        // Crear cliente de Supabase
        const supabase = createClient()

        // Obtener la sesión del servidor
        const { data, error } = await supabase.auth.getSession()

        if (error) {
          const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

          console.error('Error al obtener la sesión:', errorMessage)
          setError(errorMessage)

          // Redirigir después de un breve retraso
          setTimeout(() => {
            window.location.href = '/auth/login?error=session_error'
          }, 500)
          return
        }

        if (!data.session) {
          console.log('No se encontró sesión activa')
          setError('No se encontró una sesión activa')

          // Redirigir después de un breve retraso
          setTimeout(() => {
            window.location.href = '/auth/login?error=no_session'
          }, 500)
          return
        }

        // Guardar timestamp de la sesión para caché
        localStorage.setItem('auth_session_timestamp', Date.now().toString())

        // Verificar si la sesión está a punto de expirar (menos de 10 minutos)
        const expiresAt = data.session.expires_at
        const expiresInMs = expiresAt ? (expiresAt * 1000) - Date.now() : 0

        if (expiresInMs < 10 * 60 * 1000 && expiresInMs > 0) {
          console.log(`Sesión a punto de expirar (${Math.round(expiresInMs/1000)} segundos), refrescando...`)

          // Intentar refrescar la sesión
          const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession()

          if (refreshError) {
            console.error('Error al refrescar sesión:', refreshError.message)
            // Usar la sesión original si no se puede refrescar
            setSession(data.session)
          } else if (refreshData?.session) {
            console.log('Sesión refrescada exitosamente')
            setSession(refreshData.session)
          }
        } else {
          console.log('Sesión válida encontrada')
          setSession(data.session)
        }
      } catch (err) {
        console.error('Error inesperado al verificar la sesión:', err)
        setError(err instanceof Error ? err.message : 'Error desconocido')

        // Evitar bucles de redirección
        if (err instanceof Error && !err.message.includes('NEXT_REDIRECT')) {
          setTimeout(() => {
            window.location.href = '/auth/login?error=unexpected'
          }, 500)
        }
      } finally {
        // Marcar como cargado si no se hizo con la caché
        if (!hasCachedSession) {
          setLoading(false)
        }
      }
    }

    // Verificar la sesión al montar el componente
    checkSession()

    // Configurar un intervalo para refrescar la sesión periódicamente (cada 10 minutos)
    const refreshInterval = setInterval(() => {
      if (session) {
        console.log('Refrescando sesión automáticamente...')
        checkSession()
      }
    }, 10 * 60 * 1000)

    // Limpiar el intervalo al desmontar
    return () => clearInterval(refreshInterval)
  }, [session])

  // Componente de esqueleto para la carga inicial
  const DashboardSkeleton = () => (
    <div className="flex min-h-screen">
      <div className="hidden md:flex w-72 flex-col fixed inset-y-0 bg-gray-100 animate-pulse">
        <div className="p-6">
          <div className="h-8 w-40 bg-gray-300 rounded mb-8"></div>
          <div className="space-y-6">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="h-6 w-full bg-gray-300 rounded"></div>
            ))}
          </div>
        </div>
      </div>
      <div className="flex-1 md:pl-72">
        <div className="border-b">
          <div className="flex h-16 items-center px-4">
            <div className="h-8 w-8 bg-gray-300 rounded md:hidden"></div>
            <div className="ml-auto flex items-center space-x-4">
              <div className="h-8 w-8 bg-gray-300 rounded"></div>
              <div className="h-8 w-8 bg-gray-300 rounded"></div>
              <div className="h-10 w-10 bg-gray-300 rounded-full"></div>
            </div>
          </div>
        </div>
        <main className="p-8">
          <div className="h-10 w-40 bg-gray-300 rounded mb-8"></div>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
          <div className="grid gap-4 md:grid-cols-2 mb-8">
            {[...Array(2)].map((_, i) => (
              <div key={i} className="h-80 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
        </main>
      </div>
    </div>
  );

  // Mostrar esqueleto mientras se verifica la sesión
  if (loading) {
    return <DashboardSkeleton />;
  }

  // Mostrar pantalla de error si hay problemas
  if (error && !session) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="flex flex-col items-center space-y-4 max-w-md p-8 border rounded-lg shadow-md">
          <div className="text-red-500 text-xl font-semibold">Error al cargar el dashboard</div>
          <p className="text-center text-gray-600">{error}</p>
          <button
            onClick={() => window.location.href = '/auth/login'}
            className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors"
          >
            Volver al inicio de sesión
          </button>
        </div>
      </div>
    )
  }

  // Renderizar el dashboard cuando la sesión está lista
  return (
    <div className="flex min-h-screen">
      {/* Inicializadores - no renderizan nada visible */}
      <SupabaseInitializer />
      <ProjectsFixInitializer />
      <ProjectsApiErrorHandler />

      <div className="hidden md:flex w-72 flex-col fixed inset-y-0">
        <Sidebar />
      </div>
      <div className="flex-1 md:pl-72">
        <div className="border-b">
          <div className="flex h-16 items-center px-4">
            <MobileNav />
            <div className="ml-auto flex items-center space-x-4">
              <ConnectionStatus />
              <LanguageSwitcher />
              <NotificationCenter />
              {session && <UserNav user={session.user} />}
            </div>
          </div>
        </div>
        <main className="p-8">
          {children}
        </main>
      </div>
    </div>
  )
}