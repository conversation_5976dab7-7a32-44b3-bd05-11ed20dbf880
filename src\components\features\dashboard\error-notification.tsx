import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { useState, useEffect } from "react";

interface ErrorNotificationProps {
  errors: string[];
  onDismiss?: () => void;
}

export function ErrorNotification({ errors, onDismiss }: ErrorNotificationProps) {
  const [visible, setVisible] = useState(true);

  useEffect(() => {
    if (errors.length > 0) {
      setVisible(true);
    }
  }, [errors]);

  const handleDismiss = () => {
    setVisible(false);
    if (onDismiss) {
      onDismiss();
    }
  };

  if (!visible || errors.length === 0) {
    return null;
  }

  return (
    <Alert variant="destructive" className="mb-4">
      <AlertCircle className="h-4 w-4" />
      <AlertTitle>Atención</AlertTitle>
      <AlertDescription>
        <div className="space-y-2">
          <p>Se han detectado algunos problemas al cargar los datos del dashboard:</p>
          <ul className="list-disc pl-5 text-sm">
            {errors.map((error, index) => (
              <li key={index}>{error}</li>
            ))}
          </ul>
          <p className="text-sm mt-2">
            Algunos datos pueden ser simulados o estar incompletos. Contacte al administrador si el problema persiste.
          </p>
          <button
            onClick={handleDismiss}
            className="text-xs underline hover:no-underline mt-2"
          >
            Cerrar notificación
          </button>
        </div>
      </AlertDescription>
    </Alert>
  );
}
