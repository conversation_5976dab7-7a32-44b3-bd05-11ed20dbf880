import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

export interface Activity {
  id: string;
  user: {
    name: string;
    avatar?: string;
    email?: string;
  };
  action: string;
  target: string;
  timestamp: string | Date;
  type: "project" | "order" | "document" | "user" | "system";
}

interface ActivityListProps {
  activities: Activity[];
  className?: string;
  title?: string;
  limit?: number;
}

export function ActivityList({
  activities,
  className,
  title = "Actividades Recientes",
  limit = 5,
}: ActivityListProps) {
  const displayActivities = activities.slice(0, limit);

  // Función para formatear la fecha
  const formatDate = (date: string | Date) => {
    const d = new Date(date);
    const now = new Date();
    const diff = now.getTime() - d.getTime();

    // Menos de 24 horas
    if (diff < 24 * 60 * 60 * 1000) {
      const hours = Math.floor(diff / (60 * 60 * 1000));
      if (hours < 1) {
        const minutes = Math.floor(diff / (60 * 1000));
        return minutes < 1 ? "Justo ahora" : `Hace ${minutes} minutos`;
      }
      return `Hace ${hours} horas`;
    }

    // Menos de 7 días
    if (diff < 7 * 24 * 60 * 60 * 1000) {
      const days = Math.floor(diff / (24 * 60 * 60 * 1000));
      return `Hace ${days} días`;
    }

    // Formato de fecha normal
    return d.toLocaleDateString();
  };

  // Función para obtener el color del badge según el tipo
  const getBadgeColor = (type: Activity["type"]) => {
    switch (type) {
      case "project":
        return "bg-violet-500";
      case "order":
        return "bg-pink-700";
      case "document":
        return "bg-emerald-500";
      case "user":
        return "bg-blue-500";
      case "system":
        return "bg-gray-500";
      default:
        return "bg-gray-500";
    }
  };

  // Función para obtener las iniciales del nombre
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .substring(0, 2);
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-8">
          {displayActivities.length > 0 ? (
            displayActivities.map((activity) => (
              <div key={activity.id} className="flex items-start">
                <Avatar className="h-9 w-9">
                  <AvatarImage src={activity.user.avatar} alt={activity.user.name} />
                  <AvatarFallback>{getInitials(activity.user.name)}</AvatarFallback>
                </Avatar>
                <div className="ml-4 space-y-1">
                  <p className="text-sm font-medium">
                    <span className="font-semibold">{activity.user.name}</span> {activity.action}{" "}
                    <span className="font-semibold">{activity.target}</span>
                  </p>
                  <div className="flex items-center pt-1">
                    <Badge
                      className={cn(
                        "mr-2 px-1 py-0 text-xs",
                        getBadgeColor(activity.type)
                      )}
                      variant="secondary"
                    >
                      {activity.type.charAt(0).toUpperCase() + activity.type.slice(1)}
                    </Badge>
                    <time className="text-xs text-muted-foreground">
                      {formatDate(activity.timestamp)}
                    </time>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <p className="text-center text-muted-foreground">No hay actividades recientes</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
