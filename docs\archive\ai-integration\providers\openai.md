# OpenAI Integration

## Overview

This document provides detailed information about integrating OpenAI's models for document processing in the AdminCore  project management system.

## Features

- Advanced document text extraction and analysis
- Project information identification with high accuracy
- Support for complex document understanding
- Structured data extraction capabilities

## Prerequisites

- OpenAI account
- OpenAI API key
- Appropriate subscription for API usage

## API Key Setup

1. Visit [OpenAI API Keys](https://platform.openai.com/api-keys)
2. Create a new API key
3. Store the API key securely in environment variables
4. Never expose the API key in client-side code

## Environment Variables

```
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4
```

## Models

| Model | Description | Best For | Token Limit | Cost |
|-------|-------------|----------|-------------|------|
| gpt-4 | Most capable model | Complex document analysis | 8,192 tokens | Higher |
| gpt-4-turbo | Improved version of GPT-4 | Complex document analysis with better performance | 128,000 tokens | Higher |
| gpt-3.5-turbo | Fast, cost-effective model | Simple document analysis | 16,384 tokens | Lower |

## Implementation Details

### Installation

```bash
npm install openai
```

### Basic Usage

```typescript
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

async function analyzeDocument(documentText: string) {
  const response = await openai.chat.completions.create({
    model: process.env.OPENAI_MODEL || 'gpt-4',
    messages: [
      {
        role: 'system',
        content: 'You are a document analysis assistant that extracts project information.'
      },
      {
        role: 'user',
        content: `Extract project information from the following document: ${documentText}`
      }
    ],
    response_format: { type: 'json_object' }
  });

  return JSON.parse(response.choices[0].message.content || '{}');
}
```

### Document Processing Flow

1. Extract text from document (using appropriate library based on document type)
2. Chunk document if it exceeds token limits
3. Send text to OpenAI API with specific system and user prompts
4. Process and structure the JSON response
5. Map the structured data to project fields

### Example Prompt for Project Information Extraction

```typescript
const systemPrompt = `
You are a specialized AI assistant for engineering project management. Your task is to analyze project documents and extract key information in a structured format.

Extract the following fields:
- project_name: The name or title of the project
- description: A summary of the project's purpose and goals
- start_date: The planned start date (format as YYYY-MM-DD)
- end_date: The planned end date (format as YYYY-MM-DD)
- budget: The total budget amount (numeric value only)
- currency: The currency of the budget (USD, EUR, CLP, etc.)
- client_name: The name of the client or customer
- deliverables: An array of key deliverables
- scope: A description of the project scope
- team_requirements: An array of required team roles or skills

Format your response as a valid JSON object. If information is not found, use null for that field.
`;

const userPrompt = `Analyze this project document and extract the required information:

${documentText}`;
```

## Rate Limits and Quotas

- Tier-based rate limits (requests per minute)
- Token-based quotas (monthly usage)
- [Check current limits](https://platform.openai.com/docs/guides/rate-limits)

## Error Handling

Common errors to handle:

- Authentication errors
- Rate limit exceeded
- Token limit exceeded
- Content policy violation
- Timeout errors

Example error handling:

```typescript
try {
  const response = await openai.chat.completions.create({
    // configuration
  });
  // Process response
} catch (error) {
  if (error.status === 401) {
    // Handle authentication error
  } else if (error.status === 429) {
    // Handle rate limit error
  } else if (error.status === 400 && error.message.includes('maximum context length')) {
    // Handle token limit error
  } else {
    // Handle other errors
  }
}
```

## Best Practices

1. **Optimize token usage**
   - Use the most appropriate model for the task
   - Implement chunking for large documents
   - Remove irrelevant content before processing

2. **Improve accuracy**
   - Use detailed system prompts with examples
   - Request structured JSON responses
   - Validate and clean extracted data

3. **Manage costs**
   - Monitor token usage
   - Cache results when appropriate
   - Use cheaper models for simpler tasks

4. **Handle failures gracefully**
   - Implement retry logic with exponential backoff
   - Fall back to alternative models or providers
   - Provide clear feedback to users

## Limitations

- Token limits vary by model
- Costs can be significant for high volume usage
- May hallucinate information not present in the document
- Processing very large documents requires chunking

## Resources

- [OpenAI API Documentation](https://platform.openai.com/docs/api-reference)
- [OpenAI Cookbook](https://cookbook.openai.com/)
- [Rate Limits and Pricing](https://openai.com/pricing)
- [Best Practices for Prompt Engineering](https://platform.openai.com/docs/guides/prompt-engineering)
