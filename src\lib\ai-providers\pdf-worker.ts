/**
 * @ai-file-description: "PDF.js worker initialization"
 * @ai-related-files: ["document-text-extractor.ts"]
 * @ai-owner: "File-Based Projects"
 */

// Interfaces para PDF.js
interface PdfJsModule {
  GlobalWorkerOptions: {
    workerSrc: string;
  };
  disableWorker: boolean;
  disableFontFace: boolean;
  disableRange: boolean;
  disableStream: boolean;
  getDocument: (options: PdfLoadOptions) => PdfLoadingTask;
}

interface PdfLoadOptions {
  url: string;
  cMapUrl?: string;
  cMapPacked?: boolean;
  disableStream?: boolean;
  disableAutoFetch?: boolean;
  disableRange?: boolean;
  disableCreateObjectURL?: boolean;
  disableFontFace?: boolean;
  disableCanvasRendering?: boolean;
}

interface PdfLoadingTask {
  promise: Promise<PdfDocument>;
}

interface PdfDocument {
  numPages: number;
  getPage: (pageNumber: number) => Promise<PdfPage>;
}

interface PdfPage {
  getTextContent: () => Promise<PdfTextContent>;
}

interface PdfTextContent {
  items: PdfTextItem[];
}

interface PdfTextItem {
  str: string;
  dir?: string;
  width?: number;
  height?: number;
  transform?: number[];
  fontName?: string;
}

// Import pdfjs dynamically to avoid SSR issues
let pdfjs: PdfJsModule | null = null;

// Flag to track if initialization has been attempted
let initializationAttempted = false;

// Safely import pdfjs only in browser environment
const loadPdfJs = async (): Promise<PdfJsModule | null> => {
  // Skip in server environment
  if (typeof window === 'undefined') return null;

  // Return cached instance if available
  if (pdfjs) return pdfjs;

  try {
    // Use dynamic import with error handling
    const pdfjsModule = await import('pdfjs-dist').catch(err => {
      console.error('Failed to load pdfjs-dist:', err);
      return null;
    });

    if (pdfjsModule) {
      pdfjs = pdfjsModule as PdfJsModule;
    }

    return pdfjs;
  } catch (error) {
    console.error('Error importing pdfjs-dist:', error);
    return null;
  }
};

/**
 * Initialize PDF.js worker
 * This function must be called before using PDF.js
 */
export async function initPdfWorker() {
  // Skip on server-side
  if (typeof window === 'undefined') return;

  // Prevent multiple initialization attempts
  if (initializationAttempted) return;
  initializationAttempted = true;

  try {
    // Load pdfjs dynamically
    const pdf = await loadPdfJs();
    if (!pdf) return;

    if (!pdf.GlobalWorkerOptions.workerSrc) {
      // Use version from environment variable or fallback to hardcoded version
      const pdfjsVersion = process.env.NEXT_PUBLIC_PDFJS_VERSION || '3.11.174';

      // Use CDN for worker to avoid node-gyp issues
      pdf.GlobalWorkerOptions.workerSrc =
        `https://cdn.jsdelivr.net/npm/pdfjs-dist@${pdfjsVersion}/build/pdf.worker.min.js`;

      // Disable canvas-related features to prevent node-gyp errors
      pdf.disableWorker = false;
      pdf.disableFontFace = true;
      pdf.disableRange = false;
      pdf.disableStream = false;

      console.log('PDF.js worker initialized with CDN version:', pdfjsVersion);
    }
  } catch (error) {
    console.error('Error initializing PDF.js worker:', error);
    // Don't throw the error - fail gracefully
    console.warn('PDF functionality may be limited due to initialization failure');
  }
}

/**
 * Extracts text from a PDF document
 *
 * @param fileUrl URL of the PDF document
 * @returns Extracted text content
 */
export async function extractTextFromPdf(fileUrl: string): Promise<string> {
  try {
    console.log('Starting PDF extraction from:', fileUrl);

    // Verificar si estamos en un entorno de navegador
    if (typeof window === 'undefined') {
      console.warn('PDF extraction not supported in server environment');
      return 'PDF extraction not supported in server environment.';
    }

    // Validar la URL
    if (!fileUrl || typeof fileUrl !== 'string') {
      console.error('Invalid PDF URL provided:', fileUrl);
      return 'Error: URL de PDF inválida.';
    }

    // Load pdfjs dynamically
    const pdf = await loadPdfJs();
    if (!pdf) {
      console.error('Failed to load PDF.js library');
      return 'Error: No se pudo cargar la biblioteca PDF.js.';
    }

    // Ensure PDF.js worker is initialized (only relevant in browser)
    await initPdfWorker();

    // Intentar descargar el PDF primero para verificar que es accesible
    console.log('Fetching PDF file to verify accessibility');
    try {
      const response = await fetch(fileUrl, { method: 'HEAD' });
      if (!response.ok) {
        console.error(`Failed to access PDF: ${response.status} ${response.statusText}`);
        return `Error: No se pudo acceder al archivo PDF (${response.status} ${response.statusText}). Verifique que el archivo existe y es accesible.`;
      }
    } catch (fetchError) {
      console.error('Error fetching PDF:', fetchError);
      return `Error al acceder al archivo PDF: ${fetchError instanceof Error ? fetchError.message : 'Error desconocido'}. Verifique la URL y la conectividad.`;
    }

    // Load the PDF document with additional options for robustness
    const loadingTask = pdf.getDocument({
      url: fileUrl,
      cMapUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/cmaps/',
      cMapPacked: true,
      disableStream: false,
      disableAutoFetch: false,
      disableRange: false,
      // Disable canvas-related features to prevent node-gyp errors
      disableCreateObjectURL: true,
      disableFontFace: true,
      disableCanvasRendering: true
    });

    // Set a timeout to prevent hanging indefinitely
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('PDF loading timed out after 30 seconds')), 30000);
    });

    // Race the loading task against the timeout
    const pdfDocument = await Promise.race([loadingTask.promise, timeoutPromise]);
    console.log(`PDF loaded successfully with ${pdfDocument.numPages} pages`);

    // Verificar que el PDF tiene páginas
    if (!pdfDocument || pdfDocument.numPages <= 0) {
      console.error('PDF has no pages or is invalid');
      return 'Error: El PDF no contiene páginas o es inválido.';
    }

    let fullText = '';
    let pageErrors = 0;

    // Extract text from each page with error handling per page
    for (let i = 1; i <= pdfDocument.numPages; i++) {
      try {
        console.log(`Processing page ${i} of ${pdfDocument.numPages}`);
        const page = await pdfDocument.getPage(i);
        const content = await page.getTextContent();

        // Verificar que el contenido tiene items
        if (!content || !content.items || content.items.length === 0) {
          console.warn(`Page ${i} has no text content`);
          fullText += `[Página ${i}: Sin contenido de texto]\n\n`;
          continue;
        }

        const pageText = content.items
          .map((item: PdfTextItem) => item.str || '')
          .join(' ');

        fullText += pageText + '\n\n';
      } catch (pageError) {
        console.error(`Error extracting text from page ${i}:`, pageError);
        pageErrors++;
        fullText += `[Error en página ${i}: ${pageError instanceof Error ? pageError.message : 'Error desconocido'}]\n\n`;

        // Si hay demasiados errores de página, abortar
        if (pageErrors > Math.min(5, pdfDocument.numPages / 2)) {
          return `Error al procesar el PDF: Demasiados errores de página (${pageErrors} de ${pdfDocument.numPages}). El documento puede estar dañado o protegido.`;
        }
      }
    }

    // Verificar que se extrajo algún texto
    if (!fullText || fullText.trim() === '') {
      console.error('No text extracted from PDF');
      return 'Error: No se pudo extraer texto del PDF. El documento puede estar escaneado, protegido o contener solo imágenes.';
    }

    console.log(`PDF text extraction complete, extracted ${fullText.length} characters`);

    // Verificar si el texto es demasiado corto para ser válido
    if (fullText.length < 50 && pdfDocument.numPages > 1) {
      console.warn('Extracted text is suspiciously short for a multi-page document');
      return `Advertencia: El texto extraído es sospechosamente corto (${fullText.length} caracteres) para un documento de ${pdfDocument.numPages} páginas. El PDF puede estar protegido o contener principalmente imágenes.`;
    }

    return fullText;
  } catch (error) {
    console.error('Error extracting text from PDF:', error);
    return `Error al extraer texto del PDF: ${error instanceof Error ? error.message : 'Error desconocido'}. Por favor, intente con otro documento o contacte al soporte.`;
  }
}

// Export a function to get pdfjs instead of exporting it directly
export default async function getPdfJs(): Promise<PdfJsModule | null> {
  return await loadPdfJs();
}
