/**
 * @file Hook para usar el cliente de Supabase con limitación de tasa
 * @description Este hook proporciona acceso al cliente de Supabase con limitación de tasa
 */

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/rate-limited-client'
import { rateLimiterService, UsageStats } from '@/lib/services/rate-limiter-service'

/**
 * Hook para usar el cliente de Supabase con limitación de tasa
 * @param options Opciones para el cliente
 * @returns Cliente de Supabase con limitación de tasa y estadísticas de uso
 */
export function useRateLimitedClient(options?: {
  enableRateLimiting?: boolean
}) {
  const [stats, setStats] = useState<UsageStats | null>(null)
  const [isEnabled, setIsEnabled] = useState(options?.enableRateLimiting ?? true)
  
  // Crear cliente con limitación de tasa
  const client = createClient({
    enableRateLimiting: isEnabled,
  })
  
  // Suscribirse a actualizaciones de estadísticas
  useEffect(() => {
    // Obtener estadísticas iniciales
    setStats(rateLimiterService.getUsageStats())
    
    // Suscribirse a actualizaciones
    const unsubscribe = rateLimiterService.addStatsListener((newStats) => {
      setStats(newStats)
    })
    
    // Limpiar suscripción al desmontar
    return () => {
      unsubscribe()
    }
  }, [])
  
  // Función para habilitar/deshabilitar la limitación de tasa
  const toggleRateLimiting = (enabled: boolean) => {
    setIsEnabled(enabled)
  }
  
  return {
    client,
    stats,
    isEnabled,
    toggleRateLimiting,
    rateLimiterService,
  }
}
