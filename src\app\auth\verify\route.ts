import { type EmailOtpType } from '@supabase/supabase-js'
import { type NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'

/**
 * Ruta para manejar la confirmación de autenticación
 * 
 * @param request Solicitud HTTP
 * @returns Redirección a la página principal o de error
 */
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const token_hash = searchParams.get('token_hash')
  const type = searchParams.get('type') as EmailOtpType | null
  const next = searchParams.get('next') ?? '/dashboard'
  
  if (token_hash && type) {
    const supabase = await createClient()
    const { error } = await supabase.auth.verifyOtp({
      type,
      token_hash,
    })
    
    if (!error) {
      // Redirigir al usuario a la URL especificada o a la raíz de la aplicación
      redirect(next)
    }
  }
  
  // Redirigir al usuario a una página de error con algunas instrucciones
  redirect('/auth/error?message=Error+de+verificación+de+autenticación')
}
