import { Suspense } from "react"
import { NewWorkOrderContent } from "@/components/features/work-orders/new-work-order-content"

export default function NewWorkOrderPage() {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Nueva Orden de Trabajo</h2>
        <p className="text-muted-foreground mt-2">
          Complete el formulario para crear una nueva orden de trabajo.
        </p>
      </div>

      <div className="rounded-md border p-6">
        <Suspense fallback={<div className="p-8 text-center">Cargando formulario...</div>}>
          <NewWorkOrderContent />
        </Suspense>
      </div>
    </div>
  )
}
