/**
 * @file Hook para usar el servicio de proyectos
 * @description Proporciona acceso al servicio de proyectos con caché local
 */

import { useState, useEffect, useCallback } from 'react';
import { useLocalProjects } from '@/hooks/use-local-database';
import { projectsService, ProjectWithRelations } from '@/lib/services/projects-service-fixed';
import { CachedProject } from '@/lib/db/local-database';
import { sessionManager } from '@/lib/supabase/session-manager';
import { toast } from '@/hooks/use-toast';

/**
 * Hook para obtener y gestionar proyectos
 */
export function useProjects(options: { userId?: string; limit?: number } = {}) {
  const { userId, limit = 50 } = options;
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [projects, setProjects] = useState<CachedProject[]>([]);

  // Obtener proyectos de la base de datos local (actualización en tiempo real)
  const {
    projects: localProjects,
    isLoading: isLocalLoading
  } = useLocalProjects({ userId, limit });

  // Cargar proyectos iniciales
  useEffect(() => {
    const loadProjects = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Verificar sesión antes de cargar proyectos
        const sessionStatus = await sessionManager.checkSessionStatus();
        if (!sessionStatus.valid) {
          console.warn('Session validation failed:', sessionStatus.message);

          // Intentar refrescar la sesión
          const refreshed = await sessionManager.refreshSession();
          if (!refreshed) {
            console.error('Failed to refresh session');
            setError('Sesión inválida. Por favor, inicie sesión nuevamente.');

            // Mostrar toast solo si es un error de autenticación
            if (sessionStatus.message.includes('auth') ||
                sessionStatus.message.includes('token') ||
                sessionStatus.message.includes('sesión')) {
              toast({
                title: 'Error de sesión',
                description: 'Su sesión ha expirado. Por favor, inicie sesión nuevamente.',
                variant: 'destructive'
              });
            }

            // Intentar cargar desde caché de todos modos
            try {
              const cachedProjects = await projectsService.getProjects({
                userId,
                limit,
                forceRefresh: false
              });

              if (cachedProjects.length > 0) {
                console.log(`Using ${cachedProjects.length} cached projects due to session error`);
                setProjects(cachedProjects);
              }
            } catch (cacheErr) {
              console.error('Failed to load cached projects after session error:', cacheErr);
            }

            setIsLoading(false);
            return;
          }

          console.log('Session refreshed successfully, continuing to load projects');
        }

        // Verificar conectividad
        const isOnline = typeof navigator !== 'undefined' ? navigator.onLine : true;
        if (!isOnline) {
          console.log('Device is offline, attempting to load cached projects');
        }

        const fetchedProjects = await projectsService.getProjects({ userId, limit });

        if (fetchedProjects.length === 0) {
          console.log('No projects found or returned from service');
        } else {
          console.log(`Successfully loaded ${fetchedProjects.length} projects`);
        }

        setProjects(fetchedProjects);
      } catch (err) {
        console.error('Error loading projects:', err);
        const errorMessage = err instanceof Error ? err.message : 'Error al cargar proyectos';
        console.error('Error details:', errorMessage);
        setError(errorMessage);

        // Mostrar toast con el error
        toast({
          title: 'Error',
          description: 'No se pudieron cargar los proyectos. Intente nuevamente.',
          variant: 'destructive'
        });

        // Intentar recuperar proyectos de la caché local en caso de error
        try {
          const cachedProjects = await projectsService.getProjects({
            userId,
            limit,
            forceRefresh: false
          });

          if (cachedProjects.length > 0) {
            console.log(`Recovered ${cachedProjects.length} projects from cache after error`);
            setProjects(cachedProjects);
          }
        } catch (cacheErr) {
          console.error('Failed to recover projects from cache:', cacheErr);
        }
      } finally {
        setIsLoading(false);
      }
    };

    loadProjects();
  }, [userId, limit]);

  // Actualizar proyectos cuando cambian los proyectos locales
  useEffect(() => {
    if (localProjects && localProjects.length > 0) {
      setProjects(localProjects);
    }
  }, [localProjects]);

  // Función para refrescar proyectos
  const refreshProjects = useCallback(async (forceRefresh = true) => {
    try {
      setIsLoading(true);
      setError(null);

      // Verificar sesión antes de refrescar proyectos
      const sessionValid = await sessionManager.ensureValidSession();

      if (!sessionValid) {
        console.error('Session validation failed during refresh');
        setError('Sesión inválida. Por favor, inicie sesión nuevamente.');

        toast({
          title: 'Error de sesión',
          description: 'Su sesión ha expirado. Por favor, inicie sesión nuevamente.',
          variant: 'destructive'
        });

        return [];
      }

      const fetchedProjects = await projectsService.getProjects({
        userId,
        limit,
        forceRefresh
      });

      setProjects(fetchedProjects);

      // Mostrar mensaje de éxito o información
      if (fetchedProjects.length === 0) {
        toast({
          title: 'Sin proyectos',
          description: 'No se encontraron proyectos para mostrar.',
          variant: 'default'
        });
      } else if (forceRefresh) {
        toast({
          title: 'Proyectos actualizados',
          description: `Se cargaron ${fetchedProjects.length} proyectos correctamente.`,
          variant: 'default'
        });
      }

      return fetchedProjects;
    } catch (err) {
      console.error('Error refreshing projects:', err);
      const errorMessage = err instanceof Error ? err.message : 'Error al refrescar proyectos';
      setError(errorMessage);

      toast({
        title: 'Error',
        description: 'No se pudieron actualizar los proyectos. Intente nuevamente.',
        variant: 'destructive'
      });

      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [userId, limit]);

  // Función para crear un proyecto
  const createProject = useCallback(async (projectData: Omit<CachedProject, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      setError(null);
      return await projectsService.createProject(projectData);
    } catch (err) {
      console.error('Error creating project:', err);
      setError(err instanceof Error ? err.message : 'Error al crear proyecto');
      throw err;
    }
  }, []);

  // Función para actualizar un proyecto
  const updateProject = useCallback(async (id: string, projectData: Partial<CachedProject>) => {
    try {
      setError(null);
      return await projectsService.updateProject(id, projectData);
    } catch (err) {
      console.error('Error updating project:', err);
      setError(err instanceof Error ? err.message : 'Error al actualizar proyecto');
      throw err;
    }
  }, []);

  // Función para eliminar un proyecto
  const deleteProject = useCallback(async (id: string) => {
    try {
      setIsLoading(true);
      setError(null);

      // Verificar sesión antes de eliminar el proyecto
      const sessionValid = await sessionManager.ensureValidSession();

      if (!sessionValid) {
        console.error('Session validation failed during project deletion');
        setError('Sesión inválida. Por favor, inicie sesión nuevamente.');

        toast({
          title: 'Error de sesión',
          description: 'Su sesión ha expirado. Por favor, inicie sesión nuevamente.',
          variant: 'destructive'
        });

        return false;
      }

      await projectsService.deleteProject(id);

      // Actualizar la lista de proyectos localmente
      setProjects(currentProjects => currentProjects.filter(project => project.id !== id));

      toast({
        title: 'Proyecto eliminado',
        description: 'El proyecto ha sido eliminado correctamente.',
        variant: 'default'
      });

      return true;
    } catch (err) {
      console.error('Error deleting project:', err);
      const errorMessage = err instanceof Error ? err.message : 'Error al eliminar proyecto';
      setError(errorMessage);

      toast({
        title: 'Error',
        description: 'No se pudo eliminar el proyecto. Intente nuevamente.',
        variant: 'destructive'
      });

      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    projects,
    isLoading: isLoading || isLocalLoading,
    error,
    refreshProjects,
    createProject,
    updateProject,
    deleteProject
  };
}

/**
 * Hook para obtener y gestionar un proyecto específico
 */
export function useProject(id: string) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [project, setProject] = useState<ProjectWithRelations | null>(null);

  // Cargar proyecto inicial
  useEffect(() => {
    const loadProject = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Verificar conectividad
        const isOnline = typeof navigator !== 'undefined' ? navigator.onLine : true;
        if (!isOnline) {
          console.log(`Device is offline, attempting to load cached project ${id}`);
        }

        const fetchedProject = await projectsService.getProjectById(id);

        if (!fetchedProject) {
          console.log(`Project ${id} not found`);
        } else {
          console.log(`Successfully loaded project ${id}`);
        }

        setProject(fetchedProject);
      } catch (err) {
        console.error(`Error loading project ${id}:`, err);
        const errorMessage = err instanceof Error ? err.message : 'Error al cargar proyecto';
        console.error('Error details:', errorMessage);
        setError(errorMessage);

        // Intentar recuperar proyecto de la caché local en caso de error
        try {
          const cachedProject = await projectsService.getProjectById(id, { forceRefresh: false });

          if (cachedProject) {
            console.log(`Recovered project ${id} from cache after error`);
            setProject(cachedProject);
          }
        } catch (cacheErr) {
          console.error(`Failed to recover project ${id} from cache:`, cacheErr);
        }
      } finally {
        setIsLoading(false);
      }
    };

    if (id) {
      loadProject();
    }
  }, [id]);

  // Función para refrescar proyecto
  const refreshProject = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const fetchedProject = await projectsService.getProjectById(id, { forceRefresh: true });
      setProject(fetchedProject);
      return fetchedProject;
    } catch (err) {
      console.error(`Error refreshing project ${id}:`, err);
      setError(err instanceof Error ? err.message : 'Error al refrescar proyecto');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [id]);

  // Función para actualizar proyecto
  const updateProject = useCallback(async (projectData: Partial<CachedProject>) => {
    try {
      setError(null);
      const updatedProject = await projectsService.updateProject(id, projectData);
      setProject(prev => prev ? { ...prev, ...updatedProject } : updatedProject);
      return updatedProject;
    } catch (err) {
      console.error(`Error updating project ${id}:`, err);
      setError(err instanceof Error ? err.message : 'Error al actualizar proyecto');
      throw err;
    }
  }, [id]);

  // Función para eliminar proyecto
  const deleteProject = useCallback(async () => {
    try {
      setError(null);
      await projectsService.deleteProject(id);
      setProject(null);
    } catch (err) {
      console.error(`Error deleting project ${id}:`, err);
      setError(err instanceof Error ? err.message : 'Error al eliminar proyecto');
      throw err;
    }
  }, [id]);

  return {
    project,
    isLoading,
    error,
    refreshProject,
    updateProject,
    deleteProject
  };
}
