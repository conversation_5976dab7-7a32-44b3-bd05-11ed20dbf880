/**
 * Cliente API para el servicio de análisis de documentos
 *
 * Este cliente proporciona métodos para interactuar con el servicio de análisis de documentos
 * implementado con LM Studio.
 */

import { createClient } from '@/lib/supabase/client';

// URL base del servicio de análisis de documentos
// Usamos el puerto 8002 que corresponde al servicio document-analyzer en Docker
const DOCUMENT_ANALYSIS_SERVICE_URL = process.env.NEXT_PUBLIC_DOCUMENT_ANALYSIS_URL || 'http://localhost:8002';

// Verificar si estamos en el navegador
const isBrowser = typeof window !== 'undefined';

// Función para obtener la URL del servicio de análisis de documentos
// Si estamos en el navegador, usamos la URL completa con el protocolo
// Si estamos en el servidor, usamos la URL relativa
function getServiceUrl(): string {
  if (isBrowser) {
    // En el navegador, verificamos si la URL es localhost
    if (DOCUMENT_ANALYSIS_SERVICE_URL.includes('localhost')) {
      return DOCUMENT_ANALYSIS_SERVICE_URL;
    } else {
      // Si no es localhost, usamos la URL relativa para evitar problemas de CORS
      return '/api/document-analysis/proxy';
    }
  } else {
    // En el servidor, usamos la URL completa
    return DOCUMENT_ANALYSIS_SERVICE_URL;
  }
}

// Tipos de datos
export interface AnalysisRequest {
  file: File;
  model?: string;
  userId?: string;
}

export interface AnalysisResponse {
  analysis_id: string;
  status: 'processing' | 'completed' | 'error';
  message?: string;
}

export interface AnalysisResult {
  analysis_id: string;
  document_id: string;
  filename: string;
  status: 'processing' | 'completed' | 'error';
  model: string;
  analysis: {
    is_project_document: boolean;
    project_name: string;
    start_date: string | null;
    end_date: string | null;
    budget: string | null;
    scope: string | null;
    deliverables: string[];
    has_sufficient_info: boolean;
    confidence_score: number;
    reason: string;
  };
  is_valid_for_project: boolean;
  project_info: {
    name: string;
    start_date: string | null;
    end_date: string | null;
    budget: string | null;
    scope: string | null;
    deliverables: string[];
    confidence_score: number;
  };
  error?: string;
}

export interface SavedAnalysisData {
  id: string;
  document_id: string;
  provider: string;
  status: string;
  analysis_data: AnalysisResult;
  confidence_score: number;
  completed_at: string;
  created_at: string;
  updated_at: string;
}

export interface ModelInfo {
  id: string;
  name: string;
  description?: string;
  size?: string;
  type?: string;
}

export interface ModelsResponse {
  models: ModelInfo[];
  total: number;
}

/**
 * Cliente para el servicio de análisis de documentos
 */
export class DocumentAnalysisClient {
  private supabase = createClient();

  /**
   * Analiza un documento utilizando el servicio de análisis de documentos
   *
   * @param params Parámetros para el análisis
   * @returns Respuesta del análisis con ID para consultar resultados
   */
  async analyzeDocument(params: AnalysisRequest): Promise<AnalysisResponse> {
    try {
      // Crear FormData para enviar el archivo
      const formData = new FormData();
      formData.append('file', params.file);

      if (params.model) {
        formData.append('model', params.model);
      }

      // Realizar la petición al servicio
      const response = await fetch(`${DOCUMENT_ANALYSIS_SERVICE_URL}/analyze`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Error al analizar el documento');
      }

      return await response.json();
    } catch (error) {
      console.error('Error en el análisis de documento:', error);
      throw error;
    }
  }

  /**
   * Obtiene el resultado de un análisis
   *
   * @param analysisId ID del análisis
   * @returns Resultado del análisis
   */
  async getAnalysisResult(analysisId: string): Promise<AnalysisResult> {
    try {
      const response = await fetch(`${DOCUMENT_ANALYSIS_SERVICE_URL}/analyze/${analysisId}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Error al obtener el resultado del análisis');
      }

      const result = await response.json();

      // Manejar la estructura anidada si existe
      if (result.analysis_data && typeof result.analysis_data === 'object') {
        // Si los datos están anidados en analysis_data, extraerlos
        return {
          ...result,
          ...result.analysis_data
        };
      }

      return result;
    } catch (error) {
      console.error('Error al obtener el resultado del análisis:', error);
      throw error;
    }
  }

  /**
   * Guarda el resultado de un análisis en la base de datos
   *
   * @param result Resultado del análisis
   * @param documentId ID del documento en Supabase
   * @returns Datos del análisis guardado
   */
  async saveAnalysisResult(result: AnalysisResult, documentId: string): Promise<SavedAnalysisData> {
    try {
      // Obtener la sesión actual
      const { data: sessionData } = await this.supabase.auth.getSession();

      if (!sessionData.session) {
        throw new Error('No hay sesión activa');
      }

      // Guardar el resultado en la tabla ai_document_analyses
      const { data, error } = await this.supabase
        .from('ai_document_analyses')
        .insert({
          document_id: documentId,
          provider: 'lmstudio',
          status: result.status,
          analysis_data: result,
          confidence_score: result.project_info.confidence_score,
          completed_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error al guardar el resultado del análisis:', error);
      throw error;
    }
  }

  /**
   * Obtiene los modelos disponibles en LM Studio
   *
   * @returns Lista de modelos disponibles
   */
  async getAvailableModels(): Promise<ModelsResponse> {
    try {
      const response = await fetch(`${DOCUMENT_ANALYSIS_SERVICE_URL.replace('8002', '8003')}/models`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Error al obtener los modelos disponibles');
      }

      return await response.json();
    } catch (error) {
      console.error('Error al obtener los modelos disponibles:', error);
      throw error;
    }
  }

  /**
   * Crea un proyecto a partir del resultado de un análisis
   *
   * @param analysisResult Resultado del análisis
   * @returns Datos del proyecto creado
   */
  async createProjectFromAnalysis(analysisResult: AnalysisResult): Promise<{ id: string; name: string; status: string }> {
    try {
      // Obtener la sesión actual
      const { data: sessionData } = await this.supabase.auth.getSession();

      if (!sessionData.session) {
        throw new Error('No hay sesión activa');
      }

      // Extraer datos del proyecto, manejando la estructura anidada
      const projectInfo = analysisResult.project_info || {};
      const extractedData = analysisResult.analysis_data || analysisResult;

      // Obtener el nombre del proyecto de la estructura correcta
      const projectName = projectInfo.name || extractedData.project_name || 'Proyecto sin nombre';
      const projectScope = projectInfo.scope || extractedData.scope || '';
      const startDate = projectInfo.start_date || extractedData.start_date || null;
      const endDate = projectInfo.end_date || extractedData.end_date || null;
      const budget = projectInfo.budget || extractedData.budget || null;
      const deliverables = (projectInfo.deliverables || extractedData.deliverables || []).filter(Boolean);

      // Crear el proyecto
      const { data, error } = await this.supabase
        .from('projects')
        .insert({
          name: projectName,
          description: projectScope,
          start_date: startDate,
          end_date: endDate,
          budget: budget ? parseFloat(budget.toString().replace(/[^0-9.]/g, '')) : null,
          status: 'planning',
          owner_id: sessionData.session.user.id,
          created_by: sessionData.session.user.id,
          ai_generated: true,
          source_document_id: analysisResult.document_id
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      // Si hay entregables, crear tareas para cada uno
      if (deliverables.length > 0) {
        const workOrders = deliverables.map(deliverable => ({
          project_id: data.id,
          title: deliverable,
          description: `Entregable generado automáticamente: ${deliverable}`,
          status: 'pending',
          priority: 'medium',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }));

        const { error: workOrdersError } = await this.supabase
          .from('work_orders')
          .insert(workOrders);

        if (workOrdersError) {
          console.error('Error al crear las órdenes de trabajo:', workOrdersError);
        }
      }

      return data;
    } catch (error) {
      console.error('Error al crear el proyecto:', error);
      throw error;
    }
  }
}

// Exportar una instancia del cliente
export const documentAnalysisClient = new DocumentAnalysisClient();
