import { NextResponse } from 'next/server'

export async function POST() {
  try {
    // Simulamos la limpieza de la caché
    // En una implementación real, esto limpiaría la caché del servicio
    
    // Simulamos un retraso para la limpieza de la caché
    await new Promise(resolve => setTimeout(resolve, 500))
    
    return NextResponse.json({ 
      message: 'Caché limpiada correctamente',
      cache: {
        entries: 0,
        size_mb: 0
      }
    })
  } catch (error) {
    console.error('Error al limpiar la caché:', error)
    return NextResponse.json(
      { error: 'Error al limpiar la caché' },
      { status: 500 }
    )
  }
}
