import { Metada<PERSON> } from "next"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { UsersTable } from "@/components/features/users/users-table"
import Link from "next/link"
import { getServerSideUsers } from "./users-server"

export const metadata: Metadata = {
  title: "Usuarios | AdminCore ",
  description: "Gestión de usuarios",
}

export default async function UsersPage() {
  const users = await getServerSideUsers()

  // Add error handling for the UI
  if (!users || users.length === 0) {
    return (
      <div className="space-y-8">
        <div className="flex items-center justify-between">
          <h2 className="text-3xl font-bold tracking-tight">Usuarios</h2>
          <Button asChild>
            <Link href="/dashboard/users/new">Nuevo Usuario</Link>
          </Button>
        </div>

        <div className="p-8 text-center">
          <p className="text-muted-foreground">No se pudieron cargar los usuarios. Por favor, intenta de nuevo más tarde.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Usuarios</h2>
        <Button asChild>
          <Link href="/dashboard/users/new">Nuevo Usuario</Link>
        </Button>
      </div>

      <UsersTable data={users} />
    </div>
  )
}
