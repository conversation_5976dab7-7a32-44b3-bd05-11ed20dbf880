/**
 * @file Servicio para gestionar proyectos
 * @description Proporciona funciones para obtener, crear, actualizar y eliminar proyectos
 */

import { createClient } from '@/lib/supabase/client'
import { localDb, CachedProject } from '@/lib/db/local-database'
import { rateLimiterService, OperationType } from '@/lib/services/rate-limiter-service'
import { connectionService } from '@/lib/services/connection-service'

/**
 * Clase que implementa el servicio de proyectos
 */
export class ProjectsService {
  private static instance: ProjectsService
  private supabase = createClient()
  private cacheExpiryMs = 5 * 60 * 1000 // 5 minutos

  /**
   * Constructor privado para implementar el patrón Singleton
   */
  private constructor() {}

  /**
   * Obtiene la instancia única del servicio (patrón Singleton)
   */
  public static getInstance(): ProjectsService {
    if (!ProjectsService.instance) {
      ProjectsService.instance = new ProjectsService()
    }
    return ProjectsService.instance
  }

  /**
   * Obtiene todos los proyectos con caché local
   */
  public async getProjects(options: {
    userId?: string
    limit?: number
    forceRefresh?: boolean
  } = {}): Promise<CachedProject[]> {
    const { userId, limit = 50, forceRefresh = false } = options

    try {
      // Verificar si tenemos proyectos en caché y si la caché es válida
      if (!forceRefresh) {
        const cachedProjects = await this.getProjectsFromCache(userId, limit)

        // Si tenemos proyectos en caché y la caché no ha expirado, devolverlos
        if (cachedProjects.length > 0) {
          const cacheAge = Date.now() - (cachedProjects[0]._local_updated_at || 0)
          if (cacheAge < this.cacheExpiryMs) {
            console.log(`Usando ${cachedProjects.length} proyectos de caché local (edad: ${Math.round(cacheAge / 1000)}s)`)
            return cachedProjects
          }
        }
      }

      // Obtener estado de conexión actual
      const connectionStats = connectionService.getStats()

      // Si no hay conexión a internet o hay un error de conexión a Supabase,
      // devolver proyectos de caché aunque hayan expirado
      if (connectionStats.status !== 'connected') {
        console.log(`Sin conexión a Supabase (${connectionStats.status}), usando caché local`)
        const cachedProjects = await this.getProjectsFromCache(userId, limit)
        if (cachedProjects.length > 0) {
          return cachedProjects
        }
        // Si no hay proyectos en caché, lanzar error
        throw new Error(`No se pueden obtener proyectos: ${connectionStats.lastErrorMessage || 'Sin conexión a Supabase'}`)
      }

      // Si hay conexión, intentar obtener proyectos de Supabase
      return await this.fetchProjectsFromSupabase(userId, limit)
    } catch (error) {
      console.error('Error en getProjects:', error)

      // En caso de error, intentar devolver proyectos de caché
      const cachedProjects = await this.getProjectsFromCache(userId, limit)
      if (cachedProjects.length > 0) {
        console.log(`Usando ${cachedProjects.length} proyectos de caché local después de error`)
        return cachedProjects
      }

      // Si no hay proyectos en caché, propagar el error
      throw error
    }
  }

  /**
   * Obtiene proyectos de la caché local
   */
  private async getProjectsFromCache(userId?: string, limit: number = 50): Promise<CachedProject[]> {
    try {
      let query = localDb.projects

      // Filtrar por usuario si se proporciona
      if (userId) {
        query = query.where('user_id').equals(userId)
      }

      // Ordenar por fecha de actualización y limitar
      const projects = await query
        .orderBy('updated_at')
        .reverse()
        .limit(limit)
        .toArray()

      return projects
    } catch (error) {
      console.error('Error al obtener proyectos de caché:', error)
      return []
    }
  }

  /**
   * Obtiene proyectos de Supabase y los almacena en la caché local
   */
  private async fetchProjectsFromSupabase(userId?: string, limit: number = 50): Promise<CachedProject[]> {
    try {
      // Usar el limitador de tasa
      const { data, error } = await rateLimiterService.enqueue(
        OperationType.DATABASE,
        async () => {
          let query = this.supabase
            .from('projects')
            .select(`
              id, name, description, status, created_at, updated_at,
              owner_id, progress_percent, client_id, budget, currency,
              start_date, end_date
            `)
            .order('updated_at', { ascending: false })
            .limit(limit)

          // Filtrar por usuario si se proporciona
          if (userId) {
            // Buscar proyectos donde el usuario es propietario o está asignado
            query = query.or(`owner_id.eq.${userId},project_users.user_id.eq.${userId}`)
          }

          return query
        }
      )

      if (error) {
        const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

        console.error('Error al obtener proyectos de Supabase:', errorMessage)
        throw error
      }

      if (!data || data.length === 0) {
        console.log('No se encontraron proyectos en Supabase')
        return []
      }

      // Convertir a formato de caché
      const projectsWithMeta = data.map(project => ({
        ...project,
        _synced: true,
        _local_updated_at: Date.now()
      }))

      // Guardar en la base de datos local
      await this.saveProjectsToCache(projectsWithMeta)

      console.log(`Obtenidos ${projectsWithMeta.length} proyectos de Supabase`)
      return projectsWithMeta
    } catch (error) {
      console.error('Error al obtener proyectos de Supabase:', error)
      throw error
    }
  }

  /**
   * Guarda proyectos en la caché local
   */
  private async saveProjectsToCache(projects: CachedProject[]): Promise<void> {
    try {
      // Usar transacción para garantizar atomicidad
      await localDb.transaction('rw', localDb.projects, async () => {
        for (const project of projects) {
          await localDb.projects.put(project)
        }
      })
    } catch (error) {
      console.error('Error al guardar proyectos en caché:', error)
    }
  }
}

// Exportar instancia única
export const projectsService = ProjectsService.getInstance()
