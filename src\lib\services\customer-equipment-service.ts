/**
 * @file Customer Equipment Service
 * @description Service for managing customer equipment
 */

import { createClient } from '@/lib/supabase/client';
import { 
  CustomerEquipment, 
  CreateCustomerEquipmentInput,
  EquipmentStatus,
  ServiceRequest
} from '@/types/service-management';

/**
 * Service for managing customer equipment
 */
export class CustomerEquipmentService {
  /**
   * Get all customer equipment with optional filtering
   * 
   * @param options Filter options
   * @returns Promise with customer equipment
   */
  async getCustomerEquipment(options: {
    status?: EquipmentStatus | EquipmentStatus[];
    clientId?: string;
    search?: string;
    limit?: number;
    offset?: number;
    orderBy?: string;
    orderDirection?: 'asc' | 'desc';
    includeWarrantyInfo?: boolean;
  } = {}): Promise<{ data: CustomerEquipment[] | null; error: unknown }> {
    const supabase = createClient();
    
    // Start building the query
    let query = supabase
      .from('customer_equipment')
      .select(`
        *,
        client:client_id(*),
        inventory_item:inventory_item_id(id, name, description)
      `);
    
    // Apply filters
    if (options.status) {
      if (Array.isArray(options.status)) {
        query = query.in('status', options.status);
      } else {
        query = query.eq('status', options.status);
      }
    }
    
    if (options.clientId) {
      query = query.eq('client_id', options.clientId);
    }
    
    if (options.search) {
      query = query.or(`name.ilike.%${options.search}%,serial_number.ilike.%${options.search}%,model.ilike.%${options.search}%`);
    }
    
    // Apply ordering
    if (options.orderBy) {
      query = query.order(options.orderBy, { 
        ascending: options.orderDirection === 'asc' 
      });
    } else {
      // Default ordering by name ascending
      query = query.order('name', { ascending: true });
    }
    
    // Apply pagination
    if (options.limit) {
      query = query.limit(options.limit);
    }
    
    if (options.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
    }
    
    // Execute the query
    const { data, error } = await query;
    
    // If includeWarrantyInfo is true, calculate warranty status for each equipment
    if (!error && data && options.includeWarrantyInfo) {
      const now = new Date();
      
      return {
        data: data.map(equipment => ({
          ...equipment,
          warranty_status: this.calculateWarrantyStatus(equipment, now)
        })),
        error
      };
    }
    
    return { data, error };
  }

  /**
   * Get customer equipment by ID
   * 
   * @param id Equipment ID
   * @returns Promise with customer equipment
   */
  async getCustomerEquipmentById(id: string): Promise<{ data: CustomerEquipment | null; error: unknown }> {
    const supabase = createClient();
    
    const { data, error } = await supabase
      .from('customer_equipment')
      .select(`
        *,
        client:client_id(*),
        inventory_item:inventory_item_id(id, name, description)
      `)
      .eq('id', id)
      .single();
    
    if (!error && data) {
      // Calculate warranty status
      const now = new Date();
      return {
        data: {
          ...data,
          warranty_status: this.calculateWarrantyStatus(data, now)
        },
        error
      };
    }
    
    return { data, error };
  }

  /**
   * Create new customer equipment
   * 
   * @param equipment Equipment data
   * @returns Promise with created equipment
   */
  async createCustomerEquipment(equipment: CreateCustomerEquipmentInput): Promise<{ data: CustomerEquipment | null; error: unknown }> {
    const supabase = createClient();
    
    // Get current user for created_by field
    const { data: userData } = await supabase.auth.getUser();
    const userId = userData?.user?.id;
    
    // Prepare the data
    const newEquipment = {
      ...equipment,
      status: equipment.status || 'active',
      created_by: userId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    // Insert the equipment
    const { data, error } = await supabase
      .from('customer_equipment')
      .insert(newEquipment)
      .select(`
        *,
        client:client_id(*),
        inventory_item:inventory_item_id(id, name, description)
      `)
      .single();
    
    return { data, error };
  }

  /**
   * Update customer equipment
   * 
   * @param id Equipment ID
   * @param updates Equipment updates
   * @returns Promise with updated equipment
   */
  async updateCustomerEquipment(id: string, updates: Partial<CustomerEquipment>): Promise<{ data: CustomerEquipment | null; error: unknown }> {
    const supabase = createClient();
    
    // Prepare the updates
    const equipmentUpdates = {
      ...updates,
      updated_at: new Date().toISOString()
    };
    
    // Update the equipment
    const { data, error } = await supabase
      .from('customer_equipment')
      .update(equipmentUpdates)
      .eq('id', id)
      .select(`
        *,
        client:client_id(*),
        inventory_item:inventory_item_id(id, name, description)
      `)
      .single();
    
    if (!error && data) {
      // Calculate warranty status
      const now = new Date();
      return {
        data: {
          ...data,
          warranty_status: this.calculateWarrantyStatus(data, now)
        },
        error
      };
    }
    
    return { data, error };
  }

  /**
   * Delete customer equipment
   * 
   * @param id Equipment ID
   * @returns Promise with deletion result
   */
  async deleteCustomerEquipment(id: string): Promise<{ error: unknown }> {
    const supabase = createClient();
    
    const { error } = await supabase
      .from('customer_equipment')
      .delete()
      .eq('id', id);
    
    return { error };
  }

  /**
   * Get service history for equipment
   * 
   * @param equipmentId Equipment ID
   * @returns Promise with service requests related to the equipment
   */
  async getServiceHistory(equipmentId: string): Promise<{ data: ServiceRequest[] | null; error: unknown }> {
    const supabase = createClient();
    
    // First get service activities related to this equipment
    const { data: activities, error: activitiesError } = await supabase
      .from('service_activities')
      .select('service_request_id')
      .eq('equipment_id', equipmentId);
    
    if (activitiesError || !activities || activities.length === 0) {
      return { data: [], error: activitiesError };
    }
    
    // Get unique service request IDs
    const serviceRequestIds = [...new Set(activities.map(a => a.service_request_id))];
    
    // Get the service requests
    const { data, error } = await supabase
      .from('service_requests')
      .select(`
        *,
        client:client_id(*),
        assigned_user:assigned_to(id, email, full_name),
        work_order:work_order_id(id, title, status)
      `)
      .in('id', serviceRequestIds)
      .order('created_at', { ascending: false });
    
    return { data, error };
  }

  /**
   * Generate QR code for equipment
   * 
   * @param equipmentId Equipment ID
   * @returns Promise with QR code data
   */
  async generateQRCode(equipmentId: string): Promise<{ data: { qr_code: string } | null; error: unknown }> {
    const supabase = createClient();
    
    // Call a server function to generate QR code
    // This is a placeholder - you would implement this with a Supabase Edge Function
    // or a server API endpoint
    try {
      // For now, we'll just create a URL that can be converted to QR code on the client
      const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';
      const qrCodeData = `${baseUrl}/dashboard/customer-equipment/${equipmentId}`;
      
      // Update the equipment with the QR code data
      const { data, error } = await supabase
        .from('customer_equipment')
        .update({ qr_code: qrCodeData })
        .eq('id', equipmentId)
        .select('qr_code')
        .single();
      
      return { data, error };
    } catch (error) {
      console.error('Error generating QR code:', error);
      return { data: null, error };
    }
  }

  /**
   * Calculate warranty status for equipment
   * 
   * @param equipment Equipment data
   * @param currentDate Current date
   * @returns Warranty status object
   */
  private calculateWarrantyStatus(equipment: CustomerEquipment, currentDate: Date): {
    status: 'active' | 'expired' | 'expiring_soon' | 'unknown';
    daysRemaining?: number;
    expiryDate?: string;
  } {
    if (!equipment.warranty_start_date || !equipment.warranty_end_date) {
      return { status: 'unknown' };
    }
    
    const endDate = new Date(equipment.warranty_end_date);
    const daysRemaining = Math.ceil((endDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysRemaining <= 0) {
      return { 
        status: 'expired', 
        daysRemaining: 0,
        expiryDate: equipment.warranty_end_date
      };
    } else if (daysRemaining <= 30) {
      return { 
        status: 'expiring_soon', 
        daysRemaining,
        expiryDate: equipment.warranty_end_date
      };
    } else {
      return { 
        status: 'active', 
        daysRemaining,
        expiryDate: equipment.warranty_end_date
      };
    }
  }
}

// Export a singleton instance
export const customerEquipmentService = new CustomerEquipmentService();
