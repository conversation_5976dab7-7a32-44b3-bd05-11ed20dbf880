import { useState, useEffect, useCallback } from 'react';
import { createClient } from '@/lib/supabase/client';

/**
 * Hook para gestionar el almacenamiento offline y sincronización
 */
export function useOfflineStorage() {
  const [pendingChanges, setPendingChanges] = useState<any[]>([]);
  const [isSyncing, setIsSyncing] = useState(false);

  // Cargar cambios pendientes al iniciar
  useEffect(() => {
    const loadPendingChanges = () => {
      try {
        const storedChanges = localStorage.getItem('pendingChanges');
        if (storedChanges) {
          setPendingChanges(JSON.parse(storedChanges));
        }
      } catch (error) {
        console.error('Error al cargar cambios pendientes:', error);
      }
    };

    loadPendingChanges();
  }, []);

  // Guardar cambios pendientes cuando se actualicen
  useEffect(() => {
    if (pendingChanges.length > 0) {
      localStorage.setItem('pendingChanges', JSON.stringify(pendingChanges));
    }
  }, [pendingChanges]);

  /**
   * Guarda datos para uso offline
   * @param key Clave para identificar los datos
   * @param data Datos a guardar
   */
  const saveOfflineData = useCallback((key: string, data: unknown) => {
    try {
      localStorage.setItem(`offline_${key}`, JSON.stringify({
        data,
        timestamp: new Date().toISOString()
      }));
    } catch (error) {
      console.error(`Error al guardar datos offline (${key}):`, error);
    }
  }, []);

  /**
   * Obtiene datos guardados offline
   * @param key Clave de los datos
   * @returns Datos guardados o null si no existen
   */
  const getOfflineData = useCallback((key: string) => {
    try {
      const storedData = localStorage.getItem(`offline_${key}`);
      if (storedData) {
        const parsed = JSON.parse(storedData);
        return parsed.data;
      }
      return null;
    } catch (error) {
      console.error(`Error al obtener datos offline (${key}):`, error);
      return null;
    }
  }, []);

  /**
   * Verifica si existen datos guardados para una clave
   * @param key Clave a verificar
   * @returns true si existen datos, false en caso contrario
   */
  const hasOfflineData = useCallback((key: string) => {
    return localStorage.getItem(`offline_${key}`) !== null;
  }, []);

  /**
   * Registra un cambio para sincronizar cuando haya conexión
   * @param table Tabla a modificar
   * @param operation Operación (insert, update, delete)
   * @param data Datos de la operación
   * @param id ID del registro (para update/delete)
   */
  const addPendingChange = useCallback((table: string, operation: 'insert' | 'update' | 'delete', data: unknown, id?: string) => {
    setPendingChanges(prev => [
      ...prev,
      {
        id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        table,
        operation,
        data,
        recordId: id,
        timestamp: new Date().toISOString(),
        synced: false
      }
    ]);
  }, []);

  /**
   * Sincroniza todos los cambios pendientes con el servidor
   * @returns Promise que se resuelve cuando se completa la sincronización
   */
  const syncOfflineData = useCallback(async () => {
    if (isSyncing || pendingChanges.length === 0) return;

    setIsSyncing(true);
    const successfulSyncs: string[] = [];

    try {
      for (const change of pendingChanges) {
        if (change.synced) continue;

        try {
          switch (change.operation) {
            case 'insert':
              await supabase.from(change.table).insert(change.data);
              break;
            case 'update':
              await supabase.from(change.table).update(change.data).eq('id', change.recordId);
              break;
            case 'delete':
              await supabase.from(change.table).delete().eq('id', change.recordId);
              break;
          }

          successfulSyncs.push(change.id);
        } catch (error) {
          console.error(`Error al sincronizar cambio (${change.id}):`, error);
        }
      }

      // Actualizar cambios sincronizados
      if (successfulSyncs.length > 0) {
        setPendingChanges(prev =>
          prev.map(change =>
            successfulSyncs.includes(change.id)
              ? { ...change, synced: true }
              : change
          )
        );

        // Eliminar cambios sincronizados después de un tiempo
        setTimeout(() => {
          setPendingChanges(prev => prev.filter(change => !successfulSyncs.includes(change.id)));
        }, 5000);
      }

      return { success: true, syncedCount: successfulSyncs.length };
    } catch (error) {
      console.error('Error durante la sincronización:', error);
      return { success: false, error };
    } finally {
      setIsSyncing(false);
    }
  }, [isSyncing, pendingChanges]);

  /**
   * Realiza una operación offline y la registra para sincronización
   * @param table Tabla a modificar
   * @param operation Operación (insert, update, delete)
   * @param data Datos de la operación
   * @param id ID del registro (para update/delete)
   * @returns Datos de la operación
   */
  const performOfflineOperation = useCallback((
    table: string,
    operation: 'insert' | 'update' | 'delete',
    data: unknown,
    id?: string
  ) => {
    // Para inserciones, generar un ID temporal
    if (operation === 'insert' && !data.id) {
      data.id = `temp-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }

    // Registrar el cambio para sincronización
    addPendingChange(table, operation, data, id || data.id);

    // Actualizar datos offline
    const key = `offline_${table}`;
    const existingData = getOfflineData(table) || [];

    let updatedData;
    switch (operation) {
      case 'insert':
        updatedData = [...existingData, data];
        break;
      case 'update':
        updatedData = existingData.map((item: unknown) =>
          item.id === id ? { ...item, ...data } : item
        );
        break;
      case 'delete':
        updatedData = existingData.filter((item: unknown) => item.id !== id);
        break;
    }

    saveOfflineData(table, updatedData);

    return operation === 'delete' ? { success: true } : data;
  }, [addPendingChange, getOfflineData, saveOfflineData]);

  return {
    saveOfflineData,
    getOfflineData,
    hasOfflineData,
    addPendingChange,
    syncOfflineData,
    performOfflineOperation,
    pendingChanges,
    isSyncing
  };
}
