'use client';

import { useEffect, useState } from 'react';
import { createClient } from '@/lib/supabase/client';
import { toast } from '@/hooks/use-toast';
import { setupStorageBuckets } from '@/lib/supabase/storage-setup';

/**
 * Componente que verifica la configuración de Supabase al cargar la aplicación
 * y configura automáticamente los buckets de almacenamiento necesarios
 */
export function SupabaseInitializer() {
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    // Verificar si ya se ha inicializado en esta sesión
    const hasInitialized = sessionStorage.getItem('supabase_initialized');
    if (hasInitialized === 'true') {
      console.log('Supabase ya fue inicializado en esta sesión');
      setIsInitialized(true);
      return;
    }

    async function initializeSupabase() {
      try {
        const supabase = createClient();

        // Verificar sesión de usuario primero (más rápido)
        // No pasar parámetros a getUser() para compatibilidad con todas las versiones de Supabase
        const { data: { user }, error: userError } = await supabase.auth.getUser();

        if (userError) {
          console.error('Error al obtener usuario:', userError.message);
          markAsInitialized();
          return;
        }

        if (!user) {
          console.log('No hay sesión de usuario activa');
          markAsInitialized();
          return;
        }

        // Verificar si el usuario tiene un perfil (solo si hay usuario)
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('id, role')
          .eq('id', user.id)
          .maybeSingle();

        if (profileError && profileError.code !== 'PGRST116') {
          console.error('Error al verificar perfil:', profileError.message);
        } else if (!profile) {
          console.log('Perfil de usuario no encontrado');

          // Intentar crear perfil básico si no existe
          try {
            const { error: createError } = await supabase
              .from('profiles')
              .insert({
                id: user.id,
                email: user.email,
                updated_at: new Date().toISOString()
              });

            if (createError) {
              console.error('Error al crear perfil:', createError.message);
            } else {
              console.log('Perfil básico creado automáticamente');
            }
          } catch (createErr) {
            console.error('Excepción al crear perfil:', createErr);
          }
        } else {
          console.log('Perfil de usuario verificado');
        }

        // Configurar buckets de almacenamiento (solo en servidor o si es necesario)
        if (typeof window === 'undefined' || !localStorage.getItem('storage_buckets_initialized')) {
          console.log('Verificando buckets de almacenamiento...');
          const storageResult = await setupStorageBuckets();

          if (storageResult?.success) {
            console.log('Buckets de almacenamiento verificados');
            if (typeof window !== 'undefined') {
              localStorage.setItem('storage_buckets_initialized', 'true');
            }
          }
        }

        markAsInitialized();
      } catch (error) {
        console.error('Error al inicializar Supabase:', error);
        markAsInitialized();
      }
    }

    function markAsInitialized() {
      setIsInitialized(true);
      try {
        sessionStorage.setItem('supabase_initialized', 'true');
      } catch (e) {
        console.warn('No se pudo guardar estado en sessionStorage:', e);
      }
    }

    // Iniciar el proceso de inicialización
    initializeSupabase();
  }, []);

  // Este componente no renderiza nada visible
  return null;
}
