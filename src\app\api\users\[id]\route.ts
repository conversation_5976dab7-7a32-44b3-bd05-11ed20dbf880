import { NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// Interface para los datos de actualización de usuario
interface UserUpdateData {
  user_metadata: {
    full_name: string;
    role: string;
  };
  ban_duration: string;
}

export async function GET(
  _request: Request,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const { id } = params;
    // Create client directly in the route handler
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.SUPABASE_SERVICE_ROLE_KEY || ''
    )

    const { data, error } = await supabase.auth.admin.getUserById(id)

    if (error) {
      console.error('Error fetching user:', error)
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

      return NextResponse.json({ error: errorMessage }, { status: 500 })
    }

    if (!data.user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Transform the data to match the format expected by the client
    const transformedUser = {
      id: data.user.id,
      email: data.user.email || '',
      full_name: data.user.user_metadata?.full_name || '',
      role: data.user.user_metadata?.role || ['user'],
      status: (data.user as any).banned ? 'suspended' : (data.user.confirmed_at ? 'active' : 'inactive'),
      created_at: data.user.created_at,
      last_sign_in_at: data.user.last_sign_in_at,
      confirmed_at: data.user.confirmed_at,
      updated_at: data.user.updated_at,
    }

    return NextResponse.json(transformedUser)
  } catch (error: unknown) {
    console.error('Unexpected error fetching user:', error)
    const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
    return NextResponse.json(
      { error: 'Error del servidor', message: errorMessage },
      { status: 500 }
    )
  }
}

export async function PATCH(
  request: Request,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const { id } = params;
    const body = await request.json()

    // Create client directly in the route handler
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.SUPABASE_SERVICE_ROLE_KEY || ''
    )

    const updateData: UserUpdateData = {
      user_metadata: {
        full_name: body.full_name,
        role: body.role,
      },
      ban_duration: body.status === "suspended" ? "24h" : "none",
    };

    const { error } = await supabase.auth.admin.updateUserById(id, updateData)

    if (error) {
      console.error('Error updating user:', error)
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

      return NextResponse.json({ error: errorMessage }, { status: 500 })
    }

    return NextResponse.json({ success: true })
  } catch (error: unknown) {
    console.error('Unexpected error updating user:', error)
    const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
    return NextResponse.json(
      { error: 'Error del servidor', message: errorMessage },
      { status: 500 }
    )
  }
}

export async function DELETE(
  _request: Request,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const { id } = params;

    // Create client directly in the route handler
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.SUPABASE_SERVICE_ROLE_KEY || ''
    )

    // First, check if user exists
    const { data: existingUser, error: fetchError } = await supabase.auth.admin.getUserById(id)

    if (fetchError || !existingUser.user) {
      return NextResponse.json({ error: 'Usuario no encontrado' }, { status: 404 })
    }

    // Delete user from auth.users (this will cascade to public.users if properly configured)
    const { error: deleteError } = await supabase.auth.admin.deleteUser(id)

    if (deleteError) {
      console.error('Error deleting user from auth:', deleteError)
      return NextResponse.json({ error: deleteError.message }, { status: 500 })
    }

    // Also delete from public.users table if it exists
    try {
      const { error: publicDeleteError } = await supabase
        .from('users')
        .delete()
        .eq('id', id)

      if (publicDeleteError) {
        console.warn('Warning: Could not delete from public.users table:', publicDeleteError)
        // Don't fail the request as the auth user was deleted successfully
      }
    } catch (dbError) {
      console.warn('Warning: Exception deleting from public.users table:', dbError)
      // Continue execution even if there's an error
    }

    return NextResponse.json({ message: 'Usuario eliminado correctamente' })
  } catch (error: unknown) {
    console.error('Error deleting user:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Error al eliminar el usuario' },
      { status: 500 }
    )
  }
}
