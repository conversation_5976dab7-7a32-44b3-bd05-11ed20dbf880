"use client"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { ArrowLeft } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { UserForm } from "@/components/features/users/user-form"
import { createClient } from "@/lib/supabase/client"
import { getBaseUrl } from "@/lib/utils"
import { toast } from "@/components/ui/use-toast"
import Link from "next/link"

export default function NewUserPage() {
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()
  const supabase = createClient()

  const handleSubmit = async (data: unknown) => {
    setIsLoading(true)
    try {
      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(data.email)) {
        throw new Error('El formato del email no es válido. <NAME_EMAIL>')
      }

      // Validate password if provided
      if (data.password && data.password.length < 8) {
        throw new Error('La contraseña debe tener al menos 8 caracteres')
      }

      console.log('Submitting user data:', {
        email: data.email,
        password: data.password ? '(provided)' : '(not provided)',
        full_name: data.full_name,
        role: data.role,
        send_invite: data.send_invite
      })

      // Crear usuario a través de la API
      const response = await fetch(`${getBaseUrl()}/api/users/create`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: data.email,
          password: data.password || undefined,
          full_name: data.full_name,
          role: data.role,
          send_invite: data.send_invite
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        console.error('Server error response:', errorData)
        throw new Error(errorData.error || 'Error al crear el usuario')
      }

      // Si se seleccionó enviar invitación
      if (data.send_invite) {
        toast({
          title: "Invitación enviada",
          description: `Se ha enviado una invitación a ${data.email}`,
        })
      }

      toast({
        title: "Usuario creado",
        description: "El usuario se ha creado correctamente",
      })

      // Redirigir a la lista de usuarios
      router.push("/dashboard/users")
      router.refresh()
    } catch (error: unknown) {
      console.error("Error al crear el usuario:", error)
      const errorMessage = error instanceof Error ? error.message : "Ha ocurrido un error al crear el usuario"
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <Button variant="outline" size="icon" asChild>
          <Link href="/dashboard/users">
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <h2 className="text-3xl font-bold tracking-tight">Nuevo Usuario</h2>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Crear nuevo usuario</CardTitle>
          <CardDescription>
            Añade un nuevo usuario al sistema. Puedes enviar una invitación por email.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <UserForm onSubmit={handleSubmit} isLoading={isLoading} />
        </CardContent>
      </Card>
    </div>
  )
}
