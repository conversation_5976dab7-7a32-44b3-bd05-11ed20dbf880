import { Metada<PERSON> } from "next"
import { createClient } from "@/lib/supabase/server"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { Plus } from "lucide-react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { format } from "date-fns"
import { CustomerEquipment } from "@/types/service-management"

export const metadata: Metadata = {
  title: "Customer Equipment | AdminCore",
  description: "Manage customer equipment and assets",
}

// Function to get warranty status color
const getWarrantyStatusColor = (startDate?: string, endDate?: string): string => {
  if (!startDate || !endDate) return "bg-gray-500"

  const now = new Date()
  const end = new Date(endDate)

  if (end < now) {
    return "bg-red-500" // Expired
  }

  const daysRemaining = Math.ceil((end.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))

  if (daysRemaining <= 30) {
    return "bg-yellow-500" // Expiring soon
  }

  return "bg-green-500" // Active
}

// Function to get warranty status text
const getWarrantyStatusText = (startDate?: string, endDate?: string): string => {
  if (!startDate || !endDate) return "No Warranty"

  const now = new Date()
  const end = new Date(endDate)

  if (end < now) {
    return "Expired"
  }

  const daysRemaining = Math.ceil((end.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))

  if (daysRemaining <= 30) {
    return `Expiring in ${daysRemaining} days`
  }

  return "Active"
}

// Function to get equipment status color
const getStatusColor = (status: string): string => {
  switch (status) {
    case "active":
      return "bg-green-500"
    case "inactive":
      return "bg-gray-500"
    case "under_repair":
      return "bg-yellow-500"
    case "decommissioned":
      return "bg-red-500"
    case "in_storage":
      return "bg-blue-500"
    default:
      return "bg-gray-300"
  }
}

async function getCustomerEquipment() {
  try {
    const supabase = createClient()

    // Get customer equipment with related data
    const { data: equipment, error } = await (supabase as any)
      .from('customer_equipment')
      .select(`
        *,
        client:client_id(*),
        inventory_item:inventory_item_id(id, name, description)
      `)
      .order('name', { ascending: true })

    if (error) {
      console.error('Error loading customer equipment:', error)
      return []
    }

    return equipment || []
  } catch (error) {
    console.error('Error in getCustomerEquipment:', error)
    return []
  }
}

export default async function CustomerEquipmentPage() {
  const equipment = await getCustomerEquipment()

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Customer Equipment</h2>
          <p className="text-muted-foreground mt-2">
            Manage customer equipment, assets, and warranty information.
          </p>
        </div>
        <Button asChild>
          <Link href="/dashboard/customer-equipment/new">
            <Plus className="mr-2 h-4 w-4" /> New Equipment
          </Link>
        </Button>
      </div>

      {equipment.length === 0 ? (
        <Card>
          <CardHeader>
            <CardTitle>No Equipment Found</CardTitle>
            <CardDescription>
              No customer equipment has been registered yet.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-center py-8 text-muted-foreground">
              Start by adding your first piece of customer equipment.
            </p>
          </CardContent>
          <CardFooter className="flex justify-center">
            <Button asChild>
              <Link href="/dashboard/customer-equipment/new">
                <Plus className="mr-2 h-4 w-4" /> Add Equipment
              </Link>
            </Button>
          </CardFooter>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {equipment.map((item: CustomerEquipment) => (
            <Card key={item.id} className="overflow-hidden">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <CardTitle className="text-lg">{item.name}</CardTitle>
                  <Badge className={getStatusColor(item.status)}>
                    {item.status.charAt(0).toUpperCase() + item.status.slice(1).replace('_', ' ')}
                  </Badge>
                </div>
                <CardDescription>
                  {item.model && `Model: ${item.model}`}
                  {item.serial_number && ` • S/N: ${item.serial_number}`}
                </CardDescription>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Client:</span>
                    <span className="font-medium">{item.client?.name || 'N/A'}</span>
                  </div>

                  {item.location && (
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Location:</span>
                      <span>{item.location}</span>
                    </div>
                  )}

                  {item.manufacturer && (
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Manufacturer:</span>
                      <span>{item.manufacturer}</span>
                    </div>
                  )}

                  {item.installation_date && (
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Installed:</span>
                      <span>{format(new Date(item.installation_date), "MMM d, yyyy")}</span>
                    </div>
                  )}

                  {(item.warranty_start_date && item.warranty_end_date) && (
                    <div className="flex justify-between text-sm items-center">
                      <span className="text-muted-foreground">Warranty:</span>
                      <Badge className={getWarrantyStatusColor(item.warranty_start_date, item.warranty_end_date)}>
                        {getWarrantyStatusText(item.warranty_start_date, item.warranty_end_date)}
                      </Badge>
                    </div>
                  )}

                  {item.last_service_date && (
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Last Service:</span>
                      <span>{format(new Date(item.last_service_date), "MMM d, yyyy")}</span>
                    </div>
                  )}
                </div>
              </CardContent>
              <CardFooter className="pt-2">
                <Button variant="outline" size="sm" className="w-full" asChild>
                  <Link href={`/dashboard/customer-equipment/${item.id}`}>
                    View Details
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
