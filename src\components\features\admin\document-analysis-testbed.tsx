'use client'

import { useState, useEffect, useRef, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { Switch } from '@/components/ui/switch'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { ScrollArea } from '@/components/ui/scroll-area'
import { FileUpload } from '@/components/shared/file-upload'
import { createClient } from '@/lib/supabase/client'
import {
  AlertCircle,
  AlertTriangle,
  ArrowDownIcon,
  ArrowUpIcon,
  CheckCircle2,
  Database,
  RefreshCw,
  Save,
  Trash2,
  FileText,
  Upload,
  Download,
  Brain,
  Cpu,
  Microscope,
  Diff,
  Loader2,
  HelpCircle,
  Settings,
  Plus,
  Edit,
  BarChart,
  History,
  BookOpen,
  Sliders,
  Info,
  DownloadCloud
} from 'lucide-react'
import { diffChars, diffWords } from 'diff'

// Interfaz para los modelos disponibles
interface LLMModel {
  id: string;
  name: string;
  type: 'local' | 'cloud';
  size: string;
  capabilities: string[];
  isLoaded?: boolean;
}

// Interfaz para los resultados de análisis
interface AnalysisResult {
  originalText: string;
  extractedText: string;
  analysisResult: unknown;
  model: string;
  processingTime: number;
  accuracy?: number;
  timestamp: Date;
}

// Componente para mostrar el estado de los modelos
const ModelStatusCard = ({
  models,
  selectedModel,
  onSelectModel,
  isLoading
}: {
  models: LLMModel[],
  selectedModel: string,
  onSelectModel: (modelId: string) => void,
  isLoading: boolean
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Modelos Disponibles</CardTitle>
        <CardDescription>
          Selecciona un modelo para el análisis de documentos
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {isLoading ? (
          <div className="flex items-center justify-center py-4">
            <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
            <span className="ml-2 text-sm text-muted-foreground">Cargando modelos...</span>
          </div>
        ) : models.length === 0 ? (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>No hay modelos disponibles</AlertTitle>
            <AlertDescription>
              No se encontraron modelos configurados. Verifica la configuración de LLM Studio.
            </AlertDescription>
          </Alert>
        ) : (
          <div className="space-y-4">
            {models.map((model) => (
              <div
                key={model.id}
                className={`p-3 rounded-md border cursor-pointer transition-colors ${
                  selectedModel === model.id
                    ? 'border-primary bg-primary/5'
                    : 'border-border hover:border-primary/50'
                }`}
                onClick={() => onSelectModel(model.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    {model.type === 'local' ? (
                      <Cpu className="h-4 w-4 mr-2 text-blue-500" />
                    ) : (
                      <Brain className="h-4 w-4 mr-2 text-purple-500" />
                    )}
                    <span className="font-medium">{model.name}</span>
                  </div>
                  <Badge variant={model.isLoaded ? "default" : "outline"}>
                    {model.isLoaded ? "Cargado" : "No cargado"}
                  </Badge>
                </div>
                <div className="mt-2 text-xs text-muted-foreground">
                  <span className="inline-block mr-3">Tamaño: {model.size}</span>
                  <span className="inline-block">Tipo: {model.type === 'local' ? 'Local' : 'Cloud'}</span>
                </div>
                <div className="mt-2 flex flex-wrap gap-1">
                  {model.capabilities.map((capability, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {capability}
                    </Badge>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button variant="outline" className="w-full" onClick={() => window.location.reload()}>
          <RefreshCw className="mr-2 h-4 w-4" />
          Actualizar lista de modelos
        </Button>
      </CardFooter>
    </Card>
  )
}

// Componente para mostrar la comparación de texto
const TextComparisonCard = ({
  originalText,
  extractedText,
  isLoading,
  similarityScore
}: {
  originalText: string,
  extractedText: string,
  isLoading: boolean,
  similarityScore?: number
}) => {
  const [showDiff, setShowDiff] = useState(false);
  const [diffType, setDiffType] = useState<'chars' | 'words'>('words');

  // Calcular diferencias entre textos
  const differences = useMemo(() => {
    if (!originalText || !extractedText) return [];

    return diffType === 'chars'
      ? diffChars(originalText, extractedText)
      : diffWords(originalText, extractedText);
  }, [originalText, extractedText, diffType]);

  // Renderizar texto con diferencias resaltadas
  const renderDiff = () => {
    return differences.map((part, index) => {
      // Determinar el estilo según el tipo de diferencia
      const style = part.added
        ? { backgroundColor: 'rgba(0, 255, 0, 0.2)', textDecoration: 'none' } // Verde para adiciones
        : part.removed
        ? { backgroundColor: 'rgba(255, 0, 0, 0.2)', textDecoration: 'line-through' } // Rojo para eliminaciones
        : {}; // Sin estilo para partes sin cambios

      return (
        <span key={index} style={style}>
          {part.value}
        </span>
      );
    });
  };

  // Exportar comparación como archivo de texto
  const handleExportComparison = () => {
    if (!originalText && !extractedText) return;

    const content = `COMPARACIÓN DE TEXTO
===================

TEXTO ORIGINAL:
${originalText || 'No disponible'}

TEXTO EXTRAÍDO (OCR):
${extractedText || 'No disponible'}

SIMILITUD: ${similarityScore !== undefined ? (similarityScore * 100).toFixed(2) + '%' : 'No calculada'}

Generado el: ${new Date().toLocaleString()}
`;

    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `comparacion-texto-${new Date().toISOString().slice(0, 10)}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <Card className="col-span-2">
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="text-lg">Comparación de Texto</CardTitle>
            <CardDescription>
              Compara el texto original con el texto extraído por OCR
            </CardDescription>
          </div>
          {similarityScore !== undefined && (
            <div className="flex items-center">
              <span className="text-sm mr-2">Similitud:</span>
              <Badge variant={similarityScore > 0.8 ? "default" : similarityScore > 0.5 ? "secondary" : "destructive"}>
                {(similarityScore * 100).toFixed(2)}%
              </Badge>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            <span className="ml-2 text-muted-foreground">Procesando documento...</span>
          </div>
        ) : (
          <>
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <Switch
                  id="show-diff"
                  checked={showDiff}
                  onCheckedChange={setShowDiff}
                />
                <Label htmlFor="show-diff">Mostrar diferencias</Label>
              </div>

              {showDiff && (
                <Select value={diffType} onValueChange={(value) => setDiffType(value as 'chars' | 'words')}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Tipo de diferencia" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="chars">Por caracteres</SelectItem>
                    <SelectItem value="words">Por palabras</SelectItem>
                  </SelectContent>
                </Select>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="mb-2 block">Texto Original</Label>
                <div className="border rounded-md p-3 h-[400px] overflow-auto bg-muted/30">
                  {showDiff ? (
                    <pre className="text-sm whitespace-pre-wrap">{originalText || 'No hay texto original disponible'}</pre>
                  ) : (
                    <pre className="text-sm whitespace-pre-wrap">{originalText || 'No hay texto original disponible'}</pre>
                  )}
                </div>
              </div>
              <div>
                <Label className="mb-2 block">Texto Extraído (OCR)</Label>
                <div className="border rounded-md p-3 h-[400px] overflow-auto bg-muted/30">
                  {showDiff ? (
                    <pre className="text-sm whitespace-pre-wrap">{renderDiff()}</pre>
                  ) : (
                    <pre className="text-sm whitespace-pre-wrap">{extractedText || 'No hay texto extraído disponible'}</pre>
                  )}
                </div>
              </div>
            </div>
          </>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          disabled={!originalText && !extractedText}
          onClick={handleExportComparison}
        >
          <Download className="mr-2 h-4 w-4" />
          Exportar comparación
        </Button>
        <Button
          variant={showDiff ? "default" : "outline"}
          disabled={!originalText && !extractedText}
          onClick={() => setShowDiff(!showDiff)}
        >
          <Diff className="mr-2 h-4 w-4" />
          {showDiff ? "Ocultar diferencias" : "Ver diferencias"}
        </Button>
      </CardFooter>
    </Card>
  )
}

// Componente para mostrar los resultados del análisis
const AnalysisResultCard = ({
  result,
  isLoading
}: {
  result: unknown,
  isLoading: boolean
}) => {
  // Extraer datos del análisis, manejando la estructura anidada
  const extractedData = result?.analysis_data || result;
  return (
    <Card className="col-span-2">
      <CardHeader>
        <CardTitle className="text-lg">Resultado del Análisis</CardTitle>
        <CardDescription>
          Información extraída del documento
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            <span className="ml-2 text-muted-foreground">Analizando documento...</span>
          </div>
        ) : !result ? (
          <div className="text-center py-8 text-muted-foreground">
            <FileText className="h-12 w-12 mx-auto mb-4 opacity-20" />
            <p>No hay resultados de análisis disponibles</p>
            <p className="text-sm mt-2">Sube un documento y ejecuta el análisis para ver los resultados</p>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="mb-2 block">Título del Proyecto</Label>
                <div className="border rounded-md p-3 bg-muted/30">
                  {extractedData.project_name || 'No disponible'}
                </div>
              </div>
              <div>
                <Label className="mb-2 block">Confianza</Label>
                <div className="border rounded-md p-3 bg-muted/30">
                  {extractedData.confidence_score ? `${(extractedData.confidence_score * 100).toFixed(1)}%` : 'No disponible'}
                </div>
              </div>
            </div>

            <div>
              <Label className="mb-2 block">Descripción</Label>
              <div className="border rounded-md p-3 bg-muted/30">
                {extractedData.description || 'No disponible'}
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="mb-2 block">Fecha de Inicio</Label>
                <div className="border rounded-md p-3 bg-muted/30">
                  {extractedData.start_date || 'No disponible'}
                </div>
              </div>
              <div>
                <Label className="mb-2 block">Fecha de Fin</Label>
                <div className="border rounded-md p-3 bg-muted/30">
                  {extractedData.end_date || 'No disponible'}
                </div>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="mb-2 block">Presupuesto</Label>
                <div className="border rounded-md p-3 bg-muted/30">
                  {extractedData.budget ? `${extractedData.budget} ${extractedData.currency || ''}` : 'No disponible'}
                </div>
              </div>
              <div>
                <Label className="mb-2 block">Cliente</Label>
                <div className="border rounded-md p-3 bg-muted/30">
                  {extractedData.client_name || 'No disponible'}
                </div>
              </div>
            </div>

            <div>
              <Label className="mb-2 block">Entregables</Label>
              <div className="border rounded-md p-3 bg-muted/30">
                {extractedData.deliverables && extractedData.deliverables.length > 0 ? (
                  <ul className="list-disc pl-5">
                    {extractedData.deliverables.map((item: string, index: number) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                ) : 'No disponible'}
              </div>
            </div>

            <div>
              <Label className="mb-2 block">Etiquetas</Label>
              <div className="border rounded-md p-3 bg-muted/30">
                <div className="flex flex-wrap gap-2">
                  {extractedData.tags && extractedData.tags.length > 0 ? (
                    extractedData.tags.map((tag: string, index: number) => (
                      <Badge key={index} variant="secondary">
                        {tag}
                      </Badge>
                    ))
                  ) : 'No disponible'}
                </div>
              </div>
            </div>

            <Separator />

            <div>
              <Label className="mb-2 block">JSON Completo</Label>
              <div className="border rounded-md p-3 bg-muted/30 max-h-[200px] overflow-auto">
                <pre className="text-xs">{JSON.stringify(result, null, 2)}</pre>
              </div>
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button variant="outline" className="w-full" disabled={!result}>
          <Download className="mr-2 h-4 w-4" />
          Exportar resultados
        </Button>
      </CardFooter>
    </Card>
  )
}

// Componente para gestionar perfiles de configuración
const ModelConfigProfilesCard = ({
  modelId,
  onSelectProfile,
  onSaveProfile
}: {
  modelId: string,
  onSelectProfile: (profile: unknown) => void,
  onSaveProfile: (profile: unknown) => void
}) => {
  const [profiles, setProfiles] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newProfileName, setNewProfileName] = useState('');
  const [newProfileDescription, setNewProfileDescription] = useState('');
  const [selectedParameters, setSelectedParameters] = useState<any>({});
  const supabase = createClient();

  // Cargar perfiles al cambiar el modelo seleccionado
  useEffect(() => {
    if (!modelId) return;

    async function loadProfiles() {
      setIsLoading(true);
      try {
        const { data: session } = await supabase.auth.getSession();

        if (!session?.session) {
          throw new Error('No hay sesión activa');
        }

        const { data, error } = await supabase
          .from('model_config_profiles')
          .select('*')
          .eq('model_id', modelId)
          .eq('user_id', session.session.user.id)
          .order('created_at', { ascending: false });

        if (error) throw error;

        setProfiles(data || []);
      } catch (error) {
        console.error('Error al cargar perfiles:', error);
      } finally {
        setIsLoading(false);
      }
    }

    loadProfiles();
  }, [modelId, supabase]);

  // Cargar parámetros por defecto del modelo
  useEffect(() => {
    if (!modelId) return;

    async function loadDefaultParameters() {
      try {
        const { data, error } = await supabase
          .from('available_models')
          .select('default_parameters')
          .eq('id', modelId)
          .single();

        if (error) throw error;

        if (data?.default_parameters) {
          setSelectedParameters(data.default_parameters);
        }
      } catch (error) {
        console.error('Error al cargar parámetros por defecto:', error);
      }
    }

    loadDefaultParameters();
  }, [modelId, supabase]);

  // Guardar nuevo perfil
  const handleSaveProfile = async () => {
    if (!newProfileName || !modelId) return;

    try {
      const { data: session } = await supabase.auth.getSession();

      if (!session?.session) {
        throw new Error('No hay sesión activa');
      }

      const { data, error } = await supabase
        .from('model_config_profiles')
        .insert({
          name: newProfileName,
          description: newProfileDescription,
          model_id: modelId,
          parameters: selectedParameters,
          user_id: session.session.user.id
        })
        .select()
        .single();

      if (error) throw error;

      // Actualizar lista de perfiles
      setProfiles([data, ...profiles]);

      // Limpiar formulario
      setNewProfileName('');
      setNewProfileDescription('');
      setShowCreateDialog(false);

      // Notificar al componente padre
      if (data) {
        onSaveProfile(data);
      }
    } catch (error) {
      console.error('Error al guardar perfil:', error);
    }
  };

  // Seleccionar un perfil
  const handleSelectProfile = (profile: unknown) => {
    onSelectProfile(profile);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="text-lg">Perfiles de Configuración</CardTitle>
            <CardDescription>
              Guarda y carga configuraciones para este modelo
            </CardDescription>
          </div>
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Nuevo Perfil
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Crear Nuevo Perfil</DialogTitle>
                <DialogDescription>
                  Guarda esta configuración para usarla en futuros análisis
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="profile-name">Nombre del Perfil</Label>
                  <Input
                    id="profile-name"
                    placeholder="Ej: OCR Optimizado"
                    value={newProfileName}
                    onChange={(e) => setNewProfileName(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="profile-description">Descripción (opcional)</Label>
                  <Textarea
                    id="profile-description"
                    placeholder="Describe para qué sirve este perfil"
                    value={newProfileDescription}
                    onChange={(e) => setNewProfileDescription(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label>Parámetros</Label>
                  <div className="border rounded-md p-3 bg-muted/30">
                    <pre className="text-xs">{JSON.stringify(selectedParameters, null, 2)}</pre>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setShowCreateDialog(false)}>Cancelar</Button>
                <Button onClick={handleSaveProfile}>Guardar Perfil</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center py-4">
            <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
            <span className="ml-2 text-sm text-muted-foreground">Cargando perfiles...</span>
          </div>
        ) : profiles.length === 0 ? (
          <div className="text-center py-4 text-muted-foreground">
            <p>No hay perfiles guardados</p>
            <p className="text-sm mt-2">Crea un perfil para guardar esta configuración</p>
          </div>
        ) : (
          <ScrollArea className="h-[200px]">
            <div className="space-y-2">
              {profiles.map((profile) => (
                <div
                  key={profile.id}
                  className="p-2 border rounded-md cursor-pointer hover:border-primary transition-colors"
                  onClick={() => handleSelectProfile(profile)}
                >
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-medium">{profile.name}</p>
                      {profile.description && (
                        <p className="text-xs text-muted-foreground">{profile.description}</p>
                      )}
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {new Date(profile.created_at).toLocaleDateString()}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  );
};

// Componente para mostrar y configurar parámetros del modelo
const ModelParametersCard = ({
  modelId,
  parameters,
  onParametersChange
}: {
  modelId: string,
  parameters: unknown,
  onParametersChange: (params: unknown) => void
}) => {
  // Definiciones de parámetros con explicaciones
  const parameterDefinitions = {
    temperature: {
      name: "Temperatura",
      description: "Controla la aleatoriedad de las respuestas. Valores más bajos generan respuestas más deterministas y enfocadas, mientras que valores más altos producen respuestas más creativas y diversas.",
      min: 0,
      max: 1,
      step: 0.1,
      default: 0.2
    },
    maxTokens: {
      name: "Tokens Máximos",
      description: "Limita la longitud máxima de la respuesta generada. Un token representa aproximadamente 4 caracteres en inglés.",
      min: 100,
      max: 4000,
      step: 100,
      default: 2000
    },
    contextLength: {
      name: "Longitud de Contexto",
      description: "Define cuánto texto puede procesar el modelo en una sola solicitud. Valores más altos permiten analizar documentos más largos, pero consumen más recursos.",
      min: 2048,
      max: 32768,
      step: 1024,
      default: 8192
    },
    topP: {
      name: "Top P",
      description: "Controla la diversidad mediante muestreo de núcleo. El modelo considera solo las opciones más probables que suman la probabilidad p. Útil para controlar la aleatoriedad de manera diferente a la temperatura.",
      min: 0,
      max: 1,
      step: 0.05,
      default: 0.95
    }
  };

  // Actualizar un parámetro individual
  const handleParameterChange = (param: string, value: number) => {
    const updatedParameters = { ...parameters, [param]: value };
    onParametersChange(updatedParameters);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="text-lg">Parámetros del Modelo</CardTitle>
            <CardDescription>
              Ajusta los parámetros para optimizar el análisis
            </CardDescription>
          </div>
          <Button variant="ghost" size="sm" asChild>
            <a href="/dashboard/admin/document-analysis/parameters" target="_blank" rel="noopener noreferrer">
              <BookOpen className="h-4 w-4 mr-2" />
              Guía
            </a>
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Temperatura */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <Label htmlFor="temperature">
              {parameterDefinitions.temperature.name}
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-5 w-5 ml-1">
                    <HelpCircle className="h-3 w-3" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80">
                  <p className="text-sm">{parameterDefinitions.temperature.description}</p>
                </PopoverContent>
              </Popover>
            </Label>
            <span className="text-sm font-medium">{parameters.temperature || parameterDefinitions.temperature.default}</span>
          </div>
          <Slider
            id="temperature"
            min={parameterDefinitions.temperature.min}
            max={parameterDefinitions.temperature.max}
            step={parameterDefinitions.temperature.step}
            value={[parameters.temperature || parameterDefinitions.temperature.default]}
            onValueChange={(value) => handleParameterChange('temperature', value[0])}
          />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>Preciso</span>
            <span>Creativo</span>
          </div>
        </div>

        {/* Tokens Máximos */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <Label htmlFor="maxTokens">
              {parameterDefinitions.maxTokens.name}
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-5 w-5 ml-1">
                    <HelpCircle className="h-3 w-3" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80">
                  <p className="text-sm">{parameterDefinitions.maxTokens.description}</p>
                </PopoverContent>
              </Popover>
            </Label>
            <span className="text-sm font-medium">{parameters.maxTokens || parameterDefinitions.maxTokens.default}</span>
          </div>
          <Slider
            id="maxTokens"
            min={parameterDefinitions.maxTokens.min}
            max={parameterDefinitions.maxTokens.max}
            step={parameterDefinitions.maxTokens.step}
            value={[parameters.maxTokens || parameterDefinitions.maxTokens.default]}
            onValueChange={(value) => handleParameterChange('maxTokens', value[0])}
          />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>Corto</span>
            <span>Detallado</span>
          </div>
        </div>

        {/* Longitud de Contexto (solo para modelos locales) */}
        {modelId.includes('gemma') || modelId.includes('llama') ? (
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <Label htmlFor="contextLength">
                {parameterDefinitions.contextLength.name}
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-5 w-5 ml-1">
                      <HelpCircle className="h-3 w-3" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-80">
                    <p className="text-sm">{parameterDefinitions.contextLength.description}</p>
                  </PopoverContent>
                </Popover>
              </Label>
              <span className="text-sm font-medium">{parameters.contextLength || parameterDefinitions.contextLength.default}</span>
            </div>
            <Slider
              id="contextLength"
              min={parameterDefinitions.contextLength.min}
              max={parameterDefinitions.contextLength.max}
              step={parameterDefinitions.contextLength.step}
              value={[parameters.contextLength || parameterDefinitions.contextLength.default]}
              onValueChange={(value) => handleParameterChange('contextLength', value[0])}
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Menor memoria</span>
              <span>Mayor contexto</span>
            </div>
          </div>
        ) : null}

        {/* Top P */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <Label htmlFor="topP">
              {parameterDefinitions.topP.name}
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-5 w-5 ml-1">
                    <HelpCircle className="h-3 w-3" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80">
                  <p className="text-sm">{parameterDefinitions.topP.description}</p>
                </PopoverContent>
              </Popover>
            </Label>
            <span className="text-sm font-medium">{parameters.topP || parameterDefinitions.topP.default}</span>
          </div>
          <Slider
            id="topP"
            min={parameterDefinitions.topP.min}
            max={parameterDefinitions.topP.max}
            step={parameterDefinitions.topP.step}
            value={[parameters.topP || parameterDefinitions.topP.default]}
            onValueChange={(value) => handleParameterChange('topP', value[0])}
          />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>Enfocado</span>
            <span>Diverso</span>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button variant="outline" className="w-full" onClick={() => onParametersChange({
          temperature: parameterDefinitions.temperature.default,
          maxTokens: parameterDefinitions.maxTokens.default,
          contextLength: parameterDefinitions.contextLength.default,
          topP: parameterDefinitions.topP.default
        })}>
          <RefreshCw className="mr-2 h-4 w-4" />
          Restablecer valores predeterminados
        </Button>
      </CardFooter>
    </Card>
  );
};

// Componente para mostrar el historial de pruebas
const TestHistoryCard = ({
  onSelectTest
}: {
  onSelectTest: (test: unknown) => void
}) => {
  const [tests, setTests] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const supabase = createClient();

  // Cargar historial de pruebas
  useEffect(() => {
    async function loadTestHistory() {
      setIsLoading(true);
      try {
        const { data: session } = await supabase.auth.getSession();

        if (!session?.session) {
          throw new Error('No hay sesión activa');
        }

        const { data, error } = await supabase
          .from('document_analysis_tests')
          .select(`
            id,
            created_at,
            model_id,
            model_name,
            similarity_score,
            processing_time_ms,
            documents(filename)
          `)
          .eq('user_id', session.session.user.id)
          .order('created_at', { ascending: false })
          .limit(20);

        if (error) throw error;

        setTests(data || []);
      } catch (error) {
        console.error('Error al cargar historial de pruebas:', error);
      } finally {
        setIsLoading(false);
      }
    }

    loadTestHistory();
  }, [supabase]);

  // Formatear tiempo de procesamiento
  const formatProcessingTime = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Historial de Pruebas</CardTitle>
        <CardDescription>
          Pruebas de análisis realizadas recientemente
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center py-4">
            <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
            <span className="ml-2 text-sm text-muted-foreground">Cargando historial...</span>
          </div>
        ) : tests.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <History className="h-12 w-12 mx-auto mb-4 opacity-20" />
            <p>No hay pruebas en el historial</p>
            <p className="text-sm mt-2">Realiza análisis de documentos para ver resultados aquí</p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Documento</TableHead>
                <TableHead>Modelo</TableHead>
                <TableHead>Similitud</TableHead>
                <TableHead>Tiempo</TableHead>
                <TableHead>Fecha</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {tests.map((test) => (
                <TableRow
                  key={test.id}
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => onSelectTest(test)}
                >
                  <TableCell className="font-medium truncate max-w-[150px]">
                    {test.documents?.filename || 'Documento'}
                  </TableCell>
                  <TableCell>{test.model_name}</TableCell>
                  <TableCell>
                    {test.similarity_score !== null ? (
                      <Badge variant={test.similarity_score > 0.8 ? "default" : test.similarity_score > 0.5 ? "secondary" : "destructive"}>
                        {(test.similarity_score * 100).toFixed(0)}%
                      </Badge>
                    ) : (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </TableCell>
                  <TableCell>{test.processing_time_ms ? formatProcessingTime(test.processing_time_ms) : '-'}</TableCell>
                  <TableCell className="text-muted-foreground">
                    {new Date(test.created_at).toLocaleDateString()}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  );
};

// Componente para instalar modelos
const ModelInstallationCard = () => {
  const [isInstalling, setIsInstalling] = useState(false);

  const handleInstallModels = () => {
    setIsInstalling(true);

    // Simular instalación (en producción, esto llamaría a un endpoint real)
    setTimeout(() => {
      setIsInstalling(false);
      window.open('/scripts/install-llm-models.sh', '_blank');
    }, 1500);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Instalación de Modelos</CardTitle>
        <CardDescription>
          Instala los modelos recomendados para análisis de documentos
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex items-center">
            <Badge variant="outline" className="mr-2">Recomendado</Badge>
            <h3 className="font-medium">Gemma 3 8B Vision</h3>
          </div>
          <p className="text-sm text-muted-foreground">
            Modelo de visión optimizado para OCR y análisis de documentos. Tamaño: 8-10GB.
          </p>
        </div>

        <div className="space-y-2">
          <div className="flex items-center">
            <Badge variant="outline" className="mr-2">Recomendado</Badge>
            <h3 className="font-medium">Llama 3.2 8B Vision</h3>
          </div>
          <p className="text-sm text-muted-foreground">
            Modelo multimodal con excelente capacidad para procesar documentos complejos. Tamaño: 8-10GB.
          </p>
        </div>

        <Alert>
          <Info className="h-4 w-4" />
          <AlertTitle>Información</AlertTitle>
          <AlertDescription>
            Estos modelos se ejecutarán localmente en tu máquina. Asegúrate de tener suficiente espacio en disco y una GPU compatible.
          </AlertDescription>
        </Alert>
      </CardContent>
      <CardFooter>
        <Button
          className="w-full"
          onClick={handleInstallModels}
          disabled={isInstalling}
        >
          {isInstalling ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Preparando instalador...
            </>
          ) : (
            <>
              <DownloadCloud className="mr-2 h-4 w-4" />
              Descargar Script de Instalación
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
};

// Componente principal del área de pruebas
export function DocumentAnalysisTestbed() {
  const [activeTab, setActiveTab] = useState('document-upload')
  const [models, setModels] = useState<LLMModel[]>([])
  const [selectedModel, setSelectedModel] = useState<string>('')
  const [uploadedDocument, setUploadedDocument] = useState<any>(null)
  const [originalText, setOriginalText] = useState<string>('')
  const [extractedText, setExtractedText] = useState<string>('')
  const [analysisResult, setAnalysisResult] = useState<any>(null)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [isLoadingModels, setIsLoadingModels] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)
  const [similarityScore, setSimilarityScore] = useState<number | undefined>(undefined)
  const [processingTime, setProcessingTime] = useState<number | undefined>(undefined)
  const [modelParameters, setModelParameters] = useState<any>({
    temperature: 0.2,
    maxTokens: 2000,
    contextLength: 8192,
    topP: 0.95
  })
  const [testHistory, setTestHistory] = useState<any[]>([])
  const supabase = createClient()

  // Cargar modelos disponibles
  useEffect(() => {
    async function loadModels() {
      try {
        setIsLoadingModels(true)

        // Realizar solicitud de prueba para obtener información de modelos
        const { data, error } = await supabase.functions.invoke(
          'analyze-document',
          {
            body: {
              test: true
            }
          }
        )

        if (error) throw error

        // Verificar si hay información de LLM Studio
        if (data?.llmStudio) {
          // Crear lista de modelos disponibles
          const modelsList: LLMModel[] = []

          // Añadir modelo de LLM Studio si está disponible
          if (data.llmStudio.available) {
            modelsList.push({
              id: 'llm-studio',
              name: data.llmStudio.modelName || 'LLM Studio Model',
              type: 'local',
              size: 'Variable',
              capabilities: ['OCR', 'Análisis de documentos', 'Extracción de información'],
              isLoaded: data.llmStudio.modelLoaded
            })

            // Seleccionar por defecto
            if (!selectedModel) {
              setSelectedModel('llm-studio')
            }
          }

          // Añadir modelos predefinidos de cloud
          modelsList.push({
            id: 'gemini-1.5-flash',
            name: 'Gemini 1.5 Flash',
            type: 'cloud',
            size: 'N/A (Cloud)',
            capabilities: ['Análisis de documentos', 'Extracción de información'],
            isLoaded: true
          })

          modelsList.push({
            id: 'gpt-4o',
            name: 'GPT-4o',
            type: 'cloud',
            size: 'N/A (Cloud)',
            capabilities: ['Análisis de documentos', 'Extracción de información'],
            isLoaded: true
          })

          setModels(modelsList)
        } else {
          // Si no hay información de LLM Studio, usar solo modelos cloud
          setModels([
            {
              id: 'gemini-1.5-flash',
              name: 'Gemini 1.5 Flash',
              type: 'cloud',
              size: 'N/A (Cloud)',
              capabilities: ['Análisis de documentos', 'Extracción de información'],
              isLoaded: true
            },
            {
              id: 'gpt-4o',
              name: 'GPT-4o',
              type: 'cloud',
              size: 'N/A (Cloud)',
              capabilities: ['Análisis de documentos', 'Extracción de información'],
              isLoaded: true
            }
          ])

          // Seleccionar Gemini por defecto
          if (!selectedModel) {
            setSelectedModel('gemini-1.5-flash')
          }
        }
      } catch (err) {
        console.error('Error al cargar modelos:', err)
        setError('Error al cargar modelos disponibles')

        // Cargar modelos predeterminados en caso de error
        setModels([
          {
            id: 'gemini-1.5-flash',
            name: 'Gemini 1.5 Flash',
            type: 'cloud',
            size: 'N/A (Cloud)',
            capabilities: ['Análisis de documentos', 'Extracción de información'],
            isLoaded: true
          }
        ])

        // Seleccionar Gemini por defecto
        if (!selectedModel) {
          setSelectedModel('gemini-1.5-flash')
        }
      } finally {
        setIsLoadingModels(false)
      }
    }

    loadModels()
  }, [])

  // Manejar la carga de documentos
  const handleUploadComplete = (document: unknown) => {
    setUploadedDocument(document)
    setError(null)

    // Limpiar resultados anteriores
    setOriginalText('')
    setExtractedText('')
    setAnalysisResult(null)
  }

  // Manejar la selección de modelo
  const handleSelectModel = (modelId: string) => {
    setSelectedModel(modelId)
  }

  // Manejar el análisis del documento
  const handleAnalyzeDocument = async () => {
    if (!uploadedDocument) {
      setError('Por favor, sube un documento primero')
      return
    }

    if (!selectedModel) {
      setError('Por favor, selecciona un modelo para el análisis')
      return
    }

    try {
      setIsLoading(true)
      setError(null)

      // Obtener la sesión actual
      const { data: { session } } = await supabase.auth.getSession()

      if (!session) {
        throw new Error('No hay una sesión activa')
      }

      // Configurar opciones del proveedor según el modelo seleccionado
      let provider = 'gemini'
      let modelName = 'gemini-1.5-flash'
      let useLLMStudio = false
      let modelType = 'cloud'

      // Buscar el modelo seleccionado en la lista de modelos
      const selectedModelInfo = models.find(m => m.id === selectedModel)

      if (selectedModel === 'llm-studio' || (selectedModelInfo && selectedModelInfo.type === 'local')) {
        useLLMStudio = true
        modelType = 'local'
      } else if (selectedModel === 'gpt-4o') {
        provider = 'openai'
        modelName = 'gpt-4o'
      } else if (selectedModel.startsWith('gemini-')) {
        provider = 'gemini'
        modelName = selectedModel
      }

      // Realizar solicitud de análisis
      const response = await fetch('/api/document-analysis/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          documentId: uploadedDocument.id,
          provider,
          config: {
            ...modelParameters,
            modelName,
            useLLMStudio
          },
          testMode: true // Indicar que es una prueba para obtener texto original y extraído
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Error al analizar el documento')
      }

      const result = await response.json()

      // Actualizar estado con los resultados
      if (result.originalText) {
        setOriginalText(result.originalText)
      }

      if (result.extractedText) {
        setExtractedText(result.extractedText)
      }

      if (result.analysisResult) {
        setAnalysisResult(result.analysisResult)
      }

      // Actualizar métricas
      if (result.similarityScore !== undefined) {
        setSimilarityScore(result.similarityScore)
      }

      if (result.processingTime) {
        setProcessingTime(result.processingTime)
      }

      // Guardar resultado en el historial
      try {
        const { data: testData, error: testError } = await supabase
          .from('document_analysis_tests')
          .insert({
            user_id: session.user.id,
            document_id: uploadedDocument.id,
            model_id: selectedModel,
            model_type: modelType,
            model_name: selectedModelInfo?.name || modelName,
            parameters: modelParameters,
            original_text: result.originalText,
            extracted_text: result.extractedText,
            analysis_result: result.analysisResult,
            processing_time_ms: result.processingTime,
            similarity_score: result.similarityScore
          })
          .select()
          .single()

        if (testError) {
          console.error('Error al guardar prueba en historial:', testError)
        } else {
          // Actualizar historial local
          setTestHistory([testData, ...testHistory])
        }
      } catch (saveError) {
        console.error('Error al guardar prueba:', saveError)
      }

      // Cambiar a la pestaña de resultados
      setActiveTab('results')

    } catch (error) {
      console.error('Error al analizar documento:', error)
      setError(error instanceof Error ? error.message : 'Error inesperado al analizar el documento')
    } finally {
      setIsLoading(false)
    }
  }

  // Manejar la selección de un perfil de configuración
  const handleSelectProfile = (profile: unknown) => {
    if (profile && profile.parameters) {
      setModelParameters(profile.parameters)
    }
  }

  // Manejar el guardado de un perfil de configuración
  const handleSaveProfile = (profile: unknown) => {
    // Notificar al usuario que el perfil se ha guardado
    setError(null)
  }

  // Manejar la selección de una prueba del historial
  const handleSelectTest = async (test: unknown) => {
    try {
      setIsLoading(true)

      // Cargar detalles completos de la prueba
      const { data, error } = await supabase
        .from('document_analysis_tests')
        .select('*')
        .eq('id', test.id)
        .single()

      if (error) throw error

      // Actualizar estado con los resultados de la prueba
      if (data) {
        setOriginalText(data.original_text || '')
        setExtractedText(data.extracted_text || '')
        setAnalysisResult(data.analysis_result || null)
        setSimilarityScore(data.similarity_score)
        setProcessingTime(data.processing_time_ms)

        // Seleccionar el modelo correspondiente
        if (data.model_id) {
          setSelectedModel(data.model_id)
        }

        // Actualizar parámetros
        if (data.parameters) {
          setModelParameters(data.parameters)
        }

        // Cambiar a la pestaña de resultados
        setActiveTab('results')
      }
    } catch (error) {
      console.error('Error al cargar prueba:', error)
      setError('Error al cargar los detalles de la prueba')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Área de Pruebas de Análisis de Documentos</h2>
          <p className="text-muted-foreground">
            Prueba y compara diferentes modelos para análisis de documentos y OCR
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => window.location.reload()}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Reiniciar
          </Button>
          <Button variant="default" onClick={handleAnalyzeDocument} disabled={!uploadedDocument || !selectedModel || isLoading}>
            <Microscope className="mr-2 h-4 w-4" />
            Analizar Documento
          </Button>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="document-upload">Documento</TabsTrigger>
          <TabsTrigger value="model-selection">Modelos</TabsTrigger>
          <TabsTrigger value="results">Resultados</TabsTrigger>
          <TabsTrigger value="history">Historial</TabsTrigger>
        </TabsList>

        <TabsContent value="document-upload" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Subir Documento</CardTitle>
              <CardDescription>
                Sube un documento PDF para analizar
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FileUpload
                onUploadComplete={handleUploadComplete}
                allowedFileTypes={['.pdf']}
                maxFileSizeMB={10}
                uploadPath="test-documents"
              />

              {uploadedDocument && (
                <div className="mt-4 p-3 border rounded-md bg-muted/30">
                  <div className="flex items-center">
                    <FileText className="h-5 w-5 mr-2 text-blue-500" />
                    <div>
                      <p className="font-medium">{uploadedDocument.filename}</p>
                      <p className="text-xs text-muted-foreground">
                        {(uploadedDocument.file_size / 1024 / 1024).toFixed(2)} MB • Subido el {new Date(uploadedDocument.created_at).toLocaleString()}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" disabled={!uploadedDocument}>
                <Trash2 className="mr-2 h-4 w-4" />
                Eliminar
              </Button>
              <Button
                variant="default"
                onClick={() => setActiveTab('model-selection')}
                disabled={!uploadedDocument}
              >
                Continuar
                <ArrowDownIcon className="ml-2 h-4 w-4" />
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="model-selection" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-2">
            <div className="space-y-4">
              <ModelStatusCard
                models={models}
                selectedModel={selectedModel}
                onSelectModel={handleSelectModel}
                isLoading={isLoadingModels}
              />

              <ModelInstallationCard />
            </div>

            <div className="space-y-4">
              <ModelParametersCard
                modelId={selectedModel}
                parameters={modelParameters}
                onParametersChange={setModelParameters}
              />

              <ModelConfigProfilesCard
                modelId={selectedModel}
                onSelectProfile={handleSelectProfile}
                onSaveProfile={handleSaveProfile}
              />
            </div>
          </div>

          <div className="flex justify-between">
            <Button
              variant="outline"
              onClick={() => setActiveTab('document-upload')}
            >
              <ArrowUpIcon className="mr-2 h-4 w-4" />
              Volver
            </Button>
            <Button
              variant="default"
              onClick={handleAnalyzeDocument}
              disabled={!selectedModel || isLoading}
            >
              <Microscope className="mr-2 h-4 w-4" />
              Analizar Documento
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="results" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-2">
            <TextComparisonCard
              originalText={originalText}
              extractedText={extractedText}
              isLoading={isLoading}
              similarityScore={similarityScore}
            />
            <AnalysisResultCard
              result={analysisResult}
              isLoading={isLoading}
            />
          </div>

          {processingTime !== undefined && (
            <Alert variant="default" className="bg-muted/50 border">
              <div className="flex items-center">
                <BarChart className="h-4 w-4 mr-2" />
                <span className="font-medium">Métricas de procesamiento</span>
              </div>
              <div className="grid grid-cols-2 gap-4 mt-2">
                <div>
                  <span className="text-sm text-muted-foreground">Tiempo de procesamiento:</span>
                  <span className="ml-2 font-medium">
                    {processingTime < 1000 ? `${processingTime}ms` : `${(processingTime / 1000).toFixed(2)}s`}
                  </span>
                </div>
                <div>
                  <span className="text-sm text-muted-foreground">Similitud de texto:</span>
                  <span className="ml-2">
                    {similarityScore !== undefined ? (
                      <Badge variant={similarityScore > 0.8 ? "default" : similarityScore > 0.5 ? "secondary" : "destructive"}>
                        {(similarityScore * 100).toFixed(2)}%
                      </Badge>
                    ) : (
                      'No calculada'
                    )}
                  </span>
                </div>
              </div>
            </Alert>
          )}

          <div className="flex justify-between">
            <Button
              variant="outline"
              onClick={() => setActiveTab('model-selection')}
            >
              <ArrowUpIcon className="mr-2 h-4 w-4" />
              Volver a Modelos
            </Button>
            <Button
              variant="default"
              onClick={handleAnalyzeDocument}
              disabled={isLoading}
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Analizar de Nuevo
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <TestHistoryCard onSelectTest={handleSelectTest} />
        </TabsContent>
      </Tabs>
    </div>
  )
}
