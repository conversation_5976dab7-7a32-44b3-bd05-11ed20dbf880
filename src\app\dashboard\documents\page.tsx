import { <PERSON>ada<PERSON> } from "next"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import Link from "next/link"
import { DocumentsList } from "@/components/features/documents/documents-list"

export const metadata: Metadata = {
  title: "Documentos | AdminCore ",
  description: "Gestión de documentos",
}

export default function DocumentsPage() {
  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Documentos</h2>
        <Button asChild>
          <Link href="/dashboard/documents/new">Subir Documento</Link>
        </Button>
      </div>

      <Tabs defaultValue="list" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="list">Lista</TabsTrigger>
          <TabsTrigger value="grid">Cuadrícula</TabsTrigger>
        </TabsList>
        <TabsContent value="list" className="w-full">
          <DocumentsList />
        </TabsContent>
        <TabsContent value="grid">
          <div className="rounded-md border p-8 text-center">
            <h3 className="text-lg font-medium mb-2">Vista en cuadrícula</h3>
            <p className="text-muted-foreground mb-4">
              La vista en cuadrícula está en desarrollo y estará disponible próximamente.
            </p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
