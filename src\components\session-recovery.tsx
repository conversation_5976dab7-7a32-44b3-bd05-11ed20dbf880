'use client'

import { useEffect, useState } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useRouter } from 'next/navigation'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { RefreshCw, AlertCircle } from 'lucide-react'

/**
 * Componente para recuperar la sesión cuando se detecta que se ha perdido
 *
 * Este componente se muestra cuando se detecta que la sesión se ha perdido
 * y permite al usuario recuperarla sin perder su trabajo.
 */
export function SessionRecovery({
  onRecovered
}: {
  onRecovered: () => void
}) {
  const [isRecovering, setIsRecovering] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [countdown, setCountdown] = useState(15)
  const supabase = createClient()
  const router = useRouter()

  // Iniciar cuenta regresiva
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown(countdown - 1)
      }, 1000)
      return () => clearTimeout(timer)
    } else {
      // Intentar recuperar la sesión automáticamente cuando la cuenta regresiva llega a 0
      handleRecoverSession()
    }
  }, [countdown, handleRecoverSession])

  const handleRecoverSession = async () => {
    setIsRecovering(true)
    setError(null)

    try {
      // Verificar si hay una sesión en localStorage que podamos recuperar
      const storedSession = localStorage.getItem('supabase.auth.token')

      if (storedSession) {
        console.log('Encontrada información de sesión en localStorage, intentando recuperar...')
      } else {
        console.log('No se encontró información de sesión en localStorage')
      }

      // Intentar refrescar la sesión
      const { data, error: refreshError } = await supabase.auth.refreshSession()

      if (refreshError) {
        console.error('Error al refrescar la sesión:', refreshError)

        // Si el error es de sesión expirada, intentar redirigir al login
        if (refreshError.message.includes('expired') || refreshError.message.includes('invalid')) {
          setError('La sesión ha expirado completamente. Necesitas iniciar sesión nuevamente.')

          // Esperar 3 segundos antes de redirigir
          setTimeout(() => {
            // Guardar la URL actual para volver después del login
            const currentPath = window.location.pathname
            localStorage.setItem('auth_redirect_after_login', currentPath)

            // Redirigir al login
            router.push(`/auth/login?from=${encodeURIComponent(currentPath)}`)
          }, 3000)

          return
        }

        throw refreshError
      }

      if (data.session) {
        console.log('Sesión recuperada exitosamente')

        // Verificar que la sesión se haya recuperado correctamente
        // No pasar parámetros a getUser() para compatibilidad con todas las versiones de Supabase
        const { data: verifyData } = await supabase.auth.getUser()

        if (verifyData.user) {
          console.log('Usuario verificado después de recuperar sesión:', verifyData.user.email)

          // Notificar que la sesión se ha recuperado
          onRecovered()

          // Refrescar la página para asegurar que todos los componentes tengan la sesión actualizada
          // router.refresh() // Comentado para evitar pérdida de datos
        } else {
          throw new Error('No se pudo verificar el usuario después de recuperar la sesión')
        }
      } else {
        throw new Error('No se pudo recuperar la sesión')
      }
    } catch (err) {
      console.error('Error al recuperar la sesión:', err)
      setError(err instanceof Error ? err.message : 'Error desconocido al recuperar la sesión')
    } finally {
      setIsRecovering(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg max-w-md w-full p-6">
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Sesión perdida</AlertTitle>
          <AlertDescription>
            Se ha detectado que tu sesión se ha perdido. Esto puede ocurrir por inactividad o por un error en la aplicación.
          </AlertDescription>
        </Alert>

        <p className="text-sm text-muted-foreground mb-4">
          Intentando recuperar tu sesión automáticamente en {countdown} segundos...
        </p>

        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={() => router.push('/auth/login')}
            disabled={isRecovering}
          >
            Ir a Login
          </Button>

          <Button
            onClick={handleRecoverSession}
            disabled={isRecovering}
          >
            {isRecovering ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Recuperando...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Recuperar Ahora
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  )
}
