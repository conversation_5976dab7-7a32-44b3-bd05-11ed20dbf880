"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Loader2 } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { toast } from "@/components/ui/use-toast"
import { createClient } from "@/lib/supabase/client"
import { dataValidator } from "@/lib/services/data-validator-service"
import { errorHandler } from "@/lib/services/error-handler-service"
import { FormValidationAlert } from "@/components/ui/form-validation-alert"

// Esquema de validación para el formulario
const userFormSchema = z.object({
  email: z.string({
    required_error: "El email es obligatorio",
  }).email({
    message: "Por favor, introduce un email válido (ejemplo: <EMAIL>).",
  }).refine(
    (val) => val && val.trim() !== '',
    {
      message: "El email no puede estar vacío."
    }
  ),
  password: z
    .string()
    .min(8, {
      message: "La contraseña debe tener al menos 8 caracteres.",
    })
    .optional()
    .or(z.literal(''))
    .transform(val => val === '' ? null : val), // Convertir string vacío a null
  full_name: z.string().optional()
    .transform(val => val === '' ? null : val), // Convertir string vacío a null
  role: z.enum(["admin", "manager", "user"], {
    required_error: "El rol es obligatorio",
  }),
  status: z.enum(["active", "inactive", "suspended"], {
    required_error: "El estado es obligatorio",
  }),
  send_invite: z.boolean().default(false),
})

type UserFormValues = z.infer<typeof userFormSchema>

// Valores por defecto para un nuevo usuario
const defaultValues: Partial<UserFormValues> = {
  email: "",
  full_name: "",
  role: "user",
  status: "active",
  send_invite: false,
}

interface UserFormProps {
  initialData?: Partial<UserFormValues>
  onSubmit: (data: UserFormValues) => void
  isLoading?: boolean
  isEditing?: boolean
}

export function UserForm({
  initialData,
  onSubmit,
  isLoading = false,
  isEditing = false,
}: UserFormProps) {
  const [validationErrors, setValidationErrors] = useState<string[]>([])

  const form = useForm<UserFormValues>({
    resolver: zodResolver(userFormSchema),
    defaultValues: initialData || defaultValues,
  })

  const handleSubmit = (data: UserFormValues) => {
    // Limpiar errores previos
    setValidationErrors([])

    // Validar campos requeridos
    const requiredFields = ['email', 'role', 'status']
    const { isValid, errors } = dataValidator.validateRequiredFields(data, requiredFields)

    if (!isValid) {
      // Mostrar errores en los campos correspondientes
      Object.entries(errors).forEach(([field, message]) => {
        form.setError(field as any, {
          type: 'required',
          message
        })
      })

      // Agregar errores a la lista de validación para mostrar en el alert
      const errorMessages = Object.values(errors)
      setValidationErrors(errorMessages)
      return
    }

    // Sanitizar campos de texto para convertir strings vacíos a null
    const textFields = ['full_name']
    const sanitizedData = dataValidator.sanitizeTextFields(data, textFields)

    // Continuar con el envío del formulario
    onSubmit(sanitizedData)
  }

  return (
    <Form {...form}>
      <FormValidationAlert errors={validationErrors} />
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <div className="text-sm text-muted-foreground mb-4">
          Los campos marcados con <span className="text-red-500">*</span> son obligatorios.
        </div>
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center">
                Email
                <span className="text-red-500 ml-1">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  placeholder="<EMAIL>"
                  {...field}
                  disabled={isEditing}
                  className={form.formState.errors.email ? "border-red-500" : ""}
                  required
                />
              </FormControl>
              <FormDescription>
                El email será utilizado para iniciar sesión.
              </FormDescription>
              <FormMessage className="text-red-500" />
            </FormItem>
          )}
        />

        {!isEditing && (
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Contraseña</FormLabel>
                <FormControl>
                  <Input
                    type="password"
                    placeholder="••••••••"
                    {...field}
                    value={field.value || ""}
                  />
                </FormControl>
                <FormDescription>
                  Mínimo 8 caracteres. Deja en blanco para generar una contraseña automática.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        <FormField
          control={form.control}
          name="full_name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Nombre completo</FormLabel>
              <FormControl>
                <Input
                  placeholder="Nombre completo"
                  {...field}
                  value={field.value || ""}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <FormField
            control={form.control}
            name="role"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center">
                  Rol
                  <span className="text-red-500 ml-1">*</span>
                </FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger className={form.formState.errors.role ? "border-red-500" : ""}>
                      <SelectValue placeholder="Seleccione un rol" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="admin">Administrador</SelectItem>
                    <SelectItem value="manager">Gerente</SelectItem>
                    <SelectItem value="user">Usuario</SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>
                  El rol determina los permisos del usuario.
                </FormDescription>
                <FormMessage className="text-red-500" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center">
                  Estado
                  <span className="text-red-500 ml-1">*</span>
                </FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger className={form.formState.errors.status ? "border-red-500" : ""}>
                      <SelectValue placeholder="Seleccione un estado" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="active">Activo</SelectItem>
                    <SelectItem value="inactive">Inactivo</SelectItem>
                    <SelectItem value="suspended">Suspendido</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage className="text-red-500" />
              </FormItem>
            )}
          />
        </div>

        {!isEditing && (
          <FormField
            control={form.control}
            name="send_invite"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">Enviar invitación</FormLabel>
                  <FormDescription>
                    Enviar un email de invitación al usuario.
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        )}

        <div className="flex justify-end space-x-4">
          <Button variant="outline" type="button" onClick={() => window.history.back()}>
            Cancelar
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {isEditing ? "Actualizar usuario" : "Crear usuario"}
          </Button>
        </div>
      </form>
    </Form>
  )
}
