"use client"

import { useState, useEffect, Suspense } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { createClient } from "@/lib/supabase/client"
import { default as dynamicImport } from "next/dynamic"

// Usar dynamic import de Next.js en lugar de React.lazy
const DocumentUpload = dynamicImport(
  () => import("@/components/features/documents/document-upload").then(mod => ({ default: mod.DocumentUpload })),
  { ssr: false, loading: () => <div className="p-8 text-center">Cargando componente de subida...</div> }
)

function NewDocumentContentInner() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const projectId = searchParams.get('project')
  const workOrderId = searchParams.get('workOrder')

  const [projectName, setProjectName] = useState<string | null>(null)
  const [workOrderName, setWorkOrderName] = useState<string | null>(null)

  useEffect(() => {
    const loadRelatedData = async () => {
      const supabase = createClient()

      if (projectId) {
        const { data } = await supabase
          .from('projects')
          .select('name')
          .eq('id', projectId)
          .single()

        if (data) {
          setProjectName(data.name)
        }
      }

      if (workOrderId) {
        const { data } = await supabase
          .from('work_orders')
          .select('title')
          .eq('id', workOrderId)
          .single()

        if (data) {
          setWorkOrderName(data.title)
        }
      }
    }

    loadRelatedData()
  }, [projectId, workOrderId])

  const handleUploadComplete = (fileData: unknown) => {
    // Redirigir a la lista de documentos
    router.push('/dashboard/documents')
    router.refresh()
  }

  return (
    <div>
      {(projectName || workOrderName) && (
        <div className="flex flex-col space-y-1 mb-4">
          {projectName && (
            <p className="text-sm text-muted-foreground">
              Proyecto: <span className="font-medium text-foreground">{projectName}</span>
            </p>
          )}
          {workOrderName && (
            <p className="text-sm text-muted-foreground">
              Orden de trabajo: <span className="font-medium text-foreground">{workOrderName}</span>
            </p>
          )}
        </div>
      )}

      <DocumentUpload
        projectId={projectId || undefined}
        workOrderId={workOrderId || undefined}
        onUploadComplete={handleUploadComplete}
      />
    </div>
  )
}

export function NewDocumentContent() {
  return (
    <Suspense fallback={<div className="p-8 text-center">Cargando...</div>}>
      <NewDocumentContentInner />
    </Suspense>
  )
}
