import { NextResponse } from 'next/server'

export async function GET() {
  try {
    // Simulamos la obtención de tipos de documentos
    // En una implementación real, esto obtendría los tipos de documentos del servicio

    const documentTypes = [
      { id: "invoice", name: "<PERSON><PERSON><PERSON>" },
      { id: "contract", name: "<PERSON><PERSON><PERSON>" },
      { id: "report", name: "Informe" },
      { id: "resume", name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>" },
      { id: "legal", name: "Documento Legal" },
      { id: "technical", name: "Documento Técnico" },
    ]

    return NextResponse.json({ document_types: documentTypes })
  } catch (error: unknown) {
    console.error('Error al obtener los tipos de documentos:', error)

    const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

    return NextResponse.json(
      {
        error: 'Error al obtener los tipos de documentos',
        message: errorMessage
      },
      { status: 500 }
    )
  }
}
