import * as Sentry from "@sentry/nextjs";

Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
  tracesSampleRate: 1.0,
  environment: process.env.NODE_ENV,
  // Adjust this value in production, or use tracesSampler for greater control
  // For more information see: https://docs.sentry.io/platforms/javascript/guides/nextjs/tracing/
  // debug: process.env.NODE_ENV === 'development',

  // You can remove this option if you're not planning to use the Sentry Session Replay feature: 
  // integrations: [
  //   Sentry.replayIntegration({
  //     // Additional Replay configuration goes in here, for example:
  //     maskAllTextInputs: true,
  //     blockAllMedia: true,
  //   }),
  // ],
});
