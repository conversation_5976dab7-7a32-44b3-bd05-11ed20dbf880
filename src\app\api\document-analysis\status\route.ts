export const dynamic = 'force-dynamic'

import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { cookies } from 'next/headers';

// Función para verificar el estado de un servicio
async function checkServiceHealth(url: string) {
  try {
    const response = await fetch(`${url}/health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      cache: 'no-store',
    });

    if (response.ok) {
      const data = await response.json();
      return {
        status: 'online',
        details: data,
      };
    } else {
      return {
        status: 'error',
        message: `Error ${response.status}: ${response.statusText}`,
      };
    }
  } catch (error) {
    return {
      status: 'offline',
      message: error instanceof Error ? error.message : 'Error desconocido',
    };
  }
}

export async function GET() {
  try {
    // Verificar autenticación
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }

    // Obtener la configuración del servicio
    const configResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/document-analysis/config`);
    if (!configResponse.ok) {
      throw new Error('Error al obtener la configuración del servicio');
    }
    const config = await configResponse.json();

    // Verificar el estado de cada servicio
    const [pdfExtractorStatus, lmstudioConnectorStatus, documentAnalyzerStatus] = await Promise.all([
      checkServiceHealth(config.pdf_extractor_url),
      checkServiceHealth(config.lmstudio_connector_url),
      checkServiceHealth(config.document_analyzer_url),
    ]);

    // Determinar el estado general del servicio
    const allServicesOnline =
      pdfExtractorStatus.status === 'online' &&
      lmstudioConnectorStatus.status === 'online' &&
      documentAnalyzerStatus.status === 'online';

    // Obtener información sobre tareas en progreso
    let tasksInProgress = 0;
    try {
      const tasksResponse = await fetch(`${config.document_analyzer_url}/tasks`);
      if (tasksResponse.ok) {
        const tasksData = await tasksResponse.json();
        tasksInProgress = tasksData.in_progress || 0;
      }
    } catch (error) {
      console.error('Error al obtener información de tareas:', error);
    }

    return NextResponse.json({
      status: allServicesOnline ? 'online' : 'partial',
      pdf_extractor: pdfExtractorStatus,
      lmstudio_connector: lmstudioConnectorStatus,
      document_analyzer: documentAnalyzerStatus,
      cache: {
        status: config.cache_enabled ? 'enabled' : 'disabled',
        ttl_hours: config.cache_ttl_hours,
      },
      tasks_in_progress: tasksInProgress,
      last_checked: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error al verificar el estado del servicio:', error);
    return NextResponse.json(
      {
        error: 'Error al verificar el estado del servicio',
        message: error instanceof Error ? error.message : 'Error desconocido',
        status: 'unknown',
        last_checked: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
