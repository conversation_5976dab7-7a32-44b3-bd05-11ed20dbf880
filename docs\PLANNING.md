# PLANNING.md – AdminCore Web

## Visión General

Este documento describe la estrategia para completar la implementación del Dashboard y los módulos principales de AdminCore Web, llevando el proyecto del estado actual (aproximadamente 65% completado en general, con variaciones por módulo) a una implementación completa (100%).

## Estado Actual

Según el último informe de estado de implementación:

- **Overall Completion:** 65%

- **Dashboard Module (85% Complete)**
  - **Implementado:** Layout básico, tarjetas de métricas, gráficos básicos (Recharts), timeline de actividad, sección de tareas, diseño responsivo.
  - **Pendiente/Incompleto:** Notificaciones en tiempo real, filtros avanzados (fechas), drill-downs en gráficos, layouts personalizables, exportación.
  - **Calidad:** Variables no usadas, falta de error boundaries, datos hardcodeados.

- **Projects Module (75% Complete)**
  - **Implementado:** CRUD, vista de tabla y tarjetas, formulario de creación, páginas de detalle, gestión básica de estado.
  - **Pendiente/Incompleto:** Plantillas avanzadas, gestión de asignación de recursos, diagramas de Gantt/timeline, adjuntos de archivos, colaboración, seguimiento de presupuesto.
  - **Calidad:** Errores de autenticación SSR, manejo inconsistente de errores, tipos de TypeScript faltantes.

- **Work Orders Module (70% Complete)**
  - **Implementado:** CRUD básico, vista de tabla, Kanban con drag-and-drop, gestión de estado, asignación de usuarios.
  - **Pendiente/Incompleto:** Seguimiento de tiempo, cálculo de costos, plantillas, optimización móvil, integración con inventario.
  - **Calidad:** Problemas de estado en drag-and-drop, falta validación en transiciones de estado, lógica de asignación incompleta.

- **Documents Module (60% Complete)**
  - **Implementado:** Carga básica de archivos (drag-and-drop), integración con Supabase Storage, listado básico, indicadores de progreso.
  - **Pendiente/Incompleto:** Sistema de categorización, previsualización de archivos, búsqueda avanzada, control de versiones, compartición/permisos, operaciones masivas.
  - **Calidad:** Vista de cuadrícula es un placeholder, falta validación de tipos de archivo, sin recuperación de errores en subidas.

- **Users Module (90% Complete)**
  - **Implementado:** CRUD completo, gestión de perfiles, gestión de documentos por usuario, asignación de roles, gestión de estado de usuario, eliminación.
  - **Pendiente/Incompleto:** Sistema de permisos granulares, grupos/equipos de usuarios, personalización avanzada de roles, registro de actividad.
  - **Calidad:** Advertencias menores de TypeScript, algunos imports no usados.

- **Settings Module (50% Complete)**
  - **Implementado:** Estructura básica con pestañas, formulario de ajustes generales, UI para preferencias de notificación y apariencia, framework para ajustes de integración.
  - **Pendiente/Incompleto:** Persistencia real de ajustes, integración real con servicios externos, opciones de configuración del sistema, funcionalidad de backup/restore, ajustes avanzados de seguridad.
  - **Calidad:** Mayormente funcionalidad simulada, falta integración backend, valores de configuración hardcodeados.

- **Módulos Adicionales (En Desarrollo Temprano):**
  - AI Integration (40%)
  - GitHub Integration (30%)
  - Service Management (25%)

## Objetivos

1. **Completar el Dashboard** con todas las funcionalidades necesarias para monitorear el sistema
2. **Implementar completamente los módulos principales**:
   - Proyectos
   - Órdenes de Trabajo
   - Documentos
   - Usuarios
   - Configuración
3. **Mejorar la experiencia de usuario** con componentes interactivos y feedback visual
4. **Implementar pruebas** para garantizar la calidad del código

## Estrategia de Implementación (Roadmap Detallado)

Este roadmap proporciona un enfoque estructurado para completar AdminCore con calidad de producción.

### Fase 1: Fundación y Calidad del Código (Semanas 1-4)

#### Semana 1: Limpieza de Código y Pruebas
**Prioridad: CRÍTICA**

**Tareas:**
- [ ] Corregir todas las más de 400 advertencias de ESLint
  - Eliminar más de 150 variables no utilizadas
  - Reemplazar más de 80 tipos `any` de TypeScript con tipos adecuados
  - Corregir más de 50 advertencias de dependencias de `useEffect`
  - Eliminar más de 30 importaciones no utilizadas
- [ ] Corregir 33 pruebas fallidas
- [ ] Mejorar la cobertura de pruebas del 35% al 60%
- [ ] Configurar el mocking de pruebas adecuado para Supabase

**Entregables:**
- Código base limpio con cero advertencias de ESLint
- Todas las pruebas pasando
- Informe de cobertura de pruebas >60%

#### Semana 2: Autenticación y Seguridad
**Prioridad: CRÍTICA**

**Tareas:**
- [ ] Implementar comprobaciones exhaustivas de autenticación en todas las rutas y componentes.
- [ ] Añadir validación de entrada adecuada a todos los formularios utilizando Zod.
- [ ] Configurar políticas de Seguridad a Nivel de Fila (RLS) en Supabase para todas las tablas críticas.
- [ ] Implementar limitación de velocidad (rate limiting) para los puntos finales de la API para prevenir abusos.
- [ ] Añadir protección contra CSRF en todos los formularios que modifican el estado de la aplicación.
- [ ] Asegurar el manejo de variables de entorno y secretos exclusivamente en el backend.
- [ ] Aplicar el Principio de Menor Privilegio para las operaciones de base de datos y el acceso a la API.
- [ ] Implementar timeouts para operaciones de larga duración (ej. llamadas a IA) para evitar que el sistema se quede colgado.

**Entregables:**
- Sistema de autenticación seguro
- Formularios validados con manejo adecuado de errores
- Políticas de seguridad de base de datos implementadas

#### Semana 3: Manejo de Errores y Monitoreo
**Prioridad: ALTA**

**Tareas:**
- [ ] Implementar un boundary de error global
- [ ] Añadir registro exhaustivo de errores
- [ ] Configurar monitoreo de errores (Sentry o similar)
- [ ] Implementar estados de carga adecuados
- [ ] Añadir detección y manejo de desconexión
- [ ] Crear mecanismos de recuperación de errores

**Entregables:**
- Sistema robusto de manejo de errores
- Panel de monitoreo de errores
- Experiencia de usuario mejorada con retroalimentación adecuada

#### Semana 4: Optimización del Rendimiento
**Prioridad: ALTA**

**Tareas:**
- [ ] Implementar división de código para rutas
- [ ] Añadir carga diferida (lazy loading) para componentes
- [ ] Optimizar consultas a la base de datos
- [ ] Implementar estrategia de caché adecuada
- [ ] Reducir el tamaño del paquete (bundle)
- [ ] Añadir monitoreo de rendimiento

**Entregables:**
- Tiempos de carga de la aplicación más rápidos
- Rendimiento optimizado de la base de datos
- Configuración de monitoreo de rendimiento

### Fase 2: Finalización de Módulos Centrales (Semanas 5-12)

#### Semanas 5-6: Mejora del Dashboard
**Prioridad: ALTA**

**Tareas:**
- [ ] Implementar notificaciones en tiempo real
- [ ] Añadir filtrado avanzado por fechas
- [ ] Crear desgloses interactivos en gráficos (drill-downs)
- [ ] Añadir funcionalidad de exportación (PDF, Excel)
- [ ] Implementar diseños de dashboard personalizables
- [ ] Añadir configuración de widgets

**Entregables:**
- Dashboard completamente funcional con actualizaciones en tiempo real
- Capacidades de exportación
- Interfaz de usuario personalizable

#### Semanas 7-8: Finalización del Módulo de Proyectos
**Prioridad: ALTA**

**Tareas:**
- [ ] Implementar plantillas de proyecto
- [ ] Añadir gestión de asignación de recursos
- [ ] Crear cronogramas de proyecto/diagramas de Gantt
- [ ] Añadir sistema de adjuntos de archivos
- [ ] Implementar características de colaboración en proyectos
- [ ] Añadir seguimiento de presupuesto

**Entregables:**
- Sistema completo de gestión de proyectos
- Herramientas avanzadas de planificación de proyectos
- Características de colaboración

#### Semanas 9-10: Mejora de Órdenes de Trabajo
**Prioridad: MEDIA**

**Tareas:**
- [ ] Añadir funcionalidad de seguimiento de tiempo
- [ ] Implementar cálculo de costos de recursos
- [ ] Crear plantillas de órdenes de trabajo
- [ ] Optimizar la interfaz móvil
- [ ] Integrar con sistema de inventario
- [ ] Añadir capacidades de generación de informes

**Entregables:**
- Gestión avanzada de órdenes de trabajo
- Interfaz optimizada para móviles
- Seguimiento de costos e informes

#### Semanas 11-12: Finalización de Documentos y Usuarios
**Prioridad: MEDIA**

**Tareas:**
- [ ] Completar sistema de categorización de documentos
- [ ] Añadir funcionalidad de previsualización de archivos
- [ ] Implementar búsqueda avanzada
- [ ] Añadir control de versiones para documentos
- [ ] Crear sistema de permisos granulares
- [ ] Implementar grupos/equipos de usuarios

**Entregables:**
- Sistema completo de gestión de documentos
- Gestión avanzada de usuarios con permisos

### Fase 3: Características Avanzadas (Semanas 13-20)

#### Semanas 13-14: Ajustes y Configuración
**Prioridad: MEDIA**

**Tareas:**
- [ ] Implementar persistencia real de ajustes
- [ ] Añadir integraciones con servicios externos
- [ ] Crear opciones de configuración del sistema
- [ ] Añadir funcionalidad de copia de seguridad/restauración
- [ ] Implementar ajustes avanzados de seguridad
- [ ] Añadir registro de auditoría

**Entregables:**
- Gestión completa de ajustes
- Herramientas de administración del sistema
- Características de auditoría y cumplimiento

#### Semanas 15-16: Mejora de la Integración de IA
**Prioridad: BAJA**

**Tareas:**
- [ ] Completar la integración de IA multiproveedor
- [ ] Añadir puntuación de confianza para el análisis de IA
- [ ] Implementar plantillas de documentos
- [ ] Añadir características de extracción especializadas
- [ ] Crear gestión de modelos de IA
- [ ] Añadir monitoreo de costos

**Entregables:**
- Integración de IA lista para producción
- Análisis avanzado de documentos
- Uso rentable de la IA

#### Semanas 17-18: Gestión de Servicios
**Prioridad: BAJA**

**Tareas:**
- [ ] Completar la gestión de solicitudes de servicio
- [ ] Implementar la gestión del ciclo de vida de los contratos
- [ ] Añadir seguimiento de equipos/activos
- [ ] Crear analíticas de servicio
- [ ] Añadir portal del cliente
- [ ] Implementar integración de facturación

**Entregables:**
- Módulo completo de gestión de servicios
- Características orientadas al cliente
- Analíticas de negocio

#### Semanas 19-20: Analíticas e Informes
**Prioridad: BAJA**

**Tareas:**
- [ ] Crear panel de analíticas avanzadas
- [ ] Implementar constructor de informes personalizados
- [ ] Añadir herramientas de visualización de datos
- [ ] Crear informes automatizados
- [ ] Añadir características de inteligencia de negocio
- [ ] Implementar importación/exportación de datos

**Entregables:**
- Plataforma de analíticas avanzadas
- Herramientas de inteligencia de negocio
- Sistema de informes automatizado

### Fase 4: Preparación para Producción (Semanas 21-24)

#### Semana 21: Pruebas y Aseguramiento de Calidad
**Prioridad: CRÍTICA**

**Tareas:**
- [ ] Alcanzar más del 90% de cobertura de pruebas
- [ ] Realizar pruebas exhaustivas de integración
- [ ] Llevar a cabo pruebas de penetración de seguridad
- [ ] Pruebas de carga y validación de rendimiento
- [ ] Pruebas de aceptación del usuario (UAT)
- [ ] Pruebas de cumplimiento de accesibilidad

**Entregables:**
- Aseguramiento de calidad listo para producción
- Validación de seguridad
- Benchmarks de rendimiento

#### Semana 22: Documentación y Capacitación
**Prioridad: ALTA**

**Tareas:**
- [ ] Completar la documentación del usuario
- [ ] Crear guías para administradores
- [ ] Desarrollar documentación de la API
- [ ] Crear tutoriales en video
- [ ] Preparar materiales de capacitación
- [ ] Escribir guías de despliegue

**Entregables:**
- Documentación exhaustiva
- Recursos de capacitación
- Procedimientos de despliegue

#### Semana 23: Despliegue e Infraestructura
**Prioridad: ALTA**

**Tareas:**
- [ ] Configurar entorno de producción
- [ ] Implementar pipeline de CI/CD
- [ ] Configurar monitoreo y alertas
- [ ] Configurar copias de seguridad y recuperación ante desastres
- [ ] Implementar estrategias de escalado
- [ ] Fortalecimiento de la seguridad (Security hardening)

**Entregables:**
- Infraestructura de producción
- Pipeline de despliegue automatizado
- Sistema de monitoreo y alertas

#### Semana 24: Preparación para el Lanzamiento
**Prioridad: ALTA**

**Tareas:**
- [ ] Pruebas finales y corrección de errores
- [ ] Optimización del rendimiento
- [ ] Revisión de seguridad
- [ ] Completar lista de verificación para el lanzamiento
- [ ] Configuración del sistema de soporte
- [ ] Preparación para el Go-live

**Entregables:**
- Aplicación lista para producción
- Sistema de soporte para el lanzamiento
- Monitoreo post-lanzamiento

## Metodología de Trabajo

- **Desarrollo Basado en Componentes**: Crear componentes reutilizables para mantener la consistencia
- **Integración Continua**: Probar cada módulo a medida que se desarrolla
- **Revisión de Código**: Realizar revisiones periódicas para mantener la calidad
- **Documentación**: Documentar componentes y funcionalidades a medida que se implementan

## Configuración de Entorno de Desarrollo Local (Getting Started)

Esta sección describirá los pasos para configurar el proyecto en un entorno de desarrollo local.

1.  **Prerrequisitos:**
    *   Node.js (versión recomendada, ej: LTS)
    *   npm o yarn
    *   Git
    *   Supabase CLI (si se trabaja con migraciones locales o generación de tipos)
    *   (Cualquier otra dependencia nativa o software específico)

2.  **Clonar el Repositorio:**
    ```bash
    git clone <repository-url>
    cd admincore
    ```

3.  **Instalar Dependencias:**
    ```bash
    npm install
    # o
    # yarn install
    ```

4.  **Configurar Variables de Entorno:**
    *   Crear un archivo `.env.local` en la raíz del proyecto.
    *   Copiar el contenido de `.env.example` (si existe) o añadir las siguientes variables requeridas por Supabase y otros servicios:
        ```env
        NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
        NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
        SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
        # ...otras variables necesarias (ej: API keys para servicios de IA)
        ```

5.  **Base de Datos Supabase:**
    *   Asegurarse de que el proyecto Supabase esté configurado y accesible.
    *   Aplicar migraciones si es necesario: `npx supabase db push` (si se usa Supabase local o se gestionan migraciones manualmente).
    *   Considerar la necesidad de datos de prueba (seed data) para poblar la base de datos en desarrollo. Esto puede implicar scripts SQL o funciones de seeding.

6.  **Ejecutar la Aplicación:**
    ```bash
    npm run dev
    ```
    La aplicación debería estar disponible en `http://localhost:3000`.

7.  **Comandos Útiles Adicionales:** (Referirse a la sección "Prácticas de Desarrollo y Operaciones")

## Módulo de Integración de IA para Análisis de Documentos

Esta sección describe la arquitectura y el plan para integrar capacidades de análisis de documentos mediante IA, permitiendo la creación de proyectos a partir de ficheros como contratos, especificaciones, etc.

### Visión y Alcance

El objetivo es permitir a los gestores de proyectos crear y popular rápidamente los detalles de un proyecto subiendo documentos relevantes. La IA se encargará de extraer información clave como el alcance, cronogramas, presupuestos y entregables, pre-llenando los formularios correspondientes para su revisión.

### Arquitectura y Componentes

La arquitectura se basa en un diseño modular y agnóstico al proveedor, permitiendo intercambiar y añadir nuevos servicios de IA con un mínimo esfuerzo. Los componentes clave son:

**Componentes Principales:**
- **Componente `DocumentUploadWithAI` (Frontend):** Un componente de React que gestiona la experiencia del usuario para la carga de archivos. Sus responsabilidades incluyen:
  - **Selección de Archivo:** Permitir al usuario seleccionar un archivo local.
  - **Opción de Análisis IA:** Un interruptor (`Switch`) para habilitar o deshabilitar el análisis con IA.
  - **Selección de Proveedor:** A través de un subcomponente `AIProcessingOptions`, permite al usuario elegir qué proveedor de IA utilizar (ej. Gemini, OpenAI). La lista de proveedores se obtiene de la API (`GET /api/ai/providers`).
  - **Gestión de Estado:** Controla el estado del proceso: inactivo, subiendo, procesando, éxito o error.
  - **Feedback al Usuario:** Utiliza un subcomponente `ProcessingStatus` para mostrar el progreso de la carga y el estado del análisis de la IA en tiempo real.
- **Orquestador de Análisis (Backend):** Rutas de API que gestionan la lógica de negocio, como la selección de proveedores, el manejo de fallos y el almacenamiento de resultados.
- **Servicio de Proveedores de IA (Backend):** Una capa de abstracción que implementa el patrón de diseño *Strategy* para interactuar con los diferentes proveedores de IA.
- **Formulario de Creación de Proyectos:** Una interfaz de usuario que muestra los resultados del análisis de la IA y permite a los usuarios revisar, editar y confirmar la creación de un nuevo proyecto.

### Diseño de Proveedores de IA

Para garantizar la consistencia y la intercambiabilidad, la interacción con los proveedores de IA se estructura de la siguiente manera:

1.  **Interfaz `AIProvider` Común:** Todos los proveedores deben implementar una interfaz estandarizada que defina los métodos esenciales:

    ```typescript
    interface AIProvider {
      analyzeDocument(documentText: string): Promise<DocumentAnalysisResult>;
      validateConfig(): boolean;
      getProviderName(): string;
    }
    ```

2.  **Clase Base `BaseAIProvider`:** Una clase abstracta que contiene la lógica compartida por todos los proveedores, incluyendo:
    - **Gestión de Configuración:** Manejo de la API key y el nombre del modelo.
    - **Procesamiento de Respuestas:** Un método `processResponse` que normaliza la salida de la IA (generalmente un JSON) a la estructura de datos interna `DocumentAnalysisResult`.
    - **Generación de Prompts:** Métodos `getSystemPrompt` y `getUserPrompt` para crear los prompts estandarizados que se envían a la IA, asegurando que la tarea de extracción sea consistente independientemente del proveedor.

### Endpoints de la API

La funcionalidad de IA será expuesta a través de un conjunto de endpoints RESTful bien definidos:

```
/api/ai/
├── providers/
│   ├── route.ts                # GET, POST
│   └── [id]/
│       └── route.ts            # GET, PUT, DELETE
├── analyze-document/
│   └── route.ts                # POST
├── analysis/
│   └── [id]/
│       └── route.ts            # GET
└── create-project-from-analysis/
    └── route.ts                # POST
```

**Detalle de Endpoints:**

- **`GET /api/ai/providers`**: Obtiene la lista de proveedores de IA activos y configurados, ordenados por prioridad.
- **`POST /api/ai/providers`**: Crea un nuevo proveedor de IA o actualiza uno existente si ya se encuentra en la base de datos.
  - **Payload:** `{ provider_name, api_key, model_name, is_active, priority }`

- **`GET /api/ai/providers/[id]`**: Obtiene los detalles de un proveedor específico.
- **`PUT /api/ai/providers/[id]`**: Actualiza la configuración de un proveedor específico.
- **`DELETE /api/ai/providers/[id]`**: Elimina un proveedor de IA.

- **`POST /api/ai/analyze-document`**: Inicia el análisis de un documento.
  - **Payload:** `{ documentId, provider }`
  - **Respuesta:** Inicia un trabajo asíncrono y devuelve un ID de análisis (`analysisId`).

- **`GET /api/ai/analysis/[id]`**: Consulta el estado y los resultados de un análisis de documento utilizando su `analysisId`.

- **`POST /api/ai/create-project-from-analysis`**: Crea un nuevo proyecto en el sistema utilizando los datos extraídos de un análisis completado.
  - **Payload:** `{ analysisId, projectName, ...otros_detalles_del_proyecto }`

### Guías Específicas de Proveedores

Esta sección resume los detalles clave para la implementación de cada proveedor de IA soportado.

#### DeepSeek

- **Configuración:**
  - **Variables de Entorno:** `DEEPSEEK_API_KEY`, `DEEPSEEK_MODEL` (ej. `deepseek-coder`).
  - **Librería:** `deepseek-api` (o un cliente HTTP compatible con la API de OpenAI).
- **Modelos Recomendados:**
  - `deepseek-coder`: Especializado en análisis de documentos técnicos y código fuente.
  - `deepseek-chat`: Para análisis de propósito general, una alternativa económica.
- **Prácticas Clave:**
  - **Especialización Técnica:** Ideal para extraer información de especificaciones de ingeniería, manuales técnicos y documentos con fragmentos de código.
  - **Compatibilidad de API:** Su API es compatible con la de OpenAI, lo que permite reutilizar la lógica del cliente con mínimos cambios.
  - **Relación Costo-Beneficio:** Ofrece un buen rendimiento en tareas técnicas a un costo potencialmente menor que otros proveedores.

#### OpenAI

- **Configuración:**
  - **Variables de Entorno:** `OPENAI_API_KEY`, `OPENAI_MODEL` (ej. `gpt-4-turbo`).
  - **Librería:** `openai`.
- **Modelos Recomendados:**
  - `gpt-4-turbo`: Para análisis complejos con un contexto muy grande (128k tokens).
  - `gpt-3.5-turbo`: Una opción más rápida y económica para documentos más simples.
- **Prácticas Clave:**
  - **Modo JSON:** Utilizar el `response_format: { type: 'json_object' }` para forzar una salida JSON estructurada, lo que simplifica el procesamiento posterior.
  - **Prompts de Sistema:** Aprovechar los `system prompts` para dar instrucciones detalladas y definir el rol del asistente, mejorando la precisión de la extracción.
  - **Gestión de Costos:** Monitorizar activamente el uso de tokens y cachear resultados para evitar llamadas repetidas y costosas a la API.

#### Google Gemini

- **Configuración:**
  - **Variables de Entorno:** `GEMINI_API_KEY`, `GEMINI_MODEL` (ej. `gemini-pro`).
  - **Librería:** `@google/generative-ai`.
- **Modelos Recomendados:**
  - `gemini-pro`: Para análisis de texto general (límite de 32k tokens).
  - `gemini-pro-vision`: Para documentos que incluyen imágenes.
- **Prácticas Clave:**
  - **Optimización de Tokens:** Enviar solo el texto relevante y usar prompts concisos. Considerar la fragmentación (`chunking`) para documentos grandes.
  - **Manejo de Errores:** Implementar lógica para reintentos con backoff exponencial y gestionar errores comunes como `API key inválida` y `cuota excedida`.
  - **Formato de Salida:** Requerir siempre una salida en formato JSON en los prompts para asegurar una respuesta estructurada y predecible.

### Cambios en la Base de Datos

Se proponen las siguientes adiciones al esquema de Supabase:

**Nuevas Tablas:**
- **`ai_provider_configs`**: Para almacenar la configuración de cada proveedor de IA (API keys encriptadas, modelos, prioridad).
  - `id`, `provider_name`, `api_key`, `model_name`, `is_active`, `priority`, `created_at`, `updated_at`
- **`ai_document_analyses`**: Para registrar cada análisis realizado.
  - `id`, `document_id`, `provider`, `analysis_data` (JSONB), `status`, `error_message`, `created_at`

**Tablas Modificadas:**
- **`projects`**:
  - `ai_generated` (BOOLEAN): Para marcar proyectos generados por IA.
  - `source_document_id` (UUID): Para vincular al documento que originó el proyecto.

### Endpoints de API

Se crearán los siguientes endpoints bajo el prefijo `/api/ai/`:

- `POST /analyze-document`: Envía un documento para ser analizado.
- `GET /analysis/:id`: Obtiene los resultados de un análisis.
- `POST /create-project-from-analysis`: Crea un proyecto a partir de un análisis.
- `GET /providers`: Lista los proveedores de IA configurados.
- `POST /providers`: Añade o actualiza la configuración de un proveedor.
- `DELETE /providers/:id`: Elimina la configuración de un proveedor.

### Fases de Implementación

1.  **Fase 1 (Fundación):** Creación del framework de integración, configuración de la base de datos y extracción básica de texto.
2.  **Fase 2 (Funcionalidad Central):** Implementación del análisis con Gemini y la generación de proyectos. UI para revisión.
3.  **Fase 3 (Expansión de Proveedores):** Soporte para OpenAI y DeepSeek. Mecanismo de fallback.
4.  **Fase 4 (Funciones Avanzadas):** Puntuaciones de confianza, análisis de múltiples documentos y plantillas.

### Reglas y Buenas Prácticas de Integración

- **Interfaz Común:** Crear una interfaz agnóstica (`interface AIProvider`) que todos los proveedores de IA deban implementar para garantizar la consistencia.
- **Manejo de Errores Específicos:** Capturar y traducir los errores específicos de cada proveedor a un conjunto común de errores de la aplicación.
- **Mecanismos de Fallback:** Implementar lógica para intentar con un proveedor alternativo si el primario falla.
- **Monitorización y Logging:** Registrar el uso de cada proveedor para rastrear costos, rendimiento y cuotas de API.
- **Caché de Resultados:** Usar un sistema de caché para los análisis de documentos para reducir costos y mejorar los tiempos de respuesta en solicitudes repetidas.

### Consideraciones de Seguridad

- **Gestión de API Keys:** Las claves se almacenarán encriptadas en la base de datos y solo serán accesibles desde el backend.
- **Seguridad de Documentos:** El procesamiento se realizará en el servidor, respetando los controles de acceso existentes.
- **Permisos de Usuario:** Solo usuarios con permisos de creación de proyectos podrán usar esta funcionalidad. Se auditará toda la generación de contenido.

## Tecnologías y Herramientas

- **Frontend**: Next.js, React, TypeScript, Tailwind CSS, Shadcn/UI
- **Backend**: Supabase (Auth, Database, Storage)
- **Estado**: Zustand para estado global, React Query para datos
- **Testing**: Vitest, Testing Library
- **Integración de IA**: Google Gemini, OpenAI, DeepSeek (vía APIs/SDKs)
- **CI/CD**: GitHub Actions

## Requisitos de Recursos (del Roadmap de Mejoras)

### Equipo de Desarrollo
- 2-3 Desarrolladores Full-stack
- 1 Ingeniero DevOps
- 1 Ingeniero QA
- 1 Diseñador UI/UX (tiempo parcial)

### Infraestructura
- Despliegue de producción en Vercel
- Plan Supabase Pro
- Servicios de monitoreo y logging
- Herramientas de escaneo de seguridad
- Infraestructura de pruebas

## Consideraciones Finales

Este plan está diseñado para ser flexible y adaptarse a cambios en los requisitos o prioridades. Las estimaciones de tiempo son aproximadas y pueden ajustarse según sea necesario.

## Seguridad y Supabase Setup

- Variables de entorno requeridas (`.env.local`)
- Scripts RLS y políticas de seguridad
- Procedimiento de backups y auditoría
- Documentación o scripts para datos de prueba (seed data) para el entorno de desarrollo.

## CI/CD y Pipeline

- GitHub Actions: lint, type-check, test, build, deploy
- Vercel para frontend, Supabase migraciones automáticas
- Entornos previos: `dev`, `staging`, `prod`

## Métricas de Calidad y Éxito

- 0 ESLint warnings, 0 TypeScript errors
- 90% cobertura de tests
- <3 s carga inicial, <500 ms APIs
- Sentry monitoreo de errores

### Métricas Técnicas (del Roadmap de Mejoras)
- **Calidad de Código:** 0 advertencias ESLint, 95%+ cobertura TypeScript
- **Pruebas:** 90%+ cobertura de pruebas, 0 pruebas fallidas
- **Rendimiento:** <2s carga de página, <100ms respuesta de API
- **Seguridad:** 0 vulnerabilidades críticas, implementación completa de RLS

### Métricas de Negocio (del Roadmap de Mejoras)
- **Finalización de Características:** 95% de las características documentadas implementadas
- **Experiencia de Usuario:** <5% tasa de error, >95% tiempo de actividad
- **Documentación:** 100% cobertura de características, guías de usuario completas
- **Despliegue:** CI/CD automatizado, despliegues sin tiempo de inactividad

## Estrategia de Pruebas y Aseguramiento de Calidad (QA)

Esta sección detalla el enfoque para asegurar la calidad y fiabilidad de AdminCore a través de una estrategia de pruebas exhaustiva.

### Objetivos y Estado Actual de las Pruebas
- **Cobertura Actual:** Aproximadamente 35%.
- **Cobertura Objetivo:** 90%+
- **Estado Actual:** Existen pruebas fallidas (aproximadamente 33 de 114) que se abordarán como parte de la Fase 1 del roadmap.
- **Problemas Principales Identificados:** Mocking de Supabase, configuración de pruebas de componentes React, manejo del contexto de autenticación, operaciones asíncronas y errores de TypeScript en archivos de prueba.

### Framework y Herramientas de Pruebas
- **Stack Principal:**
  - **Jest:** Ejecutor de pruebas.
  - **React Testing Library:** Para pruebas de componentes React.
  - **@testing-library/jest-dom:** Matchers para el DOM.
  - **MSW (Mock Service Worker):** Para mocking de APIs.
  - **@testing-library/user-event:** Para simular interacciones del usuario.
- **Configuración de Jest (jest.config.js):**
  ```javascript
  // jest.config.js (resumen)
  module.exports = {
    testEnvironment: 'jsdom',
    setupFilesAfterEnv: ['<rootDir>/jest.setup.js'], // Configuración adicional para mocks globales, etc.
    moduleNameMapping: { '^@/(.*)$': '<rootDir>/src/$1' }, // Alias de rutas
    collectCoverageFrom: ['src/**/*.{js,jsx,ts,tsx}', /* exclusiones */ ],
    coverageThreshold: { global: { branches: 80, functions: 80, lines: 80, statements: 80 } },
  };
  ```

### Tipos de Pruebas y Enfoque
- **Pruebas Unitarias:** Enfocadas en componentes individuales, helpers y funciones de utilidad. Se utilizará React Testing Library para componentes.
- **Pruebas de Integración:** Para verificar la interacción entre varios componentes o servicios (ej. servicios que interactúan con el mock de Supabase).
- **Pruebas End-to-End (E2E):** Se utilizará Playwright para simular flujos de usuario completos a través de la aplicación.
  *Ejemplo de flujo E2E:* Creación de un nuevo proyecto desde el dashboard hasta la confirmación.
- **Pruebas de Rendimiento:** Pruebas de carga básicas con Jest para asegurar que los componentes clave renderizan dentro de un presupuesto de tiempo aceptable.

### Estrategia por Módulo (General)
- **Dashboard:** Pruebas de visualización de datos, estados de carga, renderizado de gráficos, interacciones.
- **Módulos CRUD (Proyectos, Órdenes de Trabajo, Documentos, Usuarios):** Pruebas exhaustivas de operaciones CRUD, validación de formularios, manejo de estados, asignaciones y características específicas del módulo.
- **Autenticación y Autorización:** Pruebas de flujos de inicio/cierre de sesión, protección de rutas, aplicación de RLS (a nivel de API mockeada).

### Utilitarios, Mocks y Gestión de Datos de Prueba
- **Mock del Cliente Supabase:** Un mock (`src/test-utils/supabase-mock.ts`) para simular las interacciones con Supabase (BD, Auth, Storage) sin realizar llamadas reales.
- **Mock del Contexto de Autenticación:** Un `MockAuthProvider` (`src/test-utils/auth-mock.tsx`) para envolver componentes que dependen del estado de autenticación.
- **Función de Renderizado Personalizada:** Una función `customRender` (`src/test-utils/render.tsx`) que incluye proveedores comunes (como el `MockAuthProvider`) para simplificar la escritura de pruebas de componentes.
- **Gestión de Datos de Prueba:** Uso de fixtures y fábricas (`src/test-utils/factories.ts`) para generar datos de prueba consistentes y reutilizables (ej. `createMockProject`, `createMockUser`).

### Integración Continua (CI) para Pruebas
Se utiliza GitHub Actions para ejecutar la suite de pruebas automáticamente en cada `push` y `pull_request`.
El workflow (`.github/workflows/test.yml`) incluye los siguientes pasos:
1.  Checkout del código.
2.  Configuración de Node.js.
3.  Instalación de dependencias (`npm ci`).
4.  Ejecución de linters (`npm run lint`).
5.  Verificación de tipos TypeScript (`npm run type-check`).
6.  Ejecución de pruebas con cobertura (`npm run test:ci`).
7.  Construcción del proyecto (`npm run build`) para verificar que no hay errores de build.

### Checklist de Pruebas y Mantenimiento
- **Antes de Cada Release:**
  - [ ] Todas las pruebas unitarias, de integración y E2E deben pasar.
  - [ ] Cobertura de pruebas por encima del umbral objetivo (ej. 80-90%).
  - [ ] Sin errores de TypeScript ni advertencias de ESLint críticas.
  - [ ] Pruebas de rendimiento dentro del presupuesto.
- **Mantenimiento Continuo:**
  - [ ] Actualizar pruebas cuando las características cambian.
  - [ ] Eliminar pruebas obsoletas.
  - [ ] Refactorizar utilitarios de prueba según sea necesario.
  - [ ] Revisar y mejorar la cobertura de pruebas regularmente.

### Directrices y Reglas de Pruebas

- **Creación de Pruebas:** Siempre se deben crear pruebas unitarias (Jest) para nuevas funcionalidades (funciones, componentes, hooks, rutas de API).
- **Ubicación:** Las pruebas deben residir en una carpeta `__tests__` adyacente al archivo que se está probando.
- **Cobertura Mínima por Característica:**
  - 1 prueba para el caso de uso esperado (happy path).
  - 1 prueba para un caso límite (edge case).
  - 1 prueba para un caso de fallo esperado.
- **Actualización de Pruebas:** Al actualizar cualquier lógica, se deben revisar y actualizar las pruebas existentes si es necesario.
- **Mocks:** Se deben mockear las APIs de servicios externos (como Supabase o los proveedores de IA) para evitar llamadas reales, costos y dependencias externas en las pruebas unitarias y de integración.

Esta estrategia integral de pruebas es fundamental para alcanzar los objetivos de calidad y fiabilidad de AdminCore.

## Prácticas de Desarrollo y Operaciones

### Estructura del Código y Modularidad

- **Límite de Longitud de Archivos:** Ningún archivo debe superar las 500 líneas de código. Si se acerca a este límite, debe ser refactorizado y dividido en módulos o helpers más pequeños y cohesivos.
- **Organización Modular:** El código debe agruparse por funcionalidad o responsabilidad en carpetas claramente definidas. Por ejemplo:
  - `src/lib/ai-providers/`: Implementaciones específicas de proveedores de IA.
  - `src/components/features/projects/`: Componentes de UI específicos para el módulo de Proyectos.
  - `src/app/api/ai/`: Rutas de API para la funcionalidad de IA.
- **Importaciones Claras:** Utilizar importaciones consistentes, prefiriendo los alias de ruta (`@/`) para evitar rutas relativas complejas (`../../...`).

### Estilo y Convenciones de Código

- **TypeScript:** Todo el código nuevo debe usar TypeScript.
- **Estilo Existente:** Seguir las convenciones del proyecto: componentes funcionales con hooks, uso de la librería de componentes Shadcn/UI y la estructura de proyecto existente.
- **Nomenclatura:** Utilizar nombres de variables y funciones significativos que indiquen claramente su propósito.

### Documentación y Explicabilidad

- **JSDoc:** Todas las funciones, componentes y tipos exportados deben estar documentados con comentarios JSDoc, explicando su propósito, parámetros y valor de retorno.
- **Comentarios de Lógica Compleja:** Al escribir lógica no obvia, añadir un comentario inline `// Razón:` que explique el *porqué* de la implementación, no solo el *qué*.
- **Actualización de Documentación:** La documentación (`PLANNING.MD`, `TASK.MD`, READMEs) debe actualizarse cuando se añadan nuevas características o se realicen cambios significativos.

### Consideraciones de Rendimiento

- **Procesamiento en Segundo Plano:** Las operaciones largas y costosas (como el análisis de documentos por IA) deben ejecutarse en segundo plano para no bloquear el hilo principal y afectar la experiencia de usuario.
- **Respuestas en Streaming:** Utilizar respuestas en streaming cuando sea apropiado para mejorar la percepción de velocidad por parte del usuario.
- **Sistema de Colas:** Considerar la implementación de un sistema de colas para gestionar múltiples solicitudes de procesamiento de documentos de manera ordenada y controlada.

### Comandos y Scripts Útiles

#### Daily Development Workflow
```bash
# Start development
npm run dev

# Run linting and fix issues
npm run lint -- --fix

# Run type checking
npm run type-check

# Run tests
npm test

# Check test coverage
npm run test:coverage

# Build for production
npm run build
```

#### Code Quality Checks
```bash
# Install additional tools (if not already present)
# npm install --save-dev @typescript-eslint/eslint-plugin eslint-plugin-react-hooks prettier

# Run comprehensive checks
npm run lint
npm run type-check
npm run test
npm run build
```

#### Database Management (Supabase)
```bash
# Supabase CLI commands
npx supabase start
npx supabase db reset
npx supabase gen types typescript --local > src/types/supabase.ts
```

### Plantilla de Daily Standup

**What was completed yesterday:**
- [ ] List specific tasks completed
- [ ] Note any blockers resolved

**What will be worked on today:**
- [ ] List specific tasks planned
- [ ] Identify potential blockers

**Blockers or concerns:**
- [ ] Technical issues
- [ ] Resource needs
- [ ] Timeline concerns

## Gestión de Riesgos

### Áreas de Alto Riesgo
1. **Database migrations** - Test thoroughly in development
2. **Authentication changes** - Maintain backward compatibility
3. **File upload changes** - Ensure no data loss
4. **Test fixes** - Don't break existing functionality

### Planes de Rollback
- Git branches for each major change
- Database backup before schema changes
- Feature flags for new functionality
- Staged deployment process
