import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server-client';
import { cookies } from 'next/headers';

// Obtener la configuración del servicio de análisis de documentos
async function getServiceConfig() {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/document-analysis/config`);
    if (!response.ok) {
      throw new Error('Error al obtener la configuración del servicio');
    }
    return await response.json();
  } catch (error) {
    console.error('Error al obtener la configuración:', error);
    return {
      pdf_extractor_url: process.env.NEXT_PUBLIC_PDF_EXTRACTOR_URL || 'http://localhost:8001',
      lmstudio_connector_url: process.env.NEXT_PUBLIC_LMSTUDIO_CONNECTOR_URL || 'http://localhost:8003',
      document_analyzer_url: process.env.NEXT_PUBLIC_DOCUMENT_ANALYSIS_URL || 'http://localhost:8002',
    };
  }
}

// Función para verificar la autenticación
async function verifyAuth() {
  const cookieStore = cookies();
  const supabase = createClient(cookieStore);
  const { data: { session } } = await supabase.auth.getSession();
  return session;
}

// Función para manejar las solicitudes al servicio de análisis de documentos
export async function POST(request: NextRequest) {
  try {
    // Verificar autenticación
    const session = await verifyAuth();
    if (!session) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }

    // Obtener la configuración del servicio
    const config = await getServiceConfig();

    // Obtener la URL del servicio de análisis de documentos
    const serviceUrl = config.document_analyzer_url;

    // Obtener el path de la solicitud
    const url = new URL(request.url);
    const path = url.pathname.replace('/api/document-analysis/proxy', '');

    // Construir la URL completa
    const fullUrl = `${serviceUrl}${path || '/analyze'}`;

    // Clonar los headers de la solicitud original
    const headers = new Headers(request.headers);

    // Agregar headers de autenticación si es necesario
    headers.set('X-User-ID', session.user.id);

    // Crear una nueva solicitud con los mismos datos pero con la URL y headers modificados
    const newRequest = new Request(fullUrl, {
      method: request.method,
      headers: headers,
      body: request.body,
      redirect: 'follow',
    });

    // Enviar la solicitud al servicio de análisis de documentos
    const response = await fetch(newRequest);

    // Devolver la respuesta del servicio
    const data = await response.json();

    return NextResponse.json(data, { status: response.status });

  } catch (error) {
    console.error('Error en el proxy:', error);
    return NextResponse.json(
      { error: 'Error al procesar la solicitud' },
      { status: 500 }
    );
  }
}

// Función para manejar las solicitudes GET
export async function GET(request: NextRequest) {
  try {
    // Verificar autenticación
    const session = await verifyAuth();
    if (!session) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }

    // Obtener la configuración del servicio
    const config = await getServiceConfig();

    // Obtener la URL del servicio de análisis de documentos
    const serviceUrl = config.document_analyzer_url;

    // Obtener el path de la solicitud
    const url = new URL(request.url);
    const path = url.pathname.replace('/api/document-analysis/proxy', '');

    // Construir la URL completa
    const fullUrl = `${serviceUrl}${path || '/health'}`;

    // Enviar la solicitud al servicio de análisis de documentos
    const response = await fetch(fullUrl);

    // Devolver la respuesta del servicio
    const data = await response.json();

    return NextResponse.json(data, { status: response.status });

  } catch (error) {
    console.error('Error en el proxy:', error);
    return NextResponse.json(
      { error: 'Error al procesar la solicitud' },
      { status: 500 }
    );
  }
}
