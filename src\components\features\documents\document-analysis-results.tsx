"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Check, FileText, Loader2, AlertCircle } from "lucide-react"
import { documentAnalysisClient } from "@/lib/document-analysis/document-analysis-client"
import { toast } from "@/hooks/use-toast"
import { Skeleton } from "@/components/ui/skeleton"
import { Badge } from "@/components/ui/badge"
import { useRouter } from "next/navigation"
import { DocumentAnalysisError } from "./document-analysis-error"

interface DocumentAnalysisResultsProps {
  analysisId: string
  onCreateProject?: (projectData: unknown) => void
}

export function DocumentAnalysisResults({
  analysisId,
  onCreateProject,
}: DocumentAnalysisResultsProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [analysisResult, setAnalysisResult] = useState<any | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState("summary")
  const [isCreatingProject, setIsCreatingProject] = useState(false)

  // Función para extraer datos anidados
  const getExtractedData = (result: unknown) => {
    if (!result) return null;
    // Manejar la estructura anidada si existe
    return result.analysis_data || result;
  }

  useEffect(() => {
    loadAnalysisResult()

    // Consultar periódicamente si el análisis está en proceso
    const intervalId = setInterval(() => {
      if (analysisResult?.status === 'processing') {
        loadAnalysisResult()
      }
    }, 5000)

    return () => clearInterval(intervalId)
  }, [analysisId])

  const loadAnalysisResult = async () => {
    try {
      setIsLoading(true)
      const result = await documentAnalysisClient.getAnalysisResult(analysisId)
      setAnalysisResult(result)

      if (result.status === 'error') {
        setError(result.error || "Error desconocido en el análisis")
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

      setError(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCreateProject = async () => {
    if (!analysisResult) return

    try {
      setIsCreatingProject(true)

      // Asegurarse de que los datos del análisis estén en el formato correcto
      const processedResult = {
        ...analysisResult,
        // Asegurarse de que los datos extraídos estén disponibles en el nivel superior
        ...getExtractedData(analysisResult)
      }

      const project = await documentAnalysisClient.createProjectFromAnalysis(processedResult)

      toast({
        title: "Proyecto creado",
        description: "Se ha creado un nuevo proyecto a partir del análisis del documento.",
        variant: "default",
      })

      if (onCreateProject) {
        onCreateProject(project)
      } else {
        // Redirigir al proyecto creado
        window.location.href = `/dashboard/projects/${project.id}`
      }
    } catch (error: unknown) {
      toast({
        title: "Error al crear el proyecto",
        description: error instanceof Error ? error.message : 'Error desconocido',
        variant: "destructive",
      })
    } finally {
      setIsCreatingProject(false)
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Análisis de Documento</CardTitle>
          <CardDescription>Cargando resultados del análisis...</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Skeleton className="h-[200px] w-full" />
          <div className="space-y-2">
            <Skeleton className="h-4 w-[250px]" />
            <Skeleton className="h-4 w-[200px]" />
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Análisis de Documento</CardTitle>
          <CardDescription>Error en el análisis</CardDescription>
        </CardHeader>
        <CardContent>
          <DocumentAnalysisError
            error={error}
            onRetry={() => loadAnalysisResult()}
            onSelectDifferentDocument={() => router.push('/dashboard/documents')}
          />
        </CardContent>
      </Card>
    )
  }

  if (analysisResult?.status === 'processing') {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Análisis de Documento</CardTitle>
          <CardDescription>El documento está siendo analizado</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <Loader2 className="h-4 w-4 animate-spin" />
            <AlertTitle>Procesando</AlertTitle>
            <AlertDescription>
              El documento está siendo analizado. Este proceso puede tardar unos minutos.
            </AlertDescription>
          </Alert>
          <Progress value={30} className="h-2" />
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Análisis de Documento</CardTitle>
        <CardDescription>
          Resultados del análisis de {analysisResult.filename}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Alert variant="default">
          <Check className="h-4 w-4" />
          <AlertTitle>Análisis completado</AlertTitle>
          <AlertDescription>
            El documento ha sido analizado correctamente con una confianza del {Math.round((getExtractedData(analysisResult)?.confidence_score || analysisResult.project_info?.confidence_score || 0) * 100)}%.
          </AlertDescription>
        </Alert>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="summary">Resumen</TabsTrigger>
            <TabsTrigger value="details">Detalles</TabsTrigger>
            <TabsTrigger value="raw">Datos Completos</TabsTrigger>
          </TabsList>

          <TabsContent value="summary" className="space-y-4">
            <div>
              <h3 className="text-lg font-medium">Información del Proyecto</h3>
              <div className="grid grid-cols-2 gap-4 mt-2">
                <div>
                  <p className="text-sm font-medium">Nombre</p>
                  <p className="text-sm text-muted-foreground">
                    {getExtractedData(analysisResult)?.project_name ||
                     analysisResult.project_info?.name ||
                     'No disponible'}
                  </p>
                </div>

                {(getExtractedData(analysisResult)?.start_date || analysisResult.project_info?.start_date) && (
                  <div>
                    <p className="text-sm font-medium">Fecha de inicio</p>
                    <p className="text-sm text-muted-foreground">
                      {getExtractedData(analysisResult)?.start_date ||
                       analysisResult.project_info?.start_date}
                    </p>
                  </div>
                )}

                {(getExtractedData(analysisResult)?.end_date || analysisResult.project_info?.end_date) && (
                  <div>
                    <p className="text-sm font-medium">Fecha de finalización</p>
                    <p className="text-sm text-muted-foreground">
                      {getExtractedData(analysisResult)?.end_date ||
                       analysisResult.project_info?.end_date}
                    </p>
                  </div>
                )}

                {(getExtractedData(analysisResult)?.budget || analysisResult.project_info?.budget) && (
                  <div>
                    <p className="text-sm font-medium">Presupuesto</p>
                    <p className="text-sm text-muted-foreground">
                      {getExtractedData(analysisResult)?.budget ||
                       analysisResult.project_info?.budget}
                    </p>
                  </div>
                )}

                {(getExtractedData(analysisResult)?.scope || analysisResult.project_info?.scope) && (
                  <div className="col-span-2">
                    <p className="text-sm font-medium">Alcance</p>
                    <p className="text-sm text-muted-foreground">
                      {getExtractedData(analysisResult)?.scope ||
                       analysisResult.project_info?.scope}
                    </p>
                  </div>
                )}
              </div>
            </div>

            {((getExtractedData(analysisResult)?.deliverables && getExtractedData(analysisResult)?.deliverables.length > 0) ||
              (analysisResult.project_info?.deliverables && analysisResult.project_info?.deliverables.length > 0)) && (
              <div>
                <h3 className="text-lg font-medium">Entregables</h3>
                <ul className="list-disc list-inside mt-2">
                  {(getExtractedData(analysisResult)?.deliverables || analysisResult.project_info?.deliverables || []).map((deliverable: string, index: number) => (
                    <li key={index} className="text-sm text-muted-foreground">{deliverable}</li>
                  ))}
                </ul>
              </div>
            )}

            <div>
              <h3 className="text-lg font-medium">Confianza del Análisis</h3>
              <div className="flex items-center mt-2">
                <Progress
                  value={(getExtractedData(analysisResult)?.confidence_score ||
                          analysisResult.project_info?.confidence_score || 0) * 100}
                  className="h-2 flex-1"
                />
                <span className="ml-2 text-sm text-muted-foreground">
                  {Math.round((getExtractedData(analysisResult)?.confidence_score ||
                               analysisResult.project_info?.confidence_score || 0) * 100)}%
                </span>
              </div>
            </div>

            <div className="flex justify-end space-x-2">
              <Button onClick={handleCreateProject} disabled={isCreatingProject}>
                {isCreatingProject ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creando...
                  </>
                ) : (
                  <>
                    <FileText className="mr-2 h-4 w-4" />
                    Crear Proyecto
                  </>
                )}
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="details" className="space-y-4">
            <div>
              <h3 className="text-lg font-medium">Detalles del Análisis</h3>
              <div className="grid grid-cols-2 gap-4 mt-2">
                <div>
                  <p className="text-sm font-medium">ID del Análisis</p>
                  <p className="text-sm text-muted-foreground">{analysisResult.analysis_id}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">ID del Documento</p>
                  <p className="text-sm text-muted-foreground">{analysisResult.document_id}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Modelo Utilizado</p>
                  <p className="text-sm text-muted-foreground">{analysisResult.model}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Estado</p>
                  <Badge variant="outline" className={analysisResult.status === 'completed' ? 'bg-green-500' : 'bg-yellow-500'}>
                    {analysisResult.status === 'completed' ? 'Completado' : 'En Proceso'}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm font-medium">Es Documento de Proyecto</p>
                  <Badge variant={(getExtractedData(analysisResult)?.is_project_document || analysisResult.analysis?.is_project_document) ? 'default' : 'destructive'}>
                    {(getExtractedData(analysisResult)?.is_project_document || analysisResult.analysis?.is_project_document) ? 'Sí' : 'No'}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm font-medium">Información Suficiente</p>
                  <Badge variant={(getExtractedData(analysisResult)?.has_sufficient_info || analysisResult.analysis?.has_sufficient_info) ? 'default' : 'destructive'}>
                    {(getExtractedData(analysisResult)?.has_sufficient_info || analysisResult.analysis?.has_sufficient_info) ? 'Sí' : 'No'}
                  </Badge>
                </div>
              </div>
            </div>

            {(getExtractedData(analysisResult)?.reason || analysisResult.analysis?.reason) && (
              <div>
                <h3 className="text-lg font-medium">Razón de la Evaluación</h3>
                <p className="text-sm text-muted-foreground mt-2">{getExtractedData(analysisResult)?.reason || analysisResult.analysis?.reason}</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="raw">
            <div className="relative">
              <pre className="p-4 rounded-md bg-muted overflow-auto max-h-[400px] text-xs">
                {JSON.stringify(analysisResult, null, 2)}
              </pre>
              <Button
                variant="outline"
                size="sm"
                className="absolute top-2 right-2"
                onClick={() => {
                  navigator.clipboard.writeText(JSON.stringify(analysisResult, null, 2))
                  toast({
                    title: "Copiado",
                    description: "Los datos han sido copiados al portapapeles.",
                    variant: "default",
                  })
                }}
              >
                Copiar
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
