/**
 * @file Servicio de notificaciones
 * @description Proporciona funciones para gestionar notificaciones de usuario
 */

import { createClient } from '@/lib/supabase/client';

export interface Notification {
  id: string;
  title: string;
  description?: string;
  type?: string;
  read: boolean;
  link?: string;
  created_at: Date;
  updated_at: Date;
}

/**
 * Obtiene las notificaciones del usuario actual
 * @param limit Número máximo de notificaciones a obtener
 * @param offset Número de notificaciones a saltar (para paginación)
 * @param unreadOnly Si es true, solo devuelve notificaciones no leídas
 * @returns Array de notificaciones
 */
export async function getUserNotifications(
  limit: number = 10,
  offset: number = 0,
  unreadOnly: boolean = false
): Promise<Notification[]> {
  try {
    const supabase = createClient();

    // Verificar la sesión actual
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
    if (sessionError || !sessionData?.session) {
      console.error('Error al obtener la sesión:', sessionError);
      return [];
    }

    const userId = sessionData.session.user.id;

    // Intentar usar la función RPC optimizada
    try {
      console.log('Intentando usar la función RPC get_user_notifications...');
      const { data: notificationsData, error: notificationsError } = await supabase.rpc(
        'get_user_notifications',
        {
          p_user_id: userId,
          p_limit: limit,
          p_offset: offset,
          p_unread_only: unreadOnly
        }
      );

      if (!notificationsError && notificationsData) {
        console.log(`Obtenidas ${notificationsData.length} notificaciones con función RPC`);

        // Convertir las fechas de string a Date
        return notificationsData.map((notification: unknown) => ({
          ...notification,
          created_at: new Date(notification.created_at),
          updated_at: new Date(notification.updated_at)
        }));
      } else {
        console.error('Error al obtener notificaciones con función RPC:', notificationsError);
        console.log('Usando método alternativo...');
      }
    } catch (rpcError) {
      console.error('Excepción al usar función RPC para notificaciones:', rpcError);
      console.log('Usando método alternativo...');
    }

    // Si la función RPC falló, usar el método tradicional
    let query = supabase
      .from('notifications')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit)
      .range(offset, offset + limit - 1);

    if (unreadOnly) {
      query = query.eq('read', false);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error al obtener notificaciones:', error);
      return [];
    }

    if (!data || data.length === 0) {
      return [];
    }

    // Convertir las fechas de string a Date
    return data.map(notification => ({
      ...notification,
      created_at: new Date(notification.created_at),
      updated_at: new Date(notification.updated_at)
    }));
  } catch (error) {
    console.error('Error inesperado al obtener notificaciones:', error);
    return [];
  }
}

/**
 * Marca una notificación como leída
 * @param notificationId ID de la notificación a marcar
 * @returns true si se marcó correctamente, false en caso contrario
 */
export async function markNotificationAsRead(notificationId: string): Promise<boolean> {
  try {
    const supabase = createClient();

    // Verificar la sesión actual
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
    if (sessionError || !sessionData?.session) {
      console.error('Error al obtener la sesión:', sessionError);
      return false;
    }

    const userId = sessionData.session.user.id;

    // Intentar usar la función RPC optimizada
    try {
      console.log('Intentando usar la función RPC mark_notification_read...');
      const { data: result, error: markError } = await supabase.rpc(
        'mark_notification_read',
        {
          p_notification_id: notificationId,
          p_user_id: userId
        }
      );

      if (!markError) {
        console.log('Notificación marcada como leída con función RPC:', result);
        return !!result;
      } else {
        console.error('Error al marcar notificación con función RPC:', markError);
        console.log('Usando método alternativo...');
      }
    } catch (rpcError) {
      console.error('Excepción al usar función RPC para marcar notificación:', rpcError);
      console.log('Usando método alternativo...');
    }

    // Si la función RPC falló, usar el método tradicional
    const { error } = await supabase
      .from('notifications')
      .update({ read: true, updated_at: new Date().toISOString() })
      .eq('id', notificationId)
      .eq('user_id', userId);

    if (error) {
      console.error('Error al marcar notificación como leída:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error inesperado al marcar notificación:', error);
    return false;
  }
}

/**
 * Obtiene el número de notificaciones no leídas
 * @returns Número de notificaciones no leídas
 */
export async function getUnreadNotificationsCount(): Promise<number> {
  try {
    const supabase = createClient();

    // Verificar la sesión actual
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
    if (sessionError || !sessionData?.session) {
      console.error('Error al obtener la sesión:', sessionError);
      return 0;
    }

    const userId = sessionData.session.user.id;

    // Intentar usar la función RPC optimizada
    try {
      console.log('Intentando usar la función RPC count_unread_notifications...');
      const { data: countData, error: countError } = await supabase.rpc(
        'count_unread_notifications',
        { p_user_id: userId }
      );

      if (!countError && countData !== null) {
        console.log('Conteo de notificaciones obtenido con función RPC:', countData);
        return countData;
      } else {
        console.error('Error al obtener conteo con función RPC:', countError);
        console.log('Usando método alternativo...');
      }
    } catch (rpcError) {
      console.error('Excepción al usar función RPC para conteo:', rpcError);
      console.log('Usando método alternativo...');
    }

    // Si la función RPC falló, usar el método tradicional
    const { count, error } = await supabase
      .from('notifications')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)
      .eq('read', false);

    if (error) {
      console.error('Error al obtener conteo de notificaciones:', error);
      return 0;
    }

    return count || 0;
  } catch (error) {
    console.error('Error inesperado al obtener conteo de notificaciones:', error);
    return 0;
  }
}

/**
 * Marca todas las notificaciones como leídas
 * @returns Número de notificaciones marcadas como leídas
 */
export async function markAllNotificationsAsRead(): Promise<number> {
  try {
    const supabase = createClient();

    // Verificar la sesión actual
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
    if (sessionError || !sessionData?.session) {
      console.error('Error al obtener la sesión:', sessionError);
      return 0;
    }

    const userId = sessionData.session.user.id;

    // Intentar usar la función RPC optimizada
    try {
      console.log('Intentando usar la función RPC mark_all_notifications_read...');
      const { data: result, error: markError } = await supabase.rpc(
        'mark_all_notifications_read',
        { p_user_id: userId }
      );

      if (!markError) {
        console.log('Notificaciones marcadas como leídas con función RPC:', result);
        return result || 0;
      } else {
        console.error('Error al marcar notificaciones con función RPC:', markError);
        console.log('Usando método alternativo...');
      }
    } catch (rpcError) {
      console.error('Excepción al usar función RPC para marcar notificaciones:', rpcError);
      console.log('Usando método alternativo...');
    }

    // Si la función RPC falló, usar el método tradicional
    const { data, error } = await supabase
      .from('notifications')
      .update({ read: true, updated_at: new Date().toISOString() })
      .eq('user_id', userId)
      .eq('read', false);

    if (error) {
      console.error('Error al marcar todas las notificaciones como leídas:', error);
      return 0;
    }

    // Contar cuántas notificaciones se actualizaron
    const { count } = await supabase
      .from('notifications')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)
      .eq('read', true);

    return count || 0;
  } catch (error) {
    console.error('Error inesperado al marcar notificaciones:', error);
    return 0;
  }
}
