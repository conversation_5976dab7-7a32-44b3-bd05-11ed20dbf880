"use client"

import { useState, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Progress } from "@/components/ui/progress"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Upload, X, FileText, AlertCircle } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { getBaseUrl } from "@/utils/get-base-url"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface UserDocumentUploadProps {
  userId: string
  onUploadComplete: () => void
  onCancel: () => void
  allowedFileTypes?: string[]
  maxFileSize?: number // in MB
}

const DOCUMENT_CATEGORIES = {
  identification: "Identificación",
  contract: "Contrato", 
  certificate: "Certificado",
  personal: "Personal",
  other: "Otro"
}

export function UserDocumentUpload({
  userId,
  onUploadComplete,
  onCancel,
  allowedFileTypes = [".pdf", ".doc", ".docx", ".jpg", ".jpeg", ".png", ".txt"],
  maxFileSize = 10
}: UserDocumentUploadProps) {
  const { toast } = useToast()
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [description, setDescription] = useState("")
  const [category, setCategory] = useState("")
  const [error, setError] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    setError(null)

    if (!file) {
      setSelectedFile(null)
      return
    }

    // Validar tipo de archivo
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase()
    if (!allowedFileTypes.includes(fileExtension)) {
      setError(`Tipo de archivo no permitido. Tipos permitidos: ${allowedFileTypes.join(', ')}`)
      setSelectedFile(null)
      return
    }

    // Validar tamaño de archivo
    const fileSizeMB = file.size / (1024 * 1024)
    if (fileSizeMB > maxFileSize) {
      setError(`El archivo es demasiado grande. Tamaño máximo: ${maxFileSize}MB`)
      setSelectedFile(null)
      return
    }

    setSelectedFile(file)
  }

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    const file = event.dataTransfer.files[0]
    
    if (file) {
      // Simular el evento de input para reutilizar la validación
      const fakeEvent = {
        target: { files: [file] }
      } as React.ChangeEvent<HTMLInputElement>
      handleFileSelect(fakeEvent)
    }
  }

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
  }

  const handleUpload = async () => {
    if (!selectedFile) {
      setError("Por favor selecciona un archivo")
      return
    }

    if (!category) {
      setError("Por favor selecciona una categoría")
      return
    }

    setIsUploading(true)
    setUploadProgress(0)
    setError(null)

    try {
      const formData = new FormData()
      formData.append('file', selectedFile)
      formData.append('description', description)
      formData.append('category', category)
      formData.append('userId', userId)

      // Simular progreso de subida
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => Math.min(prev + 10, 90))
      }, 200)

      const response = await fetch(`${getBaseUrl()}/api/users/${userId}/documents`, {
        method: 'POST',
        body: formData,
      })

      clearInterval(progressInterval)
      setUploadProgress(100)

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Error al subir el documento')
      }

      toast({
        title: "Documento subido",
        description: "El documento se ha subido correctamente",
      })

      // Limpiar formulario
      setSelectedFile(null)
      setDescription("")
      setCategory("")
      setUploadProgress(0)
      if (fileInputRef.current) {
        fileInputRef.current.value = ""
      }

      onUploadComplete()
    } catch (error: unknown) {
      console.error("Error al subir documento:", error)
      setError(error instanceof Error ? error.message : "Error al subir el documento")
      setUploadProgress(0)
      toast({
        title: "Error",
        description: "No se pudo subir el documento",
        variant: "destructive",
      })
    } finally {
      setIsUploading(false)
    }
  }

  const clearFile = () => {
    setSelectedFile(null)
    setError(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Subir Documento</CardTitle>
        <CardDescription>
          Sube un documento para este usuario. Tamaño máximo: {maxFileSize}MB
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Área de arrastrar y soltar */}
        <div
          className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
            selectedFile 
              ? 'border-green-300 bg-green-50' 
              : 'border-gray-300 hover:border-gray-400'
          }`}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
        >
          {selectedFile ? (
            <div className="space-y-2">
              <FileText className="mx-auto h-8 w-8 text-green-600" />
              <p className="text-sm font-medium">{selectedFile.name}</p>
              <p className="text-xs text-muted-foreground">
                {(selectedFile.size / (1024 * 1024)).toFixed(2)} MB
              </p>
              <Button
                variant="outline"
                size="sm"
                onClick={clearFile}
                className="mt-2"
              >
                <X className="mr-2 h-4 w-4" />
                Quitar archivo
              </Button>
            </div>
          ) : (
            <div className="space-y-2">
              <Upload className="mx-auto h-8 w-8 text-muted-foreground" />
              <p className="text-sm">
                Arrastra un archivo aquí o{" "}
                <button
                  type="button"
                  className="text-blue-600 hover:text-blue-500"
                  onClick={() => fileInputRef.current?.click()}
                >
                  selecciona uno
                </button>
              </p>
              <p className="text-xs text-muted-foreground">
                Tipos permitidos: {allowedFileTypes.join(', ')}
              </p>
            </div>
          )}
        </div>

        {/* Input de archivo oculto */}
        <input
          ref={fileInputRef}
          type="file"
          accept={allowedFileTypes.join(',')}
          onChange={handleFileSelect}
          className="hidden"
        />

        {/* Categoría */}
        <div className="space-y-2">
          <Label htmlFor="category">Categoría *</Label>
          <Select value={category} onValueChange={setCategory}>
            <SelectTrigger>
              <SelectValue placeholder="Selecciona una categoría" />
            </SelectTrigger>
            <SelectContent>
              {Object.entries(DOCUMENT_CATEGORIES).map(([key, label]) => (
                <SelectItem key={key} value={key}>
                  {label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Descripción */}
        <div className="space-y-2">
          <Label htmlFor="description">Descripción (opcional)</Label>
          <Textarea
            id="description"
            placeholder="Describe el documento..."
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            rows={3}
          />
        </div>

        {/* Barra de progreso */}
        {isUploading && (
          <div className="space-y-2">
            <Label>Progreso de subida</Label>
            <Progress value={uploadProgress} className="w-full" />
            <p className="text-sm text-muted-foreground text-center">
              {uploadProgress}% completado
            </p>
          </div>
        )}

        {/* Botones */}
        <div className="flex justify-end space-x-2">
          <Button
            variant="outline"
            onClick={onCancel}
            disabled={isUploading}
          >
            Cancelar
          </Button>
          <Button
            onClick={handleUpload}
            disabled={!selectedFile || !category || isUploading}
          >
            {isUploading ? "Subiendo..." : "Subir Documento"}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
