import { createClient } from '@/lib/supabase/client';
import {
  ServiceRequest,
  CustomerEquipment,
  MaintenanceSchedule,
  ServiceActivity,
  ServicePartUsed,
  ServiceNotification,
  ServiceManagementMetrics
} from '@/lib/types/service-management';

/**
 * Servicio para la gestión de servicios técnicos
 */
export const ServiceManagementService = {
  /**
   * Obtiene las solicitudes de servicio pendientes
   * @param limit Límite de resultados
   * @param offset Desplazamiento para paginación
   * @returns Lista de solicitudes de servicio pendientes
   */
  async getPendingServiceRequests(limit = 100, offset = 0): Promise<ServiceRequest[]> {
    const supabase = createClient();
    const { data, error } = await supabase
      .rpc('get_pending_service_requests', { p_limit: limit, p_offset: offset });

    if (error) {
      console.error('Error al obtener solicitudes de servicio pendientes:', error);
      throw error;
    }

    return data || [];
  },

  /**
   * Obtiene todas las solicitudes de servicio
   * @param limit Límite de resultados
   * @param offset Desplazamiento para paginación
   * @returns Lista de solicitudes de servicio
   */
  async getAllServiceRequests(limit = 100, offset = 0): Promise<ServiceRequest[]> {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('vw_service_requests')
      .select('*')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Error al obtener solicitudes de servicio:', error);
      throw error;
    }

    return data || [];
  },

  /**
   * Obtiene una solicitud de servicio por su ID
   * @param id ID de la solicitud de servicio
   * @returns Detalles de la solicitud de servicio
   */
  async getServiceRequestById(id: string): Promise<ServiceRequest> {
    const supabase = createClient();
    const { data, error } = await supabase
      .rpc('get_service_request_details', { p_service_request_id: id });

    if (error) {
      console.error(`Error al obtener solicitud de servicio ${id}:`, error);
      throw error;
    }

    if (!data || data.length === 0) {
      throw new Error(`No se encontró la solicitud de servicio con ID ${id}`);
    }

    return data[0];
  },

  /**
   * Crea una nueva solicitud de servicio
   * @param serviceRequest Datos de la solicitud de servicio
   * @returns La solicitud de servicio creada
   */
  async createServiceRequest(serviceRequest: Partial<ServiceRequest>): Promise<ServiceRequest> {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('service_requests')
      .insert([{
        ...serviceRequest,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }])
      .select();

    if (error) {
      console.error('Error al crear solicitud de servicio:', error);
      throw error;
    }

    return data![0];
  },

  /**
   * Actualiza una solicitud de servicio existente
   * @param id ID de la solicitud de servicio
   * @param serviceRequest Datos actualizados de la solicitud de servicio
   * @returns La solicitud de servicio actualizada
   */
  async updateServiceRequest(id: string, serviceRequest: Partial<ServiceRequest>): Promise<ServiceRequest> {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('service_requests')
      .update({
        ...serviceRequest,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select();

    if (error) {
      console.error(`Error al actualizar solicitud de servicio ${id}:`, error);
      throw error;
    }

    return data![0];
  },

  /**
   * Elimina una solicitud de servicio
   * @param id ID de la solicitud de servicio
   * @returns Verdadero si se eliminó correctamente
   */
  async deleteServiceRequest(id: string): Promise<boolean> {
    const supabase = createClient();
    const { error } = await supabase
      .from('service_requests')
      .delete()
      .eq('id', id);

    if (error) {
      console.error(`Error al eliminar solicitud de servicio ${id}:`, error);
      throw error;
    }

    return true;
  },

  /**
   * Obtiene los equipos de un cliente
   * @param clientId ID del cliente (opcional)
   * @param includeMaintenanceInfo Incluir información de mantenimiento
   * @param limit Límite de resultados
   * @param offset Desplazamiento para paginación
   * @returns Lista de equipos del cliente
   */
  async getClientEquipment(
    clientId: string | null = null,
    includeMaintenanceInfo = true,
    limit = 100,
    offset = 0
  ): Promise<CustomerEquipment[]> {
    const supabase = createClient();
    const { data, error } = await supabase
      .rpc('get_client_equipment', {
        p_client_id: clientId,
        p_include_maintenance: includeMaintenanceInfo,
        p_limit: limit,
        p_offset: offset
      });

    if (error) {
      console.error('Error al obtener equipos del cliente:', error);
      throw error;
    }

    return data || [];
  },

  /**
   * Obtiene un equipo por su ID
   * @param id ID del equipo
   * @returns Detalles del equipo
   */
  async getEquipmentById(id: string): Promise<CustomerEquipment> {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('vw_customer_equipment')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error(`Error al obtener equipo ${id}:`, error);
      throw error;
    }

    return data;
  },

  /**
   * Crea un nuevo equipo de cliente
   * @param equipment Datos del equipo
   * @returns El equipo creado
   */
  async createEquipment(equipment: Partial<CustomerEquipment>): Promise<CustomerEquipment> {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('customer_equipment')
      .insert([{
        ...equipment,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }])
      .select();

    if (error) {
      console.error('Error al crear equipo:', error);
      throw error;
    }

    return data![0];
  },

  /**
   * Actualiza un equipo existente
   * @param id ID del equipo
   * @param equipment Datos actualizados del equipo
   * @returns El equipo actualizado
   */
  async updateEquipment(id: string, equipment: Partial<CustomerEquipment>): Promise<CustomerEquipment> {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('customer_equipment')
      .update({
        ...equipment,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select();

    if (error) {
      console.error(`Error al actualizar equipo ${id}:`, error);
      throw error;
    }

    return data![0];
  },

  /**
   * Obtiene los programas de mantenimiento próximos
   * @param daysAhead Días hacia adelante para buscar
   * @param includeOverdue Incluir mantenimientos vencidos
   * @param limit Límite de resultados
   * @param offset Desplazamiento para paginación
   * @returns Lista de programas de mantenimiento
   */
  async getUpcomingMaintenanceSchedules(
    daysAhead = 30,
    includeOverdue = true,
    limit = 100,
    offset = 0
  ): Promise<MaintenanceSchedule[]> {
    const supabase = createClient();
    const { data, error } = await supabase
      .rpc('get_upcoming_maintenance_schedules', {
        p_days_ahead: daysAhead,
        p_include_overdue: includeOverdue,
        p_limit: limit,
        p_offset: offset
      });

    if (error) {
      console.error('Error al obtener programas de mantenimiento:', error);
      throw error;
    }

    return data || [];
  },

  /**
   * Crea un programa de mantenimiento a partir de una plantilla
   * @param equipmentId ID del equipo
   * @param maintenanceType Tipo de mantenimiento
   * @param frequency Frecuencia
   * @param nextMaintenanceDate Fecha del próximo mantenimiento
   * @param description Descripción
   * @param estimatedDurationHours Duración estimada en horas
   * @param notificationDaysBefore Días antes para notificar
   * @returns ID del programa de mantenimiento creado
   */
  async createMaintenanceSchedule(
    equipmentId: string,
    maintenanceType: string,
    frequency: string,
    nextMaintenanceDate: string,
    description: string,
    estimatedDurationHours = 1,
    notificationDaysBefore = 7
  ): Promise<string> {
    const supabase = createClient();
    const { data, error } = await supabase
      .rpc('create_maintenance_schedule_from_template', {
        p_equipment_id: equipmentId,
        p_maintenance_type: maintenanceType,
        p_frequency: frequency,
        p_next_maintenance_date: nextMaintenanceDate,
        p_description: description,
        p_estimated_duration_hours: estimatedDurationHours,
        p_notification_days_before: notificationDaysBefore
      });

    if (error) {
      console.error('Error al crear programa de mantenimiento:', error);
      throw error;
    }

    return data;
  },

  /**
   * Crea una solicitud de servicio a partir de un programa de mantenimiento
   * @param maintenanceScheduleId ID del programa de mantenimiento
   * @returns ID de la solicitud de servicio creada
   */
  async createServiceRequestFromMaintenance(maintenanceScheduleId: string): Promise<string> {
    const supabase = createClient();
    const { data, error } = await supabase
      .rpc('create_service_request_from_maintenance', {
        p_maintenance_schedule_id: maintenanceScheduleId
      });

    if (error) {
      console.error('Error al crear solicitud de servicio desde mantenimiento:', error);
      throw error;
    }

    return data;
  },

  /**
   * Obtiene las actividades de servicio
   * @param serviceRequestId ID de la solicitud de servicio (opcional)
   * @param equipmentId ID del equipo (opcional)
   * @param technicianId ID del técnico (opcional)
   * @param status Estado de la actividad (opcional)
   * @param limit Límite de resultados
   * @param offset Desplazamiento para paginación
   * @returns Lista de actividades de servicio
   */
  async getServiceActivities(
    serviceRequestId: string | null = null,
    equipmentId: string | null = null,
    technicianId: string | null = null,
    status: string | null = null,
    limit = 100,
    offset = 0
  ): Promise<ServiceActivity[]> {
    const supabase = createClient();
    const { data, error } = await supabase
      .rpc('get_service_activities', {
        p_service_request_id: serviceRequestId,
        p_equipment_id: equipmentId,
        p_technician_id: technicianId,
        p_status: status,
        p_limit: limit,
        p_offset: offset
      });

    if (error) {
      console.error('Error al obtener actividades de servicio:', error);
      throw error;
    }

    return data || [];
  },

  /**
   * Obtiene las métricas de gestión de servicios para el dashboard
   * @returns Métricas de gestión de servicios
   */
  async getServiceManagementMetrics(): Promise<ServiceManagementMetrics> {
    const supabase = createClient();
    const { data, error } = await supabase
      .rpc('get_service_management_metrics');

    if (error) {
      console.error('Error al obtener métricas de gestión de servicios:', error);
      throw error;
    }

    return data;
  },

  /**
   * Obtiene las notificaciones del usuario actual
   * @param includeRead Incluir notificaciones leídas
   * @param limit Límite de resultados
   * @param offset Desplazamiento para paginación
   * @returns Lista de notificaciones
   */
  async getUserNotifications(
    includeRead = false,
    limit = 50,
    offset = 0
  ): Promise<ServiceNotification[]> {
    const supabase = createClient();
    const { data, error } = await supabase
      .rpc('get_user_notifications', {
        p_include_read: includeRead,
        p_limit: limit,
        p_offset: offset
      });

    if (error) {
      console.error('Error al obtener notificaciones:', error);
      throw error;
    }

    return data || [];
  },

  /**
   * Marca una notificación como leída
   * @param notificationId ID de la notificación
   * @returns Verdadero si se marcó correctamente
   */
  async markNotificationAsRead(notificationId: string): Promise<boolean> {
    const supabase = createClient();
    const { data, error } = await supabase
      .rpc('mark_notification_as_read', {
        p_notification_id: notificationId
      });

    if (error) {
      console.error(`Error al marcar notificación ${notificationId} como leída:`, error);
      throw error;
    }

    return data;
  },

  /**
   * Cuenta las notificaciones no leídas del usuario actual
   * @returns Número de notificaciones no leídas
   */
  async countUnreadNotifications(): Promise<number> {
    const supabase = createClient();
    const { data, error } = await supabase
      .rpc('count_unread_notifications');

    if (error) {
      console.error('Error al contar notificaciones no leídas:', error);
      throw error;
    }

    return data || 0;
  }
};
