// Script para verificar errores de runtime comunes
// Ejecutar con: node scripts/check-runtime-errors.js

const fs = require('fs');
const path = require('path');

console.log('🔍 Verificando errores de runtime comunes...\n');

// Función para buscar archivos recursivamente
function findFiles(dir, extension, files = []) {
  const items = fs.readdirSync(dir);

  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
      findFiles(fullPath, extension, files);
    } else if (stat.isFile() && item.endsWith(extension)) {
      files.push(fullPath);
    }
  }

  return files;
}

// Función para verificar useSearchParams sin Suspense
function checkUseSearchParams() {
  console.log('📋 Verificando useSearchParams...');

  const tsxFiles = findFiles('./src', '.tsx');
  const problematicFiles = [];

  for (const file of tsxFiles) {
    try {
      const content = fs.readFileSync(file, 'utf8');

      // Verificar si usa useSearchParams
      if (content.includes('useSearchParams')) {
        // Verificar si está envuelto en Suspense o es un componente interno
        const hasInnerComponent = content.includes('Inner()') || content.includes('Inner }');
        const hasSuspenseWrapper = content.includes('<Suspense') && content.includes('</Suspense>');
        const isAlreadyWrapped = content.includes('SearchParamsHandler');

        if (!hasInnerComponent && !hasSuspenseWrapper && !isAlreadyWrapped) {
          problematicFiles.push(file);
        }
      }
    } catch (error) {
      console.warn(`⚠️ Error al leer ${file}:`, error.message);
    }
  }

  if (problematicFiles.length === 0) {
    console.log('✅ Todos los archivos con useSearchParams están correctamente envueltos en Suspense');
  } else {
    console.log('❌ Archivos con useSearchParams sin Suspense:');
    problematicFiles.forEach(file => console.log(`   - ${file}`));
  }

  return problematicFiles.length === 0;
}

// Función para verificar imports faltantes
function checkMissingImports() {
  console.log('\n📋 Verificando imports faltantes...');

  const tsxFiles = findFiles('./src', '.tsx');
  const problematicFiles = [];

  for (const file of tsxFiles) {
    try {
      const content = fs.readFileSync(file, 'utf8');

      // Verificar Suspense sin import (más preciso)
      if (content.includes('<Suspense') &&
          !content.includes('import { Suspense') &&
          !content.includes('import { useState, useEffect, Suspense') &&
          !content.includes('import { useState, lazy, Suspense') &&
          !content.includes('import { useState, Suspense') &&
          !content.includes('import React, { Suspense')) {
        problematicFiles.push({ file, issue: 'Suspense usado sin import' });
      }

      // Verificar useSearchParams sin import (más preciso)
      // Excluir comentarios y verificar uso real
      const hasUseSearchParamsCall = /const\s+\w+\s*=\s*useSearchParams\(\)/.test(content);
      const hasImport = content.includes('from "next/navigation"') ||
                       content.includes('from \'next/navigation\'') ||
                       content.includes('useRouter, useSearchParams') ||
                       content.includes('useSearchParams } from');

      if (hasUseSearchParamsCall && !hasImport) {
        problematicFiles.push({ file, issue: 'useSearchParams usado sin import de next/navigation' });
      }

    } catch (error) {
      console.warn(`⚠️ Error al leer ${file}:`, error.message);
    }
  }

  if (problematicFiles.length === 0) {
    console.log('✅ Todos los imports están correctos');
  } else {
    console.log('❌ Archivos con imports faltantes:');
    problematicFiles.forEach(({ file, issue }) => console.log(`   - ${file}: ${issue}`));
  }

  return problematicFiles.length === 0;
}

// Función para verificar errores de TypeScript comunes
function checkTypeScriptErrors() {
  console.log('\n📋 Verificando errores de TypeScript comunes...');

  const tsFiles = [...findFiles('./src', '.ts'), ...findFiles('./src', '.tsx')];
  const problematicFiles = [];

  for (const file of tsFiles) {
    try {
      const content = fs.readFileSync(file, 'utf8');

      // Verificar error: unknown en catch blocks
      const catchBlocks = content.match(/catch\s*\([^)]*\)\s*{[^}]*}/g);
      if (catchBlocks) {
        for (const block of catchBlocks) {
          if (block.includes('error.message') && !block.includes('instanceof Error')) {
            problematicFiles.push({ file, issue: 'Acceso a error.message sin verificar tipo' });
          }
        }
      }

    } catch (error) {
      console.warn(`⚠️ Error al leer ${file}:`, error.message);
    }
  }

  if (problematicFiles.length === 0) {
    console.log('✅ No se encontraron errores de TypeScript comunes');
  } else {
    console.log('❌ Archivos con posibles errores de TypeScript:');
    problematicFiles.forEach(({ file, issue }) => console.log(`   - ${file}: ${issue}`));
  }

  return problematicFiles.length === 0;
}

// Función principal
async function main() {
  try {
    const checks = [
      checkUseSearchParams(),
      checkMissingImports(),
      checkTypeScriptErrors()
    ];

    const allPassed = checks.every(check => check);

    console.log('\n' + '='.repeat(50));
    if (allPassed) {
      console.log('🎉 ¡Todas las verificaciones pasaron exitosamente!');
      console.log('✅ No se encontraron errores de runtime comunes');
    } else {
      console.log('⚠️ Se encontraron algunos problemas que deben ser corregidos');
    }
    console.log('='.repeat(50));

    process.exit(allPassed ? 0 : 1);
  } catch (error) {
    console.error('❌ Error durante la verificación:', error);
    process.exit(1);
  }
}

// Ejecutar la función principal
main();
