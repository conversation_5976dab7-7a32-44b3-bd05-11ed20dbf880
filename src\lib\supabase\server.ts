/**
 * @ai-file-description: "Supabase server client exports"
 * @ai-related-files: ["server-client.ts"]
 * @ai-owner: "Supabase Integration"
 */

import { createClient as createServerClientInternal } from './server-client'

/**
 * Creates a Supabase client for server components
 *
 * @returns Supabase client
 */
export async function createClient() {
  // Dynamic import to avoid issues with pages directory
  let cookieStore;
  try {
    // Only import in app directory
    if (process.env.NEXT_RUNTIME === 'nodejs') {
      const { cookies } = await import('next/headers');
      cookieStore = await cookies();
    } else {
      // Mock cookie store for pages directory
      cookieStore = {
        get: () => null,
        getAll: () => [],
        set: () => {},
        delete: () => {}
      };
    }
  } catch (e) {
    // Fallback for pages directory
    cookieStore = {
      get: () => null,
      getAll: () => [],
      set: () => {},
      delete: () => {}
    };
  }

  return createServerClientInternal(cookieStore as any)
}
