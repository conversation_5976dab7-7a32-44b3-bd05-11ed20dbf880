import { createClient } from '@/lib/supabase/client';
import {
  EquipmentFailurePrediction,
  TechnicianRouteOptimization,
  EquipmentPerformanceAnalysis,
  ServiceCostAnalysis,
  FailurePredictionParams,
  RouteOptimizationParams,
  PerformanceAnalysisParams,
  CostAnalysisParams
} from '@/lib/types/service-analytics';

// Initialize Supabase client
const supabase = createClient();

/**
 * Servicio para análisis avanzados de servicios técnicos
 */
export const ServiceAnalyticsService = {
  /**
   * Predice fallos de equipos basados en historial de servicio
   * @param params Parámetros para la predicción
   * @returns Lista de predicciones de fallos
   */
  async predictEquipmentFailures(params?: FailurePredictionParams): Promise<EquipmentFailurePrediction[]> {
    const { data, error } = await supabase
      .rpc('predict_equipment_failures', {
        p_days_ahead: params?.days_ahead || 30,
        p_min_failure_probability: params?.min_failure_probability || 0.5,
        p_limit: params?.limit || 100
      });

    if (error) {
      console.error('Error al predecir fallos de equipos:', error);
      throw error;
    }

    // Convertir common_issues de JSONB a objeto JavaScript
    return data.map(item => ({
      ...item,
      common_issues: item.common_issues || {}
    }));
  },

  /**
   * Optimiza rutas de técnicos para un día específico
   * @param params Parámetros para la optimización
   * @returns Lista de actividades con ruta optimizada
   */
  async optimizeTechnicianRoutes(params?: RouteOptimizationParams): Promise<TechnicianRouteOptimization[]> {
    const { data, error } = await supabase
      .rpc('optimize_technician_routes', {
        p_date: params?.date || new Date().toISOString().split('T')[0],
        p_technician_id: params?.technician_id || null
      });

    if (error) {
      console.error('Error al optimizar rutas de técnicos:', error);
      throw error;
    }

    return data || [];
  },

  /**
   * Analiza el rendimiento de equipos
   * @param params Parámetros para el análisis
   * @returns Lista de análisis de rendimiento
   */
  async analyzeEquipmentPerformance(params?: PerformanceAnalysisParams): Promise<EquipmentPerformanceAnalysis[]> {
    const { data, error } = await supabase
      .rpc('analyze_equipment_performance', {
        p_client_id: params?.client_id || null,
        p_equipment_type: params?.equipment_type || null,
        p_start_date: params?.start_date || new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString(),
        p_end_date: params?.end_date || new Date().toISOString()
      });

    if (error) {
      console.error('Error al analizar rendimiento de equipos:', error);
      throw error;
    }

    return data || [];
  },

  /**
   * Analiza costos de servicio
   * @param params Parámetros para el análisis
   * @returns Lista de análisis de costos
   */
  async analyzeServiceCosts(params?: CostAnalysisParams): Promise<ServiceCostAnalysis[]> {
    const { data, error } = await supabase
      .rpc('analyze_service_costs', {
        p_client_id: params?.client_id || null,
        p_start_date: params?.start_date || new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString(),
        p_end_date: params?.end_date || new Date().toISOString(),
        p_group_by: params?.group_by || 'month'
      });

    if (error) {
      console.error('Error al analizar costos de servicio:', error);
      throw error;
    }

    // Convertir cost_breakdown de JSONB a objeto JavaScript
    return data.map(item => ({
      ...item,
      cost_breakdown: item.cost_breakdown || {
        labor: 0,
        parts: 0,
        travel: 0,
        other: 0
      }
    }));
  },

  /**
   * Genera un informe de predicción de fallos en formato PDF
   * @param predictions Lista de predicciones de fallos
   * @returns URL del informe generado
   */
  async generateFailurePredictionReport(predictions: EquipmentFailurePrediction[]): Promise<string> {
    // Esta función simula la generación de un informe
    // En una implementación real, se enviarían los datos a un servicio de generación de informes
    console.log('Generando informe de predicción de fallos:', predictions);

    // Simular un tiempo de procesamiento
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Devolver una URL simulada
    return `/reports/failure-prediction-${Date.now()}.pdf`;
  },

  /**
   * Genera un informe de rendimiento de equipos en formato PDF
   * @param analyses Lista de análisis de rendimiento
   * @returns URL del informe generado
   */
  async generateEquipmentPerformanceReport(analyses: EquipmentPerformanceAnalysis[]): Promise<string> {
    // Esta función simula la generación de un informe
    console.log('Generando informe de rendimiento de equipos:', analyses);

    // Simular un tiempo de procesamiento
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Devolver una URL simulada
    return `/reports/equipment-performance-${Date.now()}.pdf`;
  },

  /**
   * Genera un informe de costos de servicio en formato PDF
   * @param analyses Lista de análisis de costos
   * @returns URL del informe generado
   */
  async generateServiceCostReport(analyses: ServiceCostAnalysis[]): Promise<string> {
    // Esta función simula la generación de un informe
    console.log('Generando informe de costos de servicio:', analyses);

    // Simular un tiempo de procesamiento
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Devolver una URL simulada
    return `/reports/service-cost-${Date.now()}.pdf`;
  },

  /**
   * Genera un informe de optimización de rutas en formato PDF
   * @param routes Lista de rutas optimizadas
   * @returns URL del informe generado
   */
  async generateRouteOptimizationReport(routes: TechnicianRouteOptimization[]): Promise<string> {
    // Esta función simula la generación de un informe
    console.log('Generando informe de optimización de rutas:', routes);

    // Simular un tiempo de procesamiento
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Devolver una URL simulada
    return `/reports/route-optimization-${Date.now()}.pdf`;
  }
};
