// Tipos e interfaces para el módulo de gestión de servicios técnicos

// Solicitud de servicio
export interface ServiceRequest {
  id: string;
  title: string;
  description: string | null;
  client_id: string | null;
  client_name?: string;
  status: ServiceRequestStatus;
  priority: ServiceRequestPriority;
  source: string | null;
  created_by: string | null;
  created_by_name?: string;
  assigned_to: string | null;
  assigned_to_name?: string;
  due_date: string | null;
  created_at: string;
  updated_at: string;
  resolution_notes: string | null;
  resolution_date: string | null;
  work_order_id: string | null;
  location: string | null;
  estimated_hours: number | null;
  actual_hours: number | null;
  is_billable: boolean;
  external_reference: string | null;
  is_overdue?: boolean;
  days_overdue?: number;
  resolution_time_hours?: number | null;
  activity_count?: number;
  parts_cost?: number;
  total_parts_used?: number;
  has_attachments?: boolean;
  has_signatures?: boolean;
}

// Estados de solicitud de servicio
export type ServiceRequestStatus = 
  | 'pending'
  | 'assigned'
  | 'in_progress'
  | 'on_hold'
  | 'completed'
  | 'cancelled';

// Prioridades de solicitud de servicio
export type ServiceRequestPriority = 
  | 'low'
  | 'medium'
  | 'high'
  | 'critical';

// Equipo de cliente
export interface CustomerEquipment {
  id: string;
  name: string;
  model: string | null;
  serial_number: string | null;
  client_id: string | null;
  client_name?: string;
  location: string | null;
  installation_date: string | null;
  warranty_start_date: string | null;
  warranty_end_date: string | null;
  is_under_warranty?: boolean;
  warranty_days_remaining?: number;
  status: string;
  notes: string | null;
  created_at: string;
  updated_at: string;
  created_by: string | null;
  created_by_name?: string;
  inventory_item_id: string | null;
  inventory_item_name?: string;
  manufacturer: string | null;
  purchase_date: string | null;
  purchase_price: number | null;
  expected_lifetime_months: number | null;
  expected_end_of_life_date?: string | null;
  last_service_date: string | null;
  days_since_last_service?: number | null;
  next_maintenance_date?: string | null;
  maintenance_type?: string | null;
  days_to_next_maintenance?: number | null;
  maintenance_overdue?: boolean;
  qr_code: string | null;
  image_url: string | null;
}

// Programa de mantenimiento
export interface MaintenanceSchedule {
  id: string;
  equipment_id: string;
  equipment_name?: string;
  equipment_model?: string | null;
  equipment_serial_number?: string | null;
  client_id?: string | null;
  client_name?: string;
  maintenance_type: string;
  frequency: MaintenanceFrequency | null;
  last_maintenance_date: string | null;
  next_maintenance_date: string | null;
  days_to_maintenance?: number | null;
  is_overdue?: boolean;
  days_overdue?: number;
  description: string | null;
  created_at: string;
  updated_at: string;
  created_by: string | null;
  created_by_name?: string;
  is_active: boolean;
  estimated_duration_hours: number | null;
  checklist_template_id: string | null;
  checklist_name?: string | null;
  notification_days_before: number;
  repeat_count: number;
  location?: string | null;
}

// Frecuencias de mantenimiento
export type MaintenanceFrequency = 
  | 'daily'
  | 'weekly'
  | 'monthly'
  | 'quarterly'
  | 'yearly'
  | 'custom';

// Actividad de servicio
export interface ServiceActivity {
  id: string;
  service_request_id: string | null;
  service_request_title?: string | null;
  equipment_id: string | null;
  equipment_name?: string | null;
  equipment_model?: string | null;
  equipment_serial_number?: string | null;
  activity_type: ServiceActivityType;
  description: string | null;
  start_time: string | null;
  end_time: string | null;
  duration_hours?: number | null;
  technician_id: string | null;
  technician_name?: string;
  status: ServiceActivityStatus;
  notes: string | null;
  created_at: string;
  updated_at: string;
  location_coordinates: string | null;
  travel_time_minutes: number | null;
  is_remote: boolean;
  diagnostic_results: string | null;
  resolution_code: string | null;
  follow_up_required: boolean;
  client_id?: string | null;
  client_name?: string | null;
  parts_count?: number;
  parts_cost?: number;
  checklist_responses_count?: number;
  signatures_count?: number;
  attachments_count?: number;
}

// Tipos de actividad de servicio
export type ServiceActivityType = 
  | 'diagnosis'
  | 'repair'
  | 'installation'
  | 'maintenance'
  | 'inspection'
  | 'training'
  | 'consultation';

// Estados de actividad de servicio
export type ServiceActivityStatus = 
  | 'scheduled'
  | 'in_progress'
  | 'completed'
  | 'cancelled'
  | 'pending';

// Pieza utilizada en servicio
export interface ServicePartUsed {
  id: string;
  service_activity_id: string;
  activity_description?: string | null;
  service_request_id?: string | null;
  service_request_title?: string | null;
  equipment_id?: string | null;
  equipment_name?: string | null;
  inventory_item_id: string;
  inventory_item_name?: string;
  inventory_item_sku?: string | null;
  inventory_item_category?: string | null;
  quantity: number;
  unit_cost: number | null;
  total_cost?: number;
  created_at: string;
  updated_at: string;
  is_billable: boolean;
  is_warranty_covered: boolean;
  notes: string | null;
  batch_number: string | null;
  source_location: string | null;
  client_id?: string | null;
  client_name?: string | null;
  technician_id?: string | null;
  technician_name?: string | null;
}

// Notificación de servicio
export interface ServiceNotification {
  id: string;
  user_id: string;
  user_name?: string;
  title: string;
  message: string;
  notification_type: string;
  reference_id: string | null;
  is_read: boolean;
  created_at: string;
  read_at: string | null;
  priority: NotificationPriority;
  action_url: string | null;
  age_minutes?: number;
  reference_name?: string | null;
}

// Prioridades de notificación
export type NotificationPriority = 
  | 'low'
  | 'normal'
  | 'high'
  | 'urgent';

// Métricas de gestión de servicios
export interface ServiceManagementMetrics {
  service_requests_count: number;
  pending_service_requests_count: number;
  customer_equipment_count: number;
  maintenance_schedules_count: number;
  overdue_maintenance_count: number;
  service_requests_trend: number;
  maintenance_trend: number;
  service_requests_by_status: ChartData[];
  service_requests_by_priority: ChartData[];
  maintenance_by_type: ChartData[];
  equipment_by_status: ChartData[];
  recent_activities: RecentActivity[];
}

// Datos para gráficos
export interface ChartData {
  name: string;
  value: number;
}

// Actividad reciente
export interface RecentActivity {
  id: string;
  description: string;
  activity_type: string;
  status: string;
  created_at: string;
  equipment_name: string | null;
  client_name: string | null;
}
