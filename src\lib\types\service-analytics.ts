// Tipos e interfaces para análisis avanzados de servicios técnicos

// Predicción de fallos de equipos
export interface EquipmentFailurePrediction {
  equipment_id: string;
  equipment_name: string;
  model: string | null;
  serial_number: string | null;
  client_id: string;
  client_name: string;
  last_service_date: string | null;
  days_since_last_service: number | null;
  service_count: number;
  failure_count: number;
  mtbf_days: number | null;
  days_until_predicted_failure: number | null;
  failure_probability: number;
  risk_level: 'critical' | 'high' | 'medium' | 'low';
  common_issues: Record<string, number>;
}

// Optimización de rutas de técnicos
export interface TechnicianRouteOptimization {
  technician_id: string;
  technician_name: string;
  activity_id: string;
  activity_description: string;
  service_request_id: string;
  service_request_title: string;
  equipment_id: string | null;
  equipment_name: string | null;
  client_id: string;
  client_name: string;
  location: string | null;
  location_coordinates: string | null;
  start_time: string;
  estimated_duration_minutes: number;
  priority: number;
  distance_from_previous: number;
  optimal_route_order: number;
}

// Análisis de rendimiento de equipos
export interface EquipmentPerformanceAnalysis {
  equipment_id: string;
  equipment_name: string;
  model: string | null;
  manufacturer: string | null;
  client_id: string;
  client_name: string;
  installation_date: string | null;
  age_days: number;
  total_service_requests: number;
  total_service_activities: number;
  total_maintenance_activities: number;
  total_repair_activities: number;
  total_downtime_hours: number;
  availability_percentage: number;
  mtbf_days: number;
  mttr_hours: number;
  total_cost: number;
  cost_per_month: number;
  reliability_score: number;
  performance_trend: 'improving' | 'stable' | 'deteriorating';
}

// Análisis de costos de servicio
export interface ServiceCostAnalysis {
  group_key: string;
  group_label: string;
  total_service_requests: number;
  total_service_activities: number;
  labor_hours: number;
  labor_cost: number;
  parts_cost: number;
  travel_cost: number;
  other_costs: number;
  total_cost: number;
  billable_amount: number;
  profit: number;
  profit_margin: number;
  cost_per_activity: number;
  cost_breakdown: {
    labor: number;
    parts: number;
    travel: number;
    other: number;
  };
}

// Parámetros para predicción de fallos
export interface FailurePredictionParams {
  days_ahead?: number;
  min_failure_probability?: number;
  limit?: number;
}

// Parámetros para optimización de rutas
export interface RouteOptimizationParams {
  date?: string;
  technician_id?: string;
}

// Parámetros para análisis de rendimiento
export interface PerformanceAnalysisParams {
  client_id?: string;
  equipment_type?: string;
  start_date?: string;
  end_date?: string;
}

// Parámetros para análisis de costos
export interface CostAnalysisParams {
  client_id?: string;
  start_date?: string;
  end_date?: string;
  group_by?: 'month' | 'quarter' | 'year' | 'client' | 'equipment';
}

// Métricas de mantenimiento
export interface MaintenanceMetrics {
  total_maintenance_activities: number;
  preventive_maintenance_count: number;
  corrective_maintenance_count: number;
  predictive_maintenance_count: number;
  maintenance_compliance_percentage: number;
  average_maintenance_duration_hours: number;
  maintenance_cost: number;
  maintenance_cost_per_equipment: number;
  maintenance_by_type: ChartData[];
  maintenance_by_month: ChartData[];
  maintenance_by_technician: ChartData[];
  maintenance_by_equipment: ChartData[];
}

// Métricas de rendimiento de técnicos
export interface TechnicianPerformanceMetrics {
  technician_id: string;
  technician_name: string;
  total_activities: number;
  completed_activities: number;
  completion_rate: number;
  average_resolution_time_hours: number;
  average_travel_time_hours: number;
  average_activities_per_day: number;
  customer_satisfaction_score: number;
  first_time_fix_rate: number;
  rework_percentage: number;
  efficiency_score: number;
  activities_by_type: ChartData[];
  activities_by_priority: ChartData[];
  activities_by_day: ChartData[];
  performance_trend: 'improving' | 'stable' | 'declining';
}

// Datos para gráficos
export interface ChartData {
  name: string;
  value: number;
}

// Datos para gráficos de series temporales
export interface TimeSeriesData {
  date: string;
  value: number;
}

// Datos para gráficos de comparación
export interface ComparisonData {
  category: string;
  current: number;
  previous: number;
  change_percentage: number;
}

// Datos para gráficos de distribución
export interface DistributionData {
  category: string;
  count: number;
  percentage: number;
}
