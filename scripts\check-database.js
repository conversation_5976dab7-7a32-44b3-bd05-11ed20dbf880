#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

async function checkDatabase() {
  console.log('🔍 Verificando estado de la base de datos...\n');

  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY
  );

  try {
    // Verificar conexión
    console.log('📡 Verificando conexión...');
    const { data: connectionTest, error: connectionError } = await supabase
      .from('projects')
      .select('count')
      .limit(1);

    if (connectionError) {
      console.log('❌ Error de conexión:', connectionError.message);
      
      // Intentar con tabla de usuarios de auth
      console.log('🔄 Intentando con auth.users...');
      const { data: authTest, error: authError } = await supabase.auth.getUser();
      
      if (authError) {
        console.log('❌ Error de auth:', authError.message);
      } else {
        console.log('✅ Conexión de auth funcionando');
      }
    } else {
      console.log('✅ Conexión exitosa');
    }

    // Verificar tablas principales
    const tables = ['projects', 'work_orders', 'users', 'documents'];
    
    for (const table of tables) {
      console.log(`\n📋 Verificando tabla: ${table}`);
      
      try {
        const { data, error, count } = await supabase
          .from(table)
          .select('*', { count: 'exact', head: true });

        if (error) {
          console.log(`❌ Error en ${table}:`, error.message);
        } else {
          console.log(`✅ Tabla ${table} existe - Registros: ${count || 0}`);
        }
      } catch (tableError) {
        console.log(`❌ Error verificando ${table}:`, tableError.message);
      }
    }

    // Verificar funciones RPC
    console.log('\n🔧 Verificando funciones RPC...');
    
    try {
      const { data: rpcData, error: rpcError } = await supabase
        .rpc('get_project_distribution');

      if (rpcError) {
        console.log('❌ Función get_project_distribution no existe:', rpcError.message);
      } else {
        console.log('✅ Función get_project_distribution funciona');
      }
    } catch (rpcError) {
      console.log('❌ Error RPC:', rpcError.message);
    }

    // Verificar variables de entorno
    console.log('\n🔐 Verificando variables de entorno...');
    console.log('NEXT_PUBLIC_SUPABASE_URL:', process.env.NEXT_PUBLIC_SUPABASE_URL ? '✅ Configurada' : '❌ Faltante');
    console.log('NEXT_PUBLIC_SUPABASE_ANON_KEY:', process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? '✅ Configurada' : '❌ Faltante');
    console.log('SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY ? '✅ Configurada' : '❌ Faltante');

  } catch (error) {
    console.error('❌ Error general:', error.message);
  }

  console.log('\n✅ Verificación completada');
}

checkDatabase();
