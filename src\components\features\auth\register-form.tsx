"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { useRouter } from "next/navigation"
import { createClient } from "@/lib/supabase/client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { toast } from "@/hooks/use-toast"
import Link from "next/link"
import { Separator } from "@/components/ui/separator"
import { GoogleAuthButton } from "@/components/auth/google-auth-button"
import { GitHubAuthButton } from "@/components/auth/github-auth-button"
import { dataValidator } from "@/lib/services/data-validator-service"
import { errorHandler } from "@/lib/services/error-handler-service"
import { FormValidationAlert } from "@/components/ui/form-validation-alert"

const formSchema = z.object({
  fullName: z.string({
    required_error: "El nombre completo es obligatorio",
  }).min(2, "El nombre debe tener al menos 2 caracteres")
  .refine(
    (val) => val && val.trim() !== '',
    {
      message: "El nombre no puede estar vacío."
    }
  ),
  email: z.string({
    required_error: "El correo electrónico es obligatorio",
  }).email("Correo electrónico inválido")
  .refine(
    (val) => val && val.trim() !== '',
    {
      message: "El correo electrónico no puede estar vacío."
    }
  ),
  password: z.string({
    required_error: "La contraseña es obligatoria",
  }).min(6, "La contraseña debe tener al menos 6 caracteres")
  .refine(
    (val) => val && val.trim() !== '',
    {
      message: "La contraseña no puede estar vacía."
    }
  ),
  confirmPassword: z.string({
    required_error: "Debe confirmar la contraseña",
  }).min(6, "La contraseña debe tener al menos 6 caracteres")
  .refine(
    (val) => val && val.trim() !== '',
    {
      message: "La confirmación de contraseña no puede estar vacía."
    }
  ),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Las contraseñas no coinciden",
  path: ["confirmPassword"],
});

export function RegisterForm() {
  const [isLoading, setIsLoading] = useState(false)
  const [registrationComplete, setRegistrationComplete] = useState(false)
  const [validationErrors, setValidationErrors] = useState<string[]>([])
  const router = useRouter()
  const supabase = createClient()

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      fullName: "",
      email: "",
      password: "",
      confirmPassword: "",
    },
  })

  async function onSubmit(values: z.infer<typeof formSchema>) {
    // Limpiar errores previos
    setValidationErrors([])

    // Validar campos requeridos
    const requiredFields = ['fullName', 'email', 'password', 'confirmPassword']
    const { isValid, errors } = dataValidator.validateRequiredFields(values as any, requiredFields)

    if (!isValid) {
      // Mostrar errores en los campos correspondientes
      Object.entries(errors).forEach(([field, message]) => {
        form.setError(field as any, {
          type: 'required',
          message
        })
      })

      // Agregar errores a la lista de validación para mostrar en el alert
      const errorMessages = Object.values(errors)
      setValidationErrors(errorMessages)
      return
    }

    setIsLoading(true)

    try {
      // Registrar usuario en Supabase
      const { data, error } = await supabase.auth.signUp({
        email: values.email,
        password: values.password,
        options: {
          data: {
            full_name: values.fullName,
          },
          // Redirigir para verificación de correo
          emailRedirectTo: `${window.location.origin}/auth/verify`,
        },
      })

      if (error) {
        throw error
      }

      // En un entorno de producción, el usuario tendría que confirmar su correo
      // Para desarrollo, podemos mostrar un mensaje informativo
      console.log("Usuario registrado. En producción, se enviaría un correo de verificación.")

      setRegistrationComplete(true)

      toast({
        title: "Registro exitoso",
        description: "Tu cuenta ha sido creada correctamente.",
      })

      // Redirigir al usuario después de un breve retraso
      setTimeout(() => {
        router.push("/auth/login")
      }, 2000)

    } catch (error: unknown) {
      // Manejar errores específicos de autenticación
      const errorDetails = errorHandler.handleError(error, { action: 'registro' })

      setValidationErrors([errorDetails.message])

      toast({
        variant: "destructive",
        title: "Error al registrarse",
        description: errorDetails.message || "Ocurrió un error inesperado",
      })
    } finally {
      setIsLoading(false)
    }
  }

  if (registrationComplete) {
    return (
      <div className="space-y-4 text-center">
        <h3 className="text-xl font-semibold">¡Registro completado!</h3>
        <p className="text-muted-foreground">
          Tu cuenta ha sido creada correctamente.
        </p>
        <p className="text-sm">
          Serás redirigido a la página de inicio de sesión en unos segundos...
        </p>
        <Button asChild className="mt-4">
          <Link href="/auth/login">Ir a iniciar sesión</Link>
        </Button>
      </div>
    )
  }

  return (
    <Form {...form}>
      <FormValidationAlert errors={validationErrors} />
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="text-sm text-muted-foreground mb-4">
          Todos los campos son obligatorios.
        </div>
        <FormField
          control={form.control}
          name="fullName"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center">
                Nombre completo
                <span className="text-red-500 ml-1">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  placeholder="Tu nombre completo"
                  {...field}
                  className={form.formState.errors.fullName ? "border-red-500" : ""}
                  required
                />
              </FormControl>
              <FormMessage className="text-red-500" />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center">
                Correo electrónico
                <span className="text-red-500 ml-1">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  placeholder="<EMAIL>"
                  type="email"
                  {...field}
                  className={form.formState.errors.email ? "border-red-500" : ""}
                  required
                />
              </FormControl>
              <FormMessage className="text-red-500" />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center">
                Contraseña
                <span className="text-red-500 ml-1">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  type="password"
                  {...field}
                  className={form.formState.errors.password ? "border-red-500" : ""}
                  required
                />
              </FormControl>
              <FormDescription>
                Mínimo 6 caracteres
              </FormDescription>
              <FormMessage className="text-red-500" />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="confirmPassword"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center">
                Confirmar contraseña
                <span className="text-red-500 ml-1">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  type="password"
                  {...field}
                  className={form.formState.errors.confirmPassword ? "border-red-500" : ""}
                  required
                />
              </FormControl>
              <FormMessage className="text-red-500" />
            </FormItem>
          )}
        />

        <Button type="submit" className="w-full" disabled={isLoading}>
          {isLoading ? "Registrando..." : "Crear cuenta"}
        </Button>

        <div className="relative my-4">
          <div className="absolute inset-0 flex items-center">
            <Separator className="w-full" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-background px-2 text-muted-foreground">
              O continúa con
            </span>
          </div>
        </div>

        <div className="space-y-2">
          <GoogleAuthButton mode="signup" fullWidth />
          <GitHubAuthButton mode="signup" fullWidth />
        </div>

        <div className="text-center text-sm">
          ¿Ya tienes una cuenta?{" "}
          <Link href="/auth/login" className="text-primary hover:underline">
            Iniciar sesión
          </Link>
        </div>
      </form>
    </Form>
  )
}
