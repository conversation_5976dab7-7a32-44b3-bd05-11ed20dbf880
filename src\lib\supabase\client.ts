import { createBrowserClient, createServerClient, type CookieOptions } from '@supabase/ssr'
import { cookies } from 'next/headers'

type ReadonlyRequestCookies = ReturnType<typeof cookies>;

import { type Database } from './types'

// Cliente singleton para evitar múltiples instancias
let supabaseClient: ReturnType<typeof createBrowserClient<Database>> | null = null;

// Crear un cliente mock para el servidor que no cause errores
export const createMockClient = () => {
  return new Proxy({} as ReturnType<typeof createBrowserClient<Database>>, {
    get: (_, prop) => {
      // Proporcionar implementaciones de no-op para métodos comunes
      if (prop === 'from') {
        return () => ({
          select: () => ({
            eq: () => ({
              single: () => Promise.resolve({ data: null, error: null }),
            }),
          }),
        });
      }
      if (prop === 'auth') {
        return {
          getUser: () => Promise.resolve({ data: { user: null }, error: null }),
          getSession: () => Promise.resolve({ data: { session: null }, error: null }),
          signOut: () => Promise.resolve({ error: null }),
          onAuthStateChange: (_cb: any) => ({
            data: { subscription: { unsubscribe: () => {} } }
          }),
        };
      }
      if (prop === 'storage') {
        return {
          listBuckets: () => Promise.resolve({ data: [], error: null }),
          getBucket: () => Promise.resolve({ data: null, error: null }),
          createBucket: () => Promise.resolve({ data: null, error: null }),
          from: () => ({
            upload: () => Promise.resolve({ data: null, error: null }),
            download: () => Promise.resolve({ data: null, error: null }),
          }),
        };
      }
      return () => ({});
    }
  });
};

export const createServerSupabaseClient = (cookieStore: ReadonlyRequestCookies) => {
  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          cookieStore.set(name, value, options)
        },
        remove(name: string, options: CookieOptions) {
          cookieStore.set(name, '', options)
        },
      },
    }
  )
}

export const createBrowserSupabaseClient = () => {
  // Verificar si estamos en el navegador
  const isBrowser = typeof window !== 'undefined';

  // Si estamos en el servidor, devolver un cliente mock
  if (!isBrowser) {
    return createMockClient();
  }



  // Si ya existe un cliente, reutilizarlo
  if (supabaseClient) {
    return supabaseClient;
  }

  // Crear un nuevo cliente de Supabase
  const client = createBrowserClient<Database, 'public'>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        persistSession: true,
        autoRefreshToken: true,
        detectSessionInUrl: true,
        storageKey: 'supabase.auth.token', // Clave consistente para almacenamiento
        storage: {
          getItem: (key) => {
            if (isBrowser) {
              try {
                const storedItem = localStorage.getItem(key);
                return storedItem;
              } catch (error) {
                console.warn('Error accessing localStorage:', error);
                return null;
              }
            }
            return null;
          },
          setItem: (key, value) => {
            if (isBrowser) {
              try {
                localStorage.setItem(key, value);
              } catch (error) {
                console.warn('Error writing to localStorage:', error);
              }
            }
          },
          removeItem: (key) => {
            if (isBrowser) {
              try {
                localStorage.removeItem(key);
              } catch (error) {
                console.warn('Error removing from localStorage:', error);
              }
            }
          }
        }
      },
      cookies: {
        get: () => null,
        set: () => {},
        remove: () => {},
      },
      global: {
        // Headers para evitar problemas de caché
        headers: {
          'Cache-Control': 'no-store, max-age=0',
          'X-Client-Info': 'supabase-js/2.38.4',
        },
        // Configuración de fetch para mejorar la estabilidad
        fetch: (url, options) => {
          const fetchOptions = {
            ...options,
            headers: {
              ...options?.headers,
              'X-Client-Info': 'supabase-js/2.38.4',
            },
          };
          return fetch(url, fetchOptions);
        },
      },
      realtime: {
        // Configuración para conexiones en tiempo real
        params: {
          eventsPerSecond: 10,
        },
      },
      db: {
        // Configuración para la base de datos
        schema: 'public',
      },
    }
  );

  // Almacenar el cliente para reutilizarlo (solo en el navegador)
  if (isBrowser) {
    supabaseClient = client;
  }

  return client;
}

export type DBClient = ReturnType<typeof createBrowserSupabaseClient> | ReturnType<typeof createServerSupabaseClient>
export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]

// Helper para extraer tipos de tablas específicas
export type TableRow<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']
export type TableInsert<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert']
export type TableUpdate<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update']

// Tipos comunes
export type Project = TableRow<'projects'>
export type WorkOrder = TableRow<'work_orders'>
export type User = TableRow<'users'>