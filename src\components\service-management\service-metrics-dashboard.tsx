import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { ServiceManagementMetrics, ChartData, RecentActivity } from '@/lib/types/service-management';
import { ServiceManagementService } from '@/lib/services/service-management-service';
import { MetricCard } from '@/components/dashboard/metric-card';
import { PieChart } from '@/components/charts/pie-chart';
import { BarChart } from '@/components/charts/bar-chart';
import { formatDate, formatDateTime } from '@/lib/utils';
import { 
  Wrench, 
  AlertTriangle, 
  Calendar, 
  ClipboardList,
  Clock,
  Activity
} from 'lucide-react';

export function ServiceMetricsDashboard() {
  const [metrics, setMetrics] = useState<ServiceManagementMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchMetrics = async () => {
      try {
        setLoading(true);
        const data = await ServiceManagementService.getServiceManagementMetrics();
        setMetrics(data);
        setError(null);
      } catch (err) {
        console.error('Error al cargar métricas de servicio:', err);
        setError('Error al cargar métricas de servicio. Intente nuevamente.');
      } finally {
        setLoading(false);
      }
    };

    fetchMetrics();
  }, []);

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {Array(4).fill(0).map((_, i) => (
            <Skeleton key={i} className="h-32 w-full" />
          ))}
        </div>
        <div className="grid gap-4 md:grid-cols-2">
          {Array(2).fill(0).map((_, i) => (
            <Skeleton key={i} className="h-80 w-full" />
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="bg-destructive/10 p-4 rounded-md text-destructive">
            {error}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!metrics) {
    return null;
  }

  return (
    <div className="space-y-4">
      {/* Métricas principales */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <MetricCard
          title="Solicitudes de Servicio"
          value={metrics.pending_service_requests_count}
          icon={Wrench}
          iconColor="text-orange-500"
          trend={metrics.service_requests_trend}
          description={`${metrics.service_requests_count} solicitudes totales`}
          href="/dashboard/service-requests"
        />
        <MetricCard
          title="Equipos de Clientes"
          value={metrics.customer_equipment_count}
          icon={ClipboardList}
          iconColor="text-blue-600"
          href="/dashboard/customer-equipment"
        />
        <MetricCard
          title="Mantenimientos Pendientes"
          value={metrics.overdue_maintenance_count}
          icon={AlertTriangle}
          iconColor="text-red-500"
          trend={metrics.maintenance_trend}
          description={`${metrics.maintenance_schedules_count} programados`}
          href="/dashboard/maintenance"
        />
        <MetricCard
          title="Actividades Recientes"
          value={metrics.recent_activities?.length || 0}
          icon={Activity}
          iconColor="text-purple-500"
          href="/dashboard/service-activities"
        />
      </div>

      {/* Gráficos y análisis */}
      <div className="grid gap-4 md:grid-cols-2">
        <Tabs defaultValue="status" className="w-full">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>Solicitudes de Servicio</CardTitle>
              <CardDescription>
                Distribución por estado y prioridad
              </CardDescription>
              <TabsList className="mt-2">
                <TabsTrigger value="status">Por Estado</TabsTrigger>
                <TabsTrigger value="priority">Por Prioridad</TabsTrigger>
              </TabsList>
            </CardHeader>
            <CardContent>
              <TabsContent value="status" className="mt-0 pt-0">
                <div className="h-60">
                  <PieChart 
                    data={metrics.service_requests_by_status || []} 
                    colors={[
                      '#f97316', // orange-500 - pending
                      '#3b82f6', // blue-500 - assigned
                      '#10b981', // emerald-500 - in_progress
                      '#22c55e', // green-500 - completed
                      '#ef4444', // red-500 - cancelled
                    ]}
                  />
                </div>
              </TabsContent>
              <TabsContent value="priority" className="mt-0 pt-0">
                <div className="h-60">
                  <PieChart 
                    data={metrics.service_requests_by_priority || []} 
                    colors={[
                      '#ef4444', // red-500 - critical
                      '#f97316', // orange-500 - high
                      '#3b82f6', // blue-500 - medium
                      '#10b981', // emerald-500 - low
                    ]}
                  />
                </div>
              </TabsContent>
            </CardContent>
          </Card>
        </Tabs>

        <Tabs defaultValue="equipment" className="w-full">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>Equipos y Mantenimiento</CardTitle>
              <CardDescription>
                Estado de equipos y tipos de mantenimiento
              </CardDescription>
              <TabsList className="mt-2">
                <TabsTrigger value="equipment">Equipos</TabsTrigger>
                <TabsTrigger value="maintenance">Mantenimiento</TabsTrigger>
              </TabsList>
            </CardHeader>
            <CardContent>
              <TabsContent value="equipment" className="mt-0 pt-0">
                <div className="h-60">
                  <BarChart 
                    data={metrics.equipment_by_status || []} 
                    colors={[
                      '#22c55e', // green-500 - active
                      '#94a3b8', // slate-400 - inactive
                      '#f97316', // orange-500 - maintenance
                      '#ef4444', // red-500 - retired
                    ]}
                  />
                </div>
              </TabsContent>
              <TabsContent value="maintenance" className="mt-0 pt-0">
                <div className="h-60">
                  <BarChart 
                    data={metrics.maintenance_by_type || []} 
                    colors={[
                      '#3b82f6', // blue-500 - preventive
                      '#f97316', // orange-500 - corrective
                      '#a855f7', // purple-500 - predictive
                    ]}
                  />
                </div>
              </TabsContent>
            </CardContent>
          </Card>
        </Tabs>
      </div>

      {/* Actividades recientes */}
      <Card>
        <CardHeader>
          <CardTitle>Actividades Recientes</CardTitle>
          <CardDescription>
            Últimas actividades de servicio registradas
          </CardDescription>
        </CardHeader>
        <CardContent>
          {metrics.recent_activities && metrics.recent_activities.length > 0 ? (
            <div className="space-y-4">
              {metrics.recent_activities.map((activity) => (
                <div 
                  key={activity.id} 
                  className="flex items-start gap-4 p-3 rounded-lg border border-border hover:bg-muted/50 transition-colors"
                >
                  <div className="bg-primary/10 p-2 rounded-full">
                    <Clock className="h-4 w-4 text-primary" />
                  </div>
                  <div className="flex-1 space-y-1">
                    <div className="flex items-center justify-between">
                      <p className="font-medium">{activity.description}</p>
                      <span className="text-xs text-muted-foreground">
                        {formatDateTime(activity.created_at)}
                      </span>
                    </div>
                    <div className="flex flex-wrap gap-2 text-sm text-muted-foreground">
                      <span className="flex items-center gap-1">
                        <Wrench className="h-3 w-3" />
                        {activity.activity_type}
                      </span>
                      {activity.equipment_name && (
                        <span className="flex items-center gap-1">
                          <ClipboardList className="h-3 w-3" />
                          {activity.equipment_name}
                        </span>
                      )}
                      {activity.client_name && (
                        <span className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {activity.client_name}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-6 text-muted-foreground">
              No hay actividades recientes para mostrar.
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
