import { useState, useEffect } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { ServiceRequest } from '@/lib/types/service-management';
import { ServiceManagementService } from '@/lib/services/service-management-service';
import { formatDate } from '@/lib/utils';
import { useRouter } from 'next/navigation';
import { 
  Clock, 
  AlertTriangle, 
  CheckCircle2, 
  XCircle, 
  ArrowUpRight, 
  User, 
  Calendar 
} from 'lucide-react';

interface ServiceRequestsTableProps {
  pendingOnly?: boolean;
  limit?: number;
  clientId?: string;
  technicianId?: string;
  onRowClick?: (serviceRequest: ServiceRequest) => void;
}

export function ServiceRequestsTable({
  pendingOnly = false,
  limit = 10,
  clientId,
  technicianId,
  onRowClick,
}: ServiceRequestsTableProps) {
  const [serviceRequests, setServiceRequests] = useState<ServiceRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    const fetchServiceRequests = async () => {
      try {
        setLoading(true);
        let data: ServiceRequest[];
        
        if (pendingOnly) {
          data = await ServiceManagementService.getPendingServiceRequests(limit, 0);
        } else {
          data = await ServiceManagementService.getAllServiceRequests(limit, 0);
        }
        
        // Filtrar por cliente si se proporciona clientId
        if (clientId) {
          data = data.filter(sr => sr.client_id === clientId);
        }
        
        // Filtrar por técnico si se proporciona technicianId
        if (technicianId) {
          data = data.filter(sr => sr.assigned_to === technicianId);
        }
        
        setServiceRequests(data);
        setError(null);
      } catch (err) {
        console.error('Error al cargar solicitudes de servicio:', err);
        setError('Error al cargar solicitudes de servicio. Intente nuevamente.');
      } finally {
        setLoading(false);
      }
    };

    fetchServiceRequests();
  }, [pendingOnly, limit, clientId, technicianId]);

  const handleRowClick = (serviceRequest: ServiceRequest) => {
    if (onRowClick) {
      onRowClick(serviceRequest);
    } else {
      router.push(`/dashboard/service-requests/${serviceRequest.id}`);
    }
  };

  const getStatusBadge = (status: string, isOverdue?: boolean) => {
    switch (status) {
      case 'pending':
        return (
          <Badge variant={isOverdue ? "destructive" : "outline"} className="flex gap-1 items-center">
            {isOverdue ? <AlertTriangle className="h-3 w-3" /> : <Clock className="h-3 w-3" />}
            {isOverdue ? 'Vencida' : 'Pendiente'}
          </Badge>
        );
      case 'assigned':
        return (
          <Badge variant="secondary" className="flex gap-1 items-center">
            <User className="h-3 w-3" />
            Asignada
          </Badge>
        );
      case 'in_progress':
        return (
          <Badge variant="default" className="flex gap-1 items-center">
            <ArrowUpRight className="h-3 w-3" />
            En progreso
          </Badge>
        );
      case 'completed':
        return (
          <Badge variant="success" className="flex gap-1 items-center">
            <CheckCircle2 className="h-3 w-3" />
            Completada
          </Badge>
        );
      case 'cancelled':
        return (
          <Badge variant="destructive" className="flex gap-1 items-center">
            <XCircle className="h-3 w-3" />
            Cancelada
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            {status}
          </Badge>
        );
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'low':
        return <Badge variant="outline">Baja</Badge>;
      case 'medium':
        return <Badge variant="secondary">Media</Badge>;
      case 'high':
        return <Badge variant="warning">Alta</Badge>;
      case 'critical':
        return <Badge variant="destructive">Crítica</Badge>;
      default:
        return <Badge variant="outline">{priority}</Badge>;
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Solicitudes de Servicio</CardTitle>
          <CardDescription>
            {pendingOnly ? 'Solicitudes pendientes y en progreso' : 'Todas las solicitudes de servicio'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {Array(5).fill(0).map((_, i) => (
              <Skeleton key={i} className="h-12 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Solicitudes de Servicio</CardTitle>
          <CardDescription>
            {pendingOnly ? 'Solicitudes pendientes y en progreso' : 'Todas las solicitudes de servicio'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-destructive/10 p-4 rounded-md text-destructive">
            {error}
          </div>
          <Button 
            variant="outline" 
            className="mt-4"
            onClick={() => window.location.reload()}
          >
            Reintentar
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Solicitudes de Servicio</CardTitle>
        <CardDescription>
          {pendingOnly ? 'Solicitudes pendientes y en progreso' : 'Todas las solicitudes de servicio'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {serviceRequests.length === 0 ? (
          <div className="text-center py-6 text-muted-foreground">
            No hay solicitudes de servicio para mostrar.
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Título</TableHead>
                  <TableHead>Cliente</TableHead>
                  <TableHead>Estado</TableHead>
                  <TableHead>Prioridad</TableHead>
                  <TableHead>Fecha límite</TableHead>
                  <TableHead>Asignado a</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {serviceRequests.map((serviceRequest) => (
                  <TableRow 
                    key={serviceRequest.id}
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleRowClick(serviceRequest)}
                  >
                    <TableCell className="font-medium">{serviceRequest.title}</TableCell>
                    <TableCell>{serviceRequest.client_name || 'Sin cliente'}</TableCell>
                    <TableCell>{getStatusBadge(serviceRequest.status, serviceRequest.is_overdue)}</TableCell>
                    <TableCell>{getPriorityBadge(serviceRequest.priority)}</TableCell>
                    <TableCell>
                      {serviceRequest.due_date ? (
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {formatDate(serviceRequest.due_date)}
                        </div>
                      ) : (
                        <span className="text-muted-foreground">Sin fecha</span>
                      )}
                    </TableCell>
                    <TableCell>{serviceRequest.assigned_to_name || 'Sin asignar'}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
        <div className="mt-4 flex justify-end">
          <Button 
            variant="outline" 
            onClick={() => router.push('/dashboard/service-requests')}
          >
            Ver todas
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
