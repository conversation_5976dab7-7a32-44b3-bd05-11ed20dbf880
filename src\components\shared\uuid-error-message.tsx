"use client"

import { AlertCircle } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

interface UUIDErrorMessageProps {
  fieldName?: string
  message?: string
  suggestions?: string[]
  onDismiss?: () => void
}

/**
 * Componente para mostrar errores relacionados con UUID
 * 
 * @param fieldName Nombre del campo con error (opcional)
 * @param message Mensaje de error personalizado (opcional)
 * @param suggestions Sugerencias para resolver el error (opcional)
 * @param onDismiss Función para cerrar el mensaje (opcional)
 */
export function UUIDErrorMessage({
  fieldName,
  message,
  suggestions,
  onDismiss
}: UUIDErrorMessageProps) {
  // Determinar el mensaje a mostrar
  const errorMessage = message || (
    fieldName 
      ? `El formato del campo ${fieldName} no es válido` 
      : "El formato del identificador no es válido"
  )
  
  // Sugerencias por defecto si no se proporcionan
  const defaultSuggestions = [
    "Seleccione un valor válido de la lista desplegable",
    "Deje el campo vacío si no aplica",
    "Verifique que el formato del ID sea correcto"
  ]
  
  // Usar las sugerencias proporcionadas o las predeterminadas
  const errorSuggestions = suggestions || defaultSuggestions
  
  return (
    <Alert variant="destructive" className="mb-4">
      <AlertCircle className="h-4 w-4" />
      <AlertTitle>Error de formato</AlertTitle>
      <AlertDescription>
        <div className="space-y-2">
          <p>{errorMessage}</p>
          {errorSuggestions.length > 0 && (
            <ul className="list-disc pl-5 text-sm">
              {errorSuggestions.map((suggestion, index) => (
                <li key={index}>{suggestion}</li>
              ))}
            </ul>
          )}
          {onDismiss && (
            <button
              onClick={onDismiss}
              className="text-xs underline hover:no-underline mt-2"
            >
              Cerrar notificación
            </button>
          )}
        </div>
      </AlertDescription>
    </Alert>
  )
}
