/**
 * @ai-file-description: "Document analyzer service for extracting project information from documents"
 * @ai-related-files: ["provider-interface.ts", "provider-factory.ts", "document-text-extractor.ts"]
 * @ai-owner: "File-Based Projects"
 */

import { DocumentAnalysisResult } from './provider-interface';
import { ProviderFactory } from './provider-factory';
import { extractTextFromDocument } from './document-text-extractor';

/**
 * Configuration for document analysis
 */
export interface DocumentAnalysisConfig {
  providerId: string;
  apiKey: string;
  modelName: string;
  maxTokens?: number;
  temperature?: number;
}

/**
 * Document data for analysis
 */
export interface DocumentData {
  id: string;
  fileName: string;
  fileUrl: string;
  fileType: string;
}

/**
 * Service for analyzing documents and extracting project information
 *
 * @ai-responsibility: "Coordinates document text extraction and AI analysis"
 */
export class DocumentAnalyzer {
  /**
   * Analyzes a document using the specified provider
   *
   * @param document Document data
   * @param config Analysis configuration
   * @returns Analysis result with extracted project information
   */
  static async analyzeDocument(
    document: DocumentData,
    config: DocumentAnalysisConfig
  ): Promise<DocumentAnalysisResult> {
    try {
      console.log(`Starting document analysis with provider: ${config.providerId}`);

      // Validate document data
      if (!document || !document.fileUrl) {
        console.error('Invalid document data:', document);
        return this.createErrorResult('Invalid document data. Missing file URL.');
      }

      // Extract text from document
      console.log(`Extracting text from ${document.fileType} document: ${document.fileName}`);
      const documentText = await extractTextFromDocument(document.fileUrl, document.fileType);

      if (!documentText || documentText.trim() === '') {
        console.error('Failed to extract text from document');
        return this.createErrorResult('Failed to extract text from document. The document may be empty or in an unsupported format.');
      }

      // Verificar si el texto contiene un mensaje de error
      if (documentText.startsWith('Error al extraer texto') ||
          documentText.startsWith('El documento parece estar vacío') ||
          documentText.startsWith('PDF extraction not supported')) {
        console.error('Text extraction returned an error message:', documentText);
        return this.createErrorResult(documentText);
      }

      console.log(`Successfully extracted ${documentText.length} characters of text`);

      // Create provider instance
      try {
        console.log('Config passed to ProviderFactory.createProvider:', config); // Log config
        const provider = ProviderFactory.createProvider(config.providerId, {
          apiKey: config.apiKey,
          modelName: config.modelName,
          maxTokens: config.maxTokens,
          temperature: config.temperature
        });

        // Validate provider configuration
        if (!provider.validateConfig()) {
          console.error(`Invalid configuration for provider: ${config.providerId}`);
          return this.createErrorResult(`Invalid configuration for provider: ${config.providerId}. Please check API key and model name.`);
        }

        // Analyze document with provider
        console.log(`Analyzing document with provider: ${config.providerId}`);
        const analysisResult = await provider.analyzeDocument(documentText);

        // Check if the result contains an error message
        if (analysisResult.error_message) {
          console.warn(`Provider returned an error: ${analysisResult.error_message}`);
          // We still return the result as it may contain partial information
        } else {
          console.log('Document analysis completed successfully');
        }

        return analysisResult;
      } catch (providerError) {
        console.error(`Error with provider ${config.providerId}:`, providerError);
        return this.createErrorResult(`Error with AI provider ${config.providerId}: ${providerError instanceof Error ? providerError.message : 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error analyzing document:', error);
      return this.createErrorResult(`Unexpected error during document analysis: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Analyzes a document with fallback to other providers if the primary provider fails
   *
   * @param document Document data
   * @param configs Array of provider configurations in priority order
   * @returns Analysis result with extracted project information
   */
  static async analyzeDocumentWithFallback(
    document: DocumentData,
    configs: DocumentAnalysisConfig[]
  ): Promise<DocumentAnalysisResult> {
    if (!configs || configs.length === 0) {
      console.error('No provider configurations specified');
      return this.createErrorResult('No AI provider configurations specified. Please configure at least one provider.');
    }

    let lastError = '';
    const partialResults: DocumentAnalysisResult[] = [];

    // Try each provider in order until one succeeds without errors
    for (const config of configs) {
      try {
        console.log(`Trying provider: ${config.providerId}`);
        const result = await this.analyzeDocument(document, config);

        // If the result has no error, return it immediately
        if (!result.error_message) {
          console.log(`Provider ${config.providerId} succeeded`);
          return result;
        }

        // Otherwise, store the result and continue to the next provider
        console.warn(`Provider ${config.providerId} returned an error: ${result.error_message}`);
        lastError = result.error_message;
        partialResults.push(result);
      } catch (error) {
        console.error(`Provider ${config.providerId} failed:`, error);
        lastError = error instanceof Error ? error.message : 'Unknown error';
        // Continue to next provider
      }
    }

    // If we have partial results, return the one with the highest confidence score
    if (partialResults.length > 0) {
      console.log(`All providers had errors, but we have ${partialResults.length} partial results`);
      // Sort by confidence score (highest first)
      partialResults.sort((a, b) => b.confidence_score - a.confidence_score);
      return partialResults[0];
    }

    // If we get here, all providers failed completely
    console.error('All providers failed completely');
    return this.createErrorResult(`All AI providers failed. Last error: ${lastError}`);
  }

  /**
   * Creates an error result when document analysis fails
   *
   * @param errorMessage The error message
   * @returns A basic document analysis result with error information
   */
  private static createErrorResult(errorMessage: string): DocumentAnalysisResult {
    console.log('Creating error result:', errorMessage);

    // Get current date
    const today = new Date();
    const startDate = new Date(today);
    startDate.setDate(today.getDate() + 7); // Start in a week

    const endDate = new Date(startDate);
    endDate.setMonth(startDate.getMonth() + 3); // End in 3 months

    return {
      project_name: 'New Project',
      description: `This is a fallback project created because document analysis failed. Error: ${errorMessage}`,
      start_date: startDate.toISOString().split('T')[0],
      end_date: endDate.toISOString().split('T')[0],
      budget: '10000',
      currency: 'CLP',
      client_name: null,
      deliverables: ['Documentation', 'Implementation', 'Testing'],
      scope: 'Default project scope. Please update with actual information.',
      team_requirements: ['Project Manager', 'Developer'],
      tags: ['default'],
      confidence_score: 0.1, // Low confidence since this is a fallback
      error_message: errorMessage
    };
  }
}
