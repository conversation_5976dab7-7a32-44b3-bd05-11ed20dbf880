{"name": "admin4humans-web", "version": "0.1.0", "private": true, "packageManager": "npm@10.9.0", "engines": {"node": ">=18.17.0", "npm": ">=9.6.7"}, "scripts": {"dev": "next dev --turbo", "dev:turbo": "next dev --turbo", "turbo:enable": "node turbo-toggle.js enable", "turbo:disable": "node turbo-toggle.js disable", "turbo:status": "node turbo-toggle.js status", "dev:legacy": "npx cross-env NODE_OPTIONS=\"--inspect\" next dev", "dev:debug": "npx cross-env DEBUG=* NODE_OPTIONS=\"--inspect\" next dev", "dev:clean": "node dev-clean.js", "clean:next": "node clean-next.js", "dev:fast": "npx cross-env NODE_OPTIONS=\"--max-old-space-size=4096 --no-warnings\" next dev", "dev:simple": "node node_modules/next/dist/bin/next dev", "reinstall": "node reinstall.js", "dev:custom": "node server.js", "setup:babel": "node setup-babel.js", "setup:dashboard": "node scripts/setup-dashboard.js", "build": "next build", "build:turbo": "turbo build", "vercel-build": "next build", "start": "next start", "lint": "next lint", "test": "vitest", "test:ci": "vitest run --coverage", "coverage": "vitest run --coverage", "clean": "node -e \"const fs=require('fs');const path=require('path');const nextDir=path.join(process.cwd(),'.next');if(fs.existsSync(nextDir)){fs.rmSync(nextDir,{recursive:true,force:true});console.log('.next directory removed');}else{console.log('.next directory not found');}\"", "setup:env": "bash scripts/setup-env.sh", "deploy:staging": "vercel --target staging", "deploy:prod": "vercel --prod", "deploy:ci": "bash scripts/deploy.sh", "upgrade:next15": "node scripts/upgrade-to-next15.js", "postinstall": "prisma generate"}, "dependencies": {"@google/generative-ai": "^0.24.0", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^3.3.2", "@prisma/client": "^5.12.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-visually-hidden": "^1.2.2", "@sentry/nextjs": "9.27.0", "@sentry/node": "9.27.0", "@sentry/react": "9.27.0", "@supabase/node-fetch": "2.6.15", "@supabase/ssr": "^0.1.0", "@supabase/supabase-js": "^2.39.7", "@tanstack/react-query": "^5.75.4", "@tanstack/react-table": "^8.10.7", "@types/diff": "^7.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "critters": "^0.0.23", "date-fns": "^3.6.0", "dexie": "^4.0.11", "dexie-react-hooks": "^1.1.7", "diff": "^7.0.0", "dotenv": "^16.5.0", "geist": "^1.3.1", "lucide-react": "^0.507.0", "mammoth": "^1.9.0", "next": "^15.1.0", "next-themes": "^0.4.4", "node-fetch": "^3.3.2", "openai": "^4.95.1", "pdfjs-dist": "^4.8.69", "prisma": "^5.22.0", "react": "^19.0.0", "react-day-picker": "^9.4.2", "react-dom": "^19.0.0", "react-hook-form": "^7.49.2", "react-icons": "^5.5.0", "recharts": "^2.10.3", "string-similarity": "^4.0.4", "tailwind-merge": "^3.2.0", "uuid": "^9.0.1", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@babel/plugin-transform-class-properties": "7.27.1", "@babel/plugin-transform-private-methods": "7.27.1", "@babel/plugin-transform-private-property-in-object": "7.27.1", "@playwright/test": "1.52.0", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.2", "@types/jest": "29.5.14", "@types/node": "^22.10.1", "@types/react": "^19.0.1", "@types/react-dom": "^19.0.1", "@types/string-similarity": "4.0.2", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^8.18.1", "@typescript-eslint/parser": "^8.18.1", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "^1.3.1", "autoprefixer": "^10.4.17", "cross-env": "7.0.3", "eslint": "^9.17.0", "eslint-config-next": "^15.1.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react-hooks": "5.2.0", "jsdom": "^23.0.1", "msw": "^2.2.2", "postcss": "^8.4.35", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9", "rimraf": "^5.0.5", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "turbo": "^2.3.3", "typescript": "^5.7.2", "vite": "^5.0.10", "vitest": "^1.3.1"}, "vercel": {"ignoreBuildErrors": false}, "optionalDependencies": {"@esbuild/win32-x64": "0.25.5", "canvas": "^2.11.2"}, "overrides": {"jsdom": {"canvas": "^2.11.2"}}}