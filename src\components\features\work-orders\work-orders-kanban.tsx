"use client"

import { useState } from "react"
import { <PERSON>agDropContext, Droppable, Draggable } from "@hello-pangea/dnd"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Calendar, Clock } from "lucide-react"
import { format } from "date-fns"
import { es } from "date-fns/locale"
import { createClient } from "@/lib/supabase/client"
import { toast } from "@/components/ui/use-toast"
import Link from "next/link"

// Tipos
interface WorkOrder {
  id: string
  title: string
  description?: string
  status: string
  priority: string
  assigned_to?: string
  project_id?: string
  due_date?: string
  created_at: string
  updated_at: string
  project?: {
    name: string
  }
  assigned_user?: {
    full_name?: string
    email: string
  }
  assigned_users?: {
    user_id: string
    role: string
    user: {
      id: string
      email: string
      full_name?: string
    }
  }[]
}

// Función para obtener el color de la prioridad
const getPriorityColor = (priority: string) => {
  switch (priority) {
    case "low":
      return "bg-green-500"
    case "medium":
      return "bg-yellow-500"
    case "high":
      return "bg-orange-500"
    case "critical":
      return "bg-red-500"
    default:
      return "bg-gray-500"
  }
}

// Función para obtener el texto de la prioridad
const getPriorityText = (priority: string) => {
  switch (priority) {
    case "low":
      return "Baja"
    case "medium":
      return "Media"
    case "high":
      return "Alta"
    case "critical":
      return "Crítica"
    default:
      return priority
  }
}

interface WorkOrdersKanbanProps {
  data: WorkOrder[]
}

export function WorkOrdersKanban({ data }: WorkOrdersKanbanProps) {
  const [workOrders, setWorkOrders] = useState<WorkOrder[]>(data)
  const supabase = createClient()

  // Agrupar órdenes por estado
  const columns = {
    pending: {
      id: "pending",
      title: "Pendientes",
      items: workOrders.filter((order) => order.status === "pending"),
    },
    in_progress: {
      id: "in_progress",
      title: "En Progreso",
      items: workOrders.filter((order) => order.status === "in_progress"),
    },
    completed: {
      id: "completed",
      title: "Completadas",
      items: workOrders.filter((order) => order.status === "completed"),
    },
    cancelled: {
      id: "cancelled",
      title: "Canceladas",
      items: workOrders.filter((order) => order.status === "cancelled"),
    },
  }

  const onDragEnd = async (result: unknown) => {
    if (!result.destination) return

    const { source, destination, draggableId } = result

    // Si no cambió de columna, no hacemos nada
    if (source.droppableId === destination.droppableId) return

    // Actualizar el estado de la orden en la base de datos
    try {
      const { error } = await supabase
        .from("work_orders")
        .update({ status: destination.droppableId })
        .eq("id", draggableId)

      if (error) throw error

      // Actualizar el estado local
      setWorkOrders(
        workOrders.map((order) =>
          order.id === draggableId
            ? { ...order, status: destination.droppableId }
            : order
        )
      )

      toast({
        title: "Orden actualizada",
        description: `La orden ha sido movida a "${
          destination.droppableId === "pending"
            ? "Pendientes"
            : destination.droppableId === "in_progress"
            ? "En Progreso"
            : destination.droppableId === "completed"
            ? "Completadas"
            : "Canceladas"
        }"`,
      })
    } catch (error) {
      console.error("Error al actualizar la orden:", error)
      toast({
        title: "Error",
        description: "No se pudo actualizar el estado de la orden",
        variant: "destructive",
      })
    }
  }

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {Object.values(columns).map((column) => (
          <div key={column.id} className="flex flex-col h-full">
            <h3 className="font-medium text-lg mb-3">{column.title}</h3>
            <Droppable droppableId={column.id}>
              {(provided) => (
                <div
                  {...provided.droppableProps}
                  ref={provided.innerRef}
                  className="bg-muted/50 rounded-md p-2 flex-1 min-h-[500px]"
                >
                  {column.items.map((item, index) => (
                    <Draggable
                      key={item.id}
                      draggableId={item.id}
                      index={index}
                    >
                      {(provided) => (
                        <Card
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          {...provided.dragHandleProps}
                          className="mb-3 cursor-grab active:cursor-grabbing"
                        >
                          <CardHeader className="p-3 pb-0">
                            <CardTitle className="text-base">
                              <Link
                                href={`/dashboard/work-orders/${item.id}`}
                                className="hover:underline"
                              >
                                {item.title}
                              </Link>
                            </CardTitle>
                          </CardHeader>
                          <CardContent className="p-3 pt-2">
                            {item.description && (
                              <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                                {item.description}
                              </p>
                            )}
                            <div className="flex flex-wrap gap-2 mb-3">
                              <Badge className={getPriorityColor(item.priority)}>
                                {getPriorityText(item.priority)}
                              </Badge>
                              {item.project && (
                                <Badge variant="outline">
                                  {item.project.name}
                                </Badge>
                              )}
                            </div>
                            <div className="flex justify-between items-center text-xs text-muted-foreground">
                              <div className="flex items-center">
                                {item.due_date && (
                                  <div className="flex items-center mr-3">
                                    <Calendar className="h-3 w-3 mr-1" />
                                    {format(new Date(item.due_date), "dd/MM/yy", {
                                      locale: es,
                                    })}
                                  </div>
                                )}
                                <div className="flex items-center">
                                  <Clock className="h-3 w-3 mr-1" />
                                  {format(
                                    new Date(item.created_at),
                                    "dd/MM/yy",
                                    { locale: es }
                                  )}
                                </div>
                              </div>
                              <div className="flex -space-x-2">
                                {item.assigned_users && item.assigned_users.length > 0 ? (
                                  // Mostrar hasta 3 avatares de usuarios asignados
                                  item.assigned_users.slice(0, 3).map((assignedUser) => (
                                    <Avatar key={assignedUser.user_id} className="h-6 w-6 border-2 border-background">
                                      <AvatarFallback className="text-[10px]">
                                        {assignedUser.user.full_name
                                          ? assignedUser.user.full_name
                                              .split(" ")
                                              .map((n) => n[0])
                                              .join("")
                                              .toUpperCase()
                                              .substring(0, 2)
                                          : assignedUser.user.email
                                              .substring(0, 2)
                                              .toUpperCase()}
                                      </AvatarFallback>
                                    </Avatar>
                                  ))
                                ) : item.assigned_user ? (
                                  // Fallback para el sistema anterior
                                  <Avatar className="h-6 w-6">
                                    <AvatarFallback className="text-[10px]">
                                      {item.assigned_user.full_name
                                        ? item.assigned_user.full_name
                                            .split(" ")
                                            .map((n) => n[0])
                                            .join("")
                                            .toUpperCase()
                                            .substring(0, 2)
                                        : item.assigned_user.email
                                            .substring(0, 2)
                                            .toUpperCase()}
                                    </AvatarFallback>
                                  </Avatar>
                                ) : null}
                                {/* Indicador de usuarios adicionales */}
                                {item.assigned_users && item.assigned_users.length > 3 && (
                                  <Avatar className="h-6 w-6 border-2 border-background bg-muted">
                                    <AvatarFallback className="text-[10px]">
                                      +{item.assigned_users.length - 3}
                                    </AvatarFallback>
                                  </Avatar>
                                )}
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                  {column.items.length === 0 && (
                    <div className="text-center py-4 text-sm text-muted-foreground">
                      No hay órdenes en esta columna
                    </div>
                  )}
                </div>
              )}
            </Droppable>
          </div>
        ))}
      </div>
    </DragDropContext>
  )
}
