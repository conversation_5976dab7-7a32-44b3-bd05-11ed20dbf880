"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Loader2, RefreshCw, Play, Square, Database, Server } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import { toast } from "@/hooks/use-toast"
import { createClient } from "@/lib/supabase/client"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

// Esquema de validación para el formulario
const serviceSettingsSchema = z.object({
  pdf_extractor_url: z.string().url({
    message: "Debe ser una URL válida.",
  }),
  lmstudio_connector_url: z.string().url({
    message: "Debe ser una URL válida.",
  }),
  document_analyzer_url: z.string().url({
    message: "Debe ser una URL válida.",
  }),
  cache_enabled: z.boolean().default(true),
  cache_ttl_hours: z.number().min(1).max(168).default(24),
  max_file_size_mb: z.number().min(1).max(100).default(10),
  allowed_file_types: z.string(),
  auto_start_service: z.boolean().default(true),
});

// Tipo para el estado del servicio
type ServiceStatus = {
  status: "running" | "stopped" | "error" | "unknown";
  pdf_extractor: "OK" | "error" | "unknown";
  lmstudio_connector: "OK" | "error" | "unknown";
  cache: {
    entries: number;
    size_mb: number;
  };
  tasks_in_progress: number;
  last_checked: string;
};

export function DocumentAnalysisServiceSettings() {
  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [isStarting, setIsStarting] = useState(false)
  const [isStopping, setIsStopping] = useState(false)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [serviceStatus, setServiceStatus] = useState<ServiceStatus>({
    status: "unknown",
    pdf_extractor: "unknown",
    lmstudio_connector: "unknown",
    cache: {
      entries: 0,
      size_mb: 0
    },
    tasks_in_progress: 0,
    last_checked: new Date().toISOString()
  })
  const [availableModels, setAvailableModels] = useState<{id: string, name: string}[]>([])
  const [selectedModel, setSelectedModel] = useState<string>("")
  const [documentTypes, setDocumentTypes] = useState<{id: string, name: string}[]>([])

  const supabase = createClient()

  // Inicializar el formulario con valores por defecto
  const form = useForm<z.infer<typeof serviceSettingsSchema>>({
    resolver: zodResolver(serviceSettingsSchema),
    defaultValues: {
      pdf_extractor_url: "http://localhost:8001",
      lmstudio_connector_url: "http://localhost:8003",
      document_analyzer_url: "http://localhost:8002",
      cache_enabled: true,
      cache_ttl_hours: 24,
      max_file_size_mb: 10,
      allowed_file_types: ".pdf,.doc,.docx,.txt",
      auto_start_service: true,
    },
  })

  // Cargar la configuración actual al montar el componente
  useEffect(() => {
    if (supabase) {
      checkServiceStatus();
      loadServiceSettings();
    }
    loadAvailableModels();
    loadDocumentTypes();
  }, [supabase, checkServiceStatus, loadServiceSettings, loadAvailableModels, loadDocumentTypes]);

  // Cargar la configuración del servicio
  const loadServiceSettings = async () => {
    setIsLoading(true)
    try {
      // Intentar cargar la configuración desde la API
      const response = await fetch('/api/document-analysis/config')

      if (response.ok) {
        const data = await response.json()
        // Actualizar el formulario con los datos cargados
        form.reset({
          pdf_extractor_url: data.pdf_extractor_url,
          lmstudio_connector_url: data.lmstudio_connector_url,
          document_analyzer_url: data.document_analyzer_url,
          cache_enabled: data.cache_enabled,
          cache_ttl_hours: data.cache_ttl_hours,
          max_file_size_mb: data.max_file_size_mb,
          allowed_file_types: data.allowed_file_types,
          auto_start_service: data.auto_start_service,
        })
      } else {
        // Si no se puede cargar, usar valores por defecto
        console.warn('No se pudo cargar la configuración del servicio, usando valores por defecto')
      }
    } catch (error) {
      console.error('Error al cargar la configuración:', error)
      toast({
        title: "Error",
        description: "No se pudo cargar la configuración del servicio.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Verificar el estado del servicio
  const checkServiceStatus = async () => {
    setIsRefreshing(true)
    try {
      // Intentar verificar el estado del servicio
      const response = await fetch('/api/document-analysis/status')

      if (response.ok) {
        const data = await response.json()
        setServiceStatus({
          status: data.status,
          pdf_extractor: data.pdf_extractor,
          lmstudio_connector: data.lmstudio_connector,
          cache: data.cache,
          tasks_in_progress: data.tasks_in_progress,
          last_checked: new Date().toISOString()
        })
      } else {
        // Si no se puede verificar, marcar como desconocido
        setServiceStatus({
          ...serviceStatus,
          status: "unknown",
          last_checked: new Date().toISOString()
        })
      }
    } catch (error) {
      console.error('Error al verificar el estado del servicio:', error)
      setServiceStatus({
        ...serviceStatus,
        status: "error",
        last_checked: new Date().toISOString()
      })
    } finally {
      setIsRefreshing(false)
    }
  }

  // Cargar modelos disponibles
  const loadAvailableModels = async () => {
    try {
      const response = await fetch('/api/document-analysis/models')

      if (response.ok) {
        const data = await response.json()
        setAvailableModels(data.models || [])
        if (data.models && data.models.length > 0) {
          setSelectedModel(data.models[0].id)
        }
      }
    } catch (error) {
      console.error('Error al cargar los modelos disponibles:', error)
    }
  }

  // Cargar tipos de documentos
  const loadDocumentTypes = async () => {
    try {
      const response = await fetch('/api/document-analysis/document-types')

      if (response.ok) {
        const data = await response.json()
        setDocumentTypes(data.document_types || [])
      }
    } catch (error) {
      console.error('Error al cargar los tipos de documentos:', error)
    }
  }

  // Guardar la configuración
  const onSubmit = async (values: z.infer<typeof serviceSettingsSchema>) => {
    setIsSaving(true)
    try {
      // Guardar la configuración a través de la API
      const response = await fetch('/api/document-analysis/config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      })

      if (response.ok) {
        toast({
          title: "Configuración guardada",
          description: "La configuración del servicio se ha guardado correctamente.",
        })
      } else {
        const error = await response.json()
        const errorMessage = error instanceof Error ? error.message : 'Error al guardar la configuración';

        throw new Error(errorMessage)
      }
    } catch (error) {
      console.error('Error al guardar la configuración:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "No se pudo guardar la configuración del servicio.",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  // Iniciar el servicio
  const startService = async () => {
    setIsStarting(true)
    try {
      const response = await fetch('/api/document-analysis/start', {
        method: 'POST',
      })

      if (response.ok) {
        toast({
          title: "Servicio iniciado",
          description: "El servicio de análisis de documentos se ha iniciado correctamente.",
        })
        // Actualizar el estado del servicio
        await checkServiceStatus()
      } else {
        const error = await response.json()
        const errorMessage = error instanceof Error ? error.message : 'Error al iniciar el servicio';

        throw new Error(errorMessage)
      }
    } catch (error) {
      console.error('Error al iniciar el servicio:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "No se pudo iniciar el servicio.",
        variant: "destructive",
      })
    } finally {
      setIsStarting(false)
    }
  }

  // Detener el servicio
  const stopService = async () => {
    setIsStopping(true)
    try {
      const response = await fetch('/api/document-analysis/stop', {
        method: 'POST',
      })

      if (response.ok) {
        toast({
          title: "Servicio detenido",
          description: "El servicio de análisis de documentos se ha detenido correctamente.",
        })
        // Actualizar el estado del servicio
        await checkServiceStatus()
      } else {
        const error = await response.json()
        const errorMessage = error instanceof Error ? error.message : 'Error al detener el servicio';

        throw new Error(errorMessage)
      }
    } catch (error) {
      console.error('Error al detener el servicio:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "No se pudo detener el servicio.",
        variant: "destructive",
      })
    } finally {
      setIsStopping(false)
    }
  }

  // Limpiar la caché
  const clearCache = async () => {
    try {
      const response = await fetch('/api/document-analysis/clear-cache', {
        method: 'POST',
      })

      if (response.ok) {
        toast({
          title: "Caché limpiada",
          description: "La caché del servicio se ha limpiado correctamente.",
        })
        // Actualizar el estado del servicio
        await checkServiceStatus()
      } else {
        const error = await response.json()
        const errorMessage = error instanceof Error ? error.message : 'Error al limpiar la caché';

        throw new Error(errorMessage)
      }
    } catch (error) {
      console.error('Error al limpiar la caché:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "No se pudo limpiar la caché.",
        variant: "destructive",
      })
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium">Servicio de Análisis de Documentos</h3>
          <p className="text-sm text-muted-foreground">
            Configura y monitorea el servicio de análisis de documentos.
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge
            variant={
              serviceStatus.status === "running" ? "success" :
              serviceStatus.status === "stopped" ? "secondary" :
              serviceStatus.status === "error" ? "destructive" :
              "outline"
            }
          >
            {serviceStatus.status === "running" ? "En ejecución" :
             serviceStatus.status === "stopped" ? "Detenido" :
             serviceStatus.status === "error" ? "Error" :
             "Desconocido"}
          </Badge>
          <Button
            variant="outline"
            size="sm"
            onClick={checkServiceStatus}
            disabled={isRefreshing}
          >
            {isRefreshing ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>

      <Tabs defaultValue="status" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="status">Estado</TabsTrigger>
          <TabsTrigger value="configuration">Configuración</TabsTrigger>
          <TabsTrigger value="models">Modelos y Tipos</TabsTrigger>
        </TabsList>

        <TabsContent value="status">
          <Card>
            <CardHeader>
              <CardTitle>Estado del Servicio</CardTitle>
              <CardDescription>
                Información sobre el estado actual del servicio de análisis de documentos.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm font-medium">PDF Extractor:</span>
                    <Badge
                      variant={
                        serviceStatus.pdf_extractor === "OK" ? "success" :
                        serviceStatus.pdf_extractor === "error" ? "destructive" :
                        "outline"
                      }
                    >
                      {serviceStatus.pdf_extractor === "OK" ? "Conectado" :
                       serviceStatus.pdf_extractor === "error" ? "Error" :
                       "Desconocido"}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm font-medium">LM Studio Connector:</span>
                    <Badge
                      variant={
                        serviceStatus.lmstudio_connector === "OK" ? "success" :
                        serviceStatus.lmstudio_connector === "error" ? "destructive" :
                        "outline"
                      }
                    >
                      {serviceStatus.lmstudio_connector === "OK" ? "Conectado" :
                       serviceStatus.lmstudio_connector === "error" ? "Error" :
                       "Desconocido"}
                    </Badge>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm font-medium">Entradas en caché:</span>
                    <span className="text-sm">{serviceStatus.cache.entries}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm font-medium">Tamaño de caché:</span>
                    <span className="text-sm">{serviceStatus.cache.size_mb} MB</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm font-medium">Tareas en progreso:</span>
                    <span className="text-sm">{serviceStatus.tasks_in_progress}</span>
                  </div>
                </div>
              </div>
              <div className="text-xs text-muted-foreground text-right">
                Última actualización: {new Date(serviceStatus.last_checked).toLocaleString()}
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <div>
                <Button
                  variant="outline"
                  onClick={clearCache}
                  disabled={serviceStatus.status !== "running"}
                >
                  <Database className="mr-2 h-4 w-4" />
                  Limpiar Caché
                </Button>
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  onClick={stopService}
                  disabled={serviceStatus.status !== "running" || isStopping}
                >
                  {isStopping ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <Square className="mr-2 h-4 w-4" />
                  )}
                  Detener
                </Button>
                <Button
                  onClick={startService}
                  disabled={serviceStatus.status === "running" || isStarting}
                >
                  {isStarting ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <Play className="mr-2 h-4 w-4" />
                  )}
                  Iniciar
                </Button>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="configuration">
          <Card>
            <CardHeader>
              <CardTitle>Configuración del Servicio</CardTitle>
              <CardDescription>
                Configura los parámetros del servicio de análisis de documentos.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="pdf_extractor_url"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>URL del PDF Extractor</FormLabel>
                          <FormControl>
                            <Input placeholder="http://localhost:8001" {...field} />
                          </FormControl>
                          <FormDescription>
                            URL del servicio de extracción de PDF.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="lmstudio_connector_url"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>URL del LM Studio Connector</FormLabel>
                          <FormControl>
                            <Input placeholder="http://localhost:8003" {...field} />
                          </FormControl>
                          <FormDescription>
                            URL del servicio de conexión con LM Studio.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="document_analyzer_url"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>URL del Document Analyzer</FormLabel>
                          <FormControl>
                            <Input placeholder="http://localhost:8002" {...field} />
                          </FormControl>
                          <FormDescription>
                            URL del servicio principal de análisis de documentos.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="max_file_size_mb"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Tamaño máximo de archivo (MB)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder="10"
                              {...field}
                              onChange={(e) => field.onChange(parseInt(e.target.value))}
                            />
                          </FormControl>
                          <FormDescription>
                            Tamaño máximo de archivo permitido en MB.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="allowed_file_types"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Tipos de archivo permitidos</FormLabel>
                          <FormControl>
                            <Input placeholder=".pdf,.doc,.docx,.txt" {...field} />
                          </FormControl>
                          <FormDescription>
                            Lista de extensiones de archivo permitidas separadas por comas.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="cache_ttl_hours"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Tiempo de vida de caché (horas)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder="24"
                              {...field}
                              onChange={(e) => field.onChange(parseInt(e.target.value))}
                            />
                          </FormControl>
                          <FormDescription>
                            Tiempo que los resultados permanecen en caché.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="cache_enabled"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">
                              Caché habilitada
                            </FormLabel>
                            <FormDescription>
                              Habilitar el almacenamiento en caché de resultados.
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="auto_start_service"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">
                              Inicio automático
                            </FormLabel>
                            <FormDescription>
                              Iniciar el servicio automáticamente al arrancar.
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>
                  <Button type="submit" disabled={isSaving}>
                    {isSaving ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Guardando...
                      </>
                    ) : (
                      "Guardar configuración"
                    )}
                  </Button>
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="models">
          <Card>
            <CardHeader>
              <CardTitle>Modelos y Tipos de Documentos</CardTitle>
              <CardDescription>
                Gestiona los modelos de IA y tipos de documentos disponibles.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h4 className="text-sm font-medium">Modelos disponibles</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {availableModels.length > 0 ? (
                    availableModels.map((model) => (
                      <div
                        key={model.id}
                        className="flex items-center justify-between p-3 border rounded-md"
                      >
                        <div className="font-medium">{model.name}</div>
                        <Badge variant="outline">{model.id}</Badge>
                      </div>
                    ))
                  ) : (
                    <div className="col-span-2 text-center text-sm text-muted-foreground py-4">
                      No hay modelos disponibles
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="text-sm font-medium">Tipos de documentos</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {documentTypes.length > 0 ? (
                    documentTypes.map((type) => (
                      <div
                        key={type.id}
                        className="flex items-center justify-between p-3 border rounded-md"
                      >
                        <div className="font-medium">{type.name}</div>
                        <Badge variant="outline">{type.id}</Badge>
                      </div>
                    ))
                  ) : (
                    <div className="col-span-3 text-center text-sm text-muted-foreground py-4">
                      No hay tipos de documentos disponibles
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="text-sm font-medium">Modelo predeterminado</h4>
                <Select value={selectedModel} onValueChange={setSelectedModel}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecciona un modelo" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableModels.map((model) => (
                      <SelectItem key={model.id} value={model.id}>
                        {model.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button
                  className="w-full"
                  disabled={!selectedModel}
                  onClick={() => {
                    toast({
                      title: "Modelo predeterminado actualizado",
                      description: `Se ha establecido ${selectedModel} como modelo predeterminado.`,
                    })
                  }}
                >
                  Establecer como predeterminado
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
