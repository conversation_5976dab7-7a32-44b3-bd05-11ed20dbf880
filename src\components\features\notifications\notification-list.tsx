"use client"

import { useState, useEffect } from 'react'
import { Bell, Check, ExternalLink } from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { es } from 'date-fns/locale'
import Link from 'next/link'

import {
  getUserNotifications,
  markNotificationAsRead,
  type Notification
} from '@/lib/services/notification-service'

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'

interface NotificationListProps {
  limit?: number
  showHeader?: boolean
  showFooter?: boolean
  className?: string
}

export function NotificationList({
  limit = 5,
  showHeader = true,
  showFooter = true,
  className = ''
}: NotificationListProps) {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Cargar notificaciones
  useEffect(() => {
    async function loadNotifications() {
      try {
        setLoading(true)
        setError(null)
        const data = await getUserNotifications(limit)
        setNotifications(data)
      } catch (err) {
        console.error('Error al cargar notificaciones:', err)
        setError('No se pudieron cargar las notificaciones')
      } finally {
        setLoading(false)
      }
    }

    loadNotifications()
  }, [limit])

  // Marcar como leída
  const handleMarkAsRead = async (id: string) => {
    try {
      const success = await markNotificationAsRead(id)
      if (success) {
        // Actualizar el estado local
        setNotifications(prev =>
          prev.map(notification =>
            notification.id === id
              ? { ...notification, read: true, updated_at: new Date() }
              : notification
          )
        )
      }
    } catch (err) {
      console.error('Error al marcar notificación como leída:', err)
    }
  }

  // Renderizar el tipo de notificación
  const renderNotificationType = (type: string | undefined) => {
    if (!type) return null

    const typeMap: Record<string, { color: string, label: string }> = {
      'info': { color: 'bg-blue-100 text-blue-800', label: 'Info' },
      'success': { color: 'bg-green-100 text-green-800', label: 'Éxito' },
      'warning': { color: 'bg-yellow-100 text-yellow-800', label: 'Advertencia' },
      'error': { color: 'bg-red-100 text-red-800', label: 'Error' },
      'project': { color: 'bg-purple-100 text-purple-800', label: 'Proyecto' },
      'document': { color: 'bg-emerald-100 text-emerald-800', label: 'Documento' },
      'task': { color: 'bg-pink-100 text-pink-800', label: 'Tarea' },
    }

    const typeInfo = typeMap[type] || { color: 'bg-gray-100 text-gray-800', label: type }

    return (
      <Badge variant="outline" className={`${typeInfo.color} ml-2`}>
        {typeInfo.label}
      </Badge>
    )
  }

  // Usar la utilidad centralizada para formatear tiempo relativo
  import { formatRelativeTime } from '@/lib/utils';

  return (
    <Card className={className}>
      {showHeader && (
        <CardHeader>
          <CardTitle className="flex items-center">
            <Bell className="mr-2 h-5 w-5" />
            Notificaciones
          </CardTitle>
          <CardDescription>
            Tus notificaciones recientes
          </CardDescription>
        </CardHeader>
      )}
      <CardContent>
        {loading ? (
          // Esqueleto de carga
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="flex items-start space-x-4">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                </div>
              </div>
            ))}
          </div>
        ) : error ? (
          // Mensaje de error
          <div className="text-center py-4 text-red-500">
            {error}
          </div>
        ) : notifications.length === 0 ? (
          // Sin notificaciones
          <div className="text-center py-4 text-gray-500">
            No tienes notificaciones
          </div>
        ) : (
          // Lista de notificaciones
          <div className="space-y-4">
            {notifications.map((notification) => (
              <div
                key={notification.id}
                className={`p-3 rounded-lg border ${notification.read ? 'bg-gray-50' : 'bg-blue-50 border-blue-100'}`}
              >
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="font-medium flex items-center">
                      {notification.title}
                      {renderNotificationType(notification.type)}
                    </div>
                    {notification.description && (
                      <p className="text-sm text-gray-600 mt-1">
                        {notification.description}
                      </p>
                    )}
                    <div className="text-xs text-gray-500 mt-2">
                      {formatRelativeTime(notification.created_at)}
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    {notification.link && (
                      <Link href={notification.link}>
                        <Button size="sm" variant="ghost">
                          <ExternalLink className="h-4 w-4" />
                        </Button>
                      </Link>
                    )}
                    {!notification.read && (
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleMarkAsRead(notification.id)}
                        title="Marcar como leída"
                      >
                        <Check className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
      {showFooter && notifications.length > 0 && (
        <CardFooter className="flex justify-center">
          <Link href="/dashboard/notifications">
            <Button variant="outline">Ver todas</Button>
          </Link>
        </CardFooter>
      )}
    </Card>
  )
}
