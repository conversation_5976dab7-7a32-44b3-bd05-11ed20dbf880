'use client'

import React, { Suspense } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertCircle } from 'lucide-react'
import Link from 'next/link'

/**
 * Página de error de autenticación
 *
 * @returns Componente de página de error
 */
export default function AuthErrorPage() {
  return (
    <Suspense fallback={<AuthErrorLoading />}>
      <AuthErrorContent />
    </Suspense>
  )
}

function AuthErrorLoading() {
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader>
          <div className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-red-500" />
            <CardTitle>Error de autenticación</CardTitle>
          </div>
          <CardDescription>
            Cargando información...
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="p-4 bg-gray-50 border border-gray-100 rounded-md text-gray-800">
            Cargando detalles del error...
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

function AuthErrorContent() {
  // Use useSearchParams hook in a client component wrapped in Suspense
  const [message, setMessage] = React.useState('Ha ocurrido un error durante la autenticación')

  // Use useEffect to safely access window in client component
  React.useEffect(() => {
    const searchParams = new URLSearchParams(window.location.search)
    const msgParam = searchParams.get('message')
    if (msgParam) {
      setMessage(msgParam)
    }
  }, [])

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader>
          <div className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-red-500" />
            <CardTitle>Error de autenticación</CardTitle>
          </div>
          <CardDescription>
            No se pudo completar el proceso de autenticación
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="p-4 bg-red-50 border border-red-100 rounded-md text-red-800">
            {message}
          </div>
          <p className="mt-4 text-sm text-gray-600">
            Por favor, intenta iniciar sesión nuevamente. Si el problema persiste, contacta a soporte técnico.
          </p>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" asChild>
            <Link href="/">Volver al inicio</Link>
          </Button>
          <Button asChild>
            <Link href="/auth/login">Iniciar sesión</Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}
