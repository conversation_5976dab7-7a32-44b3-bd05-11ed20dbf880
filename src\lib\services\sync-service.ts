/**
 * @file Servicio de sincronización entre IndexedDB y Supabase
 * @description Gestiona la sincronización controlada de datos entre la base de datos local y Supabase
 */

import { localDb, SyncConfig, PendingOperation } from '@/lib/db/local-database';
import { rateLimiterService, OperationType, OperationPriority } from '@/lib/services/rate-limiter-service';
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/lib/supabase/types';

// Eventos de sincronización
export const SYNC_EVENTS = {
  SYNC_STARTED: 'sync:started',
  SYNC_COMPLETED: 'sync:completed',
  SYNC_ERROR: 'sync:error',
  SYNC_PROGRESS: 'sync:progress',
};

// Opciones para la sincronización
export interface SyncOptions {
  force?: boolean;
  tables?: string[];
  direction?: 'pull' | 'push' | 'both';
}

/**
 * Clase que implementa el servicio de sincronización
 */
export class SyncService {
  private static instance: SyncService;
  private supabase: ReturnType<typeof createClient<Database>>;
  private syncInterval: NodeJS.Timeout | null = null;
  private isSyncing: boolean = false;
  private networkStatus: 'online' | 'offline' = 'online';
  private listeners: Array<(event: string, data: unknown) => void> = [];

  /**
   * Constructor privado para implementar el patrón Singleton
   */
  private constructor() {
    // Crear cliente de Supabase
    this.supabase = createClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''
    );

    // Inicializar configuración
    this.initializeSync();

    // Configurar listeners de red
    if (typeof window !== 'undefined') {
      window.addEventListener('online', this.handleNetworkChange.bind(this));
      window.addEventListener('offline', this.handleNetworkChange.bind(this));
    }
  }

  /**
   * Obtiene la instancia única del servicio (patrón Singleton)
   */
  public static getInstance(): SyncService {
    // Solo crear la instancia en el cliente
    if (typeof window === 'undefined') {
      // Devolver un proxy que no hace nada en el servidor
      return new Proxy({} as SyncService, {
        get: (target, prop) => {
          // Proporcionar implementaciones de no-op para métodos comunes
          if (prop === 'then') return undefined;
          if (prop === 'addRecord' || prop === 'updateRecord' || prop === 'deleteRecord' || prop === 'sync') {
            return () => Promise.resolve();
          }
          return () => {};
        }
      });
    }

    // En el cliente, usar el patrón singleton normal
    if (!SyncService.instance) {
      SyncService.instance = new SyncService();
    }
    return SyncService.instance;
  }

  /**
   * Inicializa la sincronización basada en la configuración
   */
  private async initializeSync(): Promise<void> {
    try {
      // Asegurar que la configuración existe
      const config = await localDb.initSyncConfig();

      // Si la sincronización está habilitada, configurar el intervalo
      if (config.enabled) {
        this.setupSyncInterval(config);

        // Sincronizar al inicio si está configurado
        if (config.sync_on_startup) {
          // Pequeño retraso para permitir que la aplicación se cargue
          setTimeout(() => {
            this.sync({ force: true });
          }, 2000);
        }
      }
    } catch (error) {
      console.error('Error al inicializar la sincronización:', error);
    }
  }

  /**
   * Configura el intervalo de sincronización
   */
  private setupSyncInterval(config: SyncConfig): void {
    // Limpiar intervalo existente si hay uno
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    // Configurar nuevo intervalo
    const intervalMs = config.interval_minutes * 60 * 1000;
    this.syncInterval = setInterval(() => {
      this.sync();
    }, intervalMs);

    console.log(`Sincronización automática configurada cada ${config.interval_minutes} minutos`);
  }

  /**
   * Maneja cambios en la conexión de red
   */
  private async handleNetworkChange(event: Event): void {
    const isOnline = event.type === 'online';
    this.networkStatus = isOnline ? 'online' : 'offline';

    console.log(`Estado de red cambiado: ${this.networkStatus}`);

    // Si volvemos a estar en línea y está configurado para sincronizar al reconectar
    if (isOnline) {
      const config = await localDb.syncConfig.get(1);
      if (config?.sync_on_network_reconnect) {
        console.log('Sincronizando después de reconexión de red');
        this.sync({ direction: 'push' }); // Primero enviar cambios locales
      }
    }
  }

  /**
   * Actualiza la configuración de sincronización
   */
  public async updateConfig(config: Partial<SyncConfig>): Promise<SyncConfig> {
    const updatedConfig = await localDb.updateSyncConfig(config);

    // Si se cambió el intervalo o el estado, actualizar el intervalo
    if ('enabled' in config || 'interval_minutes' in config) {
      this.setupSyncInterval(updatedConfig);
    }

    return updatedConfig;
  }

  /**
   * Obtiene la configuración actual
   */
  public async getConfig(): Promise<SyncConfig> {
    const config = await localDb.syncConfig.get(1);
    if (!config) {
      return localDb.initSyncConfig();
    }
    return config;
  }

  /**
   * Realiza la sincronización de datos
   */
  public async sync(options: SyncOptions = {}): Promise<boolean> {
    // Si ya está sincronizando, no iniciar otra sincronización
    if (this.isSyncing) {
      console.log('Ya hay una sincronización en curso');
      return false;
    }

    // Si estamos offline y no es forzado, no sincronizar
    if (this.networkStatus === 'offline' && !options.force) {
      console.log('No se puede sincronizar: sin conexión a Internet');
      this.notifyListeners(SYNC_EVENTS.SYNC_ERROR, { error: 'Sin conexión a Internet' });
      return false;
    }

    try {
      this.isSyncing = true;
      this.notifyListeners(SYNC_EVENTS.SYNC_STARTED, {});

      // Obtener configuración
      const config = await this.getConfig();

      // Determinar qué tablas sincronizar
      const tablesToSync = options.tables || config.tables_to_sync;

      // Determinar dirección de sincronización
      const direction = options.direction || 'both';

      // Procesar operaciones pendientes primero (push)
      if (direction === 'push' || direction === 'both') {
        await this.processPendingOperations();
      }

      // Sincronizar datos desde Supabase (pull)
      if (direction === 'pull' || direction === 'both') {
        for (const table of tablesToSync) {
          await this.pullTableData(table);
        }
      }

      // Actualizar timestamp de última sincronización
      await localDb.updateSyncConfig({
        last_sync: Date.now()
      });

      this.notifyListeners(SYNC_EVENTS.SYNC_COMPLETED, {
        timestamp: Date.now(),
        tables: tablesToSync
      });

      return true;
    } catch (error) {
      console.error('Error durante la sincronización:', error);
      this.notifyListeners(SYNC_EVENTS.SYNC_ERROR, { error });
      return false;
    } finally {
      this.isSyncing = false;
    }
  }

  /**
   * Procesa las operaciones pendientes (envía cambios locales a Supabase)
   */
  private async processPendingOperations(): Promise<void> {
    // Obtener operaciones pendientes ordenadas por prioridad
    const pendingOps = await localDb.getPendingOperations(20);

    if (pendingOps.length === 0) {
      return;
    }

    console.log(`Procesando ${pendingOps.length} operaciones pendientes`);

    // Procesar cada operación
    for (const op of pendingOps) {
      try {
        await this.processSingleOperation(op);

        // Si se completó con éxito, eliminar de la cola
        await localDb.completePendingOperation(op.id!);

        // Registrar en el log
        await localDb.logSync({
          table: op.table,
          operation: 'push',
          timestamp: Date.now(),
          records_count: 1,
          status: 'success'
        });
      } catch (error) {
        console.error(`Error al procesar operación ${op.id} en tabla ${op.table}:`, error);

        // Incrementar contador de intentos
        await localDb.incrementOperationAttempts(op.id!);

        // Registrar error
        await localDb.logSync({
          table: op.table,
          operation: 'push',
          timestamp: Date.now(),
          records_count: 1,
          status: 'error',
          error_message: error instanceof Error ? error.message : String(error)
        });
      }
    }
  }

  /**
   * Procesa una única operación pendiente
   */
  private async processSingleOperation(op: PendingOperation): Promise<void> {
    // Usar el limitador de tasa para evitar exceder límites
    return rateLimiterService.enqueue(
      OperationType.DATABASE,
      async () => {
        // Mapear el nombre de la tabla si es necesario
        const tableMap: { [key: string]: string } = {
          'documentAnalyses': 'ai_document_analyses',
          'document_analyses': 'ai_document_analyses'
        };
        const tableName = tableMap[op.table] || op.table;

        console.log(`Procesando operación en tabla: ${op.table} -> ${tableName}`);

        switch (op.operation) {
          case 'insert':
            await this.supabase.from(tableName).insert(op.data);
            break;

          case 'update':
            await this.supabase.from(tableName).update(op.data).eq('id', op.record_id);
            break;

          case 'delete':
            await this.supabase.from(tableName).delete().eq('id', op.record_id);
            break;

          default:
            throw new Error(`Operación no soportada: ${op.operation}`);
        }
      },
      {
        priority: op.priority as OperationPriority
      }
    );
  }

  /**
   * Obtiene datos de una tabla desde Supabase y los almacena localmente
   * @returns Promise que se resuelve cuando se completa la sincronización
   */
  private async pullTableData(table: string): Promise<void> {
    try {
      // Mapear el nombre de la tabla si es necesario
      const tableMap: { [key: string]: string } = {
        'documentAnalyses': 'ai_document_analyses',
        'document_analyses': 'ai_document_analyses'
      };
      const tableName = tableMap[table] || table;

      console.log(`Sincronizando tabla: ${table} -> ${tableName}`);

      // Obtener timestamp de última sincronización
      const config = await this.getConfig();
      if (!config) {
        throw new Error('No se pudo obtener la configuración de sincronización');
      }
      const lastSyncDate = new Date(config.last_sync);

      // Si es la primera sincronización, no usar filtro de fecha
      const useTimeFilter = config.last_sync > 0;

      // Usar el limitador de tasa para evitar exceder límites
      const { data, error } = await rateLimiterService.enqueue(
        OperationType.DATABASE,
        async () => {
          let query = this.supabase.from(tableName).select('*');

          // Si no es la primera sincronización, solo obtener registros actualizados
          if (useTimeFilter) {
            query = query.gt('updated_at', lastSyncDate.toISOString());
          }

          return query;
        }
      );

      if (error) {
        throw error;
      }

      if (!data || data.length === 0) {
        console.log(`No hay datos nuevos para sincronizar en tabla ${tableName}`);
        return Promise.resolve();
      }

      console.log(`Sincronizando ${data.length} registros de tabla ${tableName}`);

      // Notificar progreso
      this.notifyListeners(SYNC_EVENTS.SYNC_PROGRESS, {
        table: tableName,
        count: data.length
      });

      // Almacenar datos en IndexedDB
      switch (table) {
        case 'projects':
          await localDb.projects.bulkPut(data.map(item => ({
            ...item,
            _synced: true,
            _local_updated_at: Date.now()
          })));
          break;

        case 'documents':
          await localDb.documents.bulkPut(data.map(item => ({
            ...item,
            _synced: true,
            _local_updated_at: Date.now()
          })));
          break;

        case 'users':
          await localDb.users.bulkPut(data.map(item => ({
            ...item,
            _synced: true,
            _local_updated_at: Date.now()
          })));
          break;

        case 'documentAnalyses':
          await localDb.documentAnalyses.bulkPut(data.map(item => ({
            ...item,
            _synced: true,
            _local_updated_at: Date.now()
          })));
          break;

        default:
          console.warn(`Tabla no soportada para sincronización: ${tableName}`);
          return Promise.resolve();
      }

      // Registrar en el log
      await localDb.logSync({
        table: tableName,
        operation: 'fetch',
        timestamp: Date.now(),
        records_count: data.length,
        status: 'success'
      });
      return Promise.resolve();
    } catch (error) {
      console.error(`Error al sincronizar tabla ${table}:`, error);

      // Registrar error
      await localDb.logSync({
        table,
        operation: 'fetch',
        timestamp: Date.now(),
        records_count: 0,
        status: 'error',
        error_message: error instanceof Error ? error.message : String(error)
      });

      return Promise.reject(error);
    }
  }

  /**
   * Agrega un registro a la base de datos local y lo marca para sincronización
   */
  public async addRecord<T extends { id: string }>(
    table: string,
    data: T,
    options: { syncImmediately?: boolean; priority?: number } = {}
  ): Promise<string> {
    const { syncImmediately = false, priority = 3 } = options;

    // Agregar campos de control
    const recordWithMeta = {
      ...data,
      _synced: false,
      _local_updated_at: Date.now()
    };

    // Guardar en la base de datos local según la tabla
    switch (table) {
      case 'projects':
        await localDb.projects.put(recordWithMeta as any);
        break;

      case 'documents':
        await localDb.documents.put(recordWithMeta as any);
        break;

      case 'users':
        await localDb.users.put(recordWithMeta as any);
        break;

      case 'ai_document_analyses':
        await localDb.documentAnalyses.put(recordWithMeta as any);
        break;

      default:
        throw new Error(`Tabla no soportada: ${table}`);
    }

    // Agregar operación pendiente
    await localDb.addPendingOperation({
      table,
      operation: 'insert',
      record_id: data.id,
      data,
      priority
    });

    // Sincronizar inmediatamente si se solicita
    if (syncImmediately && this.networkStatus === 'online') {
      this.sync({ tables: [table], direction: 'push' });
    }

    return data.id;
  }

  /**
   * Actualiza un registro en la base de datos local y lo marca para sincronización
   */
  public async updateRecord<T extends { id: string }>(
    table: string,
    id: string,
    data: Partial<T>,
    options: { syncImmediately?: boolean; priority?: number } = {}
  ): Promise<void> {
    const { syncImmediately = false, priority = 3 } = options;

    // Obtener registro actual
    let currentRecord: unknown;

    switch (table) {
      case 'projects':
        currentRecord = await localDb.projects.get(id);
        break;

      case 'documents':
        currentRecord = await localDb.documents.get(id);
        break;

      case 'users':
        currentRecord = await localDb.users.get(id);
        break;

      case 'ai_document_analyses':
        currentRecord = await localDb.documentAnalyses.get(id);
        break;

      default:
        throw new Error(`Tabla no soportada: ${table}`);
    }

    if (!currentRecord) {
      throw new Error(`Registro no encontrado: ${id} en tabla ${table}`);
    }

    // Fusionar datos y agregar campos de control
    const updatedRecord = {
      ...currentRecord,
      ...data,
      _synced: false,
      _local_updated_at: Date.now()
    };

    // Guardar en la base de datos local
    switch (table) {
      case 'projects':
        await localDb.projects.put(updatedRecord);
        break;

      case 'documents':
        await localDb.documents.put(updatedRecord);
        break;

      case 'users':
        await localDb.users.put(updatedRecord);
        break;

      case 'ai_document_analyses':
        await localDb.documentAnalyses.put(updatedRecord);
        break;
    }

    // Agregar operación pendiente
    await localDb.addPendingOperation({
      table,
      operation: 'update',
      record_id: id,
      data: { ...data, updated_at: new Date().toISOString() },
      priority
    });

    // Sincronizar inmediatamente si se solicita
    if (syncImmediately && this.networkStatus === 'online') {
      this.sync({ tables: [table], direction: 'push' });
    }
  }

  /**
   * Elimina un registro de la base de datos local y lo marca para sincronización
   */
  public async deleteRecord(
    table: string,
    id: string,
    options: { syncImmediately?: boolean; priority?: number } = {}
  ): Promise<void> {
    const { syncImmediately = false, priority = 3 } = options;

    try {
      console.log(`Deleting record from local database: ${table} with ID ${id}`);

      // Verificar si el registro existe antes de intentar eliminarlo
      let recordExists = false;

      switch (table) {
        case 'projects':
          recordExists = await localDb.projects.get(id) !== undefined;
          if (recordExists) {
            await localDb.projects.delete(id);
          }
          break;

        case 'documents':
          recordExists = await localDb.documents.get(id) !== undefined;
          if (recordExists) {
            await localDb.documents.delete(id);
          }
          break;

        case 'users':
          recordExists = await localDb.users.get(id) !== undefined;
          if (recordExists) {
            await localDb.users.delete(id);
          }
          break;

        case 'ai_document_analyses':
          recordExists = await localDb.documentAnalyses.get(id) !== undefined;
          if (recordExists) {
            await localDb.documentAnalyses.delete(id);
          }
          break;

        default:
          throw new Error(`Tabla no soportada: ${table}`);
      }

      if (!recordExists) {
        console.warn(`Record ${id} not found in local database table ${table}`);
      }

      // Agregar operación pendiente
      const operationId = await localDb.addPendingOperation({
        table,
        operation: 'delete',
        record_id: id,
        data: { id },
        priority
      });

      console.log(`Added pending delete operation with ID ${operationId} for ${table}:${id}`);

      // Sincronizar inmediatamente si se solicita
      if (syncImmediately && this.networkStatus === 'online') {
        console.log(`Immediate sync requested for delete operation on ${table}:${id}`);
        await this.sync({ tables: [table], direction: 'push', force: true });
      }

      // Notificar a los listeners sobre la eliminación
      this.notifyListeners(SYNC_EVENTS.RECORD_DELETED, {
        table,
        id,
        timestamp: Date.now()
      });

    } catch (error) {
      console.error(`Error in deleteRecord for ${table}:${id}:`, error);
      throw error;
    }
  }

  /**
   * Fuerza una sincronización inmediata
   */
  public async forceSyncNow(options: SyncOptions = {}): Promise<boolean> {
    return this.sync({ ...options, force: true });
  }

  /**
   * Registra un listener para eventos de sincronización
   */
  public addSyncListener(listener: (event: string, data: unknown) => void): () => void {
    this.listeners.push(listener);

    // Devolver función para eliminar el listener
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index >= 0) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * Notifica a los listeners sobre eventos de sincronización
   */
  private notifyListeners(event: string, data: unknown): void {
    this.listeners.forEach(listener => {
      try {
        listener(event, data);
      } catch (error) {
        console.error('Error en listener de sincronización:', error);
      }
    });
  }

  /**
   * Obtiene estadísticas de sincronización
   */
  public async getSyncStats() {
    return localDb.getSyncStats();
  }

  /**
   * Limpia los logs antiguos
   */
  public async cleanupOldLogs(): Promise<number> {
    return localDb.cleanupOldSyncLogs();
  }
}

// Exportar una instancia única del servicio
// Asegurarse de que solo se inicializa en el cliente
export const syncService = SyncService.getInstance();
