"use client"

import { useEffect, useState, ReactNode } from 'react'
import { useInView } from 'react-intersection-observer'

interface LazySectionProps {
  children: ReactNode
  placeholder?: ReactNode
  threshold?: number
  rootMargin?: string
  triggerOnce?: boolean
  className?: string
  id?: string
}

/**
 * Componente para cargar secciones de forma diferida cuando entran en el viewport
 * 
 * Útil para optimizar el rendimiento en páginas con mucho contenido,
 * especialmente en componentes pesados que no son visibles inicialmente.
 * 
 * @param props Propiedades del componente
 * @returns Componente de sección con carga diferida
 */
export function LazySection({
  children,
  placeholder,
  threshold = 0.1,
  rootMargin = '100px',
  triggerOnce = true,
  className,
  id,
}: LazySectionProps) {
  const [loaded, setLoaded] = useState(false)
  const { ref, inView } = useInView({
    threshold,
    rootMargin,
    triggerOnce,
  })

  useEffect(() => {
    if (inView && !loaded) {
      setLoaded(true)
    }
  }, [inView, loaded])

  return (
    <div ref={ref} className={className} id={id}>
      {loaded ? children : placeholder || <div className="min-h-[100px] animate-pulse bg-muted/30 rounded-md" />}
    </div>
  )
}
