/**
 * @file Servicio para gestionar la conexión a Supabase
 * @description Proporciona funciones para verificar y monitorear la conexión a Supabase
 */

import { createClient } from '@/lib/supabase/client'
import { EventEmitter } from 'events'

// Tipos de eventos
export enum ConnectionEvent {
  STATUS_CHANGE = 'status_change',
  ERROR = 'error',
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected'
}

// Estados de conexión
export type ConnectionStatus = 'checking' | 'connected' | 'error' | 'offline'

// Interfaz para estadísticas de conexión
export interface ConnectionStats {
  lastCheck: number
  lastSuccess: number
  lastError: number
  errorCount: number
  successCount: number
  lastErrorMessage: string | null
  status: ConnectionStatus
  isOnline: boolean
}

/**
 * Servicio para gestionar la conexión a Supabase
 */
class ConnectionService extends EventEmitter {
  private static instance: ConnectionService
  private supabase = createClient()
  private checkInterval: NodeJS.Timeout | null = null
  private stats: ConnectionStats = {
    lastCheck: 0,
    lastSuccess: 0,
    lastError: 0,
    errorCount: 0,
    successCount: 0,
    lastErrorMessage: null,
    status: 'checking',
    isOnline: typeof navigator !== 'undefined' ? navigator.onLine : true
  }

  /**
   * Constructor privado para implementar el patrón Singleton
   */
  private constructor() {
    super()
    this.setupNetworkListeners()
  }

  /**
   * Obtiene la instancia única del servicio (patrón Singleton)
   */
  public static getInstance(): ConnectionService {
    if (!ConnectionService.instance) {
      ConnectionService.instance = new ConnectionService()
    }
    return ConnectionService.instance
  }

  /**
   * Configura los listeners de red
   */
  private setupNetworkListeners(): void {
    if (typeof window !== 'undefined') {
      window.addEventListener('online', this.handleOnline.bind(this))
      window.addEventListener('offline', this.handleOffline.bind(this))
    }
  }

  /**
   * Maneja el evento online
   */
  private handleOnline(): void {
    this.stats.isOnline = true
    this.emit(ConnectionEvent.STATUS_CHANGE, { ...this.stats })
    // Verificar la conexión cuando volvemos a estar online
    this.checkConnection()
  }

  /**
   * Maneja el evento offline
   */
  private handleOffline(): void {
    this.stats.isOnline = false
    this.stats.status = 'offline'
    this.emit(ConnectionEvent.STATUS_CHANGE, { ...this.stats })
    this.emit(ConnectionEvent.DISCONNECTED, { reason: 'network_offline' })
  }

  /**
   * Inicia el monitoreo de conexión
   * @param intervalMs Intervalo de verificación en milisegundos
   */
  public startMonitoring(intervalMs: number = 60000): void {
    // Detener monitoreo existente si hay uno
    this.stopMonitoring()

    // Verificar inmediatamente
    this.checkConnection()

    // Configurar verificación periódica
    this.checkInterval = setInterval(() => {
      this.checkConnection()
    }, intervalMs)
  }

  /**
   * Detiene el monitoreo de conexión
   */
  public stopMonitoring(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
      this.checkInterval = null
    }
  }

  /**
   * Verifica la conexión a Supabase
   * @returns Promesa que resuelve a true si la conexión es exitosa
   */
  public async checkConnection(): Promise<boolean> {
    // Actualizar timestamp de verificación
    this.stats.lastCheck = Date.now()
    this.stats.status = 'checking'
    this.emit(ConnectionEvent.STATUS_CHANGE, { ...this.stats })

    // Si no hay conexión a internet, no intentar conectar a Supabase
    if (!this.stats.isOnline) {
      this.stats.status = 'offline'
      this.emit(ConnectionEvent.STATUS_CHANGE, { ...this.stats })
      return false
    }

    try {
      // Usar una tabla que sabemos que existe en lugar de _health
      const { data, error } = await this.supabase.from('projects').select('count').limit(1)

      if (error) {
        this.handleConnectionError(error)
        return false
      }

      // Conexión exitosa
      this.stats.lastSuccess = Date.now()
      this.stats.successCount++
      this.stats.status = 'connected'
      this.emit(ConnectionEvent.STATUS_CHANGE, { ...this.stats })
      this.emit(ConnectionEvent.CONNECTED)
      return true
    } catch (error) {
      this.handleConnectionError(error)
      return false
    }
  }

  /**
   * Maneja errores de conexión
   * @param error Error de conexión
   */
  private handleConnectionError(error: unknown): void {
    this.stats.lastError = Date.now()
    this.stats.errorCount++
    this.stats.status = 'error'
    this.stats.lastErrorMessage = error instanceof Error ? error.message : String(error)
    
    console.error('Error de conexión a Supabase:', this.stats.lastErrorMessage)
    
    this.emit(ConnectionEvent.STATUS_CHANGE, { ...this.stats })
    this.emit(ConnectionEvent.ERROR, {
      message: this.stats.lastErrorMessage,
      timestamp: this.stats.lastError
    })
  }

  /**
   * Obtiene las estadísticas actuales de conexión
   * @returns Estadísticas de conexión
   */
  public getStats(): ConnectionStats {
    return { ...this.stats }
  }
}

// Exportar instancia única
export const connectionService = ConnectionService.getInstance()
