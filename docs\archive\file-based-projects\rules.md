# File-Based Project Creation Rules

This document outlines the rules and guidelines for implementing and maintaining the file-based project creation feature in the AdminCore  project.

## 🔄 Project Awareness & Context

- **Always read `PLANNING.md`** at the start of a new conversation to understand the project's architecture, goals, style, and constraints.
- **Check `TASK.md`** before starting a new task. If the task isn't listed, add it with a brief description and today's date.
- **Use consistent naming conventions, file structure, and architecture patterns** as described in `PLANNING.md`.
- **Refer to the main project documentation** for overall project guidelines and standards.

## 🧱 Code Structure & Modularity

- **Never create a file longer than 500 lines of code.** If a file approaches this limit, refactor by splitting it into modules or helper files.
- **Organize code into clearly separated modules**, grouped by feature or responsibility.
- **Use clear, consistent imports** (prefer relative imports within packages).
- **Follow the provider interface pattern** for all AI provider implementations.
- **Use the factory pattern** for creating provider instances.
- **Implement proper error handling** at each layer of the application.

## 🧪 Testing & Reliability

- **Always create unit tests for new features** (functions, classes, routes, etc).
- **After updating any logic**, check whether existing unit tests need to be updated. If so, do it.
- **Tests should live in a `/tests` folder** mirroring the main app structure.
  - Include at least:
    - 1 test for expected use
    - 1 edge case
    - 1 failure case
- **Test with real documents** of various types and formats.
- **Implement mock providers** for testing without API calls.

## ✅ Task Completion

- **Mark completed tasks in `TASK.md`** immediately after finishing them.
- Add new sub-tasks or TODOs discovered during development to `TASK.md` under a "Discovered During Work" section.
- **Update documentation** when implementing new features or making significant changes.
- **Create examples** for new features to help other developers understand usage.

## 📎 Style & Conventions

- **Use TypeScript** for all new code.
- **Follow the project's existing style guidelines** for consistency.
- **Use React hooks** for component state and effects.
- **Implement proper loading and error states** for all asynchronous operations.
- **Use Shadcn/UI components** for consistent UI design.
- **Write JSDoc comments** for all functions and components:
  ```typescript
  /**
   * Analyzes a document using the specified AI provider
   *
   * @param documentId - The ID of the document to analyze
   * @param providerId - The ID of the AI provider to use
   * @returns Analysis result with extracted project information
   */
  async function analyzeDocument(documentId: string, providerId: string): Promise<AnalysisResult> {
    // Implementation
  }
  ```

## 📚 Documentation & Explainability

- **Update relevant documentation** when adding or modifying features.
- **Comment non-obvious code** and ensure everything is understandable to a mid-level developer.
- When writing complex logic, **add an inline `// Reason:` comment** explaining the why, not just the what.
- **Document limitations and constraints** of each AI provider.
- **Create usage examples** for new components and functions.

## 🔒 Security & Privacy

- **Never store API keys in client-side code** or expose them to the browser.
- **Implement proper access controls** for all API endpoints.
- **Validate and sanitize all user inputs** before processing.
- **Handle sensitive information appropriately** according to data privacy regulations.
- **Implement proper error handling** that doesn't expose sensitive information.

## 🧠 AI Provider Rules

- **All providers must implement the common interface** defined in the provider interface.
- **Handle provider-specific errors** and translate them to common error types.
- **Implement proper retry logic** for transient errors.
- **Monitor API usage and costs** for each provider.
- **Implement fallback mechanisms** when a provider fails.
- **Validate provider configuration** before attempting to use a provider.
- **Document provider-specific limitations** and requirements.

## 🚀 Performance Considerations

- **Optimize document processing** for large files.
- **Implement chunking** for documents that exceed token limits.
- **Use appropriate caching** to reduce redundant processing.
- **Monitor and optimize API usage** to control costs.
- **Implement background processing** for time-consuming operations.
- **Provide appropriate feedback** to users during long-running operations.
