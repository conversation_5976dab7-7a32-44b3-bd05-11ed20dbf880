import { createClient } from '@/lib/supabase/server-client';
import { redirect } from 'next/navigation';
import { cookies } from 'next/headers';
import { DashboardShell } from '@/components/shared/layout/dashboard-shell';
import { DocumentAnalysisResults } from '@/components/features/projects/document-analysis-results';

interface PageProps {
  params: {
    id: string;
  };
}

export default async function DocumentAnalysisResultsPage({ params }: PageProps) {
  const { id } = params as { id: string };
  const cookieStore = await cookies();
  const supabase = createClient(cookieStore);

  // Verificar si el usuario está autenticado
  const { data: userData, error: userError } = await supabase.auth.getUser();

  if (userError || !userData.user) {
    redirect('/auth/signin');
  }

  // Obtener datos del análisis
  const { data: analysis, error: analysisError } = await supabase
    .from('ai_document_analyses')
    .select('*, document:document_id(*)')
    .eq('id', id as any)
    .single();

  if (analysisError || !analysis) {
    redirect('/dashboard/projects/new');
  }

  // Si el análisis no está completo, redirigir a la página de creación de proyectos
  if ((analysis as any).status !== 'completed') {
    redirect('/dashboard/projects/new');
  }

  return (
    <DashboardShell
      heading="Resultados del Análisis de Documento"
      description="Revisa la información extraída del documento y decide si crear un proyecto"
    >
      <DocumentAnalysisResults
        analysisId={id}
        analysisData={(analysis as any).analysis_data}
        documentData={(analysis as any).document}
      />
    </DashboardShell>
  );
}
