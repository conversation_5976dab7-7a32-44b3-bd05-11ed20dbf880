// setupTests.ts: configuración global para tests
import '@testing-library/jest-dom';

import { vi } from 'vitest';

// Global Supabase client mock for all tests
vi.mock('@/lib/supabase/client', () => {
  return {
    createClient: () => ({
      auth: {
        getUser: () => Promise.resolve({ data: { user: null }, error: null }),
        getSession: () => Promise.resolve({ data: { session: null }, error: null }),
        signOut: () => Promise.resolve({ error: null }),
        onAuthStateChange: (_cb: any) => ({
          data: {
            subscription: { unsubscribe: () => {} }
          }
        }),
      },
      from: () => ({
        select: () => ({
          eq: () => ({
            single: () => Promise.resolve({ data: null, error: null }),
          }),
        }),
        insert: () => Promise.resolve({ data: null, error: null }),
        update: () => Promise.resolve({ data: null, error: null }),
        delete: () => Promise.resolve({ data: null, error: null }),
        rpc: () => Promise.resolve({ data: null, error: null }),
      }),
      rpc: () => Promise.resolve({ data: null, error: null }),
      storage: {
        listBuckets: () => Promise.resolve({ data: [], error: null }),
        getBucket: () => Promise.resolve({ data: null, error: null }),
        createBucket: () => Promise.resolve({ data: null, error: null }),
        from: () => ({
          upload: () => Promise.resolve({ data: null, error: null }),
          download: () => Promise.resolve({ data: null, error: null }),
        }),
      },
    })
  };
});

