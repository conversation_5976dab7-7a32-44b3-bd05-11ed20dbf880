// Script para verificar la compilación del archivo problemático
const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🔍 Verificando compilación del archivo problemático...');

try {
  // Verificar que el archivo existe
  const filePath = path.join(__dirname, 'src/app/api/ai/analysis/[id]/route.ts');
  if (fs.existsSync(filePath)) {
    console.log('✅ Archivo encontrado:', filePath);
    
    // Intentar compilar solo este archivo
    console.log('🔄 Compilando archivo...');
    execSync('npx tsc src/app/api/ai/analysis/\\[id\\]/route.ts --noEmit', { stdio: 'inherit' });
    
    console.log('✅ Compilación exitosa!');
  } else {
    console.error('❌ Archivo no encontrado:', filePath);
  }
} catch (error) {
  console.error('❌ Error durante la compilación:', error.message);
  process.exit(1);
}
