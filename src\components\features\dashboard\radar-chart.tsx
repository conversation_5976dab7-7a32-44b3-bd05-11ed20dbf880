"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Radar<PERSON>hart as RechartsRadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, Legend, ResponsiveContainer } from "recharts"

interface RadarChartProps {
  title: string
  description?: string
  data: unknown[]
  dataKeys: string[]
  colors: string[]
  height?: number
  angleAxisDataKey?: string
  showLegend?: boolean
}

export function RadarChart({
  title,
  description,
  data,
  dataKeys,
  colors,
  height = 350,
  angleAxisDataKey = "subject",
  showLegend = true,
}: RadarChartProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <div style={{ height: height }}>
          <ResponsiveContainer width="100%" height="100%">
            <RechartsRadarChart cx="50%" cy="50%" outerRadius="80%" data={data}>
              <PolarGrid />
              <PolarAngleAxis dataKey={angleAxisDataKey} />
              <PolarRadiusAxis angle={30} domain={[0, 'auto']} />
              {dataKeys.map((key, index) => (
                <Radar
                  key={key}
                  name={key}
                  dataKey={key}
                  stroke={colors[index % colors.length]}
                  fill={colors[index % colors.length]}
                  fillOpacity={0.6}
                />
              ))}
              {showLegend && <Legend />}
            </RechartsRadarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  )
}
