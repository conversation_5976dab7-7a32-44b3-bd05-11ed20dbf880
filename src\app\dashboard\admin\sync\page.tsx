import { Metada<PERSON> } from "next"
import { DashboardShell } from "@/components/shared/layout/dashboard-shell"
import { SyncDashboard } from "@/components/features/admin/sync-dashboard"

export const metadata: Metadata = {
  title: "Sincronización Local | AdminCore ",
  description: "Gestión de sincronización entre la base de datos local y Supabase",
}

export default function SyncPage() {
  return (
    <DashboardShell heading="Sincronización">
      <SyncDashboard />
    </DashboardShell>
  )
}
