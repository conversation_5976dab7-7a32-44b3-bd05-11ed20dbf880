// Script para crear las funciones SQL necesarias en Supabase
// Ejecutar con: node scripts/fix-supabase-functions.js

// Cargar variables de entorno
require('dotenv').config();

// Obtener variables de entorno
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Error: NEXT_PUBLIC_SUPABASE_URL y SUPABASE_SERVICE_ROLE_KEY deben estar definidos en el archivo .env');
  process.exit(1);
}

/**
 * Ejecuta SQL directamente usando la API REST de Supabase
 * @param {string} sql - Consulta SQL a ejecutar
 */
async function executeSqlViaRest(sql) {
  try {
    const response = await fetch(`${supabaseUrl}/rest/v1/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseServiceKey}`,
        'apikey': supabaseServiceKey,
        'Prefer': 'params=single-object'
      },
      body: JSON.stringify({
        query: sql
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Error en respuesta API: ${JSON.stringify(errorData)}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error al ejecutar SQL via REST:', error);
    throw error;
  }
}

/**
 * Crea las funciones SQL necesarias en Supabase
 */
async function createSqlFunctions() {
  console.log('Creando funciones SQL en Supabase...');

  // SQL para crear la función pgSQL
  const createPgSqlFunction = `
  -- Función para ejecutar SQL dinámico
  CREATE OR REPLACE FUNCTION public.pgsql(query text)
  RETURNS JSONB
  LANGUAGE plpgsql
  SECURITY DEFINER
  AS $$
  DECLARE
    result JSONB;
  BEGIN
    EXECUTE query;
    result := jsonb_build_object('success', true, 'message', 'SQL executed successfully');
    RETURN result;
  EXCEPTION WHEN OTHERS THEN
    result := jsonb_build_object(
      'success', false,
      'message', SQLERRM,
      'detail', SQLSTATE,
      'query', query
    );
    RETURN result;
  END;
  $$;
  
  -- Función para verificar si existe una función en la base de datos
  CREATE OR REPLACE FUNCTION public.function_exists(function_name TEXT)
  RETURNS BOOLEAN
  LANGUAGE plpgsql
  SECURITY DEFINER
  AS $$
  DECLARE
    func_exists BOOLEAN;
  BEGIN
    -- Verificar si la función existe
    SELECT EXISTS (
      SELECT 1
      FROM pg_proc p
      JOIN pg_namespace n ON p.pronamespace = n.oid
      WHERE n.nspname = 'public'
      AND p.proname = function_name
    ) INTO func_exists;
    
    RETURN func_exists;
  END;
  $$;
  
  -- Función para ejecutar SQL dinámico (alias para compatibilidad)
  CREATE OR REPLACE FUNCTION public.exec_sql(sql TEXT)
  RETURNS VOID
  LANGUAGE plpgsql
  SECURITY DEFINER
  AS $$
  BEGIN
    EXECUTE sql;
  END;
  $$;
  `;

  try {
    // Ejecutar el SQL para crear las funciones
    await executeSqlViaRest(createPgSqlFunction);
    console.log('Funciones SQL creadas correctamente');
    
    // Verificar que las funciones se hayan creado mediante una consulta directa
    const checkFunctionsSQL = `
    SELECT proname 
    FROM pg_proc p
    JOIN pg_namespace n ON p.pronamespace = n.oid
    WHERE n.nspname = 'public'
    AND proname IN ('pgsql', 'function_exists', 'exec_sql');
    `;
    
    try {
      const result = await executeSqlViaRest(checkFunctionsSQL);
      console.log('Funciones verificadas:', result);
    } catch (checkError) {
      console.error('Error al verificar funciones:', checkError);
    }
    
    return true;
  } catch (error) {
    console.error('Error al crear funciones SQL:', error);
    return false;
  }
}

/**
 * Función principal
 */
async function main() {
  try {
    const success = await createSqlFunctions();
    
    if (success) {
      console.log('Funciones SQL creadas correctamente en Supabase');
      process.exit(0);
    } else {
      console.error('Error al crear funciones SQL en Supabase');
      process.exit(1);
    }
  } catch (error) {
    console.error('Error inesperado:', error);
    process.exit(1);
  }
}

// Ejecutar la función principal
main();
