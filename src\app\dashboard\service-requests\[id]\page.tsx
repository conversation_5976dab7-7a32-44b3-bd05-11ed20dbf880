"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import Link from "next/link"
import { createClient } from "@/lib/supabase/client"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { toast } from "@/components/ui/use-toast"
import {
  ArrowLeft,
  Pencil,
  Trash2,
  Clock,
  Calendar,
  User,
  MapPin,
  FileText,
  Tag,
  DollarSign,
  ExternalLink,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>
} from "lucide-react"
import { format } from "date-fns"
import { serviceRequestService } from "@/lib/services/service-request-service"
import { serviceActivityService } from "@/lib/services/service-activity-service"
import { ServiceRequest, ServiceActivity } from "@/types/service-management"

// Function to get status color
const getStatusColor = (status: string): string => {
  switch (status) {
    case "pending":
      return "bg-yellow-500"
    case "assigned":
      return "bg-blue-300"
    case "in_progress":
      return "bg-blue-500"
    case "on_hold":
      return "bg-purple-500"
    case "resolved":
      return "bg-green-500"
    case "closed":
      return "bg-gray-500"
    case "cancelled":
      return "bg-red-500"
    default:
      return "bg-gray-300"
  }
}

// Function to get priority color
const getPriorityColor = (priority: string): string => {
  switch (priority) {
    case "low":
      return "bg-green-500"
    case "medium":
      return "bg-yellow-500"
    case "high":
      return "bg-orange-500"
    case "critical":
      return "bg-red-500"
    default:
      return "bg-gray-300"
  }
}

export default function ServiceRequestDetailPage({ params }: { params: { id: string } }) {
  const [serviceRequest, setServiceRequest] = useState<ServiceRequest | null>(null)
  const [activities, setActivities] = useState<ServiceActivity[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isDeleting, setIsDeleting] = useState(false)
  const [isConverting, setIsConverting] = useState(false)
  const router = useRouter()
  const supabase = createClient()

  useEffect(() => {
    const fetchServiceRequest = async () => {
      setIsLoading(true)
      try {
        // Get the service request
        const { data, error } = await serviceRequestService.getServiceRequestById(params.id)

        if (error) throw error
        setServiceRequest(data)

        // Get service activities
        const { data: activitiesData, error: activitiesError } = await serviceActivityService.getServiceActivities({
          serviceRequestId: params.id
        })

        if (activitiesError) throw activitiesError
        setActivities(activitiesData || [])
      } catch (error) {
        console.error("Error fetching service request:", error)
        toast({
          title: "Error",
          description: "Failed to load service request details.",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchServiceRequest()
  }, [params.id])

  const handleDelete = async () => {
    setIsDeleting(true)
    try {
      const { error } = await serviceRequestService.deleteServiceRequest(params.id)

      if (error) throw error

      toast({
        title: "Service Request Deleted",
        description: "The service request has been deleted successfully.",
      })

      router.push("/dashboard/service-requests")
    } catch (error) {
      console.error("Error deleting service request:", error)
      toast({
        title: "Error",
        description: "Failed to delete service request.",
        variant: "destructive",
      })
    } finally {
      setIsDeleting(false)
    }
  }

  const handleConvertToWorkOrder = async () => {
    setIsConverting(true)
    try {
      const { data, error } = await serviceRequestService.convertToWorkOrder(params.id)

      if (error) throw error

      toast({
        title: "Work Order Created",
        description: "The service request has been converted to a work order.",
      })

      // Refresh the service request to show the work order link
      const { data: updatedData } = await serviceRequestService.getServiceRequestById(params.id)
      setServiceRequest(updatedData)
    } catch (error) {
      console.error("Error converting to work order:", error)
      toast({
        title: "Error",
        description: "Failed to convert service request to work order.",
        variant: "destructive",
      })
    } finally {
      setIsConverting(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2">Loading service request details...</p>
        </div>
      </div>
    )
  }

  if (!serviceRequest) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold">Service Request Not Found</h2>
        <p className="mt-2">The requested service request could not be found.</p>
        <Button asChild className="mt-4">
          <Link href="/dashboard/service-requests">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Service Requests
          </Link>
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <Button variant="outline" size="sm" asChild>
            <Link href="/dashboard/service-requests">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Service Requests
            </Link>
          </Button>
          <h2 className="text-3xl font-bold tracking-tight mt-2">{serviceRequest.title}</h2>
          <div className="flex items-center gap-2 mt-1">
            <Badge className={getStatusColor(serviceRequest.status)}>
              {serviceRequest.status.charAt(0).toUpperCase() + serviceRequest.status.slice(1).replace('_', ' ')}
            </Badge>
            <Badge className={getPriorityColor(serviceRequest.priority)}>
              {serviceRequest.priority.charAt(0).toUpperCase() + serviceRequest.priority.slice(1)}
            </Badge>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {serviceRequest.work_order_id ? (
            <Button variant="outline" asChild>
              <Link href={`/dashboard/work-orders/${serviceRequest.work_order_id}`}>
                <FileCheck className="mr-2 h-4 w-4" />
                View Work Order
              </Link>
            </Button>
          ) : (
            <Button variant="outline" onClick={handleConvertToWorkOrder} disabled={isConverting}>
              <FileText className="mr-2 h-4 w-4" />
              {isConverting ? "Converting..." : "Convert to Work Order"}
            </Button>
          )}
          <Button variant="outline" asChild>
            <Link href={`/dashboard/service-requests/${params.id}/edit`}>
              <Pencil className="mr-2 h-4 w-4" />
              Edit
            </Link>
          </Button>
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="destructive">
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                <AlertDialogDescription>
                  This will permanently delete this service request and all associated activities.
                  This action cannot be undone.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction onClick={handleDelete} disabled={isDeleting}>
                  {isDeleting ? "Deleting..." : "Delete"}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      <Tabs defaultValue="details" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="details">Details</TabsTrigger>
          <TabsTrigger value="activities">Activities ({activities.length})</TabsTrigger>
          <TabsTrigger value="attachments">Attachments</TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Client Information</CardTitle>
              </CardHeader>
              <CardContent>
                <dl className="space-y-2">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Client</dt>
                    <dd className="mt-1">
                      {serviceRequest.client ? (
                        <Link href={`/dashboard/clients/${serviceRequest.client.id}`} className="text-blue-600 hover:underline">
                          {serviceRequest.client.name}
                        </Link>
                      ) : (
                        <span className="text-gray-400">No client assigned</span>
                      )}
                    </dd>
                  </div>
                  {serviceRequest.location && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Location</dt>
                      <dd className="mt-1 flex items-center">
                        <MapPin className="h-4 w-4 mr-1 text-gray-400" />
                        {serviceRequest.location}
                      </dd>
                    </div>
                  )}
                </dl>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Service Details</CardTitle>
              </CardHeader>
              <CardContent>
                <dl className="space-y-2">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Status</dt>
                    <dd className="mt-1">
                      <Badge className={getStatusColor(serviceRequest.status)}>
                        {serviceRequest.status.charAt(0).toUpperCase() + serviceRequest.status.slice(1).replace('_', ' ')}
                      </Badge>
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Priority</dt>
                    <dd className="mt-1">
                      <Badge className={getPriorityColor(serviceRequest.priority)}>
                        {serviceRequest.priority.charAt(0).toUpperCase() + serviceRequest.priority.slice(1)}
                      </Badge>
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Source</dt>
                    <dd className="mt-1 capitalize">{serviceRequest.source || "N/A"}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Billable</dt>
                    <dd className="mt-1 flex items-center">
                      <DollarSign className="h-4 w-4 mr-1 text-gray-400" />
                      {serviceRequest.is_billable ? "Yes" : "No"}
                    </dd>
                  </div>
                  {serviceRequest.external_reference && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">External Reference</dt>
                      <dd className="mt-1 flex items-center">
                        <ExternalLink className="h-4 w-4 mr-1 text-gray-400" />
                        {serviceRequest.external_reference}
                      </dd>
                    </div>
                  )}
                </dl>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Assignment & Timing</CardTitle>
              </CardHeader>
              <CardContent>
                <dl className="space-y-2">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Assigned To</dt>
                    <dd className="mt-1 flex items-center">
                      <User className="h-4 w-4 mr-1 text-gray-400" />
                      {serviceRequest.assigned_user ? (
                        serviceRequest.assigned_user.full_name || serviceRequest.assigned_user.email
                      ) : (
                        <span className="text-gray-400">Unassigned</span>
                      )}
                    </dd>
                  </div>
                  {serviceRequest.due_date && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Due Date</dt>
                      <dd className="mt-1 flex items-center">
                        <Calendar className="h-4 w-4 mr-1 text-gray-400" />
                        {format(new Date(serviceRequest.due_date), "PPP")}
                      </dd>
                    </div>
                  )}
                  {serviceRequest.estimated_hours !== null && serviceRequest.estimated_hours !== undefined && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Estimated Hours</dt>
                      <dd className="mt-1 flex items-center">
                        <Clock className="h-4 w-4 mr-1 text-gray-400" />
                        {serviceRequest.estimated_hours} hours
                      </dd>
                    </div>
                  )}
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Created</dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {format(new Date(serviceRequest.created_at), "PPP")}
                    </dd>
                  </div>
                  {serviceRequest.resolution_date && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Resolved</dt>
                      <dd className="mt-1 text-sm text-gray-900">
                        {format(new Date(serviceRequest.resolution_date), "PPP")}
                      </dd>
                    </div>
                  )}
                </dl>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Description</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="prose max-w-none">
                {serviceRequest.description ? (
                  <p>{serviceRequest.description}</p>
                ) : (
                  <p className="text-gray-400">No description provided.</p>
                )}
              </div>
            </CardContent>
          </Card>

          {serviceRequest.resolution_notes && (
            <Card>
              <CardHeader>
                <CardTitle>Resolution Notes</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="prose max-w-none">
                  <p>{serviceRequest.resolution_notes}</p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="activities">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Service Activities</CardTitle>
              <Button asChild>
                <Link href={`/dashboard/service-requests/${params.id}/activities/new`}>
                  <Wrench className="mr-2 h-4 w-4" />
                  New Activity
                </Link>
              </Button>
            </CardHeader>
            <CardContent>
              {activities.length > 0 ? (
                <div className="space-y-4">
                  {activities.map((activity) => (
                    <Card key={activity.id}>
                      <CardHeader className="pb-2">
                        <div className="flex justify-between items-center">
                          <CardTitle className="text-lg">
                            {activity.activity_type.charAt(0).toUpperCase() + activity.activity_type.slice(1)}
                          </CardTitle>
                          <Badge className={getStatusColor(activity.status)}>
                            {activity.status.charAt(0).toUpperCase() + activity.status.slice(1).replace('_', ' ')}
                          </Badge>
                        </div>
                        <CardDescription>
                          {activity.technician ? (
                            <span>Performed by {activity.technician.full_name || activity.technician.email}</span>
                          ) : (
                            <span>Unassigned</span>
                          )}
                          {activity.start_time && (
                            <span> • Started {format(new Date(activity.start_time), "PPP")}</span>
                          )}
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        {activity.description ? (
                          <p>{activity.description}</p>
                        ) : (
                          <p className="text-gray-400">No description provided.</p>
                        )}
                      </CardContent>
                      <CardFooter className="pt-0">
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/dashboard/service-requests/${params.id}/activities/${activity.id}`}>
                            View Details
                          </Link>
                        </Button>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <ClipboardList className="h-12 w-12 mx-auto text-gray-400" />
                  <h3 className="mt-2 text-lg font-medium">No activities yet</h3>
                  <p className="mt-1 text-gray-500">
                    Create a new service activity to track work performed for this service request.
                  </p>
                  <Button className="mt-4" asChild>
                    <Link href={`/dashboard/service-requests/${params.id}/activities/new`}>
                      <Wrench className="mr-2 h-4 w-4" />
                      New Activity
                    </Link>
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="attachments">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Attachments</CardTitle>
              <Button>
                <FileText className="mr-2 h-4 w-4" />
                Upload Attachment
              </Button>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <FileText className="h-12 w-12 mx-auto text-gray-400" />
                <h3 className="mt-2 text-lg font-medium">No attachments yet</h3>
                <p className="mt-1 text-gray-500">
                  Upload documents, photos, or other files related to this service request.
                </p>
                <Button className="mt-4">
                  <FileText className="mr-2 h-4 w-4" />
                  Upload Attachment
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
