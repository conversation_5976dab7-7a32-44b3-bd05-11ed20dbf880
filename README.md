# AdminCore Web

AdminCore es una plataforma de gestión para empresas de construcción y proyectos. Esta aplicación web está construida con Next.js y Supabase.

## Estado Actual del Proyecto

### Implementación Real: 65% Completado

**Módulos Implementados:**

- **Dashboard (85%)**: Estructura básica, métricas, gráficos básicos implementados. Faltan filtros avanzados y notificaciones en tiempo real.
- **Proyectos (75%)**: CRUD básico, tabla con filtros, vista de tarjetas, formularios de creación/edición. Falta gestión avanzada de estados y asignación de recursos.
- **Órdenes de Trabajo (70%)**: Vista de tabla, Kanban básico, formularios CRUD. Falta seguimiento detallado y gestión de recursos.
- **Documentos (60%)**: Carga básica implementada, falta categorización completa, previsualización y búsqueda avanzada.
- **Usuarios (90%)**: CRUD completo, gestión de documentos de usuarios, roles básicos. Sistema de permisos granulares pendiente.
- **Configuración (50%)**: Estructura básica, ajustes simulados. Falta integración real con servicios externos.

**Módulos Adicionales en Desarrollo:**
- **Integración con IA**: Análisis de documentos con múltiples proveedores (Gemini, OpenAI, LM Studio)
- **GitHub Integration**: Sincronización de repositorios y proyectos
- **Service Management**: Gestión de contratos y solicitudes de servicio
- **Analytics**: Dashboard de métricas avanzadas

### Tecnologías Utilizadas

- **Frontend**: Next.js, React, TypeScript, Tailwind CSS, shadcn/ui
- **Backend**: Supabase (PostgreSQL, Auth, Storage)
- **Despliegue**: Vercel

Este proyecto fue creado con [Next.js](https://nextjs.org) usando [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Instalación y Ejecución

1. Clona el repositorio
2. Instala las dependencias:

```bash
npm install
# o
yarn install
```

3. Configura las variables de entorno (crea un archivo `.env.local` basado en `.env.example`)
4. Inicia el servidor de desarrollo:

```bash
npm run dev
# o
yarn dev
```

5. Abre [http://localhost:3000](http://localhost:3000) en tu navegador para ver la aplicación.

## Características

- **Diseño Responsive**: Funciona en dispositivos móviles, tablets y escritorio
- **Autenticación**: Sistema completo con Supabase Auth
- **Gestión de Proyectos**: Seguimiento detallado de proyectos
- **Gestión de Documentos**: Almacenamiento y previsualización de archivos
- **Dashboard Interactivo**: Gráficos y métricas en tiempo real
- **Personalización**: Temas y ajustes de interfaz

Este proyecto utiliza [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) para optimizar y cargar [Geist](https://vercel.com/font), una nueva familia de fuentes de Vercel.

## Estructura del Proyecto

```
/
├── app/              # Rutas y páginas de Next.js
├── components/       # Componentes React reutilizables
│   ├── features/     # Componentes específicos de funcionalidades
│   └── shared/       # Componentes UI compartidos
├── lib/              # Utilidades y servicios
│   ├── services/     # Servicios para comunicación con APIs
│   └── supabase/     # Cliente y configuración de Supabase
├── public/           # Archivos estáticos
└── styles/           # Estilos globales
```

## Despliegue

La forma más sencilla de desplegar esta aplicación es utilizar la [Plataforma Vercel](https://vercel.com/new).

### Preparación para el Despliegue

Para asegurar un despliegue exitoso en Vercel, sigue estos pasos:

1. Ejecuta el script de limpieza para preparar el proyecto:

```bash
node clean-for-deploy.js
```

2. Verifica que las variables de entorno estén correctamente configuradas:

```bash
node vercel-env-check.js
```

3. Asegúrate de configurar las siguientes variables de entorno en Vercel:
   - `NEXT_PUBLIC_SUPABASE_URL`
   - `NEXT_PUBLIC_SUPABASE_ANON_KEY`
   - `SUPABASE_SERVICE_ROLE_KEY`

### Solución de Problemas Comunes

- **Error con canvas**: La dependencia `canvas` está configurada como opcional y excluida del bundle en `next.config.js`.
- **IndexedDB en el servidor**: El código ha sido modificado para detectar el entorno y evitar usar IndexedDB en el servidor.
- **Variables de entorno mal formateadas**: Asegúrate de que no tengan espacios ni saltos de línea.

## Licencia

Este proyecto está licenciado bajo la Licencia MIT - ver el archivo LICENSE para más detalles.
