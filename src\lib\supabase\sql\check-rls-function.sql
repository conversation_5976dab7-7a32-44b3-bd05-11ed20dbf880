-- Función para verificar si RLS está habilitado en una tabla
CREATE OR REPLACE FUNCTION public.check_rls_enabled(table_name TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  rls_enabled BOOLEAN;
BEGIN
  -- Verificar si la tabla existe
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = 'public' AND table_name = check_rls_enabled.table_name
  ) THEN
    RAISE EXCEPTION 'La tabla % no existe', table_name;
  END IF;

  -- Verificar si RLS está habilitado
  SELECT relrowsecurity INTO rls_enabled
  FROM pg_class
  WHERE oid = (quote_ident('public') || '.' || quote_ident(table_name))::regclass;

  RETURN rls_enabled;
END;
$$;

-- Función para verificar si el usuario tiene acceso a un proyecto específico
CREATE OR REPLACE FUNCTION public.check_project_access(project_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  has_access BOOLEAN;
  current_user_id UUID;
BEGIN
  -- Obtener el ID del usuario actual
  current_user_id := auth.uid();
  
  -- Verificar si el usuario es nulo (no autenticado)
  IF current_user_id IS NULL THEN
    RETURN FALSE;
  END IF;
  
  -- Verificar si el usuario tiene acceso al proyecto
  SELECT EXISTS (
    SELECT 1 FROM public.projects
    WHERE id = project_id AND (
      owner_id = current_user_id OR
      EXISTS (
        SELECT 1 FROM public.project_users
        WHERE project_id = check_project_access.project_id AND user_id = current_user_id
      )
    )
  ) INTO has_access;
  
  RETURN has_access;
END;
$$;

-- Función para crear proyectos de demostración para el usuario actual
CREATE OR REPLACE FUNCTION public.create_demo_projects()
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_user_id UUID;
  result JSONB;
  inserted_count INTEGER;
BEGIN
  -- Obtener el ID del usuario actual
  current_user_id := auth.uid();
  
  -- Verificar si el usuario es nulo (no autenticado)
  IF current_user_id IS NULL THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Usuario no autenticado',
      'count', 0
    );
  END IF;
  
  -- Insertar proyectos de demostración
  WITH inserted AS (
    INSERT INTO public.projects (
      name,
      description,
      status,
      owner_id,
      created_at,
      updated_at
    )
    VALUES
      (
        'Proyecto Demo 1',
        'Proyecto de demostración creado automáticamente',
        'pending',
        current_user_id,
        NOW(),
        NOW()
      ),
      (
        'Proyecto Demo 2',
        'Proyecto de demostración en progreso',
        'in_progress',
        current_user_id,
        NOW(),
        NOW()
      ),
      (
        'Proyecto Demo 3',
        'Proyecto de demostración completado',
        'completed',
        current_user_id,
        NOW(),
        NOW()
      )
    RETURNING id
  )
  SELECT COUNT(*) INTO inserted_count FROM inserted;
  
  -- Construir resultado
  result := jsonb_build_object(
    'success', true,
    'message', 'Proyectos de demostración creados correctamente',
    'count', inserted_count
  );
  
  RETURN result;
END;
$$;

-- Función para diagnosticar problemas con proyectos
CREATE OR REPLACE FUNCTION public.diagnose_projects()
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_user_id UUID;
  result JSONB;
  projects_count INTEGER;
  user_projects_count INTEGER;
  rls_enabled BOOLEAN;
BEGIN
  -- Obtener el ID del usuario actual
  current_user_id := auth.uid();
  
  -- Verificar si el usuario es nulo (no autenticado)
  IF current_user_id IS NULL THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Usuario no autenticado',
      'auth_status', 'unauthenticated'
    );
  END IF;
  
  -- Verificar si la tabla projects existe
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = 'public' AND table_name = 'projects'
  ) THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'La tabla projects no existe',
      'auth_status', 'authenticated',
      'table_exists', false
    );
  END IF;
  
  -- Verificar si RLS está habilitado
  SELECT relrowsecurity INTO rls_enabled
  FROM pg_class
  WHERE oid = 'public.projects'::regclass;
  
  -- Contar todos los proyectos
  SELECT COUNT(*) INTO projects_count FROM public.projects;
  
  -- Contar proyectos del usuario
  SELECT COUNT(*) INTO user_projects_count FROM public.projects
  WHERE owner_id = current_user_id OR EXISTS (
    SELECT 1 FROM public.project_users
    WHERE project_id = projects.id AND user_id = current_user_id
  );
  
  -- Construir resultado
  result := jsonb_build_object(
    'success', true,
    'auth_status', 'authenticated',
    'table_exists', true,
    'rls_enabled', rls_enabled,
    'total_projects', projects_count,
    'user_projects', user_projects_count,
    'user_id', current_user_id
  );
  
  RETURN result;
END;
$$;
