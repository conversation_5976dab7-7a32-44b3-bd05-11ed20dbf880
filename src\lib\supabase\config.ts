// Configuración de Supabase
export const supabaseConfig = {
  url: process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
  serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY || '',
}

// Función para verificar si las variables de entorno están configuradas correctamente
export function validateSupabaseConfig() {
  const { url, anonKey, serviceRoleKey } = supabaseConfig
  let isValid = true;

  if (!url) {
    console.error('Missing NEXT_PUBLIC_SUPABASE_URL environment variable')
    isValid = false;
  }

  if (!anonKey) {
    console.error('Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable')
    isValid = false;
  }

  // Verificar formato de las claves (deben ser JWT)
  const jwtPattern = /^eyJ[a-zA-Z0-9_-]+\.[a-zA-Z0-9_-]+\.[a-zA-Z0-9_-]+$/

  if (anonKey && !jwtPattern.test(anonKey)) {
    console.error('NEXT_PUBLIC_SUPABASE_ANON_KEY has invalid format')
    isValid = false;
  }

  // La clave de servicio es opcional para el cliente, pero necesaria para operaciones de administración
  if (!serviceRoleKey) {
    // En el cliente web, no mostramos advertencia ya que es normal que no esté disponible
    if (typeof window === 'undefined') {
      console.warn('Missing SUPABASE_SERVICE_ROLE_KEY environment variable. Admin operations will not be available.')
    }
    // No marcamos como inválido, ya que es opcional para el cliente normal
  } else if (!jwtPattern.test(serviceRoleKey)) {
    console.error('SUPABASE_SERVICE_ROLE_KEY has invalid format')
    isValid = false;
  }

  return isValid;
}
