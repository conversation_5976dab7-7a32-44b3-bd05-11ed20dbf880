/**
 * Simplified Vercel Build Script
 * 
 * Minimal build script optimized for Vercel deployment
 */

const { execSync } = require('child_process');

console.log('🚀 Starting simplified Vercel build...');

// Set production environment
process.env.NODE_ENV = 'production';
process.env.NEXT_TELEMETRY_DISABLED = '1';
process.env.NODE_OPTIONS = '--max-old-space-size=4096';

// Quick dependency check
try {
  const fs = require('fs');
  
  // Check if critical files exist
  const criticalFiles = [
    './src/components/ui/icons.ts',
    './next.config.js',
    './tsconfig.json'
  ];
  
  for (const file of criticalFiles) {
    if (fs.existsSync(file)) {
      console.log(`✅ Found: ${file}`);
    } else {
      console.warn(`⚠️ Missing: ${file}`);
    }
  }
} catch (error) {
  console.warn('⚠️ File check warning:', error.message);
}

// Run Next.js build directly
console.log('📦 Building Next.js application...');
try {
  execSync('next build', {
    stdio: 'inherit',
    env: {
      ...process.env,
      NODE_ENV: 'production',
      NEXT_TELEMETRY_DISABLED: '1',
      NODE_OPTIONS: '--max-old-space-size=4096'
    }
  });
  console.log('✅ Build completed successfully!');
} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}
