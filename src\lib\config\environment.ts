/**
 * @ai-file-description: "Configuración de entornos para la aplicación"
 * @ai-related-files: ["../supabase/client.ts"]
 * @ai-owner: "Infrastructure"
 */

/**
 * Configuración de entornos para la aplicación
 *
 * @ai-responsibility: "Proporciona configuración específica para diferentes entornos (desarrollo, producción)"
 */

// Detectar si estamos en entorno de desarrollo
export const isDevelopment = process.env.NODE_ENV === 'development';

// Configuración de servicios
export const services = {
  // Servicio de análisis de documentos
  documentAnalysis: {
    // URL base del servicio
    baseUrl: process.env.NEXT_PUBLIC_DOCUMENT_ANALYSIS_URL ||
      (isDevelopment ? 'http://localhost:8002' : 'https://xdboxokpjubowptrcytl.supabase.co/functions/v1/analyze-document'),

    // Verificar si debemos usar Docker local para el análisis
    useLocalDocker: process.env.NEXT_PUBLIC_USE_LOCAL_DOCKER === 'true' || isDevelopment,

    // Verificar si debemos usar LLM Studio local
    useLLMStudio: process.env.NEXT_PUBLIC_USE_LLM_STUDIO === 'true' || isDevelopment,

    // Endpoints específicos
    endpoints: {
      analyze: process.env.NEXT_PUBLIC_DOCUMENT_ANALYSIS_ANALYZE_ENDPOINT || (isDevelopment ? '/analyze' : ''),
      estimateCost: process.env.NEXT_PUBLIC_DOCUMENT_ANALYSIS_ESTIMATE_COST_ENDPOINT || (isDevelopment ? '/estimate-cost' : ''),
      checkStatus: process.env.NEXT_PUBLIC_DOCUMENT_ANALYSIS_CHECK_STATUS_ENDPOINT || (isDevelopment ? '/status' : ''),
    },

    // Cabeceras CORS para el servicio
    corsHeaders: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, x-user-id, X-User-ID, origin, cache-control',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Max-Age': '86400',
    }
  },

  // Servicio de LLM Studio
  llmStudio: {
    // URL base del servicio
    baseUrl: isDevelopment
      ? 'http://localhost:1234/v1'
      : 'https://api.llmstudio.ai/v1',

    // Verificar disponibilidad
    checkAvailability: isDevelopment,

    // Modelos predefinidos para desarrollo
    localModels: [
      {
        id: 'gemma-3-8b-vision-instruct',
        name: 'Gemma 3 8B Vision',
        isActive: true,
        capabilities: ['vision', 'text']
      },
      {
        id: 'llama-3.2-8b-vision-instruct',
        name: 'Llama 3.2 8B Vision',
        isActive: true,
        capabilities: ['vision', 'text']
      }
    ]
  },

  // Configuración de Supabase
  supabase: {
    // Siempre usar Supabase real, incluso en desarrollo
    useReal: true,

    // URL de la API
    url: process.env.NEXT_PUBLIC_SUPABASE_URL || '',

    // Clave anónima
    anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',

    // Funciones
    functions: {
      // Cabeceras CORS para funciones
      corsHeaders: {
        'Access-Control-Allow-Origin': isDevelopment ? 'http://localhost:3000' : '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Authorization, X-Client-Info, apikey, X-User-ID, x-user-id, Content-Type, Origin'
      }
    }
  }
};

/**
 * Obtiene la URL completa para un servicio y endpoint específicos
 *
 * @param serviceName Nombre del servicio
 * @param endpoint Endpoint específico (opcional)
 * @returns URL completa
 */
export function getServiceUrl(serviceName: keyof typeof services, endpoint?: string): string {
  const service = services[serviceName];

  if (!service || !('baseUrl' in service)) {
    throw new Error(`Servicio "${serviceName}" no encontrado o no tiene baseUrl`);
  }

  const baseUrl = service.baseUrl as string;

  // Para el servicio de análisis de documentos en producción, no añadir endpoint
  if (serviceName === 'documentAnalysis' && !isDevelopment && !endpoint) {
    return baseUrl;
  }

  if (!endpoint) {
    // Asegurarse de que la URL use http:// para localhost
    const finalUrl = baseUrl.replace(/^https:\/\/localhost/, 'http://localhost');
    console.log(`getServiceUrl: Returning base URL for ${serviceName}: ${finalUrl}`);
    return finalUrl;
  }

  // Si el endpoint ya comienza con /, no añadir otro
  const formattedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;

  // Asegurarse de que la URL use http:// para localhost
  const finalUrl = `${baseUrl}${formattedEndpoint}`.replace(/^https:\/\/localhost/, 'http://localhost');
  console.log(`getServiceUrl: Returning URL for ${serviceName} with endpoint ${endpoint}: ${finalUrl}`);
  return finalUrl;
}

/**
 * Obtiene las cabeceras comunes para peticiones a servicios
 *
 * @param includeAuth Incluir cabeceras de autenticación
 * @param accessToken Token de acceso opcional
 * @returns Objeto con cabeceras
 */
export function getServiceHeaders(includeAuth: boolean = true, accessToken?: string): HeadersInit {
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    'Origin': typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000',
    'Cache-Control': 'no-cache'
  };

  if (includeAuth) {
    if (accessToken) {
      headers['Authorization'] = `Bearer ${accessToken}`;
    }

    const apiKey = services.supabase.anonKey;
    if (apiKey) {
      headers['apikey'] = apiKey;
    }
  }

  return headers;
}
