"use client"

import { useState, useEffect } from "react"
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import { ArrowUpDown, ChevronDown, MoreHorizontal, Plus } from "lucide-react"
import Link from "next/link"
import { format } from "date-fns"
import { es } from "date-fns/locale"

import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"

// Tipos
interface WorkOrder {
  id: string
  title: string
  description?: string
  status: string
  priority: string
  assigned_to?: string
  project_id?: string
  due_date?: string
  created_at: string
  updated_at: string
  project?: {
    name: string
  }
  assigned_user?: {
    full_name?: string
    email: string
  }
  assigned_users?: {
    user_id: string
    role: string
    user: {
      id: string
      email: string
      full_name?: string
    }
  }[]
}

// Función para obtener el color del estado
const getStatusColor = (status: string) => {
  switch (status) {
    case "pending":
      return "bg-yellow-500"
    case "in_progress":
      return "bg-blue-500"
    case "completed":
      return "bg-green-500"
    case "cancelled":
      return "bg-red-500"
    default:
      return "bg-gray-500"
  }
}

// Función para obtener el texto del estado
const getStatusText = (status: string) => {
  switch (status) {
    case "pending":
      return "Pendiente"
    case "in_progress":
      return "En Progreso"
    case "completed":
      return "Completada"
    case "cancelled":
      return "Cancelada"
    default:
      return status
  }
}

// Función para obtener el color de la prioridad
const getPriorityColor = (priority: string) => {
  switch (priority) {
    case "low":
      return "bg-green-500"
    case "medium":
      return "bg-yellow-500"
    case "high":
      return "bg-orange-500"
    case "critical":
      return "bg-red-500"
    default:
      return "bg-gray-500"
  }
}

// Función para obtener el texto de la prioridad
const getPriorityText = (priority: string) => {
  switch (priority) {
    case "low":
      return "Baja"
    case "medium":
      return "Media"
    case "high":
      return "Alta"
    case "critical":
      return "Crítica"
    default:
      return priority
  }
}

export const columns: ColumnDef<WorkOrder>[] = [
  {
    accessorKey: "title",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Título
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => (
      <div className="font-medium">
        <Link href={`/dashboard/work-orders/${row.original.id}`} className="hover:underline">
          {row.getValue("title")}
        </Link>
      </div>
    ),
  },
  {
    accessorKey: "status",
    header: "Estado",
    cell: ({ row }) => {
      const status = row.getValue("status") as string
      return (
        <Badge className={getStatusColor(status)}>
          {getStatusText(status)}
        </Badge>
      )
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
  },
  {
    accessorKey: "priority",
    header: "Prioridad",
    cell: ({ row }) => {
      const priority = row.getValue("priority") as string
      return priority ? (
        <Badge className={getPriorityColor(priority)}>
          {getPriorityText(priority)}
        </Badge>
      ) : (
        "-"
      )
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
  },
  {
    accessorKey: "project",
    header: "Proyecto",
    cell: ({ row }) => {
      const project = row.original.project
      return project ? (
        <Link
          href={`/dashboard/projects/${row.original.project_id}`}
          className="hover:underline"
        >
          {project.name}
        </Link>
      ) : (
        "-"
      )
    },
  },
  {
    accessorKey: "assigned_users",
    header: "Usuarios Asignados",
    cell: ({ row }) => {
      // Primero verificamos si tenemos assigned_users
      const assignedUsers = row.original.assigned_users;

      if (assignedUsers && assignedUsers.length > 0) {
        return (
          <div className="flex flex-wrap gap-1">
            {assignedUsers.map((item, index) => (
              <div key={item.user_id} className="flex items-center">
                <Avatar className="h-6 w-6 mr-1">
                  <AvatarFallback>
                    {item.user.full_name
                      ? item.user.full_name
                          .split(" ")
                          .map((n) => n[0])
                          .join("")
                          .toUpperCase()
                          .substring(0, 2)
                      : item.user.email.substring(0, 2).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                {index < 2 && (
                  <span className="text-xs">
                    {item.user.full_name?.split(' ')[0] || item.user.email.split('@')[0]}
                  </span>
                )}
                {index === 2 && assignedUsers.length > 3 && (
                  <span className="text-xs text-muted-foreground">+{assignedUsers.length - 2}</span>
                )}
              </div>
            )).slice(0, 3)}
          </div>
        );
      }

      // Si no hay assigned_users, usamos el assigned_user tradicional como fallback
      const user = row.original.assigned_user;
      if (user) {
        return (
          <div className="flex items-center">
            <Avatar className="h-6 w-6 mr-1">
              <AvatarFallback>
                {user.full_name
                  ? user.full_name
                      .split(" ")
                      .map((n) => n[0])
                      .join("")
                      .toUpperCase()
                      .substring(0, 2)
                  : user.email.substring(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <span className="text-xs">{user.full_name || user.email}</span>
          </div>
        );
      }

      return <span className="text-muted-foreground text-xs">Sin asignar</span>;
    },
  },
  {
    accessorKey: "due_date",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Fecha límite
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => {
      const date = row.getValue("due_date") as string | null
      return date ? (
        <div className="font-medium">
          {format(new Date(date), "dd/MM/yyyy", { locale: es })}
        </div>
      ) : (
        <span className="text-muted-foreground">Sin fecha</span>
      )
    },
  },
  {
    accessorKey: "created_at",
    header: "Creado",
    cell: ({ row }) => {
      return (
        <div className="text-muted-foreground">
          {format(new Date(row.getValue("created_at")), "dd/MM/yyyy", {
            locale: es,
          })}
        </div>
      )
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const workOrder = row.original

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Abrir menú</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Acciones</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(workOrder.id)}
            >
              Copiar ID
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              <Link href={`/dashboard/work-orders/${workOrder.id}`}>
                Ver detalles
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Link href={`/dashboard/work-orders/${workOrder.id}/edit`}>
                Editar
              </Link>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="text-red-600">
              Eliminar
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    },
  },
]

interface WorkOrdersTableProps {
  data: WorkOrder[]
}

export function WorkOrdersTable({ data }: WorkOrdersTableProps) {
  const [sorting, setSorting] = useState<SortingState>([])
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({})
  const [rowSelection, setRowSelection] = useState({})
  const [statusFilter, setStatusFilter] = useState<string[]>([])
  const [priorityFilter, setPriorityFilter] = useState<string[]>([])

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  })

  // Aplicar filtro de estado
  const handleStatusFilterChange = (status: string) => {
    setStatusFilter((prev) => {
      if (prev.includes(status)) {
        return prev.filter((s) => s !== status)
      } else {
        return [...prev, status]
      }
    })
  }

  // Aplicar filtro de prioridad
  const handlePriorityFilterChange = (priority: string) => {
    setPriorityFilter((prev) => {
      if (prev.includes(priority)) {
        return prev.filter((p) => p !== priority)
      } else {
        return [...prev, priority]
      }
    })
  }

  // Actualizar los filtros de la tabla cuando cambien los filtros de estado y prioridad
  useEffect(() => {
    if (statusFilter.length > 0) {
      table.getColumn("status")?.setFilterValue(statusFilter)
    } else {
      table.getColumn("status")?.setFilterValue(undefined)
    }

    if (priorityFilter.length > 0) {
      table.getColumn("priority")?.setFilterValue(priorityFilter)
    } else {
      table.getColumn("priority")?.setFilterValue(undefined)
    }
  }, [statusFilter, priorityFilter, table])

  return (
    <div className="w-full">
      <div className="flex flex-col md:flex-row items-center py-4 gap-2">
        <Input
          placeholder="Filtrar órdenes..."
          value={(table.getColumn("title")?.getFilterValue() as string) ?? ""}
          onChange={(event) =>
            table.getColumn("title")?.setFilterValue(event.target.value)
          }
          className="max-w-sm"
        />
        <div className="flex flex-wrap gap-2 ml-auto">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                Estado <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Filtrar por estado</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {["pending", "in_progress", "completed", "cancelled"].map(
                (status) => (
                  <DropdownMenuCheckboxItem
                    key={status}
                    checked={statusFilter.includes(status)}
                    onCheckedChange={() => handleStatusFilterChange(status)}
                  >
                    {getStatusText(status)}
                  </DropdownMenuCheckboxItem>
                )
              )}
            </DropdownMenuContent>
          </DropdownMenu>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                Prioridad <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Filtrar por prioridad</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {["low", "medium", "high", "critical"].map((priority) => (
                <DropdownMenuCheckboxItem
                  key={priority}
                  checked={priorityFilter.includes(priority)}
                  onCheckedChange={() => handlePriorityFilterChange(priority)}
                >
                  {getPriorityText(priority)}
                </DropdownMenuCheckboxItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                Columnas <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(!!value)
                      }
                    >
                      {column.id === "title"
                        ? "Título"
                        : column.id === "status"
                        ? "Estado"
                        : column.id === "priority"
                        ? "Prioridad"
                        : column.id === "project"
                        ? "Proyecto"
                        : column.id === "assigned_user"
                        ? "Asignado a"
                        : column.id === "due_date"
                        ? "Fecha límite"
                        : column.id === "created_at"
                        ? "Creado"
                        : column.id}
                    </DropdownMenuCheckboxItem>
                  )
                })}
            </DropdownMenuContent>
          </DropdownMenu>
          <Button asChild>
            <Link href="/dashboard/work-orders/new">
              <Plus className="mr-2 h-4 w-4" /> Nueva Orden
            </Link>
          </Button>
        </div>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No se encontraron resultados.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredRowModel().rows.length} orden(es) encontrada(s).
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Anterior
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Siguiente
          </Button>
        </div>
      </div>
    </div>
  )
}
