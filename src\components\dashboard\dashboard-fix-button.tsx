"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, CheckCircle, Loader2 } from "lucide-react";
import { fixDashboardFunctions, checkDashboardDependencies } from "@/lib/actions/fix-dashboard-functions";
import { toast } from "@/hooks/use-toast";

interface DashboardFixButtonProps {
  onFixed?: () => void;
}

export function DashboardFixButton({ onFixed }: DashboardFixButtonProps) {
  const [isFixing, setIsFixing] = useState(false);
  const [isChecking, setIsChecking] = useState(false);
  const [status, setStatus] = useState<{
    checked: boolean;
    needsFix: boolean;
    details?: {
      tables?: {
        performance_metrics?: boolean;
        resource_allocation?: boolean;
      };
      functions?: {
        get_performance_metrics?: boolean;
        get_project_distribution?: boolean;
      };
    };
  }>({
    checked: false,
    needsFix: false
  });

  const handleCheck = async () => {
    setIsChecking(true);
    try {
      const result = await checkDashboardDependencies();
      
      if (result.success && result.data) {
        const { tables, functions } = result.data;
        
        // Determinar si se necesita una corrección
        const needsFix = !tables.performance_metrics || 
                         !tables.resource_allocation || 
                         !functions.get_performance_metrics || 
                         !functions.get_project_distribution;
        
        setStatus({
          checked: true,
          needsFix,
          details: {
            tables,
            functions
          }
        });
        
        if (!needsFix) {
          toast({
            title: "Todo en orden",
            description: "Las tablas y funciones del dashboard están correctamente configuradas.",
            variant: "default",
          });
        }
      } else {
        toast({
          title: "Error al verificar",
          description: result.error || "No se pudo verificar el estado de las dependencias del dashboard.",
          variant: "destructive",
        });
        
        setStatus({
          checked: true,
          needsFix: true
        });
      }
    } catch (error) {
      console.error("Error al verificar dependencias:", error);
      toast({
        title: "Error",
        description: "Ocurrió un error al verificar las dependencias del dashboard.",
        variant: "destructive",
      });
    } finally {
      setIsChecking(false);
    }
  };

  const handleFix = async () => {
    setIsFixing(true);
    try {
      const result = await fixDashboardFunctions();
      
      if (result.success) {
        toast({
          title: "Corrección exitosa",
          description: "Las funciones y tablas del dashboard han sido corregidas correctamente.",
          variant: "default",
        });
        
        // Actualizar el estado
        setStatus({
          checked: true,
          needsFix: false,
          details: {
            tables: {
              performance_metrics: true,
              resource_allocation: true
            },
            functions: {
              get_performance_metrics: true,
              get_project_distribution: true
            }
          }
        });
        
        // Llamar al callback si existe
        if (onFixed) {
          onFixed();
        }
        
        // Recargar la página después de un breve retraso
        setTimeout(() => {
          window.location.reload();
        }, 1500);
      } else {
        toast({
          title: "Error al corregir",
          description: result.error || "No se pudieron corregir las funciones del dashboard.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error al corregir funciones:", error);
      toast({
        title: "Error",
        description: "Ocurrió un error al corregir las funciones del dashboard.",
        variant: "destructive",
      });
    } finally {
      setIsFixing(false);
    }
  };

  return (
    <div className="space-y-4">
      {status.checked && status.needsFix && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Problemas detectados</AlertTitle>
          <AlertDescription>
            Se han detectado problemas con las funciones o tablas del dashboard.
            {status.details && (
              <ul className="mt-2 text-sm">
                {!status.details.tables?.performance_metrics && (
                  <li>• Tabla performance_metrics no encontrada</li>
                )}
                {!status.details.tables?.resource_allocation && (
                  <li>• Tabla resource_allocation no encontrada</li>
                )}
                {!status.details.functions?.get_performance_metrics && (
                  <li>• Función get_performance_metrics no encontrada</li>
                )}
                {!status.details.functions?.get_project_distribution && (
                  <li>• Función get_project_distribution tiene errores</li>
                )}
              </ul>
            )}
          </AlertDescription>
        </Alert>
      )}
      
      {status.checked && !status.needsFix && (
        <Alert variant="default" className="bg-green-50 dark:bg-green-950 border-green-200 dark:border-green-800">
          <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
          <AlertTitle>Todo en orden</AlertTitle>
          <AlertDescription>
            Las tablas y funciones del dashboard están correctamente configuradas.
          </AlertDescription>
        </Alert>
      )}
      
      <div className="flex gap-2">
        <Button 
          variant="outline" 
          onClick={handleCheck} 
          disabled={isChecking || isFixing}
        >
          {isChecking ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Verificando...
            </>
          ) : (
            "Verificar dependencias"
          )}
        </Button>
        
        <Button 
          variant="default" 
          onClick={handleFix} 
          disabled={isFixing || isChecking || (status.checked && !status.needsFix)}
        >
          {isFixing ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Corrigiendo...
            </>
          ) : (
            "Corregir funciones"
          )}
        </Button>
      </div>
    </div>
  );
}
