import { <PERSON>ada<PERSON> } from "next"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rig<PERSON> } from "@/components/ui/tabs"
import { createClient } from "@/lib/supabase/server"
import { WorkOrdersTable } from "@/components/features/work-orders/work-orders-table"
import { WorkOrdersKanban } from "@/components/features/work-orders/work-orders-kanban"
import { Button } from "@/components/ui/button"
import Link from "next/link"

export const metadata: Metadata = {
  title: "Órdenes de Trabajo | AdminCore ",
  description: "Gestión de órdenes de trabajo",
}

async function getWorkOrders() {
  try {
    const supabase = await createClient()

    // Obtener las órdenes de trabajo con sus relaciones básicas
    const { data: workOrders, error } = await supabase
      .from('work_orders')
      .select(`
        *,
        project:project_id(id, name)
      `)
      .order('created_at', { ascending: true })

    if (error) {
      console.error('Error al cargar órden<PERSON> de trabajo:', error)
      return []
    }

    // Para cada orden de trabajo, obtener los usuarios asignados
    if (workOrders && workOrders.length > 0) {
      const workOrderIds = workOrders.map((order: unknown) => order.id)

      const { data: assignedUsers, error: usersError } = await supabase
        .from('work_order_users')
        .select(`
          work_order_id,
          user_id,
          role,
          user:user_id(id, email, full_name)
        `)
        .in('work_order_id', workOrderIds)

      if (usersError) {
        console.error('Error al cargar usuarios asignados:', usersError)
      } else if (assignedUsers) {
        // Agrupar usuarios por orden de trabajo
        const usersByWorkOrder = assignedUsers.reduce((acc: unknown, item: unknown) => {
          if (!acc[item.work_order_id]) {
            acc[item.work_order_id] = []
          }
          acc[item.work_order_id].push(item)
          return acc
        }, {} as Record<string, any[]>)

        // Asignar los usuarios a cada orden de trabajo
        workOrders.forEach((order: unknown) => {
          order.assigned_users = usersByWorkOrder[order.id] || []
        })
      }
    }

    return workOrders || []
  } catch (error) {
    console.error('Error inesperado al cargar órdenes de trabajo:', error)
    return []
  }
}

export default async function WorkOrdersPage() {
  const workOrders = await getWorkOrders() || []

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Órdenes de Trabajo</h2>
        <Button asChild>
          <Link href="/dashboard/work-orders/new">Nueva Orden</Link>
        </Button>
      </div>

      <Tabs defaultValue="table" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="table">Tabla</TabsTrigger>
          <TabsTrigger value="kanban">Kanban</TabsTrigger>
        </TabsList>
        <TabsContent value="table" className="w-full">
          <WorkOrdersTable data={workOrders || []} />
        </TabsContent>
        <TabsContent value="kanban">
          <WorkOrdersKanban data={workOrders || []} />
        </TabsContent>
      </Tabs>

      {(!workOrders || workOrders.length === 0) && (
        <div className="text-center py-10">
          <p className="text-muted-foreground">No hay órdenes de trabajo creadas aún.</p>
        </div>
      )}
    </div>
  )
}
