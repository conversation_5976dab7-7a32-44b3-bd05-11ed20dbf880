import { NextResponse } from 'next/server';
import { z } from 'zod';
import { createServerSupabaseClient } from '@/lib/supabase/client';
import { withValidation } from '@/lib/middleware/validation-middleware';
import { logger } from '@/lib/services/logger-service';
import { cookies } from 'next/headers';

// --- Project Schemas ---

const projectInputSchema = z.object({
  name: z.string({
    required_error: "El nombre es obligatorio",
  }).min(3, {
    message: "El nombre debe tener al menos 3 caracteres.",
  }).refine(
    (val) => val && val.trim() !== '',
    {
      message: "El nombre no puede estar vacío."
    }
  ),
  description: z.string().optional()
    .transform(val => val === '' ? null : val), // Convertir string vacío a null
  status: z.enum(["pending", "in_progress", "completed", "cancelled"] as const, {
    required_error: "El estado es obligatorio",
  }).default("pending"),
  start_date: z.string().optional().nullable()
    .transform(val => val === '' ? null : val), // Convertir string vacío a null
  end_date: z.string().optional().nullable()
    .transform(val => val === '' ? null : val), // Convertir string vacío a null
  client_id: z.string().optional().nullable()
    .transform(val => val === '' ? null : val) // Convertir string vacío a null
    .refine(
      (val) => val === null || val === undefined || /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(val),
      {
        message: "El ID del cliente debe ser un UUID válido"
      }
    ),
  budget: z.string().optional().nullable()
    .transform(val => val === '' ? null : val) // Convertir string vacío a null
    .refine(
      (val) => val === null || val === undefined || !isNaN(parseFloat(val)),
      {
        message: "El presupuesto debe ser un número válido"
      }
    ),
  currency: z.enum(["USD", "CLP"] as const, {
    required_error: "La divisa es obligatoria",
  }).default("CLP"),
  project_type: z.string().optional().nullable()
    .transform(val => val === '' ? null : val), // Convertir string vacío a null
});

// Project output schema (to client)
const projectOutputSchema = z.object({
  id: z.string(),
  name: z.string(),
  root_path: z.string(),
  description: z.string().nullable(),
  project_type: z.string().nullable(),
  status: z.enum(["pending", "in_progress", "completed", "cancelled"] as const),
  start_date: z.string().nullable(),
  end_date: z.string().nullable(),
  client_id: z.string().nullable(),
  budget: z.string().nullable(),
  currency: z.enum(["USD", "CLP"] as const),
  owner_id: z.string().nullable(),
  progress_percent: z.number().nullable(),
  created_at: z.string().optional(),
  updated_at: z.string().optional(),
  last_scanned_at: z.string().optional(),
  scan_count: z.number().optional(),
  config: z.any().optional(),
});



// Type for project data (from client)
type ProjectInput = z.infer<typeof projectInputSchema>;


// Asegura que los datos enviados a Supabase estén completos y sin undefined
function toDbProject(data: ProjectInput) {
  // Generate a unique root_path based on project name
  const sanitizedName = data.name.toLowerCase()
    .replace(/[^a-z0-9]/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '');

  const timestamp = Date.now();
  const uniqueRootPath = `/${sanitizedName}-${timestamp}`;

  // Only include fields that exist in the database schema
  return {
    name: data.name,
    description: data.description ?? null,
    status: data.status ?? 'pending',
    start_date: data.start_date ?? null,
    end_date: data.end_date ?? null,
    client_id: data.client_id ?? null,
    budget: data.budget ? parseFloat(data.budget) : null, // Convert to number
    currency: data.currency ?? 'CLP',
    project_type: data.project_type ?? null,
    root_path: uniqueRootPath, // Unique root path for each project
  };
}



/**
 * GET handler for projects
 */
export async function GET() {
  const cookieStore = cookies();
  const supabase = createServerSupabaseClient(cookieStore);

  try {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(100); // Add limit to prevent over-fetching

    if (error) {
      logger.error('Error fetching projects', { error });
      throw error;
    }

    return NextResponse.json({
      success: true,
      data: data || []
    });
  } catch (error: unknown) {
    logger.error('Error in GET /api/projects', { error });

    const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch projects',
        details: errorMessage
      },
      { status: 500 }
    );
  }
}

/**
 * POST handler for creating a new project
 */
// Add revalidation settings for Next.js
// This helps with Vercel's edge caching
export const revalidate = 60; // 60 seconds

export const POST = withValidation(
  projectInputSchema,
  async (_, { data }) => {
    const cookieStore = cookies();
    const supabase = createServerSupabaseClient(cookieStore);

    try {
      // Get current user for owner_id
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      if (userError || !user) {
        logger.error('Error getting user', { error: userError });
        return NextResponse.json(
          { error: 'Usuario no autenticado' },
          { status: 401 }
        );
      }

      // Prepare project data with owner_id
      const projectData = {
        ...toDbProject(data),
        owner_id: user.id,
      };

      // Insert project with validated data
      const { data: project, error } = await supabase
        .from('projects')
        .insert([projectData as any])
        .select()
        .single();

      if (error) {
        logger.error('Error creating project', { error, projectData });

        // Handle specific database errors
        if (error.code === '23514') {
          return NextResponse.json(
            { error: 'Valor de estado no válido. Los estados permitidos son: pending, in_progress, completed, cancelled' },
            { status: 400 }
          );
        }

        if (error.code === '23505') {
          return NextResponse.json(
            { error: 'Ya existe un proyecto con esta ruta. Por favor, intente con un nombre diferente.' },
            { status: 400 }
          );
        }

        if (error.code === '23502') {
          return NextResponse.json(
            { error: 'Faltan campos obligatorios. Verifique que el nombre del proyecto esté completo.' },
            { status: 400 }
          );
        }

        const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

        return NextResponse.json(
          { error: 'Error al crear el proyecto: ' + errorMessage },
          { status: 500 }
        );
      }

      // Validar la respuesta con el esquema de salida
      const validatedProject = projectOutputSchema.parse(project);

      return NextResponse.json(validatedProject, { status: 201 });
    } catch (error: unknown) {
      logger.error('Error in POST /api/projects', { error });

      // Handle Zod validation errors
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            error: 'Error de validación',
            details: error.errors,
            message: 'Los datos proporcionados no son válidos',
          },
          { status: 400 }
        );
      }

      // Handle other errors
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido al procesar la solicitud';

      return NextResponse.json(
        {
          error: 'Error interno del servidor',
          message: errorMessage,
        },
        { status: 500 }
      );
    }
  }
);

/**
 * PUT handler for updating an existing project
 */
export const PUT = withValidation(
  projectInputSchema.merge(z.object({ id: z.string() })),
  async (_, { data }) => {
    const { id, ...projectData } = data as ProjectInput & { id: string };
    const cookieStore = cookies();
    const supabase = createServerSupabaseClient(cookieStore);

    try {
      // Update project with validated data
      const updateData = toDbProject(projectData);
      const { data: project, error } = await supabase
        .from('projects')
        .update(updateData as any)
        .eq('id', id as any)
        .select()
        .single();

      if (error) {
        logger.error('Error updating project', { error });
        return NextResponse.json(
          { error: 'Error al actualizar el proyecto' },
          { status: 500 }
        );
      }

      // Validar la respuesta con el esquema de salida
      const validatedProject = projectOutputSchema.parse(project);

      return NextResponse.json(validatedProject);
    } catch (error: unknown) {
      logger.error('Error in PUT /api/projects', { error });

      // Handle Zod validation errors
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            error: 'Error de validación',
            details: error.errors,
            message: 'Los datos proporcionados no son válidos',
          },
          { status: 400 }
        );
      }

      // Handle other errors
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido al procesar la solicitud';

      return NextResponse.json(
        {
          error: 'Error interno del servidor',
          message: errorMessage,
        },
        { status: 500 }
      );
    }
  }
);
