#!/bin/bash

# Exit on error
set -e

echo "🚀 Setting up development environment..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18.x or later."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v)
if [[ ! $NODE_VERSION =~ v18\. ]] && [[ ! $NODE_VERSION =~ v20\. ]]; then
    echo "⚠️  Warning: You're using Node.js $NODE_VERSION. This project is tested with Node.js 18.x or 20.x"
    read -p "Do you want to continue anyway? (y/N) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm."
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env.local ]; then
    echo "📄 Creating .env.local file..."
    cat > .env.local <<EOL
# Environment Configuration
NEXT_PUBLIC_API_URL=your_api_url_here
# Add other environment variables here
EOL
    echo "✅ Created .env.local file. Please update it with your configuration."
else
    echo "ℹ️  .env.local already exists. Skipping creation."
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm ci

# Run database migrations (if any)
if [ -f prisma/schema.prisma ]; then
    echo "🔄 Running database migrations..."
    npx prisma migrate dev --name init
fi

echo "✨ Environment setup complete!"
echo "To start the development server, run: npm run dev"
