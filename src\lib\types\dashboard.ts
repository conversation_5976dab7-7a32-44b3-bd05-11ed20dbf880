import { Database } from "@/lib/supabase/types"

export type ProjectRow = Database['public']['Tables']['projects']['Row']
export type UserRow = Database['public']['Tables']['users']['Row']
export type ServiceRequestRow = Database['public']['Tables']['work_orders']['Row']
export type NotificationRow = Database['public']['Tables']['notifications']['Row']
export type WorkOrderTaskRow = Database['public']['Tables']['work_order_tasks']['Row']

export interface User {
  id: string;
  name: string;
  avatar?: string;
  email?: string;
}

export interface Activity {
  id: string;
  user: {
    name: string;
    avatar?: string;
    email?: string;
  };
  action: string;
  target: string;
  timestamp: string | Date;
  type: "project" | "order" | "document" | "user" | "system";
}

export interface Task {
  id: string;
  title: string;
  completed: boolean;
  dueDate?: string | Date;
  priority: "low" | "medium" | "high";
  project?: string;
  assignedTo?: {
    name: string;
    avatar?: string;
  };
}

export interface MonthlyStats {
  name: string;
  projects: number;
  orders: number;
  documents: number;
  users: number;
}

export interface MetricsState {
  projectsCount: number;
  activeProjectsCount: number;
  ordersCount: number;
  pendingOrdersCount: number;
  documentsCount: number;
  usersCount: number;
  serviceRequestsCount: number;
  pendingServiceRequestsCount: number;
  customerEquipmentCount: number;
  maintenanceSchedulesCount: number;
  overdueMaintenanceCount: number;
  projectsTrend: number;
  ordersTrend: number;
  documentsTrend: number;
  usersTrend: number;
  serviceRequestsTrend: number;
  maintenanceTrend: number;
}

export function mapUserRowToUser(user: UserRow): User {
  return {
    id: user.id,
    name: `${user.first_name || ''} ${user.last_name || ''}`.trim() || 'Usuario sin nombre',
    email: user.email || undefined,
    avatar: user.avatar_url || undefined
  }
}

export function mapNotificationToActivity(notification: NotificationRow): Activity {
  const titleParts = notification.title.split(':')
  const action = titleParts[0] || 'realizó una acción'
  const target = titleParts[1] || notification.description || 'un elemento'

  return {
    id: notification.id,
    user: {
      name: 'Usuario',
      avatar: undefined,
      email: undefined
    },
    action,
    target,
    timestamp: notification.created_at || new Date(),
    type: (notification.type as Activity['type']) || 'system'
  }
}

export function mapWorkOrderTaskToTask(task: WorkOrderTaskRow): Task {
  return {
    id: task.id,
    title: task.title || 'Tarea sin título',
    completed: task.is_completed || false,
    dueDate: task.completed_at || undefined,
    priority: 'medium',
    project: task.work_order_id ? `Orden #${task.work_order_id}` : undefined
  }
}