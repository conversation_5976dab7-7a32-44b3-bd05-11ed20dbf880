"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { createClient } from "@/lib/supabase/client"
import { <PERSON>, Card<PERSON>ontent, CardHeader, Card<PERSON><PERSON>le, CardFooter } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Loader2, RefreshCw, AlertCircle, CheckCircle, Shield } from "lucide-react"
import { toast } from "@/hooks/use-toast"
import { sessionManager } from "@/lib/supabase/session-manager"

/**
 * Componente para diagnosticar problemas de autenticación
 */
export function AuthDiagnostics() {
  const [loading, setLoading] = useState(false)
  const [results, setResults] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  
  /**
   * Ejecuta diagnósticos de autenticación
   */
  const runDiagnostics = async () => {
    setLoading(true)
    setError(null)
    
    try {
      const supabase = createClient()
      const diagnosticResults: unknown = {}
      
      // Verificar sesión
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession()
      
      diagnosticResults.session = {
        exists: !!sessionData?.session,
        error: sessionError?.message,
        expiresAt: sessionData?.session?.expires_at 
          ? new Date(sessionData.session.expires_at * 1000).toLocaleString() 
          : 'N/A',
        user: sessionData?.session?.user?.email || 'N/A',
        userId: sessionData?.session?.user?.id || 'N/A',
        role: sessionData?.session?.user?.app_metadata?.role || 'N/A'
      }
      
      // Verificar cookies
      diagnosticResults.cookies = {
        accessToken: document.cookie.includes('sb-access-token'),
        refreshToken: document.cookie.includes('sb-refresh-token')
      }
      
      // Verificar localStorage
      try {
        const authToken = localStorage.getItem('supabase.auth.token')
        const sessionTimestamp = localStorage.getItem('auth_session_timestamp')
        
        diagnosticResults.localStorage = {
          authToken: !!authToken,
          sessionTimestamp: sessionTimestamp 
            ? new Date(parseInt(sessionTimestamp)).toLocaleString() 
            : 'N/A',
          cacheAge: sessionTimestamp 
            ? Math.round((Date.now() - parseInt(sessionTimestamp)) / 1000) + ' segundos' 
            : 'N/A'
        }
      } catch (storageError) {
        diagnosticResults.localStorage = {
          error: storageError instanceof Error ? storageError.message : 'Error desconocido'
        }
      }
      
      // Verificar conexión a Supabase
      try {
        const { error: healthError } = await supabase.from('_health').select('count').maybeSingle()
        diagnosticResults.connection = {
          success: !healthError,
          error: healthError?.message
        }
      } catch (connError) {
        diagnosticResults.connection = {
          success: false,
          error: connError instanceof Error ? connError.message : 'Error desconocido'
        }
      }
      
      // Verificar acceso a proyectos
      try {
        const { data: projectsData, error: projectsError } = await supabase
          .from('projects')
          .select('count')
          .limit(1)
        
        diagnosticResults.projects = {
          success: !projectsError,
          error: projectsError?.message,
          count: projectsData?.length || 0
        }
      } catch (projectsError) {
        diagnosticResults.projects = {
          success: false,
          error: projectsError instanceof Error ? projectsError.message : 'Error desconocido',
          count: 0
        }
      }
      
      setResults(diagnosticResults)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido al ejecutar diagnóstico')
    } finally {
      setLoading(false)
    }
  }
  
  /**
   * Refresca la sesión
   */
  const refreshSession = async () => {
    setLoading(true)
    
    try {
      const refreshed = await sessionManager.refreshSession()
      
      if (refreshed) {
        toast({
          title: "Sesión actualizada",
          description: "La sesión ha sido refrescada correctamente"
        })
        
        // Ejecutar diagnóstico nuevamente para ver los cambios
        await runDiagnostics()
      } else {
        setError('No se pudo refrescar la sesión')
        
        toast({
          title: "Error",
          description: "No se pudo refrescar la sesión",
          variant: "destructive"
        })
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido al refrescar sesión')
      
      toast({
        title: "Error",
        description: "Ocurrió un error al refrescar la sesión",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }
  
  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Shield className="mr-2 h-5 w-5" />
          Diagnóstico de Autenticación
        </CardTitle>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        {loading ? (
          <div className="flex justify-center py-4">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        ) : results ? (
          <div className="space-y-4">
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Sesión</h3>
              <div className="flex items-center">
                {results.session.exists ? (
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                ) : (
                  <AlertCircle className="h-4 w-4 text-red-500 mr-2" />
                )}
                <span>
                  {results.session.exists ? 'Sesión activa' : 'Sin sesión activa'}
                </span>
              </div>
              {results.session.exists && (
                <>
                  <div className="text-sm">Usuario: {results.session.user}</div>
                  <div className="text-sm">ID: {results.session.userId}</div>
                  <div className="text-sm">Rol: {results.session.role}</div>
                  <div className="text-sm">Expira: {results.session.expiresAt}</div>
                </>
              )}
              {results.session.error && (
                <div className="text-sm text-red-500">Error: {results.session.error}</div>
              )}
            </div>
            
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Cookies</h3>
              <div className="flex items-center">
                {results.cookies.accessToken ? (
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                ) : (
                  <AlertCircle className="h-4 w-4 text-red-500 mr-2" />
                )}
                <span>
                  {results.cookies.accessToken ? 'Access Token presente' : 'Access Token ausente'}
                </span>
              </div>
              <div className="flex items-center">
                {results.cookies.refreshToken ? (
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                ) : (
                  <AlertCircle className="h-4 w-4 text-red-500 mr-2" />
                )}
                <span>
                  {results.cookies.refreshToken ? 'Refresh Token presente' : 'Refresh Token ausente'}
                </span>
              </div>
            </div>
            
            <div className="space-y-2">
              <h3 className="text-sm font-medium">LocalStorage</h3>
              <div className="flex items-center">
                {results.localStorage.authToken ? (
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                ) : (
                  <AlertCircle className="h-4 w-4 text-red-500 mr-2" />
                )}
                <span>
                  {results.localStorage.authToken ? 'Auth Token presente' : 'Auth Token ausente'}
                </span>
              </div>
              {results.localStorage.sessionTimestamp && (
                <>
                  <div className="text-sm">Timestamp: {results.localStorage.sessionTimestamp}</div>
                  <div className="text-sm">Edad de caché: {results.localStorage.cacheAge}</div>
                </>
              )}
              {results.localStorage.error && (
                <div className="text-sm text-red-500">Error: {results.localStorage.error}</div>
              )}
            </div>
            
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Conexión a Supabase</h3>
              <div className="flex items-center">
                {results.connection.success ? (
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                ) : (
                  <AlertCircle className="h-4 w-4 text-red-500 mr-2" />
                )}
                <span>
                  {results.connection.success ? 'Conectado' : 'Error de conexión'}
                </span>
              </div>
              {results.connection.error && (
                <div className="text-sm text-red-500">Error: {results.connection.error}</div>
              )}
            </div>
            
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Acceso a Proyectos</h3>
              <div className="flex items-center">
                {results.projects.success ? (
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                ) : (
                  <AlertCircle className="h-4 w-4 text-red-500 mr-2" />
                )}
                <span>
                  {results.projects.success ? 'Acceso correcto' : 'Error de acceso'}
                </span>
              </div>
              {results.projects.error && (
                <div className="text-sm text-red-500">Error: {results.projects.error}</div>
              )}
            </div>
          </div>
        ) : (
          <div className="text-center py-4 text-muted-foreground">
            Haga clic en "Ejecutar Diagnóstico" para verificar la autenticación
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button 
          variant="outline" 
          onClick={runDiagnostics} 
          disabled={loading}
        >
          {loading ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <AlertCircle className="mr-2 h-4 w-4" />
          )}
          Ejecutar Diagnóstico
        </Button>
        <Button 
          onClick={refreshSession} 
          disabled={loading}
        >
          <RefreshCw className={`mr-2 h-4 w-4 ${loading ? "animate-spin" : ""}`} />
          Refrescar Sesión
        </Button>
      </CardFooter>
    </Card>
  )
}
