# Auto detect text files and perform LF normalization
* text=auto

# JS and TS files must always use LF for tools to work
*.js text eol=lf
*.jsx text eol=lf
*.ts text eol=lf
*.tsx text eol=lf
*.json text eol=lf

# CSS files must always use LF for tools to work
*.css text eol=lf
*.scss text eol=lf

# Environment files
*.env text eol=lf
*.env.* text eol=lf

# Config files
*.yml text eol=lf
*.yaml text eol=lf
*.config.js text eol=lf
.babelrc text eol=lf
.eslintrc text eol=lf
.prettierrc text eol=lf
.npmrc text eol=lf

# Ensure no CRLF in environment files
.env.* text eol=lf
.env text eol=lf
