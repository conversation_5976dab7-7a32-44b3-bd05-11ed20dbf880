// Configuración de OAuth para Supabase
export const oauthConfig = {
  // Configuración de Google
  google: {
    clientId: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || '',
    clientSecret: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_SECRET || '',
    redirectUrl: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/auth/callback`,
  },
  // Configuración de GitHub
  github: {
    clientId: process.env.NEXT_PUBLIC_GITHUB_CLIENT_ID || '',
    clientSecret: process.env.NEXT_PUBLIC_GITHUB_CLIENT_SECRET || '',
    redirectUrl: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/auth/callback`,
  },
};

// Función para verificar si las variables de entorno están configuradas correctamente
export function validateOAuthConfig(provider: 'google' | 'github' = 'google') {
  if (provider === 'google') {
    const { google } = oauthConfig;

    if (!google.clientId || google.clientId.includes('your-google-client-id')) {
      console.warn('NEXT_PUBLIC_GOOGLE_CLIENT_ID no está configurado correctamente');
      return false;
    }

    if (!google.clientSecret || google.clientSecret.includes('your-google-client-secret')) {
      console.warn('NEXT_PUBLIC_GOOGLE_CLIENT_SECRET no está configurado correctamente');
      return false;
    }

    return true;
  } else if (provider === 'github') {
    const { github } = oauthConfig;

    if (!github.clientId || github.clientId.includes('your-github-client-id')) {
      console.warn('NEXT_PUBLIC_GITHUB_CLIENT_ID no está configurado correctamente');
      return false;
    }

    if (!github.clientSecret || github.clientSecret.includes('your-github-client-secret')) {
      console.warn('NEXT_PUBLIC_GITHUB_CLIENT_SECRET no está configurado correctamente');
      return false;
    }

    return true;
  }

  return false;
}

// Función para obtener la URL de redirección para OAuth
export function getOAuthRedirectUrl(provider: 'google' | 'github') {
  return oauthConfig[provider].redirectUrl;
}
