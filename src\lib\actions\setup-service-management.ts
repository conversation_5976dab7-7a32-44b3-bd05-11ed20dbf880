"use server"

import { createClient } from "@/lib/supabase/server"
import { revalidatePath } from "next/cache"
import { tableExists } from "@/lib/supabase/table-utils"

/**
 * Setup the service management database tables
 *
 * @returns Result of the setup operation
 */
export async function setupServiceManagement(): Promise<{ success: boolean; message: string }> {
  try {
    const supabase = createClient()

    // Check if service_requests table already exists
    const serviceRequestsExists = await tableExists(supabase, 'service_requests')

    if (serviceRequestsExists) {
      return {
        success: false,
        message: "Service management tables already exist. No changes were made."
      }
    }

    // Create service management tables
    const { error } = await supabase.rpc('execute_sql', {
      sql_query: `
        -- 1. Service Requests Table
        CREATE TABLE IF NOT EXISTS public.service_requests (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          title TEXT NOT NULL,
          description TEXT,
          client_id UUID REFERENCES public.clients(id) ON DELETE SET NULL,
          status TEXT NOT NULL DEFAULT 'pending',
          priority TEXT NOT NULL DEFAULT 'medium',
          source TEXT, -- email, phone, web, etc.
          created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
          assigned_to UUID REFERENCES auth.users(id) ON DELETE SET NULL,
          due_date TIMESTAMP WITH TIME ZONE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          resolution_notes TEXT,
          resolution_date TIMESTAMP WITH TIME ZONE,
          work_order_id UUID REFERENCES public.work_orders(id) ON DELETE SET NULL,
          location TEXT, -- Service location
          estimated_hours NUMERIC, -- Estimated hours to complete
          actual_hours NUMERIC, -- Actual hours spent
          is_billable BOOLEAN DEFAULT TRUE,
          external_reference TEXT -- Reference number from external system
        );

        -- 2. Customer Equipment Table
        CREATE TABLE IF NOT EXISTS public.customer_equipment (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          name TEXT NOT NULL,
          model TEXT,
          serial_number TEXT,
          client_id UUID REFERENCES public.clients(id) ON DELETE CASCADE,
          location TEXT,
          installation_date TIMESTAMP WITH TIME ZONE,
          warranty_start_date TIMESTAMP WITH TIME ZONE,
          warranty_end_date TIMESTAMP WITH TIME ZONE,
          status TEXT DEFAULT 'active',
          notes TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
          inventory_item_id UUID REFERENCES public.inventory_items(id) ON DELETE SET NULL,
          manufacturer TEXT,
          purchase_date TIMESTAMP WITH TIME ZONE,
          purchase_price NUMERIC,
          expected_lifetime_months INTEGER,
          last_service_date TIMESTAMP WITH TIME ZONE,
          qr_code TEXT, -- QR code for quick equipment identification
          image_url TEXT -- URL to equipment image
        );

        -- 3. Maintenance Schedules Table
        CREATE TABLE IF NOT EXISTS public.maintenance_schedules (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          equipment_id UUID REFERENCES public.customer_equipment(id) ON DELETE CASCADE,
          maintenance_type TEXT NOT NULL, -- preventive, corrective
          frequency TEXT, -- daily, weekly, monthly, quarterly, yearly
          last_maintenance_date TIMESTAMP WITH TIME ZONE,
          next_maintenance_date TIMESTAMP WITH TIME ZONE,
          description TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
          is_active BOOLEAN DEFAULT TRUE,
          estimated_duration_hours NUMERIC,
          checklist_template_id UUID, -- Reference to a checklist template if implemented
          notification_days_before INTEGER DEFAULT 7, -- Days before to send notification
          repeat_count INTEGER DEFAULT 0 -- Number of times this maintenance has been performed
        );

        -- 4. Service Activities Table
        CREATE TABLE IF NOT EXISTS public.service_activities (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          service_request_id UUID REFERENCES public.service_requests(id) ON DELETE CASCADE,
          equipment_id UUID REFERENCES public.customer_equipment(id) ON DELETE SET NULL,
          activity_type TEXT NOT NULL, -- diagnosis, repair, installation, maintenance
          description TEXT,
          start_time TIMESTAMP WITH TIME ZONE,
          end_time TIMESTAMP WITH TIME ZONE,
          technician_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
          status TEXT DEFAULT 'pending',
          notes TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          location_coordinates TEXT, -- GPS coordinates where service was performed
          travel_time_minutes INTEGER, -- Time spent traveling to location
          is_remote BOOLEAN DEFAULT FALSE, -- Whether service was performed remotely
          diagnostic_results TEXT, -- Findings from diagnosis
          resolution_code TEXT, -- Standardized resolution code
          follow_up_required BOOLEAN DEFAULT FALSE -- Whether follow-up is needed
        );

        -- 5. Service Parts Used Table
        CREATE TABLE IF NOT EXISTS public.service_parts_used (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          service_activity_id UUID REFERENCES public.service_activities(id) ON DELETE CASCADE,
          inventory_item_id UUID REFERENCES public.inventory_items(id) ON DELETE SET NULL,
          quantity NUMERIC NOT NULL,
          unit_cost NUMERIC,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          is_billable BOOLEAN DEFAULT TRUE,
          is_warranty_covered BOOLEAN DEFAULT FALSE,
          notes TEXT,
          batch_number TEXT, -- For tracking specific batches of parts
          source_location TEXT -- Where the part was sourced from (warehouse, vehicle, etc.)
        );

        -- 6. Service Signatures Table
        CREATE TABLE IF NOT EXISTS public.service_signatures (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          service_activity_id UUID REFERENCES public.service_activities(id) ON DELETE CASCADE,
          signature_data TEXT, -- Base64 encoded signature
          signed_by TEXT,
          signed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          client_id UUID REFERENCES public.clients(id) ON DELETE SET NULL,
          ip_address TEXT, -- IP address where signature was captured
          device_info TEXT, -- Information about the device used
          geo_location TEXT, -- Geographic location where signature was captured
          signature_type TEXT DEFAULT 'customer' -- customer, technician, supervisor, etc.
        );

        -- 7. Service Attachments Table
        CREATE TABLE IF NOT EXISTS public.service_attachments (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          service_request_id UUID REFERENCES public.service_requests(id) ON DELETE CASCADE,
          service_activity_id UUID REFERENCES public.service_activities(id) ON DELETE CASCADE,
          file_name TEXT NOT NULL,
          file_type TEXT,
          file_size INTEGER,
          file_url TEXT,
          uploaded_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
          uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          description TEXT,
          is_before_photo BOOLEAN, -- Whether this is a "before" photo
          is_after_photo BOOLEAN -- Whether this is an "after" photo
        );

        -- 8. Service Checklists Table
        CREATE TABLE IF NOT EXISTS public.service_checklists (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          name TEXT NOT NULL,
          description TEXT,
          created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          is_active BOOLEAN DEFAULT TRUE,
          equipment_type TEXT, -- Type of equipment this checklist is for
          maintenance_type TEXT -- Type of maintenance this checklist is for
        );

        -- 9. Service Checklist Items Table
        CREATE TABLE IF NOT EXISTS public.service_checklist_items (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          checklist_id UUID REFERENCES public.service_checklists(id) ON DELETE CASCADE,
          description TEXT NOT NULL,
          order_number INTEGER,
          is_required BOOLEAN DEFAULT TRUE,
          item_type TEXT DEFAULT 'checkbox', -- checkbox, text, number, photo
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- 10. Service Checklist Responses Table
        CREATE TABLE IF NOT EXISTS public.service_checklist_responses (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          service_activity_id UUID REFERENCES public.service_activities(id) ON DELETE CASCADE,
          checklist_item_id UUID REFERENCES public.service_checklist_items(id) ON DELETE CASCADE,
          response TEXT,
          completed_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
          completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          notes TEXT,
          attachment_url TEXT -- URL to any attachment related to this response
        );

        -- Modify existing tables to support service management

        -- 11. Modify Work Orders Table
        ALTER TABLE public.work_orders
        ADD COLUMN IF NOT EXISTS service_type TEXT,
        ADD COLUMN IF NOT EXISTS equipment_id UUID REFERENCES public.customer_equipment(id) ON DELETE SET NULL,
        ADD COLUMN IF NOT EXISTS maintenance_schedule_id UUID REFERENCES public.maintenance_schedules(id) ON DELETE SET NULL,
        ADD COLUMN IF NOT EXISTS is_service_related BOOLEAN DEFAULT FALSE;

        -- 12. Modify Inventory Transactions Table
        ALTER TABLE public.inventory_transactions
        ADD COLUMN IF NOT EXISTS service_activity_id UUID REFERENCES public.service_activities(id) ON DELETE SET NULL,
        ADD COLUMN IF NOT EXISTS is_service_related BOOLEAN DEFAULT FALSE;

        -- Create indexes for performance
        CREATE INDEX IF NOT EXISTS idx_service_requests_client_id ON public.service_requests(client_id);
        CREATE INDEX IF NOT EXISTS idx_service_requests_assigned_to ON public.service_requests(assigned_to);
        CREATE INDEX IF NOT EXISTS idx_service_requests_status ON public.service_requests(status);
        CREATE INDEX IF NOT EXISTS idx_customer_equipment_client_id ON public.customer_equipment(client_id);
        CREATE INDEX IF NOT EXISTS idx_maintenance_schedules_equipment_id ON public.maintenance_schedules(equipment_id);
        CREATE INDEX IF NOT EXISTS idx_maintenance_schedules_next_date ON public.maintenance_schedules(next_maintenance_date);
        CREATE INDEX IF NOT EXISTS idx_service_activities_request_id ON public.service_activities(service_request_id);
        CREATE INDEX IF NOT EXISTS idx_service_activities_technician_id ON public.service_activities(technician_id);
        CREATE INDEX IF NOT EXISTS idx_service_parts_used_activity_id ON public.service_parts_used(service_activity_id);

        -- Enable Row Level Security
        ALTER TABLE public.service_requests ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.customer_equipment ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.maintenance_schedules ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.service_activities ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.service_parts_used ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.service_signatures ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.service_attachments ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.service_checklists ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.service_checklist_items ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.service_checklist_responses ENABLE ROW LEVEL SECURITY;

        -- Create RLS policies (basic authenticated access)
        CREATE POLICY "Authenticated users can read service_requests"
          ON public.service_requests FOR SELECT
          USING (auth.role() = 'authenticated');

        CREATE POLICY "Authenticated users can insert service_requests"
          ON public.service_requests FOR INSERT
          WITH CHECK (auth.role() = 'authenticated');

        CREATE POLICY "Authenticated users can update service_requests"
          ON public.service_requests FOR UPDATE
          USING (auth.role() = 'authenticated');

        CREATE POLICY "Authenticated users can delete service_requests"
          ON public.service_requests FOR DELETE
          USING (auth.role() = 'authenticated');

        -- Create function to create work order from service request
        CREATE OR REPLACE FUNCTION create_work_order_from_service_request(
          service_request_id UUID,
          work_order_title TEXT,
          work_order_description TEXT,
          work_order_priority TEXT,
          work_order_assigned_to UUID
        )
        RETURNS JSON
        LANGUAGE plpgsql
        SECURITY DEFINER
        AS $$
        DECLARE
          v_service_request RECORD;
          v_work_order_id UUID;
          v_result JSON;
        BEGIN
          -- Get the service request
          SELECT * INTO v_service_request FROM public.service_requests WHERE id = service_request_id;

          IF NOT FOUND THEN
            RETURN json_build_object('error', 'Service request not found');
          END IF;

          -- Check if work order already exists
          IF v_service_request.work_order_id IS NOT NULL THEN
            RETURN json_build_object('error', 'Work order already exists for this service request');
          END IF;

          -- Create the work order
          INSERT INTO public.work_orders (
            title,
            description,
            status,
            priority,
            assigned_to,
            due_date,
            client_id,
            service_type,
            equipment_id,
            is_service_related
          ) VALUES (
            work_order_title,
            work_order_description,
            'pending',
            work_order_priority,
            work_order_assigned_to,
            v_service_request.due_date,
            v_service_request.client_id,
            'service_request',
            (SELECT equipment_id FROM public.service_activities WHERE service_request_id = service_request_id LIMIT 1),
            TRUE
          )
          RETURNING id INTO v_work_order_id;

          -- Update the service request with the work order ID
          UPDATE public.service_requests
          SET work_order_id = v_work_order_id,
              updated_at = NOW()
          WHERE id = service_request_id;

          -- Return the result
          v_result := json_build_object(
            'id', v_work_order_id,
            'service_request_id', service_request_id
          );

          RETURN v_result;
        END;
        $$;
      `
    })

    if (error) {
      console.error('Error setting up service management tables:', error)
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

      return {
        success: false,
        message: `Error setting up service management tables: ${errorMessage}`
      }
    }

    // Revalidate paths
    revalidatePath('/dashboard/service-requests')
    revalidatePath('/dashboard/customer-equipment')
    revalidatePath('/dashboard/maintenance')

    return {
      success: true,
      message: "Service management tables created successfully."
    }
  } catch (error) {
    console.error('Error in setupServiceManagement:', error)
    return {
      success: false,
      message: `An unexpected error occurred: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}
