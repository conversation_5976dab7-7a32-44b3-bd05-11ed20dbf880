"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import {
  FileText,
  Upload,
  Download,
  Trash2,
  Eye,
  Calendar,
  User,
  FileIcon
} from "lucide-react"
import { format } from "date-fns"
import { es } from "date-fns/locale"
import { useToast } from "@/hooks/use-toast"
import { getBaseUrl } from "@/utils/get-base-url"
import { UserDocumentUpload } from "./user-document-upload"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

interface UserDocument {
  id: string
  filename: string
  file_url: string
  file_type: string
  file_size: number
  description?: string
  category?: string
  created_at: string
  uploaded_by?: string
}

interface UserDocumentsSectionProps {
  userId: string
  userEmail: string
  userName?: string
}

const DOCUMENT_CATEGORIES = {
  identification: "Identificación",
  contract: "Contrato",
  certificate: "Certificado",
  personal: "Personal",
  other: "Otro"
}

const getFileIcon = (fileType: string) => {
  if (fileType.includes('pdf')) return <FileText className="h-4 w-4" />
  if (fileType.includes('image')) return <FileIcon className="h-4 w-4" />
  if (fileType.includes('word') || fileType.includes('document')) return <FileText className="h-4 w-4" />
  return <FileIcon className="h-4 w-4" />
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

export function UserDocumentsSection({ userId, userEmail, userName }: UserDocumentsSectionProps) {
  const { toast } = useToast()
  const [documents, setDocuments] = useState<UserDocument[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [showUpload, setShowUpload] = useState(false)
  const [deletingDocId, setDeletingDocId] = useState<string | null>(null)

  const fetchUserDocuments = async () => {
    setIsLoading(true)
    try {
      const response = await fetch(`${getBaseUrl()}/api/users/${userId}/documents`)

      if (!response.ok) {
        throw new Error('Error al cargar los documentos')
      }

      const data = await response.json()
      setDocuments(data)
    } catch (error: unknown) {
      console.error("Error al cargar documentos:", error)
      toast({
        title: "Error",
        description: "No se pudieron cargar los documentos del usuario",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchUserDocuments()
  }, [fetchUserDocuments])

  const handleDeleteDocument = async (documentId: string) => {
    setDeletingDocId(documentId)
    try {
      const response = await fetch(`${getBaseUrl()}/api/users/${userId}/documents/${documentId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Error al eliminar el documento')
      }

      toast({
        title: "Documento eliminado",
        description: "El documento se ha eliminado correctamente",
      })

      // Actualizar la lista de documentos
      await fetchUserDocuments()
    } catch (error: unknown) {
      console.error("Error al eliminar documento:", error)
      toast({
        title: "Error",
        description: "No se pudo eliminar el documento",
        variant: "destructive",
      })
    } finally {
      setDeletingDocId(null)
    }
  }

  const handleUploadComplete = () => {
    setShowUpload(false)
    fetchUserDocuments()
  }

  const groupedDocuments = documents.reduce((acc, doc) => {
    const category = doc.category || 'other'
    if (!acc[category]) {
      acc[category] = []
    }
    acc[category].push(doc)
    return acc
  }, {} as Record<string, UserDocument[]>)

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Documentos del Usuario</CardTitle>
          <CardDescription>Cargando documentos...</CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Documentos del Usuario</CardTitle>
          <CardDescription>
            Documentos asociados a {userName || userEmail}
          </CardDescription>
        </div>
        <Button onClick={() => setShowUpload(!showUpload)}>
          <Upload className="mr-2 h-4 w-4" />
          {showUpload ? 'Cancelar' : 'Subir Documento'}
        </Button>
      </CardHeader>
      <CardContent>
        {showUpload && (
          <div className="mb-6">
            <UserDocumentUpload
              userId={userId}
              onUploadComplete={handleUploadComplete}
              onCancel={() => setShowUpload(false)}
            />
          </div>
        )}

        {documents.length === 0 ? (
          <div className="text-center py-8">
            <FileText className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="mt-2 text-sm font-medium">No hay documentos</h3>
            <p className="mt-1 text-sm text-muted-foreground">
              Este usuario no tiene documentos asociados.
            </p>
          </div>
        ) : (
          <Tabs defaultValue="all" className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="all">Todos ({documents.length})</TabsTrigger>
              {Object.entries(groupedDocuments).map(([category, docs]) => (
                <TabsTrigger key={category} value={category}>
                  {DOCUMENT_CATEGORIES[category as keyof typeof DOCUMENT_CATEGORIES]} ({docs.length})
                </TabsTrigger>
              ))}
            </TabsList>

            <TabsContent value="all">
              <div className="space-y-4">
                {documents.map((doc) => (
                  <DocumentCard
                    key={doc.id}
                    document={doc}
                    onDelete={handleDeleteDocument}
                    isDeleting={deletingDocId === doc.id}
                  />
                ))}
              </div>
            </TabsContent>

            {Object.entries(groupedDocuments).map(([category, docs]) => (
              <TabsContent key={category} value={category}>
                <div className="space-y-4">
                  {docs.map((doc) => (
                    <DocumentCard
                      key={doc.id}
                      document={doc}
                      onDelete={handleDeleteDocument}
                      isDeleting={deletingDocId === doc.id}
                    />
                  ))}
                </div>
              </TabsContent>
            ))}
          </Tabs>
        )}
      </CardContent>
    </Card>
  )
}

interface DocumentCardProps {
  document: UserDocument
  onDelete: (id: string) => void
  isDeleting: boolean
}

function DocumentCard({ document, onDelete, isDeleting }: DocumentCardProps) {
  return (
    <div className="flex items-center justify-between border rounded-lg p-4">
      <div className="flex items-center space-x-4">
        <div className="flex-shrink-0">
          {getFileIcon(document.file_type)}
        </div>
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium truncate">
            {document.filename}
          </p>
          {document.description && (
            <p className="text-sm text-muted-foreground truncate">
              {document.description}
            </p>
          )}
          <div className="flex items-center space-x-4 mt-1">
            <span className="text-xs text-muted-foreground">
              {formatFileSize(document.file_size)}
            </span>
            <span className="text-xs text-muted-foreground flex items-center">
              <Calendar className="mr-1 h-3 w-3" />
              {format(new Date(document.created_at), "dd/MM/yyyy", { locale: es })}
            </span>
            {document.category && (
              <Badge variant="outline" className="text-xs">
                {DOCUMENT_CATEGORIES[document.category as keyof typeof DOCUMENT_CATEGORIES]}
              </Badge>
            )}
          </div>
        </div>
      </div>
      <div className="flex items-center space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => window.open(document.file_url, '_blank')}
        >
          <Eye className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            const link = document.createElement('a')
            link.href = document.file_url
            link.download = document.filename
            link.click()
          }}
        >
          <Download className="h-4 w-4" />
        </Button>
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button variant="outline" size="sm" className="text-red-600">
              <Trash2 className="h-4 w-4" />
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>¿Eliminar documento?</AlertDialogTitle>
              <AlertDialogDescription>
                Esta acción no se puede deshacer. Se eliminará permanentemente el documento
                <strong> {document.filename}</strong>.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancelar</AlertDialogCancel>
              <AlertDialogAction
                onClick={() => onDelete(document.id)}
                disabled={isDeleting}
                className="bg-red-600 hover:bg-red-700"
              >
                {isDeleting ? "Eliminando..." : "Eliminar"}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  )
}
