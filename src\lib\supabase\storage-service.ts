import { createClient } from './client';

/**
 * Descarga un archivo desde el bucket de Supabase
 * @param bucketName Nombre del bucket
 * @param filePath Ruta del archivo dentro del bucket
 * @returns El blob del archivo y metadata, o error si falla
 */
export async function downloadFileFromBucket(bucketName: string, filePath: string) {
  try {
    const supabase = createClient();

    // Verificar acceso al bucket
    const { data: bucketList, error: bucketError } = await supabase.storage
      .from(bucketName)
      .list('', { limit: 1 });

    if (bucketError) {
      throw new Error(`Error al acceder al bucket: ${bucketError.message}`);
    }

    // Descargar el archivo
    const { data, error } = await supabase.storage
      .from(bucketName)
      .download(filePath);

    if (error) {
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

      throw new Error(`Error al descargar el archivo: ${errorMessage}`);
    }

    if (!data) {
      throw new Error('No se pudo obtener el archivo');
    }

    return {
      blob: data,
      metadata: {
        contentType: data.type,
        size: data.size
      }
    };

  } catch (error) {
    console.error('Error en downloadFileFromBucket:', error);
    throw error;
  }
}

/**
 * Obtiene una URL firmada para acceder a un archivo
 * @param bucketName Nombre del bucket
 * @param filePath Ruta del archivo dentro del bucket
 * @param expiresIn Tiempo de expiración en segundos (por defecto 60)
 * @returns URL firmada para acceder al archivo
 */
export async function getSignedUrl(bucketName: string, filePath: string, expiresIn: number = 60) {
  try {
    const supabase = createClient();

    const { data, error } = await supabase.storage
      .from(bucketName)
      .createSignedUrl(filePath, expiresIn);

    if (error) {
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

      throw new Error(`Error al crear URL firmada: ${errorMessage}`);
    }

    if (!data?.signedUrl) {
      throw new Error('No se pudo obtener la URL firmada');
    }

    return data.signedUrl;

  } catch (error) {
    console.error('Error en getSignedUrl:', error);
    throw error;
  }
}