/**
 * @file Servicio centralizado para manejo de errores
 * @description Proporciona funciones para manejar errores de forma consistente en toda la aplicación
 */

import { toast } from "@/hooks/use-toast";

// Tipos de errores que pueden ocurrir en la aplicación
export enum ErrorType {
  // Errores de validación
  VALIDATION = 'validation',
  UUID_INVALID = 'uuid_invalid',
  DATA_TYPE = 'data_type',
  REQUIRED_FIELD = 'required_field',
  EMPTY_FIELD = 'empty_field',

  // Errores de conexión
  CONNECTION = 'connection',
  API_ERROR = 'api_error',

  // Errores de autenticación
  AUTH = 'auth',

  // Errores de base de datos
  DATABASE = 'database',

  // Otros errores
  UNKNOWN = 'unknown'
}

// Interfaz para errores estructurados
export interface ErrorDetails {
  type: ErrorType;
  code: string;
  message: string;
  field?: string;
  details?: string;
  suggestions?: string[];
  originalError?: unknown;
}

/**
 * Servicio para manejar errores de forma centralizada
 */
class ErrorHandlerService {
  private static instance: ErrorHandlerService;

  // Registro de errores para análisis
  private errorLog: ErrorDetails[] = [];

  // Mensajes de error personalizados para tipos comunes
  private errorMessages: Record<string, string> = {
    'uuid_invalid': 'El formato del identificador no es válido',
    'invalid_input_syntax_for_type_uuid': 'El formato del identificador no es válido',
    '22P02': 'El formato del identificador no es válido',
    'foreign_key_violation': 'La referencia a otro registro no es válida',
    '23503': 'La referencia a otro registro no es válida',
    'check_constraint_violation': 'El valor proporcionado no es válido',
    '23514': 'El valor proporcionado no es válido para este campo',
    'not_null_violation': 'Este campo es obligatorio',
    '23502': 'Este campo es obligatorio',
    'required_field': 'Este campo es obligatorio',
    'empty_field': 'Este campo no puede estar vacío',
  };

  // Sugerencias para tipos comunes de errores
  private errorSuggestions: Record<string, string[]> = {
    'uuid_invalid': [
      'Seleccione un valor válido de la lista desplegable',
      'Deje el campo vacío si no aplica',
      'Verifique que el formato del ID sea correcto'
    ],
    'invalid_input_syntax_for_type_uuid': [
      'Seleccione un valor válido de la lista desplegable',
      'Deje el campo vacío si no aplica',
      'Verifique que el formato del ID sea correcto'
    ],
    'required_field': [
      'Complete este campo obligatorio',
      'No puede continuar sin completar este campo'
    ],
    'empty_field': [
      'Ingrese un valor válido',
      'Este campo no puede quedar en blanco'
    ],
    'check_constraint_violation': [
      'Verifique que el valor esté dentro de las opciones permitidas',
      'Revise los valores válidos para este campo',
      'Contacte al administrador si necesita ayuda'
    ],
    '23514': [
      'El valor seleccionado no está permitido para este campo',
      'Verifique las opciones disponibles en el formulario',
      'Contacte al administrador si el problema persiste'
    ],
    'not_null_violation': [
      'Este campo es obligatorio y debe tener un valor',
      'Complete el campo antes de continuar',
      'Verifique que no haya campos vacíos'
    ],
    '23502': [
      'Este campo es obligatorio y debe tener un valor',
      'Complete el campo antes de continuar',
      'Verifique que no haya campos vacíos'
    ],
  };

  /**
   * Constructor privado para implementar el patrón Singleton
   */
  private constructor() {
    // Inicialización del servicio
  }

  /**
   * Obtiene la instancia única del servicio
   */
  public static getInstance(): ErrorHandlerService {
    if (!ErrorHandlerService.instance) {
      ErrorHandlerService.instance = new ErrorHandlerService();
    }
    return ErrorHandlerService.instance;
  }

  /**
   * Maneja un error y devuelve detalles estructurados
   * @param error Error original
   * @param context Contexto adicional
   * @returns Detalles estructurados del error
   */
  public handleError(error: unknown, context?: { field?: string, action?: string }): ErrorDetails {
    // Determinar el tipo de error
    const errorDetails = this.parseError(error, context);

    // Registrar el error para análisis
    this.logError(errorDetails);

    // Devolver los detalles estructurados
    return errorDetails;
  }

  /**
   * Analiza un error y determina su tipo y detalles
   * @param error Error original
   * @param context Contexto adicional
   * @returns Detalles estructurados del error
   */
  private parseError(error: unknown, context?: { field?: string, action?: string }): ErrorDetails {
    let errorType = ErrorType.UNKNOWN;
    let errorCode = 'unknown';
    let errorMessage = 'Ha ocurrido un error inesperado';
    let errorField = context?.field;
    let suggestions: string[] = ['Intente nuevamente'];

    // Extraer mensaje de error si es un objeto Error
    let originalMessage: string;
    if (error instanceof Error) {
      originalMessage = error.message;
    } else if (typeof error === 'object' && error !== null && 'message' in error && typeof error.message === 'string') {
      originalMessage = error.message;
    } else if (typeof error === 'object' && error !== null && 'code' in error && typeof error.code === 'string') {
      originalMessage = error.code;
    } else {
      originalMessage = String(error);
    }

    // Detectar errores de campo requerido o vacío
    if (
      originalMessage.includes('required') ||
      originalMessage.includes('obligatorio') ||
      originalMessage.includes('required_field')
    ) {
      errorType = ErrorType.REQUIRED_FIELD;
      errorCode = 'required_field';
      errorMessage = this.errorMessages[errorCode] || 'Este campo es obligatorio';
      suggestions = this.errorSuggestions[errorCode] || suggestions;

      // Si tenemos información del campo, personalizar el mensaje
      if (errorField) {
        errorMessage = `El campo ${errorField} es obligatorio`;
      }
    }
    // Detectar errores de campo vacío
    else if (
      originalMessage.includes('empty') ||
      originalMessage.includes('vacío') ||
      originalMessage.includes('blank') ||
      originalMessage.includes('empty_field')
    ) {
      errorType = ErrorType.EMPTY_FIELD;
      errorCode = 'empty_field';
      errorMessage = this.errorMessages[errorCode] || 'Este campo no puede estar vacío';
      suggestions = this.errorSuggestions[errorCode] || suggestions;

      // Si tenemos información del campo, personalizar el mensaje
      if (errorField) {
        errorMessage = `El campo ${errorField} no puede estar vacío`;
      }
    }
    // Detectar errores de UUID
    else if (
      originalMessage.includes('invalid input syntax for type uuid') ||
      originalMessage.includes('22P02') ||
      originalMessage.includes('uuid_invalid')
    ) {
      errorType = ErrorType.UUID_INVALID;
      errorCode = 'uuid_invalid';
      errorMessage = this.errorMessages[errorCode] || 'El formato del identificador no es válido';
      suggestions = this.errorSuggestions[errorCode] || suggestions;

      // Si tenemos información del campo, personalizar el mensaje
      if (errorField) {
        errorMessage = `El formato del campo ${errorField} no es válido`;
      }
    }
    // Detectar errores de clave foránea
    else if (
      originalMessage.includes('foreign key constraint') ||
      originalMessage.includes('23503')
    ) {
      errorType = ErrorType.DATA_TYPE;
      errorCode = 'foreign_key_violation';
      errorMessage = this.errorMessages[errorCode] || 'La referencia a otro registro no es válida';

      // Si tenemos información del campo, personalizar el mensaje
      if (errorField) {
        errorMessage = `La referencia en el campo ${errorField} no es válida`;
      }
    }
    // Detectar errores de restricción de verificación (check constraint)
    else if (
      originalMessage.includes('check constraint') ||
      originalMessage.includes('23514') ||
      originalMessage.includes('projects_status_check')
    ) {
      errorType = ErrorType.VALIDATION;
      errorCode = '23514';
      errorMessage = this.errorMessages[errorCode] || 'El valor proporcionado no es válido para este campo';
      suggestions = this.errorSuggestions[errorCode] || suggestions;

      // Personalizar mensaje para restricción de estado
      if (originalMessage.includes('projects_status_check')) {
        errorMessage = 'El estado seleccionado no es válido. Por favor, seleccione un estado válido de la lista.';
        suggestions = [
          'Seleccione uno de los estados disponibles: Pendiente, Planificación, En Progreso, Completado, Cancelado, En Pausa',
          'Verifique que no haya modificado manualmente el valor del campo',
          'Contacte al administrador si el problema persiste'
        ];
      }
    }
    // Detectar errores de campo obligatorio (not null)
    else if (
      originalMessage.includes('not-null constraint') ||
      originalMessage.includes('23502') ||
      originalMessage.includes('null value in column')
    ) {
      errorType = ErrorType.REQUIRED_FIELD;
      errorCode = '23502';
      errorMessage = this.errorMessages[errorCode] || 'Este campo es obligatorio';
      suggestions = this.errorSuggestions[errorCode] || suggestions;

      // Extraer el nombre del campo del mensaje de error
      const fieldMatch = originalMessage.match(/column "([^"]+)"/);
      if (fieldMatch) {
        const fieldName = fieldMatch[1];
        errorMessage = `El campo ${fieldName} es obligatorio y no puede estar vacío.`;
        errorField = fieldName;
      }
    }
    // Errores de conexión
    else if (originalMessage.includes('network') || originalMessage.includes('connection')) {
      errorType = ErrorType.CONNECTION;
      errorCode = 'connection_error';
      errorMessage = 'Error de conexión con el servidor';
      suggestions = [
        'Verifique su conexión a internet',
        'Intente nuevamente en unos momentos',
        'Si el problema persiste, contacte al administrador'
      ];
    }
    // Otros errores de base de datos
    else if (originalMessage.includes('database') || (typeof error === 'object' && error !== null && 'code' in error && typeof error.code === 'string' && error.code.startsWith('2'))) {
      errorType = ErrorType.DATABASE;
      errorCode = (typeof error === 'object' && error !== null && 'code' in error && typeof error.code === 'string') ? error.code : 'database_error';
      errorMessage = this.errorMessages[errorCode] || 'Error en la base de datos';
    }

    return {
      type: errorType,
      code: errorCode,
      message: errorMessage,
      field: errorField,
      details: originalMessage,
      suggestions,
      originalError: error
    };
  }

  /**
   * Registra un error en el log para análisis
   * @param errorDetails Detalles del error
   */
  private logError(errorDetails: ErrorDetails): void {
    // Añadir al log local
    this.errorLog.push(errorDetails);

    // Limitar el tamaño del log
    if (this.errorLog.length > 100) {
      this.errorLog.shift();
    }

    // Registrar en consola para depuración
    console.error('Error detectado:', errorDetails);
  }

  /**
   * Muestra un toast con el error
   * @param errorDetails Detalles del error
   */
  public showErrorToast(errorDetails: ErrorDetails): void {
    toast({
      title: errorDetails.message,
      description: errorDetails.suggestions?.join(' '),
      variant: "destructive",
    });
  }

  /**
   * Obtiene el log de errores
   * @returns Log de errores
   */
  public getErrorLog(): ErrorDetails[] {
    return [...this.errorLog];
  }
}

// Exportar instancia única
export const errorHandler = ErrorHandlerService.getInstance();
