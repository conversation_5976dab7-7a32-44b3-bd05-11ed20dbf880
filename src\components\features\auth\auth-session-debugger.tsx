"use client"

import { useState, useEffect } from "react"
import { createClient } from "@/lib/supabase/client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, RefreshCw } from "lucide-react"
import { toast } from "@/hooks/use-toast"

/**
 * Componente para diagnosticar y corregir problemas con la sesión de autenticación
 */
export function AuthSessionDebugger() {
  const [isOpen, setIsOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [sessionInfo, setSessionInfo] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)

  const checkSession = async () => {
    setIsLoading(true)
    setError(null)

    try {
      const supabase = createClient()

      // Verificar si hay cookies de sesión
      const hasSessionCookie = document.cookie.includes('sb-access-token') ||
                              document.cookie.includes('sb-refresh-token')

      // Verificar si hay datos en localStorage
      let localStorageData = null
      try {
        const authToken = localStorage.getItem('supabase.auth.token')
        localStorageData = authToken ? JSON.parse(authToken) : null
      } catch (e) {
        console.error('Error parsing localStorage data:', e)
      }

      // Intentar obtener la sesión actual
      const { data, error } = await supabase.auth.getSession()

      if (error) {
        throw error
      }

      // Intentar obtener el usuario actual
      // No pasar parámetros a getUser() para compatibilidad con todas las versiones de Supabase
      const { data: userData, error: userError } = await supabase.auth.getUser()

      if (userError) {
        console.warn('Error getting user:', userError)
      }

      // Recopilar información de diagnóstico
      setSessionInfo({
        hasSession: !!data?.session,
        hasUser: !!userData?.user,
        hasSessionCookie,
        hasLocalStorage: !!localStorageData,
        sessionExpiry: data?.session?.expires_at ? new Date(data.session.expires_at * 1000).toLocaleString() : 'N/A',
        userId: userData?.user?.id || data?.session?.user?.id || 'No disponible',
        userEmail: userData?.user?.email || data?.session?.user?.email || 'No disponible',
        userRole: userData?.user?.user_metadata?.role || 'No disponible',
        timestamp: new Date().toLocaleString()
      })

      console.log('Session diagnostic info:', {
        session: data?.session,
        user: userData?.user,
        hasSessionCookie,
        hasLocalStorage: !!localStorageData
      })
    } catch (err) {
      console.error('Error checking session:', err)
      setError(err instanceof Error ? err.message : 'Error desconocido al verificar la sesión')
    } finally {
      setIsLoading(false)
    }
  }

  const fixSession = async () => {
    setIsLoading(true)

    try {
      const supabase = createClient()

      // 1. Intentar refrescar la sesión
      const { data, error } = await supabase.auth.refreshSession()

      if (error) {
        // Si no se puede refrescar, intentar cerrar sesión y volver a iniciar
        console.warn('No se pudo refrescar la sesión, cerrando sesión para reiniciar...')
        await supabase.auth.signOut({ scope: 'local' })

        toast({
          title: "Sesión reiniciada",
          description: "Por favor, inicie sesión nuevamente",
          variant: "destructive"
        })

        // Redirigir al login después de un breve retraso
        setTimeout(() => {
          window.location.href = '/auth/login?error=session_reset'
        }, 1500)

        return
      }

      if (data?.session) {
        console.log('Sesión refrescada exitosamente')

        // Actualizar timestamp de la sesión para caché
        localStorage.setItem('auth_session_timestamp', Date.now().toString())

        toast({
          title: "Sesión restaurada",
          description: "La sesión ha sido refrescada correctamente"
        })

        // Recargar la página para aplicar los cambios
        setTimeout(() => {
          window.location.reload()
        }, 1500)
      }
    } catch (err) {
      console.error('Error fixing session:', err)
      setError(err instanceof Error ? err.message : 'Error desconocido al intentar corregir la sesión')

      toast({
        title: "Error",
        description: "No se pudo restaurar la sesión",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    if (isOpen) {
      checkSession()
    }
  }, [isOpen])

  if (!isOpen) {
    return (
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsOpen(true)}
        className="text-xs"
      >
        Diagnosticar Sesión
      </Button>
    )
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Diagnóstico de Sesión</CardTitle>
        <CardDescription>
          Información sobre el estado actual de la sesión
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {isLoading ? (
          <div className="flex justify-center py-4">
            <RefreshCw className="h-6 w-6 animate-spin text-primary" />
          </div>
        ) : sessionInfo ? (
          <div className="space-y-2 text-sm">
            <div className="grid grid-cols-2 gap-2">
              <div className="font-medium">Estado de sesión:</div>
              <div>{sessionInfo.hasSession ? '✅ Activa' : '❌ Inactiva'}</div>

              <div className="font-medium">Usuario:</div>
              <div>{sessionInfo.hasUser ? '✅ Disponible' : '❌ No disponible'}</div>

              <div className="font-medium">Cookie de sesión:</div>
              <div>{sessionInfo.hasSessionCookie ? '✅ Presente' : '❌ Ausente'}</div>

              <div className="font-medium">Datos en localStorage:</div>
              <div>{sessionInfo.hasLocalStorage ? '✅ Presentes' : '❌ Ausentes'}</div>

              <div className="font-medium">Expiración:</div>
              <div>{sessionInfo.sessionExpiry}</div>

              <div className="font-medium">ID de usuario:</div>
              <div className="truncate">{sessionInfo.userId}</div>

              <div className="font-medium">Email:</div>
              <div className="truncate">{sessionInfo.userEmail}</div>

              <div className="font-medium">Rol:</div>
              <div>{typeof sessionInfo.userRole === 'string' ?
                sessionInfo.userRole :
                (Array.isArray(sessionInfo.userRole) ?
                  sessionInfo.userRole.join(', ') :
                  'Formato desconocido')}
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-4 text-muted-foreground">
            No hay información disponible
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsOpen(false)}
        >
          Cerrar
        </Button>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={checkSession}
            disabled={isLoading}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? "animate-spin" : ""}`} />
            Actualizar
          </Button>
          <Button
            variant="default"
            size="sm"
            onClick={fixSession}
            disabled={isLoading}
          >
            Restaurar Sesión
          </Button>
        </div>
      </CardFooter>
    </Card>
  )
}
