/**
 * @ai-file-description: "Google Gemini AI provider implementation"
 * @ai-related-files: ["../base-provider.ts", "../provider-interface.ts", "../provider-factory.ts"]
 * @ai-owner: "File-Based Projects"
 */

import { GoogleGenerativeAI, GenerativeModel } from '@google/generative-ai';
import { BaseAIProvider } from '../base-provider';
import { AIProviderConfig, DocumentAnalysisResult } from '../provider-interface';

/**
 * Google Gemini AI provider implementation
 *
 * @ai-responsibility: "Implements document analysis using Google's Gemini AI"
 */
export class GeminiProvider extends BaseAIProvider {
  private genAI: GoogleGenerativeAI;
  private model: GenerativeModel;

  constructor(config: AIProviderConfig) {
    super(config);
    this.genAI = new GoogleGenerativeAI(this.apiKey);
    this.model = this.genAI.getGenerativeModel({
      model: this.modelName || 'gemini-pro'
    });
  }

  /**
   * Gets the name of the provider
   */
  getProviderName(): string {
    return 'gemini';
  }

  /**
   * Gets the display name of the provider for UI
   */
  getProviderDisplayName(): string {
    return 'Google Gemini';
  }

  /**
   * Analyzes document text using Google Gemini
   *
   * @param documentText The text content of the document
   * @returns Structured project information
   */
  async analyzeDocument(documentText: string): Promise<DocumentAnalysisResult> {
    try {
      // Validate configuration
      if (!this.validateConfig()) {
        console.error('Invalid Gemini configuration');
        return this.getFallbackAnalysisResult('Invalid Gemini configuration. Please check API key and model name.');
      }

      // Check if document text is valid
      if (!documentText || documentText.trim().length < 10) {
        console.warn('Document text is too short or empty');
        return this.getFallbackAnalysisResult('Document text is too short or empty. Please provide a valid document.');
      }

      // Check if we're using a dummy API key (for development/testing)
      if (this.apiKey === 'dummy-api-key') { // Modificada la condición para solo verificar 'dummy-api-key'
        console.log('Using mock implementation for Gemini provider');
        return this.getMockAnalysisResult(documentText);
      }

      console.log('Preparing to analyze document with Gemini, text length:', documentText.length);

      // Prepare the prompts
      const systemPrompt = this.getSystemPrompt();
      const userPrompt = this.getUserPrompt(documentText);

      // Set up a timeout for the API call
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Gemini API call timed out after 60 seconds')), 60000);
      });

      // Call Gemini API with timeout
      const apiCallPromise = this.model.generateContent([
        systemPrompt,
        userPrompt
      ]);

      // Race the API call against the timeout
      const result = await Promise.race([apiCallPromise, timeoutPromise]);

      const response = result.response;
      const text = response.text();

      if (!text) {
        console.error('Empty response from Gemini');
        return this.getFallbackAnalysisResult('Empty response from Gemini API. Please try again later.');
      }

      console.log('Received response from Gemini, processing...');

      // Extract JSON from the response
      const jsonMatch = text.match(/```json\n([\s\S]*?)\n```/) ||
                      text.match(/```\n([\s\S]*?)\n```/) ||
                      text.match(/{[\s\S]*?}/);

      let jsonText = jsonMatch ? jsonMatch[0] : text;

      // Clean up the JSON text
      jsonText = jsonText.replace(/```json\n|```\n|```/g, '');

      let parsedResponse;
      try {
        parsedResponse = JSON.parse(jsonText);
      } catch (e) {
        console.error('Failed to parse Gemini response as JSON:', e);
        console.log('Raw response:', text);

        // Try to extract JSON more aggressively
        try {
          // Look for anything that looks like a JSON object
          const potentialJson = text.match(/{[\s\S]*}/)?.[0];
          if (potentialJson) {
            parsedResponse = JSON.parse(potentialJson);
            console.log('Successfully extracted JSON with aggressive parsing');
          } else {
            throw new Error('No JSON-like structure found');
          }
        } catch (extractError) {
          console.error('Failed aggressive JSON extraction:', extractError);
          return this.getFallbackAnalysisResult('Failed to parse Gemini response as JSON. Please try again.');
        }
      }

      return this.processResponse(parsedResponse);
    } catch (error) {
      console.error('Error analyzing document with Gemini:', error);
      return this.getFallbackAnalysisResult(`Error analyzing document: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate a mock analysis result for testing purposes
   * Used when a dummy API key is provided
   */
  private getMockAnalysisResult(documentText: string): DocumentAnalysisResult {
    // Extract some basic information from the document text to make it seem realistic
    const lines = documentText.split('\n').filter(line => line.trim().length > 0);

    // Try to extract a project name from the first few lines
    let projectName = 'New Project';
    for (let i = 0; i < Math.min(5, lines.length); i++) {
      if (lines[i].toLowerCase().includes('project') || lines[i].length < 50) {
        projectName = lines[i].trim();
        break;
      }
    }

    // Generate a description from the first few lines
    const description = lines.slice(0, 3).join(' ').substring(0, 200);

    // Generate random dates
    const today = new Date();
    const startDate = new Date(today);
    startDate.setDate(today.getDate() + 7); // Start in a week

    const endDate = new Date(startDate);
    endDate.setMonth(startDate.getMonth() + 3); // End in 3 months

    // Extract potential deliverables (lines with bullet points or numbers)
    const deliverables = lines
      .filter(line => /^[\s]*[•\-\*\d\.]+\s+/.test(line))
      .map(line => line.replace(/^[\s]*[•\-\*\d\.]+\s+/, '').trim())
      .slice(0, 5);

    // Generate some tags based on the content
    const potentialTags = ['engineering', 'development', 'design', 'planning', 'implementation', 'testing', 'deployment'];
    const tags = potentialTags.filter(() => Math.random() > 0.5).slice(0, 3);

    return {
      project_name: projectName,
      description: description,
      start_date: startDate.toISOString().split('T')[0],
      end_date: endDate.toISOString().split('T')[0],
      budget: Math.floor(Math.random() * 50000 + 10000).toString(),
      currency: 'CLP',
      client_name: 'Sample Client',
      deliverables: deliverables.length > 0 ? deliverables : ['Documentation', 'Implementation', 'Testing'],
      scope: 'The project scope includes all deliverables mentioned above.',
      team_requirements: ['Project Manager', 'Developer', 'Designer'],
      tags: tags,
      confidence_score: 0.85,
    };
  }

  /**
   * Gets the system prompt for document analysis
   * Customized for Gemini's capabilities
   */
  protected getSystemPrompt(): string {
    return `
You are a specialized AI assistant for engineering project management. Your task is to analyze project documents and extract key information in a structured format.

Extract the following information from the document:
1. Project name
2. Project description
3. Start date (in ISO format YYYY-MM-DD)
4. End date (in ISO format YYYY-MM-DD)
5. Budget amount (as a string)
6. Currency (USD, CLP, etc.)
7. Client name
8. Key deliverables (as an array)
9. Project scope
10. Team requirements (as an array)
11. Tags (as an array of keywords)

Format your response as a JSON object with these fields. If information is not found, use null.
Provide a confidence_score between 0 and 1 indicating your confidence in the extracted information.

IMPORTANT: Your response must be valid JSON. Wrap the JSON in triple backticks with the json language specifier like this:
\`\`\`json
{
  "project_name": "Example Project",
  ...
}
\`\`\`
`;
  }

  /**
   * Creates a fallback analysis result when the API fails
   *
   * @param errorMessage The error message to include
   * @returns A basic analysis result with error information
   */
  private getFallbackAnalysisResult(errorMessage: string): DocumentAnalysisResult {
    console.log('Using fallback analysis result due to error:', errorMessage);

    // Get current date
    const today = new Date();
    const startDate = new Date(today);
    startDate.setDate(today.getDate() + 7); // Start in a week

    const endDate = new Date(startDate);
    endDate.setMonth(startDate.getMonth() + 3); // End in 3 months

    return {
      project_name: 'New Project',
      description: `This is a fallback project created because document analysis failed. Error: ${errorMessage}`,
      start_date: startDate.toISOString().split('T')[0],
      end_date: endDate.toISOString().split('T')[0],
      budget: '10000',
      currency: 'CLP',
      client_name: 'Default Client',
      deliverables: ['Documentation', 'Implementation', 'Testing'],
      scope: 'Default project scope. Please update with actual information.',
      team_requirements: ['Project Manager', 'Developer'],
      tags: ['default'],
      confidence_score: 0.1, // Low confidence since this is a fallback
      error_message: errorMessage
    };
  }

  /**
   * Process the raw response from the API into the expected format
   *
   * @param response The parsed JSON response from the API
   * @returns Formatted document analysis result
   */
  protected processResponse(response: unknown): DocumentAnalysisResult { // Cambiado a protected
    // Ensure all required fields are present
    const result: DocumentAnalysisResult = {
      project_name: response.project_name || 'New Project',
      description: response.description || '',
      start_date: response.start_date || null,
      end_date: response.end_date || null,
      budget: response.budget || null,
      currency: response.currency || 'CLP',
      client_name: response.client_name || null,
      deliverables: Array.isArray(response.deliverables) ? response.deliverables : [],
      scope: response.scope || '',
      team_requirements: Array.isArray(response.team_requirements) ? response.team_requirements : [],
      tags: Array.isArray(response.tags) ? response.tags : [],
      confidence_score: typeof response.confidence_score === 'number' ? response.confidence_score : 0.5
    };

    return result;
  }

  /**
   * Gets the user prompt for document analysis
   *
   * @param documentText The document text to analyze
   * @returns Formatted user prompt
   */
  protected getUserPrompt(documentText: string): string {
    // Truncate document text if it's too long to avoid token limits
    const maxLength = 15000;
    const truncatedText = documentText.length > maxLength
      ? documentText.substring(0, maxLength) + '... [text truncated due to length]'
      : documentText;

    return `Analyze the following project document and extract the requested information:\n\n${truncatedText}`;
  }
}
