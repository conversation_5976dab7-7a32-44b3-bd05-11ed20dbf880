/**
 * @ai-file-description: "Factory for creating AI provider instances"
 * @ai-related-files: ["provider-interface.ts", "providers/gemini-provider.ts", "providers/openai-provider.ts", "providers/deepseek-provider.ts"]
 * @ai-owner: "File-Based Projects"
 */

import { AIProvider, AIProviderConfig } from './provider-interface';
import { GeminiProvider } from './providers/gemini-provider';
import { OpenAIProvider } from './providers/openai-provider';
import { DeepSeekProvider } from './providers/deepseek-provider';

/**
 * Factory for creating AI provider instances
 *
 * @ai-responsibility: "Creates and configures AI provider instances based on provider name"
 */
export class ProviderFactory {
  /**
   * Creates an instance of the appropriate AI provider
   *
   * @param providerName Name of the provider
   * @param config Provider configuration
   * @returns AI provider instance
   */
  static createProvider(providerName: string, config: AIProviderConfig): AIProvider {
    switch (providerName.toLowerCase()) {
      case 'gemini':
        return new GeminiProvider(config);
      case 'openai':
        return new OpenAIProvider(config);
      case 'deepseek':
        return new DeepSeekProvider(config);
      default:
        throw new Error(`Unsupported provider: ${providerName}`);
    }
  }

  /**
   * Gets an AI provider instance with default configuration
   *
   * @param providerName Name of the provider
   * @returns AI provider instance with default configuration
   */
  static getAIProvider(providerName: string): AIProvider {
    // Default configuration for testing/development
    const defaultConfig: AIProviderConfig = {
      apiKey: 'dummy-api-key',
      modelName: providerName === 'gemini' ? 'gemini-pro' :
                providerName === 'openai' ? 'gpt-4' : 'deepseek-chat'
    };

    return this.createProvider(providerName, defaultConfig);
  }

  /**
   * Gets a list of supported provider names
   *
   * @returns Array of provider names
   */
  static getSupportedProviders(): string[] {
    return ['gemini', 'openai', 'deepseek'];
  }

  /**
   * Gets a list of supported providers with display names
   *
   * @returns Array of provider objects with id and name
   */
  static getSupportedProvidersWithDisplayNames(): Array<{id: string, name: string}> {
    return [
      { id: 'gemini', name: 'Google Gemini' },
      { id: 'openai', name: 'OpenAI' },
      { id: 'deepseek', name: 'DeepSeek' }
    ];
  }

  /**
   * Gets the display name for a provider
   *
   * @param providerName Name of the provider
   * @returns Display name for the provider
   */
  static getProviderDisplayName(providerName: string): string {
    const displayNames: Record<string, string> = {
      'gemini': 'Google Gemini',
      'openai': 'OpenAI',
      'deepseek': 'DeepSeek'
    };

    return displayNames[providerName.toLowerCase()] || providerName;
  }
}
