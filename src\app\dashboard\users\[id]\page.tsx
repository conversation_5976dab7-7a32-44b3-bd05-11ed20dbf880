"use client"

import { useState, useEffect } from "react"
// import { useRouter } from "next/navigation"
import { ArrowLeft, Mail, Calendar, Clock, Shield, UserCog } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { createClient } from "@/lib/supabase/client"
import { getBaseUrl } from "@/lib/utils"
import { toast } from "@/hooks/use-toast"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import Link from "next/link"
import { format } from "date-fns"
import { es } from "date-fns/locale"
import { UserDocumentsSection } from "@/components/features/users/user-documents-section"

interface UserPageProps {
  params: {
    id: string
  }
}

export default function UserPage({ params }: UserPageProps) {
  // const router = useRouter() // Not needed for now
  const [user, setUser] = useState<any>(null)
  const [userProjects, setUserProjects] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isLoadingProjects, setIsLoadingProjects] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const supabase = createClient()

  useEffect(() => {
    const fetchUser = async () => {
      setIsLoading(true)
      setError(null)

      try {
        // Obtener usuario a través de la API
        const response = await fetch(`${getBaseUrl()}/api/users/${params.id}`, { cache: 'no-store' })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Error al obtener el usuario')
        }

        const userData = await response.json()

        setUser(userData)
      } catch (error: unknown) {
        console.error("Error al cargar el usuario:", error)
        const errorMessage = error instanceof Error ? error.message : "Ha ocurrido un error al cargar el usuario"

        setError(errorMessage)
      } finally {
        setIsLoading(false)
      }
    }

    fetchUser()
  }, [params.id, supabase])

  // Cargar los proyectos asignados al usuario
  useEffect(() => {
    const fetchUserProjects = async () => {
      if (!params.id) return

      setIsLoadingProjects(true)
      try {
        const { data, error } = await supabase
          .from('project_users')
          .select(`
            project_id,
            role,
            project:project_id(id, name, status, start_date, end_date, budget, currency)
          `)
          .eq('user_id', params.id)

        if (error) throw error

        setUserProjects(data || [])
      } catch (error) {
        console.error('Error al cargar los proyectos del usuario:', error)
      } finally {
        setIsLoadingProjects(false)
      }
    }

    fetchUserProjects()
  }, [params.id, supabase])

  const handleSuspendUser = async () => {
    try {
      // Suspender usuario a través de la API
      const response = await fetch(`${getBaseUrl()}/api/users/${params.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'suspended',
          role: user.role,
          full_name: user.full_name
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Error al suspender el usuario')
      }

      toast({
        title: "Usuario suspendido",
        description: "El usuario ha sido suspendido correctamente",
      })

      // Actualizar estado local
      setUser((prev: unknown) => ({ ...prev, status: 'suspended' }))
    } catch (error: unknown) {
      console.error("Error al suspender el usuario:", error)
      const errorMessage = error instanceof Error ? error.message : "Ha ocurrido un error al suspender el usuario"

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    }
  }

  const handleActivateUser = async () => {
    try {
      // Activar usuario a través de la API
      const response = await fetch(`${getBaseUrl()}/api/users/${params.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'active',
          role: user.role,
          full_name: user.full_name
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Error al activar el usuario')
      }

      toast({
        title: "Usuario activado",
        description: "El usuario ha sido activado correctamente",
      })

      // Actualizar estado local
      setUser((prev: unknown) => ({ ...prev, status: 'active' }))
    } catch (error: unknown) {
      console.error("Error al activar el usuario:", error)
      const errorMessage = error instanceof Error ? error.message : "Ha ocurrido un error al activar el usuario"

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    }
  }

  const getRoleBadge = (role: string | string[]) => {
    if (Array.isArray(role)) {
      if (role.includes("admin")) {
        return <Badge className="bg-red-500">Administrador</Badge>
      } else if (role.includes("manager")) {
        return <Badge className="bg-blue-500">Gerente</Badge>
      } else if (role.includes("user")) {
        return <Badge className="bg-green-500">Usuario</Badge>
      } else {
        return <Badge>{role.join(", ")}</Badge>
      }
    } else {
      switch (role) {
        case "admin":
          return <Badge className="bg-red-500">Administrador</Badge>
        case "manager":
          return <Badge className="bg-blue-500">Gerente</Badge>
        case "user":
          return <Badge className="bg-green-500">Usuario</Badge>
        default:
          return <Badge>{role}</Badge>
      }
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge variant="outline" className="text-green-500 border-green-500">Activo</Badge>
      case "inactive":
        return <Badge variant="outline" className="text-yellow-500 border-yellow-500">Inactivo</Badge>
      case "suspended":
        return <Badge variant="outline" className="text-red-500 border-red-500">Suspendido</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  if (isLoading) {
    return (
      <div className="flex h-[500px] items-center justify-center">
        <div className="flex flex-col items-center">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          <p className="mt-2 text-sm text-muted-foreground">Cargando usuario...</p>
        </div>
      </div>
    )
  }

  if (error || !user) {
    return (
      <div className="flex h-[500px] flex-col items-center justify-center">
        <p className="text-center text-muted-foreground">{error || "Usuario no encontrado"}</p>
        <Button
          variant="outline"
          className="mt-4"
          asChild
        >
          <Link href="/dashboard/users">
            Volver a usuarios
          </Link>
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="icon" asChild>
            <Link href="/dashboard/users">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h2 className="text-3xl font-bold tracking-tight">
            {user.full_name || "Usuario"}
          </h2>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            asChild
          >
            <Link href={`/dashboard/users/${user.id}/edit`}>
              <UserCog className="mr-2 h-4 w-4" />
              Editar
            </Link>
          </Button>
          {user.status === "suspended" ? (
            <Button
              variant="outline"
              onClick={handleActivateUser}
            >
              Activar
            </Button>
          ) : (
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive">
                  Suspender
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>¿Suspender usuario?</AlertDialogTitle>
                  <AlertDialogDescription>
                    Esta acción impedirá que el usuario inicie sesión. ¿Estás seguro?
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancelar</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleSuspendUser}
                    className="bg-red-600 hover:bg-red-700"
                  >
                    Suspender
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Información del usuario</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex flex-col items-center space-y-2">
              <Avatar className="h-20 w-20">
                <AvatarFallback className="text-xl">
                  {user.full_name
                    ? user.full_name
                        .split(" ")
                        .map((n: string) => n[0])
                        .join("")
                        .toUpperCase()
                        .substring(0, 2)
                    : user.email.substring(0, 2).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div className="text-center">
                <h3 className="font-medium text-lg">{user.full_name || "Sin nombre"}</h3>
                <div className="flex items-center justify-center space-x-1 text-sm text-muted-foreground">
                  <Mail className="h-3 w-3" />
                  <span>{user.email}</span>
                </div>
              </div>
              <div className="flex space-x-2 mt-2">
                {getRoleBadge(user.role)}
                {getStatusBadge(user.status)}
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-medium">ID</h4>
                <p className="text-sm text-muted-foreground break-all">{user.id}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium">Rol</h4>
                <div className="flex items-center space-x-2">
                  <Shield className="h-4 w-4 text-muted-foreground" />
                  <p className="text-sm">
                    {Array.isArray(user.role) && user.role.includes("admin")
                      ? "Administrador"
                      : Array.isArray(user.role) && user.role.includes("manager")
                      ? "Gerente"
                      : "Usuario"}
                  </p>
                </div>
              </div>
              <div>
                <h4 className="text-sm font-medium">Estado</h4>
                <p className="text-sm">
                  {user.status === "active"
                    ? "Activo"
                    : user.status === "inactive"
                    ? "Inactivo"
                    : "Suspendido"}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="md:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Actividad</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Fecha de creación</h4>
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <p className="text-sm">
                      {user.created_at
                        ? format(new Date(user.created_at), "PPP", { locale: es })
                        : "Desconocida"}
                    </p>
                  </div>
                </div>
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Último inicio de sesión</h4>
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <p className="text-sm">
                      {user.last_sign_in_at
                        ? format(new Date(user.last_sign_in_at), "PPP", { locale: es })
                        : "Nunca"}
                    </p>
                  </div>
                </div>
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Email confirmado</h4>
                  <p className="text-sm">
                    {user.confirmed_at ? "Sí" : "No"}
                  </p>
                </div>
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Última actualización</h4>
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <p className="text-sm">
                      {user.updated_at
                        ? format(new Date(user.updated_at), "PPP", { locale: es })
                        : "Desconocida"}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Proyectos asignados</CardTitle>
              <CardDescription>
                Proyectos en los que participa el usuario
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingProjects ? (
                <div className="flex justify-center py-6">
                  <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
                </div>
              ) : userProjects.length === 0 ? (
                <div className="text-center py-6">
                  <p className="text-muted-foreground">
                    No hay proyectos asignados a este usuario.
                  </p>
                  <Button
                    variant="outline"
                    className="mt-4"
                    asChild
                  >
                    <Link href="/dashboard/projects">
                      Ver proyectos
                    </Link>
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {userProjects.map((projectUser) => (
                    <div key={projectUser.project_id} className="flex items-center justify-between border-b pb-4">
                      <div>
                        <Link
                          href={`/dashboard/projects/${projectUser.project.id}`}
                          className="font-medium hover:underline"
                        >
                          {projectUser.project.name}
                        </Link>
                        <div className="flex items-center space-x-2 mt-1">
                          <Badge variant="outline">
                            {projectUser.project.status === "pending" ? "Pendiente" :
                             projectUser.project.status === "in_progress" ? "En progreso" :
                             projectUser.project.status === "completed" ? "Completado" :
                             projectUser.project.status === "cancelled" ? "Cancelado" :
                             projectUser.project.status}
                          </Badge>
                          <Badge variant="secondary">
                            {projectUser.role === "admin" ? "Administrador" :
                             projectUser.role === "manager" ? "Gerente" :
                             projectUser.role === "member" ? "Miembro" :
                             projectUser.role}
                          </Badge>
                        </div>
                      </div>
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/dashboard/projects/${projectUser.project.id}`}>
                          Ver
                        </Link>
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Sección de documentos del usuario */}
          <UserDocumentsSection
            userId={user.id}
            userEmail={user.email}
            userName={user.full_name}
          />
        </div>
      </div>
    </div>
  )
}
