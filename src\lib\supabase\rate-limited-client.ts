/**
 * @file Cliente de Supabase con limitación de tasa
 * @description Wrapper para el cliente de Supabase que implementa limitación de tasa para evitar exceder los límites del plan gratuito
 */

import { createClient as createSupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/lib/supabase/types';
import { rateLimiterService, OperationType, OperationPriority } from '@/lib/services/rate-limiter-service';

// Tipo para las opciones del cliente con limitación de tasa
export interface RateLimitedClientOptions {
  // URL de Supabase
  supabaseUrl: string;
  // Clave anónima de Supabase
  supabaseKey: string;
  // Habilitar limitación de tasa
  enableRateLimiting?: boolean;
  // Opciones adicionales para el cliente de Supabase
  supabaseOptions?: unknown;
}

/**
 * Crea un cliente de Supabase con limitación de tasa
 */
export function createRateLimitedClient(options: RateLimitedClientOptions) {
  const {
    supabaseUrl,
    supabaseKey,
    enableRateLimiting = true,
    supabaseOptions = {},
  } = options;

  // Crear el cliente de Supabase normal
  const supabase = createSupabaseClient<Database>(supabaseUrl, supabaseKey, supabaseOptions as any);

  // Si la limitación de tasa está deshabilitada, devolver el cliente normal
  if (!enableRateLimiting) {
    return supabase;
  }

  // Crear un proxy para interceptar las llamadas al cliente
  return new Proxy(supabase, {
    get(target, prop, receiver) {
      // Obtener la propiedad original
      const originalValue = Reflect.get(target, prop, receiver);

      // Si no es una función, devolver el valor original
      if (typeof originalValue !== 'function') {
        // Si es un objeto (como auth, storage, etc.), aplicar el proxy recursivamente
        if (originalValue && typeof originalValue === 'object') {
          return new Proxy(originalValue, {
            get(subTarget, subProp, subReceiver) {
              const subValue = Reflect.get(subTarget, subProp, subReceiver);

              // Si es una función, aplicar limitación de tasa
              if (typeof subValue === 'function') {
                return wrapWithRateLimiter(subValue, subTarget, determineOperationType(prop, String(subProp)));
              }

              // Si es otro objeto anidado, aplicar el proxy recursivamente
              if (subValue && typeof subValue === 'object') {
                return new Proxy(subValue, {
                  get(deepTarget, deepProp, deepReceiver) {
                    const deepValue = Reflect.get(deepTarget, deepProp, deepReceiver);

                    if (typeof deepValue === 'function') {
                      return wrapWithRateLimiter(deepValue, deepTarget, determineOperationType(prop, `${String(subProp)}.${String(deepProp)}`));
                    }

                    return deepValue;
                  }
                });
              }

              return subValue;
            }
          });
        }

        return originalValue;
      }

      // Envolver la función con el limitador de tasa
      return wrapWithRateLimiter(originalValue, target, determineOperationType(prop));
    }
  });
}

/**
 * Determina el tipo de operación basado en la propiedad accedida
 */
function determineOperationType(prop: string | symbol, subProp?: string): OperationType {
  const propStr = String(prop);
  const fullProp = subProp ? `${propStr}.${subProp}` : propStr;

  // Mapear propiedades a tipos de operación
  if (fullProp.includes('auth')) {
    return OperationType.AUTH;
  } else if (fullProp.includes('storage')) {
    return OperationType.STORAGE;
  } else if (fullProp.includes('functions')) {
    return OperationType.FUNCTIONS;
  } else if (fullProp.includes('realtime') || fullProp.includes('channel')) {
    return OperationType.REALTIME;
  } else {
    // Por defecto, asumir que es una operación de base de datos
    return OperationType.DATABASE;
  }
}

/**
 * Determina la prioridad de una operación basada en su tipo y nombre
 */
function determineOperationPriority(type: OperationType, methodName: string): OperationPriority {
  // Operaciones críticas que no deberían ser limitadas
  const criticalOperations = [
    'getSession',
    'refreshSession',
    'getUser',
  ];

  // Operaciones de alta prioridad
  const highPriorityOperations = [
    'signIn',
    'signOut',
    'signUp',
    'verifyOtp',
    'resetPasswordForEmail',
  ];

  // Operaciones de prioridad media
  const mediumPriorityOperations = [
    'update',
    'upsert',
    'delete',
    'remove',
    'upload',
    'download',
    'invoke',
  ];

  if (criticalOperations.some(op => methodName.includes(op))) {
    return OperationPriority.CRITICAL;
  } else if (highPriorityOperations.some(op => methodName.includes(op))) {
    return OperationPriority.HIGH;
  } else if (mediumPriorityOperations.some(op => methodName.includes(op))) {
    return OperationPriority.MEDIUM;
  } else {
    return OperationPriority.LOW;
  }
}

/**
 * Envuelve una función con el limitador de tasa
 */
function wrapWithRateLimiter(
  originalFunction: (...args: any[]) => any,
  target: unknown,
  operationType: OperationType
): (...args: any[]) => any {
  return function(...args: unknown[]) {
    const methodName = originalFunction.name || 'unknown';
    const priority = determineOperationPriority(operationType, methodName);

    // Encolar la operación en el limitador de tasa
    return rateLimiterService.enqueue(
      operationType,
      () => originalFunction.apply(target, args),
      { priority }
    );
  };
}

// Exportar una función para crear un cliente con limitación de tasa
export function createClient(options?: Partial<RateLimitedClientOptions>) {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

  return createRateLimitedClient({
    supabaseUrl,
    supabaseKey,
    enableRateLimiting: true,
    ...options,
  });
}
