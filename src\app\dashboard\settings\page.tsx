"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs"
import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, Card<PERSON><PERSON><PERSON>, CardFooter } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { toast } from "@/hooks/use-toast"
import { createClient } from "@/lib/supabase/client"
import Link from "next/link"
import { Settings, Zap, FileText, Server } from "lucide-react"

import { GeneralSettings } from "@/components/features/settings/general-settings"
import { NotificationSettings } from "@/components/features/settings/notification-settings"
import { AppearanceSettings } from "@/components/features/settings/appearance-settings"
import { IntegrationSettings } from "@/components/features/settings/integration-settings"

// Interfaces para tipos de datos
interface GeneralSettingsData {
  company_name: string;
  company_address: string;
  company_phone: string;
  company_email: string;
  company_website: string;
  enable_notifications: boolean;
  enable_activity_tracking: boolean;
}

interface NotificationSettingsData {
  email_notifications: boolean;
  in_app_notifications: boolean;
  project_updates: boolean;
  work_order_updates: boolean;
  document_updates: boolean;
  user_mentions: boolean;
  notification_frequency: "daily" | "immediate" | "hourly" | "weekly";
}

interface AppearanceSettingsData {
  theme: "system" | "light" | "dark";
  enable_animations: boolean;
  high_contrast: boolean;
  font_size: "small" | "medium" | "large";
  sidebar_position: "left" | "right";
}

interface IntegrationData {
  id: string;
  name: string;
  api_key: string;
  api_url: string;
  is_active: boolean;
  created_at: string;
}

export default function SettingsPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [isAdmin, setIsAdmin] = useState(false)
  const supabase = createClient()

  useEffect(() => {
    async function checkAdminStatus() {
      try {
        const { data: { user } } = await supabase.auth.getUser()

        if (!user) return

        // Verificar si el usuario es administrador
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('role')
          .eq('id', user.id)
          .single()

        if (!profileError && profile?.role === 'admin') {
          setIsAdmin(true)
        }
      } catch (error) {
        console.error('Error al verificar estado de administrador:', error)
      }
    }

    checkAdminStatus()
  }, [])

  // Simulación de datos iniciales
  const initialGeneralSettings = {
    company_name: "AdminCore ",
    company_address: "Calle Principal 123, Ciudad",
    company_phone: "+34 ***********",
    company_email: "<EMAIL>",
    company_website: "https://admin4humans.com",
    enable_notifications: true,
    enable_activity_tracking: true,
  }

  const initialNotificationSettings = {
    email_notifications: true,
    in_app_notifications: true,
    project_updates: true,
    work_order_updates: true,
    document_updates: true,
    user_mentions: true,
    notification_frequency: "daily" as "daily" | "immediate" | "hourly" | "weekly",
  }

  const initialAppearanceSettings = {
    theme: "system" as "system" | "light" | "dark",
    enable_animations: true,
    high_contrast: false,
    font_size: "medium" as "small" | "medium" | "large",
    sidebar_position: "left" as "left" | "right",
  }

  const initialIntegrations = [
    {
      id: "1",
      name: "Google Calendar",
      api_key: "AIzaSyDqK7LVp4hbTVeW5RVqL4",
      api_url: "https://www.googleapis.com/calendar/v3",
      is_active: true,
      created_at: new Date().toISOString(),
    },
    {
      id: "2",
      name: "Slack",
      api_key: "xoxb-1234567890-abcdefghijklm",
      api_url: "https://slack.com/api",
      is_active: false,
      created_at: new Date().toISOString(),
    },
  ]

  const handleGeneralSettingsSubmit = async (data: GeneralSettingsData) => {
    setIsLoading(true)
    try {
      // Aquí se implementaría la lógica para guardar los ajustes generales
      // Simulamos una operación asíncrona
      await new Promise(resolve => setTimeout(resolve, 1000))

      toast({
        title: "Ajustes guardados",
        description: "Los ajustes generales se han guardado correctamente.",
      })
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : "Ha ocurrido un error al guardar los ajustes.";

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleNotificationSettingsSubmit = async (data: NotificationSettingsData) => {
    setIsLoading(true)
    try {
      // Aquí se implementaría la lógica para guardar los ajustes de notificaciones
      // Simulamos una operación asíncrona
      await new Promise(resolve => setTimeout(resolve, 1000))

      toast({
        title: "Ajustes guardados",
        description: "Los ajustes de notificaciones se han guardado correctamente.",
      })
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : "Ha ocurrido un error al guardar los ajustes.";

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleAppearanceSettingsSubmit = async (data: AppearanceSettingsData) => {
    setIsLoading(true)
    try {
      // Aquí se implementaría la lógica para guardar los ajustes de apariencia
      // Simulamos una operación asíncrona
      await new Promise(resolve => setTimeout(resolve, 1000))

      toast({
        title: "Ajustes guardados",
        description: "Los ajustes de apariencia se han guardado correctamente.",
      })
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : "Ha ocurrido un error al guardar los ajustes.";

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleAddIntegration = async (data: Omit<IntegrationData, 'id' | 'created_at'>) => {
    // Aquí se implementaría la lógica para añadir una integración
    // Simulamos una operación asíncrona
    await new Promise(resolve => setTimeout(resolve, 1000))
  }

  const handleDeleteIntegration = async (id: string) => {
    // Aquí se implementaría la lógica para eliminar una integración
    // Simulamos una operación asíncrona
    await new Promise(resolve => setTimeout(resolve, 1000))
  }

  const handleToggleIntegration = async (id: string, isActive: boolean) => {
    // Aquí se implementaría la lógica para activar/desactivar una integración
    // Simulamos una operación asíncrona
    await new Promise(resolve => setTimeout(resolve, 1000))
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Configuración</h2>
        <p className="text-muted-foreground mt-2">
          Administra la configuración de tu cuenta y preferencias del sistema.
        </p>
      </div>

      <Tabs defaultValue="general" className="w-full">
        <TabsList className="grid w-full grid-cols-4 mb-4">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="notifications">Notificaciones</TabsTrigger>
          <TabsTrigger value="appearance">Apariencia</TabsTrigger>
          <TabsTrigger value="integrations">Integraciones</TabsTrigger>
        </TabsList>

        <TabsContent value="general">
          <Card>
            <CardHeader>
              <CardTitle>Ajustes Generales</CardTitle>
              <CardDescription>
                Configura los ajustes generales de la aplicación y la información de la empresa.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <GeneralSettings
                initialData={initialGeneralSettings}
                onSubmit={handleGeneralSettingsSubmit}
                isLoading={isLoading}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications">
          <Card>
            <CardHeader>
              <CardTitle>Ajustes de Notificaciones</CardTitle>
              <CardDescription>
                Configura cómo y cuándo recibir notificaciones.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <NotificationSettings
                initialData={initialNotificationSettings}
                onSubmit={handleNotificationSettingsSubmit}
                isLoading={isLoading}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="appearance">
          <Card>
            <CardHeader>
              <CardTitle>Ajustes de Apariencia</CardTitle>
              <CardDescription>
                Personaliza la apariencia de la aplicación.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AppearanceSettings
                initialData={initialAppearanceSettings}
                onSubmit={handleAppearanceSettingsSubmit}
                isLoading={isLoading}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="integrations">
          <Card>
            <CardHeader>
              <CardTitle>Integraciones</CardTitle>
              <CardDescription>
                Configura integraciones con servicios externos.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <IntegrationSettings
                initialData={initialIntegrations}
                onAddIntegration={handleAddIntegration}
                onDeleteIntegration={handleDeleteIntegration}
                onToggleIntegration={handleToggleIntegration}
                isLoading={isLoading}
              />
            </CardContent>
            {isAdmin && (
              <CardFooter className="flex flex-col items-start gap-4 border-t pt-6">
                <div>
                  <h3 className="text-lg font-medium">Configuración Avanzada</h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    Opciones de configuración avanzada solo disponibles para administradores.
                  </p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 w-full">
                  <Card className="border border-dashed">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base flex items-center">
                        <Settings className="mr-2 h-4 w-4" />
                        Proveedores de IA
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="pb-2">
                      <p className="text-sm text-muted-foreground">
                        Configura los proveedores de IA para el análisis de documentos.
                      </p>
                    </CardContent>
                    <CardFooter>
                      <Button asChild variant="outline" size="sm" className="w-full">
                        <Link href="/dashboard/settings/ai-providers">
                          Configurar Proveedores
                        </Link>
                      </Button>
                    </CardFooter>
                  </Card>
                  <Card className="border border-dashed">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base flex items-center">
                        <Zap className="mr-2 h-4 w-4" />
                        Diagnóstico de IA
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="pb-2">
                      <p className="text-sm text-muted-foreground">
                        Prueba la conexión con los proveedores de IA y diagnostica problemas.
                      </p>
                    </CardContent>
                    <CardFooter>
                      <Button asChild variant="outline" size="sm" className="w-full">
                        <Link href="/dashboard/test-document-analyzer">
                          Probar Conexión
                        </Link>
                      </Button>
                    </CardFooter>
                  </Card>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 w-full mt-4">
                  <Card className="border border-dashed">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base flex items-center">
                        <Server className="mr-2 h-4 w-4" />
                        Servicio de Análisis
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="pb-2">
                      <p className="text-sm text-muted-foreground">
                        Configura y monitorea el servicio de análisis de documentos.
                      </p>
                    </CardContent>
                    <CardFooter>
                      <Button asChild variant="outline" size="sm" className="w-full">
                        <Link href="/dashboard/settings/document-analysis-service">
                          Gestionar Servicio
                        </Link>
                      </Button>
                    </CardFooter>
                  </Card>
                  <Card className="border border-dashed">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base flex items-center">
                        <FileText className="mr-2 h-4 w-4" />
                        Logs del Sistema
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="pb-2">
                      <p className="text-sm text-muted-foreground">
                        Visualiza los logs del sistema y monitorea la actividad.
                      </p>
                    </CardContent>
                    <CardFooter>
                      <Button asChild variant="outline" size="sm" className="w-full">
                        <Link href="/dashboard/settings/document-analysis-service?tab=logs">
                          Ver Logs
                        </Link>
                      </Button>
                    </CardFooter>
                  </Card>
                </div>
              </CardFooter>
            )}
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
