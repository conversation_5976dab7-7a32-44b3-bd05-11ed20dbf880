'use client'

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { EquipmentFailurePrediction } from '@/components/analytics/equipment-failure-prediction';
import { PageHeader } from '@/components/page-header';
import {
  AlertTriangle,
  Wrench,
  FileText,
  Users,
  Calendar
} from '@/components/ui/icons';
import {
  <PERSON><PERSON>hart,
  PieChart,
  LineChart,
  Map,
  DollarSign,
  Download
} from 'lucide-react';

export default function AnalyticsPage() {
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <div className="container mx-auto py-6 space-y-6">
      <PageHeader
        title="Análisis Avanzado"
        description="Análisis predictivo y optimización de servicios técnicos"
        actions={
          <Button>
            <Download className="mr-2 h-4 w-4" />
            Exportar datos
          </Button>
        }
      />

      <Tabs defaultValue="overview" className="w-full" onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="overview">
            <BarChart className="h-4 w-4 mr-2" />
            Resumen
          </TabsTrigger>
          <TabsTrigger value="equipment">
            <Wrench className="h-4 w-4 mr-2" />
            Equipos
          </TabsTrigger>
          <TabsTrigger value="routes">
            <Map className="h-4 w-4 mr-2" />
            Rutas
          </TabsTrigger>
          <TabsTrigger value="costs">
            <DollarSign className="h-4 w-4 mr-2" />
            Costos
          </TabsTrigger>
          <TabsTrigger value="technicians">
            <Users className="h-4 w-4 mr-2" />
            Técnicos
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-0">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Predicción de Fallos</CardTitle>
                <CardDescription>
                  Equipos con alta probabilidad de fallo
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex justify-between items-center">
                  <div className="text-3xl font-bold">12</div>
                  <div className="bg-amber-100 text-amber-800 p-2 rounded-full">
                    <AlertTriangle className="h-5 w-5" />
                  </div>
                </div>
                <div className="text-sm text-muted-foreground mt-2">
                  5 equipos con riesgo crítico
                </div>
                <Button
                  variant="link"
                  className="p-0 h-auto mt-2"
                  onClick={() => setActiveTab('equipment')}
                >
                  Ver detalles
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Optimización de Rutas</CardTitle>
                <CardDescription>
                  Actividades programadas para hoy
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex justify-between items-center">
                  <div className="text-3xl font-bold">8</div>
                  <div className="bg-blue-100 text-blue-800 p-2 rounded-full">
                    <Map className="h-5 w-5" />
                  </div>
                </div>
                <div className="text-sm text-muted-foreground mt-2">
                  3 técnicos con rutas optimizadas
                </div>
                <Button
                  variant="link"
                  className="p-0 h-auto mt-2"
                  onClick={() => setActiveTab('routes')}
                >
                  Ver detalles
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Análisis de Costos</CardTitle>
                <CardDescription>
                  Costos de servicio este mes
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex justify-between items-center">
                  <div className="text-3xl font-bold">$12,450</div>
                  <div className="bg-green-100 text-green-800 p-2 rounded-full">
                    <DollarSign className="h-5 w-5" />
                  </div>
                </div>
                <div className="text-sm text-muted-foreground mt-2">
                  Margen de beneficio: 32%
                </div>
                <Button
                  variant="link"
                  className="p-0 h-auto mt-2"
                  onClick={() => setActiveTab('costs')}
                >
                  Ver detalles
                </Button>
              </CardContent>
            </Card>
          </div>

          <div className="mt-6">
            <EquipmentFailurePrediction limit={5} />
          </div>

          <div className="grid gap-4 md:grid-cols-2 mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Rendimiento de Equipos</CardTitle>
                <CardDescription>
                  Análisis de fiabilidad y disponibilidad
                </CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <div className="flex items-center justify-center h-full">
                  <div className="text-center text-muted-foreground">
                    <LineChart className="h-16 w-16 mx-auto mb-4 text-muted-foreground/50" />
                    <p>Gráfico de rendimiento de equipos</p>
                    <Button
                      variant="link"
                      className="mt-2"
                      onClick={() => setActiveTab('equipment')}
                    >
                      Ver análisis completo
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Rendimiento de Técnicos</CardTitle>
                <CardDescription>
                  Análisis de eficiencia y productividad
                </CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <div className="flex items-center justify-center h-full">
                  <div className="text-center text-muted-foreground">
                    <BarChart className="h-16 w-16 mx-auto mb-4 text-muted-foreground/50" />
                    <p>Gráfico de rendimiento de técnicos</p>
                    <Button
                      variant="link"
                      className="mt-2"
                      onClick={() => setActiveTab('technicians')}
                    >
                      Ver análisis completo
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="equipment" className="mt-0">
          <div className="space-y-6">
            <EquipmentFailurePrediction limit={10} />

            <Card>
              <CardHeader>
                <CardTitle>Análisis de Rendimiento de Equipos</CardTitle>
                <CardDescription>
                  Métricas de fiabilidad, disponibilidad y mantenimiento
                </CardDescription>
              </CardHeader>
              <CardContent className="h-96">
                <div className="flex items-center justify-center h-full">
                  <div className="text-center text-muted-foreground">
                    <FileText className="h-16 w-16 mx-auto mb-4 text-muted-foreground/50" />
                    <p>Esta funcionalidad estará disponible próximamente</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="routes" className="mt-0">
          <Card>
            <CardHeader>
              <CardTitle>Optimización de Rutas de Técnicos</CardTitle>
              <CardDescription>
                Planificación eficiente de rutas para técnicos en campo
              </CardDescription>
            </CardHeader>
            <CardContent className="h-96">
              <div className="flex items-center justify-center h-full">
                <div className="text-center text-muted-foreground">
                  <Map className="h-16 w-16 mx-auto mb-4 text-muted-foreground/50" />
                  <p>Esta funcionalidad estará disponible próximamente</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="costs" className="mt-0">
          <Card>
            <CardHeader>
              <CardTitle>Análisis de Costos de Servicio</CardTitle>
              <CardDescription>
                Análisis detallado de costos, ingresos y rentabilidad
              </CardDescription>
            </CardHeader>
            <CardContent className="h-96">
              <div className="flex items-center justify-center h-full">
                <div className="text-center text-muted-foreground">
                  <DollarSign className="h-16 w-16 mx-auto mb-4 text-muted-foreground/50" />
                  <p>Esta funcionalidad estará disponible próximamente</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="technicians" className="mt-0">
          <Card>
            <CardHeader>
              <CardTitle>Análisis de Rendimiento de Técnicos</CardTitle>
              <CardDescription>
                Métricas de productividad, eficiencia y calidad de servicio
              </CardDescription>
            </CardHeader>
            <CardContent className="h-96">
              <div className="flex items-center justify-center h-full">
                <div className="text-center text-muted-foreground">
                  <Users className="h-16 w-16 mx-auto mb-4 text-muted-foreground/50" />
                  <p>Esta funcionalidad estará disponible próximamente</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
