"use client"

import { useState, useEffect } from "react"
import { createClient } from "@/lib/supabase/client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, RefreshCw, LogOut } from "lucide-react"
import { toast } from "@/hooks/use-toast"

/**
 * Componente para reconectar automáticamente la sesión
 */
export function AuthReconnect() {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)

  const reconnect = async () => {
    setIsLoading(true)
    setError(null)
    setSuccess(false)
    
    try {
      const supabase = createClient()
      
      // 1. Intentar refrescar la sesión
      const { data, error } = await supabase.auth.refreshSession()
      
      if (error) {
        console.error('Error al refrescar la sesión:', error)
        const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

        setError(errorMessage)
        
        // Si no se puede refrescar, sugerir cerrar sesión
        toast({
          title: "Error de sesión",
          description: "No se pudo refrescar la sesión. Intente cerrar sesión y volver a iniciar.",
          variant: "destructive"
        })
        
        return
      }
      
      if (data?.session) {
        console.log('Sesión refrescada exitosamente')
        setSuccess(true)
        
        // Actualizar timestamp de la sesión para caché
        localStorage.setItem('auth_session_timestamp', Date.now().toString())
        
        toast({
          title: "Sesión restaurada",
          description: "La sesión ha sido refrescada correctamente"
        })
        
        // Recargar la página después de un breve retraso
        setTimeout(() => {
          window.location.reload()
        }, 1500)
      } else {
        setError('No se pudo obtener una nueva sesión')
      }
    } catch (err) {
      console.error('Error inesperado al reconectar:', err)
      setError(err instanceof Error ? err.message : 'Error desconocido')
      
      toast({
        title: "Error",
        description: "Ocurrió un error inesperado",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }
  
  const logout = async () => {
    setIsLoading(true)
    
    try {
      const supabase = createClient()
      await supabase.auth.signOut()
      
      toast({
        title: "Sesión cerrada",
        description: "Se ha cerrado la sesión correctamente"
      })
      
      // Redirigir al login
      window.location.href = '/auth/login'
    } catch (err) {
      console.error('Error al cerrar sesión:', err)
      
      toast({
        title: "Error",
        description: "No se pudo cerrar la sesión",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }
  
  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Reconexión de Sesión</CardTitle>
        <CardDescription>
          Se ha detectado un problema con tu sesión. Intenta reconectar para restaurar el acceso.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        {success && (
          <Alert variant="default">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Éxito</AlertTitle>
            <AlertDescription>Sesión reconectada correctamente. Recargando página...</AlertDescription>
          </Alert>
        )}
        
        <p className="text-sm text-muted-foreground">
          Este problema puede ocurrir cuando la sesión ha expirado o hay problemas de conectividad.
          Intenta reconectar para restaurar el acceso a tus proyectos.
        </p>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button 
          variant="outline" 
          onClick={logout}
          disabled={isLoading}
        >
          <LogOut className="mr-2 h-4 w-4" />
          Cerrar Sesión
        </Button>
        <Button 
          onClick={reconnect} 
          disabled={isLoading || success}
        >
          <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? "animate-spin" : ""}`} />
          Reconectar
        </Button>
      </CardFooter>
    </Card>
  )
}
