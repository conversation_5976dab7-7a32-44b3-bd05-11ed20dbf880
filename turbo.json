{"$schema": "https://turbo.build/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**"], "env": ["NODE_ENV", "NEXT_PUBLIC_SUPABASE_URL", "NEXT_PUBLIC_SUPABASE_ANON_KEY", "SUPABASE_SERVICE_ROLE_KEY", "OPENAI_API_KEY", "GOOGLE_AI_API_KEY"]}, "dev": {"cache": false, "persistent": true}, "lint": {"dependsOn": ["^lint"]}, "type-check": {"dependsOn": ["^type-check"], "outputs": []}, "test": {"dependsOn": ["^test"], "outputs": ["coverage/**"]}, "clean": {"cache": false}}, "globalDependencies": ["package.json", "tsconfig.json", "next.config.js", "tailwind.config.js", ".env.local", ".env.production"]}