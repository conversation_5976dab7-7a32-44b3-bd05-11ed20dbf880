import { useQuery } from "@tanstack/react-query"
import { createClient } from "@/lib/supabase/client"
import { Database } from "@/types/database.types"
import { Document } from "@/types/documents"

export async function fetchDocuments(): Promise<Document[]> {
  try {
    const supabase = createClient()

    // Verificar primero si la tabla existe
    const { data: tableExists, error: tableError } = await supabase
      .from('documents')
      .select('id')
      .limit(1)
      .maybeSingle()

    if (tableError) {
      console.error('Error al verificar la tabla documents:', tableError)
      return []
    }

    // Si la tabla existe, obtener los documentos
    const { data, error } = await supabase
      .from('documents')
      .select(`
        id,
        filename,
        file_url,
        description,
        upload_date,
        created_at,
        project:project_id(id, name),
        work_order:work_order_id(id, title),
        uploaded_by_user:uploaded_by(id, email, full_name)
      `)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error al obtener documentos:', error)
      return []
    }

    return (data as unknown as Document[]) || []
  } catch (err) {
    console.error('Excepción al obtener documentos:', err)
    return []
  }
}

export function useDocuments() {
  return useQuery<Document[]>({
    queryKey: ['documents'],
    queryFn: fetchDocuments,
  })
}
