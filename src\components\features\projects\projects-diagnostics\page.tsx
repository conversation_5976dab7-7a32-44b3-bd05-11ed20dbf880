import React, { useEffect } from 'react';
import { checkSupabaseConnection, getProjectsCount, getTableStructure, runDiagnostics } from '@/lib/supabase';

const ProjectsDiagnostics: React.FC = () => {
  useEffect(() => {
    checkSupabaseConnection();
    getProjectsCount();
    getTableStructure();
    runDiagnostics();
  }, [checkSupabaseConnection, getProjectsCount, getTableStructure, runDiagnostics]);

  return (
    <div>ProjectsDiagnostics component</div>
  );
};

export default ProjectsDiagnostics;