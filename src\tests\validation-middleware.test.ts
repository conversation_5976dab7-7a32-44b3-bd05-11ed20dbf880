/**
 * @file Tests for validation middleware
 * @description Unit tests for API validation middleware
 */

import { vi } from 'vitest';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { withValidation, withRequiredFields } from '@/lib/middleware/validation-middleware';

// Mock NextRequest and NextResponse
vi.mock('next/server', () => {
  return {
    NextRequest: vi.fn().mockImplementation((url, init) => ({
      json: vi.fn().mockImplementation(async () => init?.body ? JSON.parse(init.body) : {}),
      nextUrl: { pathname: url || '/test' },
    })),
    NextResponse: {
      json: vi.fn().mockImplementation((body, init) => ({
        body,
        status: init?.status || 200,
      })),
    },
  };
});

// Mock logger service
vi.mock('@/lib/services/logger-service', () => ({
  logger: {
    warn: vi.fn(),
    error: vi.fn(),
  },
}));

describe('Validation Middleware', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('withValidation', () => {
    const testSchema = z.object({
      name: z.string().min(3),
      email: z.string().email(),
      age: z.number().min(18).optional(),
    });

    const mockHandler = vi.fn().mockImplementation((req, data) => {
      return NextResponse.json({ success: true, data });
    });

    const validatedHandler = withValidation(testSchema, mockHandler);

    it('should pass validation and call handler with valid data', async () => {
      // Valid data
      const validData = {
        name: 'Test User',
        email: '<EMAIL>',
        age: 25,
      };

      // Create mock request
      const req = new NextRequest('/test', {
        method: 'POST',
        body: JSON.stringify(validData),
      });

      // Call handler
      const response = await validatedHandler(req);

      // Verify handler was called with validated data
      expect(mockHandler).toHaveBeenCalledWith(req, validData);
      
      // Verify response
      expect(response.body).toEqual({
        success: true,
        data: validData,
      });
      expect(response.status).toBe(200);
    });

    it('should return validation error for invalid data', async () => {
      // Invalid data
      const invalidData = {
        name: 'Te', // Too short
        email: 'not-an-email',
        age: 15, // Too young
      };

      // Create mock request
      const req = new NextRequest('/test', {
        method: 'POST',
        body: JSON.stringify(invalidData),
      });

      // Call handler
      const response = await validatedHandler(req);

      // Verify handler was not called
      expect(mockHandler).not.toHaveBeenCalled();
      
      // Verify error response
      expect(response.body.success).toBe(false);
      expect(response.body.errors).toHaveLength(3);
      expect(response.status).toBe(400);
    });

    it('should handle JSON parsing errors', async () => {
      // Create mock request with invalid JSON
      const req = new NextRequest('/test', {
        method: 'POST',
        body: 'not-json',
      });

      // Mock JSON parsing error
      req.json = vi.fn().mockRejectedValue(new Error('Invalid JSON'));

      // Call handler
      const response = await validatedHandler(req);

      // Verify handler was not called
      expect(mockHandler).not.toHaveBeenCalled();
      
      // Verify error response
      expect(response.body.success).toBe(false);
      expect(response.body.errors).toHaveLength(1);
      expect(response.body.errors[0].message).toBe('Invalid request data');
      expect(response.status).toBe(400);
    });
  });

  describe('withRequiredFields', () => {
    const mockHandler = vi.fn().mockImplementation((req, data) => {
      return NextResponse.json({ success: true, data });
    });

    const requiredFields = ['name', 'email'];
    const validatedHandler = withRequiredFields(requiredFields, mockHandler);

    it('should pass validation when all required fields are present', async () => {
      // Valid data with all required fields
      const validData = {
        name: 'Test User',
        email: '<EMAIL>',
        age: 25,
      };

      // Create mock request
      const req = new NextRequest('/test', {
        method: 'POST',
        body: JSON.stringify(validData),
      });

      // Call handler
      const response = await validatedHandler(req);

      // Verify handler was called with data
      expect(mockHandler).toHaveBeenCalledWith(req, validData);
      
      // Verify response
      expect(response.body).toEqual({
        success: true,
        data: validData,
      });
      expect(response.status).toBe(200);
    });

    it('should return validation error when required fields are missing', async () => {
      // Invalid data missing required fields
      const invalidData = {
        name: 'Test User',
        // email is missing
        age: 25,
      };

      // Create mock request
      const req = new NextRequest('/test', {
        method: 'POST',
        body: JSON.stringify(invalidData),
      });

      // Call handler
      const response = await validatedHandler(req);

      // Verify handler was not called
      expect(mockHandler).not.toHaveBeenCalled();
      
      // Verify error response
      expect(response.body.success).toBe(false);
      expect(response.body.errors).toHaveLength(1);
      expect(response.body.errors[0].field).toBe('email');
      expect(response.status).toBe(400);
    });

    it('should return validation error when required fields are empty', async () => {
      // Invalid data with empty required fields
      const invalidData = {
        name: '',
        email: '   ',
        age: 25,
      };

      // Create mock request
      const req = new NextRequest('/test', {
        method: 'POST',
        body: JSON.stringify(invalidData),
      });

      // Call handler
      const response = await validatedHandler(req);

      // Verify handler was not called
      expect(mockHandler).not.toHaveBeenCalled();
      
      // Verify error response
      expect(response.body.success).toBe(false);
      expect(response.body.errors).toHaveLength(2);
      expect(response.status).toBe(400);
    });
  });
});
