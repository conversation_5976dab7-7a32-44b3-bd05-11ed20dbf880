'use client'

import { useEffect, useState } from 'react'
import { setupDashboardFunctions } from '@/lib/actions/setup-dashboard-actions'
import { useToast } from '@/components/ui/use-toast'

/**
 * Componente que inicializa las funciones y tablas del dashboard
 * No renderiza nada visible, solo ejecuta la lógica de inicialización
 */
export function DashboardInitializer() {
  const [initialized, setInitialized] = useState(false)
  const [initializing, setInitializing] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    // Solo ejecutar una vez
    if (!initialized && !initializing) {
      const initDashboard = async () => {
        try {
          setInitializing(true)
          // Intentar configurar las funciones y tablas del dashboard
          const result = await setupDashboardFunctions()

          if (result.success) {
            console.log('Dashboard inicializado correctamente:', result.message)
          } else {
            console.error('Error al inicializar el dashboard:', result.message)
            toast({
              title: "Error al inicializar el dashboard",
              description: result.message,
              variant: "destructive",
            })
          }

          setInitialized(true)
        } catch (error) {
          console.error('Error al inicializar el dashboard:', error)
          toast({
            title: "Error al inicializar el dashboard",
            description: error instanceof Error ? error.message : "Error desconocido",
            variant: "destructive",
          })
        } finally {
          setInitializing(false)
        }
      }

      initDashboard()
    }
  }, [initialized, initializing, toast])

  // Este componente no renderiza nada visible
  return null
}
