"use client"

import React from 'react'
import { AlertCircle } from 'lucide-react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'

interface FormValidationAlertProps {
  /**
   * List of error messages to display
   */
  errors: string[]
  
  /**
   * Optional title for the alert
   */
  title?: string
  
  /**
   * Optional className for styling
   */
  className?: string
}

/**
 * Component for displaying form validation errors
 * 
 * @param props Component properties
 * @returns JSX element
 */
export function FormValidationAlert({
  errors,
  title = "Error de validación",
  className = "",
}: FormValidationAlertProps) {
  if (!errors || errors.length === 0) return null
  
  return (
    <Alert variant="destructive" className={`mb-4 ${className}`}>
      <AlertCircle className="h-4 w-4" />
      <AlertTitle>{title}</AlertTitle>
      <AlertDescription>
        <ul className="list-disc pl-5 text-sm">
          {errors.map((error, index) => (
            <li key={index}>{error}</li>
          ))}
        </ul>
      </AlertDescription>
    </Alert>
  )
}
