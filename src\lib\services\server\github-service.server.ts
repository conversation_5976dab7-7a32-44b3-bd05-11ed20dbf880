'use server';

import { createClient } from '@/lib/supabase/server-client';
import { cookies } from 'next/headers';
import { GitHubConnection, GitHubRepository } from '../github-service';

export async function getGitHubConnection(): Promise<GitHubConnection | null> {
  try {
    const cookieStore = await cookies();
    const supabase = createClient(cookieStore);
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error || !user) {
      console.error('Error getting user:', error);
      return null;
    }
    const { data, error: connError } = await supabase
      .from('github_connections')
      .select('*')
      .eq('user_id', user.id)
      .maybeSingle();
    if (connError) {
      throw connError;
    }
    return data as unknown as GitHubConnection | null;
  } catch (error) {
    console.error('Error fetching GitHub connection:', error);
    return null;
  }
}

export async function getGitHubRepositories(): Promise<GitHubRepository[]> {
  try {
    const cookieStore = await cookies();
    const supabase = createClient(cookieStore);
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error || !user) {
      console.error('Error getting user:', error);
      return [];
    }
    const { data, error: repoError } = await supabase
      .from('github_repositories')
      .select('*')
      .eq('user_id', user.id)
      .order('updated_at', { ascending: false });
    if (repoError) {
      throw repoError;
    }
    return (data as unknown as GitHubRepository[]) || [];
  } catch (error) {
    console.error('Error fetching GitHub repositories:', error);
    return [];
  }
}

export async function syncUserRepositories(): Promise<boolean> {
  try {
    const cookieStore = await cookies();
    const supabase = createClient(cookieStore);
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error || !user) {
      console.error('Error getting user:', error);
      return false;
    }
    const { data: connection } = await supabase
      .from('github_connections')
      .select('*')
      .eq('user_id', user.id)
      .maybeSingle();
    if (!connection) {
      return false;
    }
    const response = await fetch('https://api.github.com/user/repos', {
      headers: {
        Authorization: `token ${connection.access_token}`,
        Accept: 'application/vnd.github.v3+json',
      },
    });
    if (!response.ok) {
      throw new Error(`Error al obtener repositorios: ${response.statusText}`);
    }
    const repos = await response.json();
    const { error: upsertError } = await supabase.from('github_repositories').upsert(
      repos.map((repo: unknown) => ({
        connection_id: connection.id,
        repository_id: repo.id.toString(),
        repository_name: repo.name,
        repository_full_name: repo.full_name,
        repository_url: repo.html_url,
        repository_description: repo.description,
        is_private: repo.private,
        default_branch: repo.default_branch,
      })),
      { onConflict: 'connection_id,repository_id' }
    );
    if (upsertError) {
      console.error('Error al guardar repositorios:', upsertError);
      return false;
    }
    const { error: updateError } = await supabase
      .from('github_connections')
      .update({ last_synced_at: new Date().toISOString() })
      .eq('user_id', user.id);
    if (updateError) {
      console.error('Error updating last_synced_at:', updateError);
      return false;
    }
    return true;
  } catch (error) {
    console.error('Error en syncUserRepositories:', error);
    return false;
  }
}
