/**
 * @file Tests for the ErrorHandlerService
 * @description Unit tests for the error handling functions
 */

import { errorHandler, ErrorType } from '@/lib/services/error-handler-service';
import { vi } from 'vitest';
import { toast } from '@/hooks/use-toast';


// Mock the toast function
vi.mock('@/hooks/use-toast', () => ({
  toast: vi.fn(),
}));

describe('ErrorHandlerService', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    vi.clearAllMocks();
    
    // Spy on console.error to prevent actual logging during tests
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  describe('handleError', () => {
    it('should handle UUID validation errors', () => {
      const error = new Error('invalid input syntax for type uuid');
      const result = errorHandler.handleError(error);
      
      expect(result.type).toBe(ErrorType.UUID_INVALID);
      expect(result.code).toBe('uuid_invalid');
      expect(result.message).toContain('identificador');
    });

    it('should handle UUID validation errors with field context', () => {
      const error = new Error('invalid input syntax for type uuid');
      const result = errorHandler.handleError(error, { field: 'user_id' });
      
      expect(result.type).toBe(ErrorType.UUID_INVALID);
      expect(result.field).toBe('user_id');
      expect(result.message).toContain('user_id');
    });

    it('should handle required field errors', () => {
      const error = new Error('required_field');
      const result = errorHandler.handleError(error);
      
      expect(result.type).toBe(ErrorType.REQUIRED_FIELD);
      expect(result.code).toBe('required_field');
      expect(result.message).toContain('obligatorio');
    });

    it('should handle empty field errors', () => {
      const error = new Error('empty_field');
      const result = errorHandler.handleError(error);
      
      expect(result.type).toBe(ErrorType.EMPTY_FIELD);
      expect(result.code).toBe('empty_field');
      expect(result.message).toContain('vacío');
    });

    it('should handle foreign key constraint errors', () => {
      const error = new Error('foreign key constraint');
      const result = errorHandler.handleError(error);
      
      expect(result.type).toBe(ErrorType.DATA_TYPE);
      expect(result.code).toBe('foreign_key_violation');
      expect(result.message).toContain('referencia');
    });

    it('should handle connection errors', () => {
      const error = new Error('network error');
      const result = errorHandler.handleError(error);
      
      expect(result.type).toBe(ErrorType.CONNECTION);
      expect(result.code).toBe('connection_error');
      expect(result.message).toContain('conexión');
    });

    it('should handle unknown errors', () => {
      const error = new Error('some unknown error');
      const result = errorHandler.handleError(error);
      
      expect(result.type).toBe(ErrorType.UNKNOWN);
      expect(result.code).toBe('unknown');
      expect(result.message).toContain('inesperado');
    });
  });

  describe('showErrorToast', () => {
    it('should call the toast function with the error details', () => {
      const errorDetails = {
        type: ErrorType.VALIDATION,
        code: 'validation_error',
        message: 'Error de validación',
        suggestions: ['Sugerencia 1', 'Sugerencia 2'],
      };
      
      errorHandler.showErrorToast(errorDetails);
      
      // Cast toast to a mocked function to satisfy TypeScript
      expect(toast).toHaveBeenCalledWith(expect.objectContaining({
        title: 'Error de validación',
        variant: 'destructive',
      }));
    });
  });
});
