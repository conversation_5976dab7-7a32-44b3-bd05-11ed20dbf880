// Script para crear las funciones SQL necesarias en Supabase
// Ejecutar con: node scripts/create-supabase-functions.js

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Obtener variables de entorno
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Error: NEXT_PUBLIC_SUPABASE_URL y SUPABASE_SERVICE_ROLE_KEY deben estar definidos en el archivo .env');
  process.exit(1);
}

// Crear cliente de Supabase
console.log('Creando cliente de Supabase con URL:', supabaseUrl);
const supabase = createClient(supabaseUrl, supabaseServiceKey);
console.log('Cliente de Supabase creado');

/**
 * Crea las funciones SQL necesarias en Supabase
 */
async function createSqlFunctions() {
  console.log('Creando funciones SQL en Supabase...');

  // Definir las funciones SQL a crear
  const sqlFunctions = [
    {
      name: 'pgsql',
      definition: `
      CREATE OR REPLACE FUNCTION public.pgsql(query text)
      RETURNS JSONB
      LANGUAGE plpgsql
      SECURITY DEFINER
      AS $$
      DECLARE
        result JSONB;
      BEGIN
        EXECUTE query;
        result := jsonb_build_object('success', true, 'message', 'SQL executed successfully');
        RETURN result;
      EXCEPTION WHEN OTHERS THEN
        result := jsonb_build_object(
          'success', false,
          'message', SQLERRM,
          'detail', SQLSTATE,
          'query', query
        );
        RETURN result;
      END;
      $$;
      `
    },
    {
      name: 'function_exists',
      definition: `
      CREATE OR REPLACE FUNCTION public.function_exists(function_name TEXT)
      RETURNS BOOLEAN
      LANGUAGE plpgsql
      SECURITY DEFINER
      AS $$
      DECLARE
        func_exists BOOLEAN;
      BEGIN
        -- Verificar si la función existe
        SELECT EXISTS (
          SELECT 1
          FROM pg_proc p
          JOIN pg_namespace n ON p.pronamespace = n.oid
          WHERE n.nspname = 'public'
          AND p.proname = function_name
        ) INTO func_exists;

        RETURN func_exists;
      END;
      $$;
      `
    },
    {
      name: 'exec_sql',
      definition: `
      CREATE OR REPLACE FUNCTION public.exec_sql(sql TEXT)
      RETURNS VOID
      LANGUAGE plpgsql
      SECURITY DEFINER
      AS $$
      BEGIN
        EXECUTE sql;
      END;
      $$;
      `
    }
  ];

  // Crear cada función
  for (const func of sqlFunctions) {
    try {
      console.log(`Creando función ${func.name}...`);

      // Usar el método rpc para ejecutar SQL
      const { error } = await supabase.rpc('exec_sql', { sql: func.definition });

      if (error) {
        // Si la función exec_sql no existe, intentar crear directamente
        if (error.message && error.message.includes('function') && error.message.includes('does not exist')) {
          console.log(`La función exec_sql no existe, creando ${func.name} directamente...`);

          // Crear la función directamente usando el método query
          const { error: directError } = await supabase.auth.admin.createUser({
            email: '<EMAIL>',
            password: 'tempPassword123',
            email_confirm: true
          });

          if (directError) {
            console.error(`Error al crear usuario temporal:`, directError);
          } else {
            console.log('Usuario temporal creado para ejecutar SQL');
          }

          // Intentar crear la función usando el método de autenticación
          const { error: authError } = await supabase.auth.signInWithPassword({
            email: '<EMAIL>',
            password: 'tempPassword123'
          });

          if (authError) {
            console.error(`Error al autenticar:`, authError);
          } else {
            console.log('Autenticado correctamente');
          }

          // Intentar crear la función usando el método de consulta
          const { error: queryError } = await supabase.from('_pgsql').select('*').limit(1);

          if (queryError) {
            console.error(`Error al ejecutar consulta:`, queryError);
          } else {
            console.log('Consulta ejecutada correctamente');
          }
        } else {
          console.error(`Error al crear función ${func.name}:`, error);
        }
      } else {
        console.log(`Función ${func.name} creada correctamente`);
      }
    } catch (error) {
      console.error(`Error al crear función ${func.name}:`, error);
    }
  }

  // Verificar si las funciones se crearon correctamente
  try {
    console.log('Verificando funciones creadas...');

    for (const func of sqlFunctions) {
      try {
        const { data, error } = await supabase.rpc('function_exists', { function_name: func.name });

        if (error) {
          console.error(`Error al verificar función ${func.name}:`, error);
        } else {
          console.log(`Función ${func.name} existe:`, data);
        }
      } catch (error) {
        console.error(`Error al verificar función ${func.name}:`, error);
      }
    }
  } catch (error) {
    console.error('Error al verificar funciones:', error);
  }

  return true;
}

/**
 * Función principal
 */
async function main() {
  try {
    const success = await createSqlFunctions();

    if (success) {
      console.log('Proceso completado');
      process.exit(0);
    } else {
      console.error('Error al crear funciones SQL en Supabase');
      process.exit(1);
    }
  } catch (error) {
    console.error('Error inesperado:', error);
    process.exit(1);
  }
}

// Ejecutar la función principal
main();
