/**
 * @ai-file-description: "DeepSeek provider implementation for document analysis"
 * @ai-related-files: ["../base-provider.ts", "../provider-interface.ts", "../provider-factory.ts"]
 * @ai-owner: "File-Based Projects"
 */

import { BaseAIProvider } from '../base-provider';
import { AIProviderConfig, DocumentAnalysisResult } from '../provider-interface';

/**
 * DeepSeek provider implementation
 * 
 * @ai-responsibility: "Implements document analysis using DeepSeek models"
 */
export class DeepSeekProvider extends BaseAIProvider {
  constructor(config: AIProviderConfig) {
    super(config);
  }
  
  /**
   * Gets the name of the provider
   */
  getProviderName(): string {
    return 'deepseek';
  }
  
  /**
   * Gets the display name of the provider for UI
   */
  getProviderDisplayName(): string {
    return 'DeepSeek';
  }
  
  /**
   * Analyzes document text using DeepSeek
   * 
   * @param documentText The text content of the document
   * @returns Structured project information
   */
  async analyzeDocument(documentText: string): Promise<DocumentAnalysisResult> {
    try {
      if (!this.validateConfig()) {
        throw new Error('Invalid DeepSeek configuration');
      }
      
      // Prepare the prompt
      const systemPrompt = this.getSystemPrompt();
      const userPrompt = this.getUserPrompt(documentText);
      
      // Call DeepSeek API
      const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model: this.modelName || 'deepseek-chat',
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
          ],
          temperature: this.temperature,
          max_tokens: this.maxTokens,
          response_format: { type: 'json_object' }
        })
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`DeepSeek API error: ${errorData.error?.message || response.statusText}`);
      }
      
      const data = await response.json();
      const content = data.choices?.[0]?.message?.content;
      
      if (!content) {
        throw new Error('Empty response from DeepSeek');
      }
      
      let parsedResponse;
      try {
        parsedResponse = JSON.parse(content);
      } catch (e) {
        console.error('Failed to parse DeepSeek response as JSON:', e);
        console.log('Raw response:', content);
        throw new Error('Failed to parse DeepSeek response as JSON');
      }
      
      return this.processResponse(parsedResponse);
    } catch (error) {
      console.error('Error analyzing document with DeepSeek:', error);
      throw error;
    }
  }
  
  /**
   * Gets the system prompt for document analysis
   * Customized for DeepSeek's capabilities
   */
  protected getSystemPrompt(): string {
    return `
You are a specialized AI assistant for engineering project management. Your task is to analyze project documents and extract key information in a structured format.

Extract the following information from the document:
1. Project name
2. Project description
3. Start date (in ISO format YYYY-MM-DD)
4. End date (in ISO format YYYY-MM-DD)
5. Budget amount (as a string)
6. Currency (USD, CLP, etc.)
7. Client name
8. Key deliverables (as an array)
9. Project scope
10. Team requirements (as an array)
11. Tags (as an array of keywords)

Format your response as a JSON object with these fields. If information is not found, use null.
Provide a confidence_score between 0 and 1 indicating your confidence in the extracted information.

IMPORTANT: Your response must be valid JSON. You must respond with a JSON object only, no additional text.
`;
  }
}
