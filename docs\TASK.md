# TASK.md – AdminCore Web

Este documento detalla las tareas específicas para completar la implementación del Dashboard y los módulos principales de AdminCore  Web.

## Dashboard (70% → 100%)

### Métricas y Visualizaciones
- [ ] **Alta** Implementar componente de gráfico de líneas para tendencias temporales
  - Crear componente base con Recharts
  - Añadir opciones de personalización
  - Implementar tooltips informativos
- [ ] **Alta** Implementar gráfico circular para distribución de recursos
  - Crear componente base con Recharts
  - Añadir leyenda interactiva
  - Implementar animaciones de transición
- [ ] **Media** Añadir selector de rango de fechas para filtrar datos
  - Implementar componente de calendario
  - Conectar con estado global
  - Añadir presets (hoy, semana, mes, año)
- [ ] **Media** Mejorar tarjetas de métricas con indicadores de tendencia
  - Añadir iconos de tendencia (arriba/abajo)
  - Mostrar porcentaje de cambio
  - Implementar código de colores según tendencia

### Actividades y Tareas
- [ ] **Alta** Implementar lista de actividades recientes
  - Crear componente de timeline
  - Conectar con API de Supabase
  - Implementar paginación y carga infinita
- [ ] **Alta** Añadir sección de tareas pendientes con acciones
  - Crear componente de lista de tareas
  - Implementar acciones (completar, asignar, posponer)
  - Añadir filtros por estado y prioridad
- [ ] **Media** Implementar sistema de notificaciones en tiempo real
  - Configurar cliente de Supabase Realtime
  - Crear componente de notificaciones
  - Implementar indicadores visuales

## Módulo de Proyectos (30% → 100%)

### Listado y Creación
- [ ] **Alta** Implementar tabla de proyectos con filtros avanzados
  - Crear componente de tabla con TanStack Table
  - Añadir filtros por estado, cliente, fecha
  - Implementar búsqueda global
- [ ] **Alta** Crear formulario de creación/edición de proyectos
  - Implementar formulario con React Hook Form
  - Añadir validación con Zod
  - Crear campos dinámicos según tipo de proyecto
- [ ] **Media** Implementar vista de tarjetas alternativa
  - Crear componente de tarjeta de proyecto
  - Añadir toggle entre vista de tabla y tarjetas
  - Implementar ordenación y filtrado visual

### Detalles y Gestión
- [ ] **Alta** Crear página de detalle de proyecto
  - Implementar tabs para diferentes secciones
  - Mostrar información general, equipo, recursos
  - Añadir timeline de actividades
- [ ] **Alta** Implementar gestión de estados de proyecto
  - Crear componente de cambio de estado
  - Implementar flujo de trabajo configurable
  - Añadir historial de cambios de estado
- [ ] **Media** Añadir asignación de usuarios a proyectos
  - Crear selector de usuarios con búsqueda
  - Implementar gestión de roles en proyecto
  - Añadir notificaciones de asignación

## Módulo de Órdenes de Trabajo (30% → 100%)

### Listado y Creación
- [ ] **Alta** Implementar tabla de órdenes con filtros avanzados
  - Crear componente de tabla con TanStack Table
  - Añadir filtros por estado, técnico, prioridad
  - Implementar búsqueda global
- [ ] **Alta** Crear formulario de creación/edición de órdenes
  - Implementar formulario con React Hook Form
  - Añadir validación con Zod
  - Crear campos dinámicos según tipo de orden
- [ ] **Media** Implementar vista de Kanban alternativa
  - Crear componente de tablero Kanban
  - Añadir funcionalidad drag-and-drop
  - Implementar actualización de estado visual

### Detalles y Seguimiento
- [ ] **Alta** Crear página de detalle de orden de trabajo
  - Implementar tabs para diferentes secciones
  - Mostrar información general, tareas, recursos
  - Añadir timeline de actividades
- [ ] **Alta** Implementar checklist de tareas
  - Crear componente de lista de tareas interactiva
  - Añadir progreso visual
  - Implementar asignación de responsables
- [ ] **Media** Añadir registro de tiempo y recursos
  - Crear componente de registro de horas
  - Implementar cálculo de costos
  - Añadir exportación de informes

## Módulo de Documentos (30% → 100%)

### Gestión de Documentos
- [x] **Alta** Implementar carga y almacenamiento de documentos
  - ✅ Crear componente de carga con drag-and-drop
  - ✅ Integrar con Supabase Storage
  - ✅ Añadir barra de progreso y gestión de errores
- [ ] **Alta** Crear sistema de categorización y etiquetado
  - Implementar selector de categorías
  - Añadir sistema de etiquetas personalizadas
  - Crear filtros por categoría y etiqueta
- [ ] **Media** Implementar previsualización de documentos
  - Crear visor de PDF integrado
  - Añadir previsualización de imágenes
  - Implementar previsualización de documentos Office

### Organización y Búsqueda
- [ ] **Alta** Crear sistema de carpetas y subcarpetas
  - Implementar navegación jerárquica
  - Añadir acciones de mover y copiar
  - Crear breadcrumbs de navegación
- [ ] **Alta** Implementar búsqueda avanzada de documentos
  - Crear búsqueda por contenido y metadatos
  - Añadir filtros combinados
  - Implementar historial de búsquedas
- [ ] **Media** Añadir control de versiones de documentos
  - Crear sistema de versionado
  - Implementar comparación de versiones
  - Añadir restauración de versiones anteriores

## Módulo de Usuarios (95% → 100%)

### Gestión de Usuarios - COMPLETADO
- [x] **Alta** Implementar listado y búsqueda de usuarios
  - ✅ Crear tabla de usuarios con filtros
  - ✅ Añadir búsqueda por nombre, email, rol
  - ✅ Implementar paginación
- [x] **Alta** Crear formulario de creación/edición de usuarios
  - ✅ Implementar formulario con validación
  - ✅ Crear selector de roles y permisos
- [x] **Media** Implementar gestión de estado de usuarios
  - ✅ Crear acciones de activar/desactivar
  - ✅ Añadir bloqueo temporal

### Funcionalidades Completadas Recientemente
- [x] **Alta** Implementar eliminación de usuarios
  - ✅ Crear API DELETE /api/users/[id]
  - ✅ Agregar botón de eliminación con confirmación
  - ✅ Implementar eliminación permanente con AlertDialog
- [x] **Alta** Gestión de documentos de usuarios
  - ✅ Crear sección de documentos en perfil de usuario
  - ✅ Implementar subida de documentos personales
  - ✅ Categorizar documentos (ID, contratos, certificados)
  - ✅ Crear APIs específicas para documentos de usuarios
  - ✅ Componente UserDocumentsSection con tabs por categoría
  - ✅ Componente UserDocumentUpload con drag & drop
  - ✅ APIs GET/POST /api/users/[id]/documents
  - ✅ API DELETE /api/users/[id]/documents/[docId]

### Roles y Permisos
- [ ] **Alta** Crear sistema de roles predefinidos
  - Implementar roles básicos (Admin, Manager, User)
  - Añadir matriz de permisos por rol
  - Crear interfaz de asignación de roles
- [ ] **Alta** Implementar permisos granulares
  - Crear sistema de permisos por módulo
  - Añadir permisos de lectura/escritura/eliminación
  - Implementar herencia de permisos
- [ ] **Media** Añadir grupos de usuarios
  - Crear sistema de grupos personalizables
  - Implementar asignación de usuarios a grupos
  - Añadir permisos a nivel de grupo

## Módulo de Configuración (30% → 100%)

### Configuración General
- [ ] **Alta** Implementar ajustes generales del sistema
  - Crear formulario de configuración general
  - Añadir opciones de zona horaria y formato de fecha
  - Implementar guardado automático de cambios
- [ ] **Alta** Crear configuración de notificaciones
  - Implementar opciones por tipo de notificación
  - Añadir canales (email, in-app, push)
  - Crear horarios y frecuencias
- [ ] **Media** Añadir personalización de la interfaz
  - Implementar selector de tema (claro/oscuro/sistema)
  - Añadir opciones de densidad de información
  - Crear ajustes de accesibilidad

### Configuración Avanzada
- [ ] **Alta** Implementar configuración de integración con servicios
  - Crear formularios para APIs externas
  - Añadir prueba de conexión
  - Implementar registro de actividad
- [ ] **Alta** Crear gestión de plantillas
  - Implementar editor de plantillas de email
  - Añadir plantillas de documentos
  - Crear sistema de variables dinámicas
- [ ] **Media** Añadir configuración de copias de seguridad
  - Implementar programación de backups
  - Añadir opciones de almacenamiento
  - Crear interfaz de restauración

## Módulo de Integración de IA (0% → 100%)

### Fase 1: Fundación y MVP con Gemini
- [ ] **Alta** Configurar la fundación de la integración de IA
  - [ ] Crear interfaz agnóstica para proveedores de IA
  - [ ] Implementar configuración de variables de entorno para APIs
  - [ ] Definir y aplicar el esquema de base de datos para `ai_provider_configs` y `ai_document_analyses`
- [ ] **Alta** Mejorar la carga de documentos para el análisis de IA
  - [ ] Extender el componente de carga de documentos existente para incluir una opción de "Analizar con IA"
  - [ ] Añadir validación de tipos de archivo soportados (PDF, DOCX, TXT)
- [ ] **Alta** Implementar la integración con Google Gemini
  - [ ] Crear cliente de API para Gemini
  - [ ] Implementar la lógica para extraer texto del documento
  - [ ] Desarrollar y probar prompts para extraer información clave del proyecto
- [ ] **Alta** Crear proyecto a partir del análisis de IA
  - [ ] Diseñar y construir la UI para revisar las sugerencias de la IA
  - [ ] Implementar la pre-población del formulario de creación de proyectos
  - [ ] Añadir una visualización de la puntuación de confianza para los datos extraídos

### Fase 2: Expansión y Gestión de Proveedores
- [ ] **Media** Integrar OpenAI
  - [ ] Crear cliente de API para OpenAI (GPT-4)
  - [ ] Implementar el análisis de documentos
  - [ ] Comparar resultados con Gemini para evaluar precisión
- [ ] **Media** Integrar DeepSeek
  - [ ] Crear cliente de API para DeepSeek
  - [ ] Implementar el análisis de documentos
  - [ ] Optimizar para el procesamiento de documentos técnicos
- [ ] **Media** Crear UI para la gestión de proveedores
  - [ ] Crear interfaz para configurar proveedores (activar/desactivar, añadir API keys)
  - [ ] Implementar la gestión segura de las claves de API
  - [ ] Añadir funcionalidad para probar la conexión con cada proveedor

### Fase 3: Funcionalidades Avanzadas
- [ ] **Baja** Implementar procesamiento avanzado de documentos
  - [ ] Añadir soporte para documentos basados en imágenes (OCR)
  - [ ] Implementar extracción de tablas y gráficos
  - [ ] Crear plantillas especializadas para diferentes tipos de documentos (contratos, RFP, etc.)

### Hitos
1.  **Fundación Completa:** Infraestructura básica y la integración con Gemini funcionando.
2.  **Lanzamiento MVP:** Flujo completo de carga de documento a creación de proyecto con un solo proveedor.
3.  **Soporte Multi-Proveedor:** Los tres proveedores de IA (Gemini, OpenAI, DeepSeek) integrados.
4.  **Funciones Avanzadas:** Puntuaciones de confianza, plantillas y extracción especializada implementadas.

> **Notas:** La prioridad es conseguir un flujo de trabajo de extremo a extremo con un solo proveedor. El monitoreo de costos será crucial. Considerar un mecanismo de caché para los análisis para reducir costos de API.

## Pruebas y Optimización

### Pruebas
- [ ] **Alta** Implementar pruebas unitarias para componentes clave
  - Crear tests para componentes de UI
  - Añadir tests para hooks personalizados
  - Implementar tests para utilidades
- [ ] **Alta** Crear pruebas de integración
  - Implementar tests para flujos completos
  - Añadir tests para formularios
  - Crear tests para interacciones complejas
- [ ] **Media** Realizar pruebas de rendimiento
  - Implementar análisis de carga
  - Añadir pruebas de estrés
  - Crear benchmarks de referencia

### Optimización
- [ ] **Alta** Mejorar tiempos de carga
  - Implementar code splitting
  - Añadir lazy loading de componentes
  - Optimizar tamaño de bundle
- [ ] **Alta** Optimizar consultas a la base de datos
  - Implementar caché con React Query
  - Añadir paginación eficiente
  - Crear índices y optimizar consultas
- [ ] **Media** Mejorar experiencia de usuario
  - Implementar estados de carga elegantes
  - Añadir transiciones y animaciones
  - Crear feedback visual para acciones

## Prioridades

### CRITICAL PRIORITY (Week 1-2) 🚨
- **Code Quality Emergency**
  - [ ] Fix 400+ ESLint warnings (remove unused variables, replace `any`, etc.)
  - [ ] Resolve 33 failing tests (mock Supabase, fix auth context)
  - [ ] Achieve 60%+ test coverage (currently 35%)
  - [ ] Fix all TypeScript compilation errors
  - [ ] Implement global error boundaries
- **Security & Authentication**
  - [ ] Fix SSR authentication errors (Projects & Work Orders)
  - [ ] Implement Row Level Security (RLS) policies on all tables
  - [ ] Add comprehensive input validation with Zod schemas
  - [ ] Implement CSRF protection
- **Database**
  - [ ] Fix missing `root_path` column (error 42703)
  - [ ] Create proper indexes & audit logging
  - [ ] Configure backup procedures

### HIGH PRIORITY (Week 3-4) ⚡
- **Core Module Completion**
  - [ ] Complete Projects module (file attachments, templates, auth fixes)
  - [ ] Enhance Work Orders module (time tracking, drag-and-drop fixes)
  - [ ] Improve Documents module (categorization, preview, search)
  - [ ] Polish Users module (granular permissions, groups)
- **Performance & UX**
  - [ ] Code splitting & lazy loading
  - [ ] Optimize DB queries (<500 ms)
  - [ ] Implement loading states & error recovery

### MEDIUM PRIORITY (Week 5-8) 📈
- **Advanced Features** (notifications, export, mobile, offline)
- **Dashboard Enhancements** (real-time data, interactive charts, layouts)
- **Integration & External Services** (AI providers, GitHub sync, service management)

### LOW PRIORITY (Week 9-12) 🔧
- Advanced templates, workflow automation, analytics & BI, theming, I18n, DevOps monitoring.

> _This section consolidates content from `updated-tasks.md`, `action-plan.md`, and `progress.md`._

## Objetivos de Calidad y Éxito (Fase 1)

### Code Quality Targets
- [ ] **0 ESLint warnings** (currently 400+)
- [ ] **0 TypeScript errors** (currently 50+)
- [ ] **100% test pass rate** (currently 71%)
- [ ] **60%+ test coverage** (currently 35%)

### Security Targets
- [ ] **All forms validated** with Zod schemas
- [ ] **RLS policies implemented** for all tables
- [ ] **Authentication working** on all routes
- [ ] **No security vulnerabilities** in audit

### Performance Targets
- [ ] **<3s page load time** (currently 5s+)
- [ ] **<500ms API responses** (currently 1s+)
- [ ] **Proper error handling** on all components
- [ ] **Loading states** implemented everywhere

### Functionality Targets
- [ ] **Core CRUD operations** working in all modules
- [ ] **File uploads** working reliably
- [ ] **User management** fully functional
- [ ] **Project management** feature complete
