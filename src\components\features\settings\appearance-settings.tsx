"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Loader2, Check } from "lucide-react"

import { Button } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Switch } from "@/components/ui/switch"
import { toast } from "@/hooks/use-toast"
import { createClient } from "@/lib/supabase/client"

// Esquema de validación para el formulario
const appearanceSettingsSchema = z.object({
  theme: z.enum(["light", "dark", "system"]),
  enable_animations: z.boolean().default(true),
  high_contrast: z.boolean().default(false),
  font_size: z.enum(["small", "medium", "large"]),
  sidebar_position: z.enum(["left", "right"]),
})

type AppearanceSettingsValues = z.infer<typeof appearanceSettingsSchema>

interface AppearanceSettingsProps {
  initialData?: Partial<AppearanceSettingsValues>
  onSubmit: (data: AppearanceSettingsValues) => void
  isLoading?: boolean
}

export function AppearanceSettings({
  initialData,
  onSubmit,
  isLoading = false,
}: AppearanceSettingsProps) {
  const form = useForm<AppearanceSettingsValues>({
    resolver: zodResolver(appearanceSettingsSchema),
    defaultValues: {
      theme: "system",
      enable_animations: true,
      high_contrast: false,
      font_size: "medium",
      sidebar_position: "left",
      ...initialData,
    },
  })

  const handleSubmit = (data: AppearanceSettingsValues) => {
    onSubmit(data)
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Tema</h3>
          <FormField
            control={form.control}
            name="theme"
            render={({ field }) => (
              <FormItem className="space-y-1">
                <FormLabel>Selecciona un tema</FormLabel>
                <FormDescription>
                  Personaliza la apariencia de la aplicación.
                </FormDescription>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    className="grid grid-cols-3 gap-4"
                  >
                    <FormItem>
                      <FormControl>
                        <div className="[&:has([data-state=checked])>div]:border-primary">
                          <RadioGroupItem value="light" className="sr-only" />
                          <div className="flex flex-col items-center justify-between rounded-md border-2 border-muted p-4 hover:border-accent">
                            <div className="mb-3 rounded-md bg-[#FFFFFF] p-2 shadow-sm">
                              <div className="space-y-2">
                                <div className="h-2 w-[80px] rounded-lg bg-[#EAEAEA]" />
                                <div className="h-2 w-[100px] rounded-lg bg-[#EAEAEA]" />
                              </div>
                            </div>
                            <span className="block w-full text-center font-normal">
                              Claro
                            </span>
                          </div>
                        </div>
                      </FormControl>
                    </FormItem>
                    <FormItem>
                      <FormControl>
                        <div className="[&:has([data-state=checked])>div]:border-primary">
                          <RadioGroupItem value="dark" className="sr-only" />
                          <div className="flex flex-col items-center justify-between rounded-md border-2 border-muted p-4 hover:border-accent">
                            <div className="mb-3 rounded-md bg-[#1F1F1F] p-2 shadow-sm">
                              <div className="space-y-2">
                                <div className="h-2 w-[80px] rounded-lg bg-[#444444]" />
                                <div className="h-2 w-[100px] rounded-lg bg-[#444444]" />
                              </div>
                            </div>
                            <span className="block w-full text-center font-normal">
                              Oscuro
                            </span>
                          </div>
                        </div>
                      </FormControl>
                    </FormItem>
                    <FormItem>
                      <FormControl>
                        <div className="[&:has([data-state=checked])>div]:border-primary">
                          <RadioGroupItem value="system" className="sr-only" />
                          <div className="flex flex-col items-center justify-between rounded-md border-2 border-muted p-4 hover:border-accent">
                            <div className="mb-3 flex items-center justify-center rounded-md bg-gradient-to-r from-[#FFFFFF] to-[#1F1F1F] p-2 shadow-sm">
                              <div className="space-y-2">
                                <div className="h-2 w-[80px] rounded-lg bg-gradient-to-r from-[#EAEAEA] to-[#444444]" />
                                <div className="h-2 w-[100px] rounded-lg bg-gradient-to-r from-[#EAEAEA] to-[#444444]" />
                              </div>
                            </div>
                            <span className="block w-full text-center font-normal">
                              Sistema
                            </span>
                          </div>
                        </div>
                      </FormControl>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Tamaño de fuente</h3>
          <FormField
            control={form.control}
            name="font_size"
            render={({ field }) => (
              <FormItem className="space-y-1">
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    className="flex space-x-4"
                  >
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="small" />
                      </FormControl>
                      <FormLabel className="text-sm font-normal">
                        Pequeño
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="medium" />
                      </FormControl>
                      <FormLabel className="font-normal">
                        Mediano
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="large" />
                      </FormControl>
                      <FormLabel className="text-lg font-normal">
                        Grande
                      </FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Posición de la barra lateral</h3>
          <FormField
            control={form.control}
            name="sidebar_position"
            render={({ field }) => (
              <FormItem className="space-y-1">
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    className="grid grid-cols-2 gap-4"
                  >
                    <FormItem>
                      <FormControl>
                        <div className="[&:has([data-state=checked])>div]:border-primary">
                          <RadioGroupItem value="left" className="sr-only" />
                          <div className="flex flex-col items-center justify-between rounded-md border-2 border-muted p-4 hover:border-accent">
                            <div className="mb-3 flex h-20 w-full">
                              <div className="w-1/4 bg-muted" />
                              <div className="w-3/4 p-2">
                                <div className="space-y-2">
                                  <div className="h-2 w-[80px] rounded-lg bg-muted" />
                                  <div className="h-2 w-[100px] rounded-lg bg-muted" />
                                </div>
                              </div>
                            </div>
                            <span className="block w-full text-center font-normal">
                              Izquierda
                            </span>
                          </div>
                        </div>
                      </FormControl>
                    </FormItem>
                    <FormItem>
                      <FormControl>
                        <div className="[&:has([data-state=checked])>div]:border-primary">
                          <RadioGroupItem value="right" className="sr-only" />
                          <div className="flex flex-col items-center justify-between rounded-md border-2 border-muted p-4 hover:border-accent">
                            <div className="mb-3 flex h-20 w-full">
                              <div className="w-3/4 p-2">
                                <div className="space-y-2">
                                  <div className="h-2 w-[80px] rounded-lg bg-muted" />
                                  <div className="h-2 w-[100px] rounded-lg bg-muted" />
                                </div>
                              </div>
                              <div className="w-1/4 bg-muted" />
                            </div>
                            <span className="block w-full text-center font-normal">
                              Derecha
                            </span>
                          </div>
                        </div>
                      </FormControl>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Accesibilidad</h3>
          <div className="space-y-4">
            <FormField
              control={form.control}
              name="enable_animations"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Animaciones</FormLabel>
                    <FormDescription>
                      Habilitar animaciones en la interfaz.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="high_contrast"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Alto contraste</FormLabel>
                    <FormDescription>
                      Aumentar el contraste para mejorar la legibilidad.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </div>

        <div className="flex justify-end space-x-4">
          <Button variant="outline" type="button">
            Cancelar
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Guardar cambios
          </Button>
        </div>
      </form>
    </Form>
  )
}
