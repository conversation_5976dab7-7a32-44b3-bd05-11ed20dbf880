import React, { useEffect } from 'react';
import { checkSupabaseConnection, getProjectsCount, getTableStructure } from '@/lib/supabase';

const ProjectsDiagnosticsConnection: React.FC = () => {
  useEffect(() => {
    checkSupabaseConnection();
    getProjectsCount();
    getTableStructure();
  }, [checkSupabaseConnection, getProjectsCount, getTableStructure]);

  return (
    <div>
      {/* Render your component content here */}
    </div>
  );
};

export default ProjectsDiagnosticsConnection;