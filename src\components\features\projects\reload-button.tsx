"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { RefreshCw } from "lucide-react"
import { useState } from "react"
import { AuthSessionDebugger } from "@/components/features/auth/auth-session-debugger"

/**
 * Botón para recargar la página con diagnóstico de sesión
 */
export function ReloadButton() {
  const [isLoading, setIsLoading] = useState(false)

  const handleReload = () => {
    setIsLoading(true)
    window.location.reload()
  }

  return (
    <div className="flex items-center space-x-2">
      <Button 
        variant="outline" 
        size="sm" 
        onClick={handleReload}
        disabled={isLoading}
      >
        <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? "animate-spin" : ""}`} />
        Recargar
      </Button>
      <AuthSessionDebugger />
    </div>
  )
}
