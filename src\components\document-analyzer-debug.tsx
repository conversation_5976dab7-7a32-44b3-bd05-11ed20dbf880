"use client"

import { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { createClient } from '@/lib/supabase/client'
import { Loader2, FileText } from '@/components/ui/icons'
import { toast } from '@/hooks/use-toast'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

export function DocumentAnalyzerDebug() {
  const [analyzing, setAnalyzing] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  const [logs, setLogs] = useState<string[]>([])

  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toISOString()}: ${message}`])
  }

  const [provider, setProvider] = useState('gemini')
  const [useJwt, setUseJwt] = useState(true)

  const testConnection = async () => {
    try {
      setAnalyzing(true)
      setError(null)
      setResult(null)
      setLogs([])

      // Crear cliente de Supabase
      const supabase = createClient()

      addLog('Iniciando prueba de conexión')

      // 1. Verificar sesión
      const { data: sessionData } = await supabase.auth.getSession()
      const session = sessionData.session

      if (!session) {
        throw new Error('No hay sesión activa. Por favor inicia sesión.')
      }

      addLog(`Sesión activa: Usuario ${session.user.id}`)
      addLog(`Token disponible: ${session.access_token ? 'Sí' : 'No'}`)
      addLog(`Proveedor seleccionado: ${provider}`)
      addLog(`Usando JWT: ${useJwt ? 'Sí' : 'No'}`)

      // 2. Verificar acceso a storage
      addLog('Probando acceso a storage...')
      const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets()

      if (bucketsError) {
        addLog(`Error al listar buckets: ${bucketsError.message}`)
      } else {
        addLog(`Buckets disponibles: ${buckets.map(b => b.name).join(', ')}`)
      }

      // 3. Verificar acceso a la tabla documents
      addLog('Probando acceso a la tabla documents...')
      const { data: documents, error: documentsError } = await supabase
        .from('documents')
        .select('id, filename')
        .limit(5)

      if (documentsError) {
        addLog(`Error al consultar documents: ${documentsError.message}`)
      } else {
        addLog(`Documentos encontrados: ${documents.length}`)
        if (documents.length > 0) {
          addLog(`Primer documento: ${documents[0].id} - ${documents[0].filename}`)
        }
      }

      // 4. Probar la función Edge directamente
      addLog('Probando función Edge analyze-document...')
      try {
        // Determinar qué token usar
        const jwtToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.pPJrQI9QRDyOdoVaHRP0AlMU5DpY0jtS4kg9J_4tRYQ';
        const token = useJwt ? jwtToken : session.access_token;
        addLog(`Usando token: ${useJwt ? 'JWT' : 'Sesión'}`)

        // Configurar opciones para el proveedor
        const configOptions: unknown = {
          temperature: 0.7
        };

        // Usar max_completion_tokens para OpenAI
        if (provider === 'openai') {
          configOptions.max_completion_tokens = 2000;
          addLog('Configurando max_completion_tokens para OpenAI');
        } else {
          configOptions.maxTokens = 2000;
          addLog('Configurando maxTokens para otros proveedores');
        }

        addLog(`Opciones de configuración: ${JSON.stringify(configOptions)}`);

        const response = await fetch('https://xdboxokpjubowptrcytl.supabase.co/functions/v1/analyze-document', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
            'X-User-ID': session.user.id
          },
          body: JSON.stringify({
            test: true,
            userId: session.user.id,
            provider: provider,
            config: configOptions
          })
        })

        const responseStatus = response.status
        addLog(`Respuesta de la función: Status ${responseStatus}`)

        let responseData
        try {
          responseData = await response.json()
          addLog(`Datos de respuesta: ${JSON.stringify(responseData)}`)
        } catch (jsonError) {
          addLog(`Error al parsear respuesta: ${response.statusText}`)
          const text = await response.text()
          addLog(`Texto de respuesta: ${text.substring(0, 100)}...`)
        }

        setResult({
          status: responseStatus,
          data: responseData
        })
      } catch (fetchError: unknown) {
        addLog(`Error al llamar a la función: ${fetchError.message}`)
        throw new Error(`Error al llamar a la función: ${fetchError.message}`)
      }

      toast({
        title: "Prueba completada",
        description: "La prueba de conexión ha finalizado",
      })

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error inesperado')
      addLog(`ERROR: ${err instanceof Error ? err.message : 'Error inesperado'}`)
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : 'Error inesperado',
        variant: "destructive"
      })
    } finally {
      setAnalyzing(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Diagnóstico de Conexión</CardTitle>
        <CardDescription>
          Prueba la conexión con Supabase y la función Edge
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div className="space-y-2">
            <Label htmlFor="provider">Proveedor de IA</Label>
            <Select value={provider} onValueChange={setProvider}>
              <SelectTrigger id="provider">
                <SelectValue placeholder="Selecciona un proveedor" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="openai">OpenAI</SelectItem>
                <SelectItem value="gemini">Google Gemini</SelectItem>
                <SelectItem value="deepseek">DeepSeek</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="auth-type">Tipo de autenticación</Label>
            <Select value={useJwt ? "jwt" : "session"} onValueChange={(v) => setUseJwt(v === "jwt")}>
              <SelectTrigger id="auth-type">
                <SelectValue placeholder="Selecciona tipo de autenticación" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="jwt">Token JWT (Service Role)</SelectItem>
                <SelectItem value="session">Token de sesión</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Button
          onClick={testConnection}
          disabled={analyzing}
          className="w-full"
        >
          {analyzing ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Probando...
            </>
          ) : (
            <>
              <FileText className="mr-2 h-4 w-4" />
              Probar Conexión
            </>
          )}
        </Button>

        {error && (
          <div className="p-4 text-red-500 bg-red-50 rounded">
            {error}
          </div>
        )}

        {logs.length > 0 && (
          <div className="mt-4 p-4 bg-gray-50 rounded">
            <h3 className="font-semibold mb-2">Logs:</h3>
            <pre className="whitespace-pre-wrap text-sm h-60 overflow-y-auto">
              {logs.join('\n')}
            </pre>
          </div>
        )}

        {result && (
          <div className="mt-4 p-4 bg-green-50 rounded">
            <h3 className="font-semibold mb-2">Resultado:</h3>
            <pre className="whitespace-pre-wrap text-sm">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
