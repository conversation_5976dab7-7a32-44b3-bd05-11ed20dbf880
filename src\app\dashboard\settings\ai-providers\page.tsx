"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { createClient } from "@/lib/supabase/client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { toast } from "@/hooks/use-toast";
import { Loader2, Plus, Trash2, RefreshCw } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { DashboardShell } from "@/components/shared/layout/dashboard-shell";

interface AIProvider {
  id: string;
  provider_name: string;
  api_key: string;
  model_name: string;
  is_active: boolean;
  priority: number;
  created_at: string;
  updated_at: string;
}

interface AIModel {
  name: string;
  description: string;
}

export default function AIProvidersPage() {
  const [providers, setProviders] = useState<AIProvider[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [availableModels, setAvailableModels] = useState<AIModel[]>([]);
  const [selectedProvider, setSelectedProvider] = useState("");
  const [newProvider, setNewProvider] = useState({
    provider_name: "",
    api_key: "",
    model_name: "",
    is_active: true,
    priority: 1,
  });
  const router = useRouter();
  const supabase = createClient();

  useEffect(() => {
    fetchProviders();
  }, []);

  const fetchProviders = async () => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from("ai_provider_configs")
        .select("*")
        .order("priority", { ascending: true });

      if (error) throw error;
      setProviders(data || []);
    } catch (error) {
      console.error("Error fetching AI providers:", error);
      toast({
        title: "Error",
        description: "No se pudieron cargar los proveedores de IA",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewProvider((prev) => ({ ...prev, [name]: value }));
  };

  const handleSwitchChange = (checked: boolean) => {
    setNewProvider((prev) => ({ ...prev, is_active: checked }));
  };

  const handleProviderSelect = (provider: string) => {
    setSelectedProvider(provider);
    // Reset available models
    setAvailableModels([]);
    // Reset form with selected provider
    setNewProvider({
      provider_name: provider,
      api_key: "",
      model_name: "",
      is_active: true,
      priority: 1,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate form
      if (!newProvider.provider_name || !newProvider.api_key || !newProvider.model_name) {
        throw new Error("Todos los campos son obligatorios");
      }

      // Save provider to database
      const { data, error } = await supabase
        .from("ai_provider_configs")
        .upsert({
          provider_name: newProvider.provider_name,
          api_key: newProvider.api_key,
          model_name: newProvider.model_name,
          is_active: newProvider.is_active,
          priority: newProvider.priority,
          updated_at: new Date().toISOString(),
        })
        .select();

      if (error) throw error;

      toast({
        title: "Éxito",
        description: "Proveedor de IA guardado correctamente",
      });

      // Reset form and close dialog
      setNewProvider({
        provider_name: "",
        api_key: "",
        model_name: "",
        is_active: true,
        priority: 1,
      });
      setIsDialogOpen(false);

      // Refresh providers list
      fetchProviders();
    } catch (error) {
      console.error("Error saving AI provider:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Error al guardar el proveedor de IA",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async (providerId: string) => {
    try {
      const { error } = await supabase
        .from("ai_provider_configs")
        .delete()
        .eq("provider_name", providerId);

      if (error) throw error;

      toast({
        title: "Éxito",
        description: "Proveedor de IA eliminado correctamente",
      });

      // Refresh providers list
      fetchProviders();
    } catch (error) {
      console.error("Error deleting AI provider:", error);
      toast({
        title: "Error",
        description: "Error al eliminar el proveedor de IA",
        variant: "destructive",
      });
    }
  };

  const handleToggleActive = async (providerId: string, isActive: boolean) => {
    try {
      const { error } = await supabase
        .from("ai_provider_configs")
        .update({ is_active: isActive, updated_at: new Date().toISOString() })
        .eq("provider_name", providerId);

      if (error) throw error;

      toast({
        title: "Éxito",
        description: `Proveedor de IA ${isActive ? "activado" : "desactivado"} correctamente`,
      });

      // Refresh providers list
      fetchProviders();
    } catch (error) {
      console.error("Error updating AI provider:", error);
      toast({
        title: "Error",
        description: "Error al actualizar el proveedor de IA",
        variant: "destructive",
      });
    }
  };

  const fetchAvailableModels = async () => {
    if (!newProvider.provider_name || !newProvider.api_key) {
      toast({
        title: "Error",
        description: "Selecciona un proveedor y proporciona una clave API",
        variant: "destructive",
      });
      return;
    }

    setIsTestingConnection(true);

    try {
      // Llamar a la función Edge para obtener los modelos disponibles
      const response = await fetch("https://xdboxokpjubowptrcytl.supabase.co/functions/v1/analyze-document", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${supabase.auth.getSession().then(res => res.data.session?.access_token)}`,
        },
        body: JSON.stringify({
          listModels: true,
          provider: newProvider.provider_name,
          apiKey: newProvider.api_key,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Error al obtener los modelos disponibles");
      }

      const data = await response.json();
      
      if (data.models && Array.isArray(data.models)) {
        setAvailableModels(data.models);
        
        // Si hay modelos disponibles y hay un modelo recomendado, seleccionarlo
        if (data.models.length > 0 && data.recommendedModel) {
          setNewProvider(prev => ({
            ...prev,
            model_name: data.recommendedModel
          }));
        }
        
        toast({
          title: "Éxito",
          description: `Conexión exitosa. Se encontraron ${data.models.length} modelos disponibles.`,
        });
      } else {
        throw new Error("Formato de respuesta inválido");
      }
    } catch (error) {
      console.error("Error fetching available models:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Error al obtener los modelos disponibles",
        variant: "destructive",
      });
    } finally {
      setIsTestingConnection(false);
    }
  };

  const selectModel = (modelName: string) => {
    setNewProvider(prev => ({
      ...prev,
      model_name: modelName
    }));
  };

  return (
    <DashboardShell heading="Proveedores de IA">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Proveedores de IA</h2>
          <p className="text-muted-foreground">
            Configura los proveedores de IA para el análisis de documentos
          </p>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Añadir Proveedor
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Añadir Proveedor de IA</DialogTitle>
              <DialogDescription>
                Configura un nuevo proveedor de IA para el análisis de documentos
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit}>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="provider_name" className="text-right">
                    Proveedor
                  </Label>
                  <div className="col-span-3">
                    <div className="flex gap-2">
                      <Button
                        type="button"
                        variant={newProvider.provider_name === "gemini" ? "default" : "outline"}
                        className="flex-1"
                        onClick={() => handleProviderSelect("gemini")}
                      >
                        Google Gemini
                      </Button>
                      <Button
                        type="button"
                        variant={newProvider.provider_name === "openai" ? "default" : "outline"}
                        className="flex-1"
                        onClick={() => handleProviderSelect("openai")}
                      >
                        OpenAI
                      </Button>
                      <Button
                        type="button"
                        variant={newProvider.provider_name === "deepseek" ? "default" : "outline"}
                        className="flex-1"
                        onClick={() => handleProviderSelect("deepseek")}
                      >
                        DeepSeek
                      </Button>
                    </div>
                  </div>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="api_key" className="text-right">
                    Clave API
                  </Label>
                  <div className="col-span-3">
                    <Input
                      id="api_key"
                      name="api_key"
                      value={newProvider.api_key}
                      onChange={handleInputChange}
                      placeholder="Ingresa tu clave API"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <div className="text-right">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={fetchAvailableModels}
                      disabled={!newProvider.provider_name || !newProvider.api_key || isTestingConnection}
                    >
                      {isTestingConnection ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Probando...
                        </>
                      ) : (
                        <>
                          <RefreshCw className="mr-2 h-4 w-4" />
                          Probar Conexión
                        </>
                      )}
                    </Button>
                  </div>
                  <div className="col-span-3">
                    {availableModels.length > 0 && (
                      <div className="border rounded-md p-2 max-h-40 overflow-y-auto">
                        <p className="text-sm font-medium mb-2">Modelos Disponibles:</p>
                        <div className="flex flex-wrap gap-2">
                          {availableModels.map((model) => (
                            <Badge
                              key={model.name}
                              variant={newProvider.model_name === model.name ? "default" : "outline"}
                              className="cursor-pointer"
                              onClick={() => selectModel(model.name)}
                            >
                              {model.name}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="model_name" className="text-right">
                    Modelo
                  </Label>
                  <Input
                    id="model_name"
                    name="model_name"
                    className="col-span-3"
                    value={newProvider.model_name}
                    onChange={handleInputChange}
                    placeholder="Nombre del modelo (ej. gemini-1.5-flash)"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="priority" className="text-right">
                    Prioridad
                  </Label>
                  <Input
                    id="priority"
                    name="priority"
                    type="number"
                    className="col-span-3"
                    value={newProvider.priority}
                    onChange={handleInputChange}
                    placeholder="Prioridad (menor número = mayor prioridad)"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="is_active" className="text-right">
                    Activo
                  </Label>
                  <div className="flex items-center space-x-2 col-span-3">
                    <Switch
                      id="is_active"
                      checked={newProvider.is_active}
                      onCheckedChange={handleSwitchChange}
                    />
                    <Label htmlFor="is_active">
                      {newProvider.is_active ? "Activo" : "Inactivo"}
                    </Label>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Guardando...
                    </>
                  ) : (
                    "Guardar"
                  )}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Proveedores Configurados</CardTitle>
          <CardDescription>
            Lista de proveedores de IA configurados para el análisis de documentos
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : providers.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <p>No hay proveedores configurados</p>
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => setIsDialogOpen(true)}
              >
                Añadir Proveedor
              </Button>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Proveedor</TableHead>
                  <TableHead>Modelo</TableHead>
                  <TableHead>Prioridad</TableHead>
                  <TableHead>Estado</TableHead>
                  <TableHead className="text-right">Acciones</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {providers.map((provider) => (
                  <TableRow key={provider.provider_name}>
                    <TableCell className="font-medium">
                      {provider.provider_name === "gemini"
                        ? "Google Gemini"
                        : provider.provider_name === "openai"
                        ? "OpenAI"
                        : provider.provider_name === "deepseek"
                        ? "DeepSeek"
                        : provider.provider_name}
                    </TableCell>
                    <TableCell>{provider.model_name}</TableCell>
                    <TableCell>{provider.priority}</TableCell>
                    <TableCell>
                      <Badge
                        variant={provider.is_active ? "default" : "secondary"}
                      >
                        {provider.is_active ? "Activo" : "Inactivo"}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Switch
                          checked={provider.is_active}
                          onCheckedChange={(checked) =>
                            handleToggleActive(provider.provider_name, checked)
                          }
                        />
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 text-red-500 hover:text-red-600"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>
                                ¿Eliminar este proveedor?
                              </AlertDialogTitle>
                              <AlertDialogDescription>
                                Esta acción no se puede deshacer. ¿Estás seguro
                                de que quieres eliminar este proveedor de IA?
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancelar</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() =>
                                  handleDelete(provider.provider_name)
                                }
                                className="bg-red-500 hover:bg-red-600"
                              >
                                Eliminar
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </DashboardShell>
  );
}
