import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

// Re-exportar funciones de formateo de fechas
export {
  formatDate,
  formatTime,
  formatDateTime,
  formatRelativeTime
} from '@/lib/date-utils'

// Re-exportar funciones de formateo de moneda
export { formatCurrency } from '@/lib/currency'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function getBaseUrl() {
  if (typeof window !== 'undefined') {
    return window.location.origin
  }

  // For server-side rendering
  return process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'
}
