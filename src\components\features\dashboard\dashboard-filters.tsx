"use client"

import { useState } from "react"
import { Check, ChevronsUpDown, Filter } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { DateRangePicker } from "@/components/features/dashboard/date-range-picker"
import { DateRange } from "react-day-picker"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command"
import { cn } from "@/lib/utils"

interface Project {
  id: string
  name: string
}

interface User {
  id: string
  name: string
}

interface DashboardFiltersProps {
  onDateRangeChange: (range: DateRange | undefined) => void
  onProjectsChange: (projectIds: string[]) => void
  onUsersChange: (userIds: string[]) => void
  onStatusesChange: (statuses: string[]) => void
  projects: Project[]
  users: User[]
  initialDateRange?: DateRange
}

export function DashboardFilters({
  onDateRangeChange,
  onProjectsChange,
  onUsersChange,
  onStatusesChange,
  projects,
  users,
  initialDateRange,
}: DashboardFiltersProps) {
  const [selectedProjects, setSelectedProjects] = useState<string[]>([])
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([])
  const [openProjectSelect, setOpenProjectSelect] = useState(false)
  const [openUserSelect, setOpenUserSelect] = useState(false)

  const statuses = [
    { id: "pending", name: "Pendiente" },
    { id: "in_progress", name: "En Progreso" },
    { id: "completed", name: "Completado" },
    { id: "cancelled", name: "Cancelado" },
  ]

  const handleProjectSelect = (projectId: string) => {
    const newSelectedProjects = selectedProjects.includes(projectId)
      ? selectedProjects.filter((id) => id !== projectId)
      : [...selectedProjects, projectId]

    setSelectedProjects(newSelectedProjects)
    onProjectsChange(newSelectedProjects)
  }

  const handleUserSelect = (userId: string) => {
    const newSelectedUsers = selectedUsers.includes(userId)
      ? selectedUsers.filter((id) => id !== userId)
      : [...selectedUsers, userId]

    setSelectedUsers(newSelectedUsers)
    onUsersChange(newSelectedUsers)
  }

  const handleStatusSelect = (statusId: string) => {
    const newSelectedStatuses = selectedStatuses.includes(statusId)
      ? selectedStatuses.filter((id) => id !== statusId)
      : [...selectedStatuses, statusId]

    setSelectedStatuses(newSelectedStatuses)
    onStatusesChange(newSelectedStatuses)
  }

  const clearFilters = () => {
    setSelectedProjects([])
    setSelectedUsers([])
    setSelectedStatuses([])
    onProjectsChange([])
    onUsersChange([])
    onStatusesChange([])
  }

  return (
    <div className="flex flex-col md:flex-row items-center gap-4 mb-6">
      <DateRangePicker onChange={onDateRangeChange} initialDateRange={initialDateRange} />

      <div className="flex flex-wrap gap-2">
        <Popover open={openProjectSelect} onOpenChange={setOpenProjectSelect}>
          <PopoverTrigger asChild>
            <Button variant="outline" className="min-w-[200px] justify-between">
              {selectedProjects.length > 0
                ? `${selectedProjects.length} proyecto${selectedProjects.length > 1 ? "s" : ""}`
                : "Proyectos"}
              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[200px] p-0">
            <Command>
              <CommandInput placeholder="Buscar proyecto..." />
              <CommandEmpty>No se encontraron proyectos.</CommandEmpty>
              <CommandGroup>
                {projects.map((project) => (
                  <CommandItem
                    key={project.id}
                    value={project.id}
                    onSelect={() => handleProjectSelect(project.id)}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        selectedProjects.includes(project.id)
                          ? "opacity-100"
                          : "opacity-0"
                      )}
                    />
                    {project.name}
                  </CommandItem>
                ))}
              </CommandGroup>
            </Command>
          </PopoverContent>
        </Popover>

        <Popover open={openUserSelect} onOpenChange={setOpenUserSelect}>
          <PopoverTrigger asChild>
            <Button variant="outline" className="min-w-[200px] justify-between">
              {selectedUsers.length > 0
                ? `${selectedUsers.length} usuario${selectedUsers.length > 1 ? "s" : ""}`
                : "Usuarios"}
              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[200px] p-0">
            <Command>
              <CommandInput placeholder="Buscar usuario..." />
              <CommandEmpty>No se encontraron usuarios.</CommandEmpty>
              <CommandGroup>
                {users.map((user) => (
                  <CommandItem
                    key={user.id}
                    value={user.id}
                    onSelect={() => handleUserSelect(user.id)}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        selectedUsers.includes(user.id)
                          ? "opacity-100"
                          : "opacity-0"
                      )}
                    />
                    {user.name}
                  </CommandItem>
                ))}
              </CommandGroup>
            </Command>
          </PopoverContent>
        </Popover>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline">
              {selectedStatuses.length > 0
                ? `${selectedStatuses.length} estado${selectedStatuses.length > 1 ? "s" : ""}`
                : "Estados"}
              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56">
            <DropdownMenuLabel>Estados</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {statuses.map((status) => (
              <DropdownMenuCheckboxItem
                key={status.id}
                checked={selectedStatuses.includes(status.id)}
                onCheckedChange={() => handleStatusSelect(status.id)}
              >
                {status.name}
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {(selectedProjects.length > 0 || selectedUsers.length > 0 || selectedStatuses.length > 0) && (
          <Button variant="ghost" onClick={clearFilters} size="sm">
            Limpiar filtros
          </Button>
        )}
      </div>
    </div>
  )
}
