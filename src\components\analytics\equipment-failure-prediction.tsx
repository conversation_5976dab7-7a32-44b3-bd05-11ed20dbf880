import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Slider } from '@/components/ui/slider';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { EquipmentFailurePrediction } from '@/lib/types/service-analytics';
import { ServiceAnalyticsService } from '@/lib/services/service-analytics-service';
import { formatDate } from '@/lib/utils';
import { useRouter } from 'next/navigation';
import { 
  AlertTriangle, 
  Clock, 
  Calendar, 
  Building,
  FileText,
  Download,
  Bar<PERSON>hart,
  <PERSON>ch,
  AlertCircle,
  CheckCircle,
  Info
} from 'lucide-react';

interface EquipmentFailurePredictionProps {
  clientId?: string;
  limit?: number;
}

export function EquipmentFailurePrediction({
  clientId,
  limit = 10,
}: EquipmentFailurePredictionProps) {
  const [predictions, setPredictions] = useState<EquipmentFailurePrediction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [daysAhead, setDaysAhead] = useState(30);
  const [probability, setProbability] = useState(0.5);
  const [generating, setGenerating] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const fetchPredictions = async () => {
      try {
        setLoading(true);
        const data = await ServiceAnalyticsService.predictEquipmentFailures({
          days_ahead: daysAhead,
          min_failure_probability: probability,
          limit
        });
        setPredictions(data);
        setError(null);
      } catch (err) {
        console.error('Error al cargar predicciones de fallos:', err);
        setError('Error al cargar predicciones de fallos. Intente nuevamente.');
      } finally {
        setLoading(false);
      }
    };

    fetchPredictions();
  }, [daysAhead, probability, limit, clientId]);

  const handleGenerateReport = async () => {
    try {
      setGenerating(true);
      const reportUrl = await ServiceAnalyticsService.generateFailurePredictionReport(predictions);
      // En una implementación real, aquí se abriría o descargaría el informe
      alert(`Informe generado: ${reportUrl}`);
    } catch (err) {
      console.error('Error al generar informe:', err);
      alert('Error al generar informe. Intente nuevamente.');
    } finally {
      setGenerating(false);
    }
  };

  const getRiskBadge = (riskLevel: string) => {
    switch (riskLevel) {
      case 'critical':
        return (
          <Badge variant="destructive" className="flex gap-1 items-center">
            <AlertTriangle className="h-3 w-3" />
            Crítico
          </Badge>
        );
      case 'high':
        return (
          <Badge variant="warning" className="flex gap-1 items-center">
            <AlertCircle className="h-3 w-3" />
            Alto
          </Badge>
        );
      case 'medium':
        return (
          <Badge variant="secondary" className="flex gap-1 items-center">
            <Info className="h-3 w-3" />
            Medio
          </Badge>
        );
      case 'low':
        return (
          <Badge variant="outline" className="flex gap-1 items-center">
            <CheckCircle className="h-3 w-3" />
            Bajo
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            {riskLevel}
          </Badge>
        );
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Predicción de Fallos de Equipos</CardTitle>
          <CardDescription>
            Predicciones basadas en historial de servicio
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {Array(5).fill(0).map((_, i) => (
              <Skeleton key={i} className="h-12 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Predicción de Fallos de Equipos</CardTitle>
          <CardDescription>
            Predicciones basadas en historial de servicio
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-destructive/10 p-4 rounded-md text-destructive">
            {error}
          </div>
          <Button 
            variant="outline" 
            className="mt-4"
            onClick={() => window.location.reload()}
          >
            Reintentar
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Predicción de Fallos de Equipos</CardTitle>
        <CardDescription>
          Predicciones basadas en historial de servicio y patrones de mantenimiento
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Filtros */}
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label>Días hacia adelante</Label>
              <div className="flex items-center gap-2">
                <Slider 
                  value={[daysAhead]} 
                  min={7} 
                  max={365} 
                  step={1}
                  onValueChange={(value) => setDaysAhead(value[0])}
                />
                <Input 
                  type="number" 
                  value={daysAhead} 
                  onChange={(e) => setDaysAhead(parseInt(e.target.value))}
                  className="w-20"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label>Probabilidad mínima de fallo</Label>
              <div className="flex items-center gap-2">
                <Slider 
                  value={[probability * 100]} 
                  min={0} 
                  max={100} 
                  step={5}
                  onValueChange={(value) => setProbability(value[0] / 100)}
                />
                <Input 
                  type="number" 
                  value={Math.round(probability * 100)} 
                  onChange={(e) => setProbability(parseInt(e.target.value) / 100)}
                  className="w-20"
                />
                <span>%</span>
              </div>
            </div>
          </div>

          {/* Tabla de predicciones */}
          {predictions.length === 0 ? (
            <div className="text-center py-6 text-muted-foreground">
              No hay predicciones de fallos que cumplan con los criterios seleccionados.
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Equipo</TableHead>
                    <TableHead>Cliente</TableHead>
                    <TableHead>Riesgo</TableHead>
                    <TableHead>Probabilidad</TableHead>
                    <TableHead>Días hasta fallo</TableHead>
                    <TableHead>Último servicio</TableHead>
                    <TableHead>Problemas comunes</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {predictions.map((prediction) => (
                    <TableRow 
                      key={prediction.equipment_id}
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => router.push(`/dashboard/customer-equipment/${prediction.equipment_id}`)}
                    >
                      <TableCell className="font-medium">
                        <div>
                          {prediction.equipment_name}
                          {prediction.model && (
                            <div className="text-xs text-muted-foreground">
                              {prediction.model}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Building className="h-3 w-3" />
                          {prediction.client_name}
                        </div>
                      </TableCell>
                      <TableCell>{getRiskBadge(prediction.risk_level)}</TableCell>
                      <TableCell>
                        <div className="font-medium">
                          {Math.round(prediction.failure_probability * 100)}%
                        </div>
                      </TableCell>
                      <TableCell>
                        {prediction.days_until_predicted_failure !== null ? (
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3 text-muted-foreground" />
                            <span>{prediction.days_until_predicted_failure} días</span>
                          </div>
                        ) : (
                          <span className="text-muted-foreground">No disponible</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {prediction.last_service_date ? (
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3 text-muted-foreground" />
                            <span>{formatDate(prediction.last_service_date)}</span>
                          </div>
                        ) : (
                          <span className="text-muted-foreground">Sin servicios</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {Object.keys(prediction.common_issues).length > 0 ? (
                          <div className="flex flex-wrap gap-1">
                            {Object.entries(prediction.common_issues)
                              .sort((a, b) => b[1] - a[1])
                              .slice(0, 2)
                              .map(([issue, count]) => (
                                <Badge key={issue} variant="outline" className="text-xs">
                                  {issue.substring(0, 15)}{issue.length > 15 ? '...' : ''} ({count})
                                </Badge>
                              ))}
                          </div>
                        ) : (
                          <span className="text-muted-foreground">Sin datos</span>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button 
          variant="outline" 
          onClick={() => router.push('/dashboard/analytics/equipment-failures')}
        >
          <BarChart className="mr-2 h-4 w-4" />
          Ver análisis detallado
        </Button>
        <Button 
          variant="default" 
          onClick={handleGenerateReport}
          disabled={generating || predictions.length === 0}
        >
          <Download className="mr-2 h-4 w-4" />
          {generating ? 'Generando...' : 'Generar informe'}
        </Button>
      </CardFooter>
    </Card>
  );
}
