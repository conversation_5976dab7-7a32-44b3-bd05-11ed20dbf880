/**
 * @file Service Request Service
 * @description Service for managing technical service requests
 */

import { createClient } from '@/lib/supabase/client';
import {
  ServiceRequest,
  CreateServiceRequestInput,
  ServiceRequestStatus,
  ServiceRequestPriority
} from '@/types/service-management';

/**
 * Service for managing technical service requests
 */
export class ServiceRequestService {
  /**
   * Get all service requests with optional filtering
   *
   * @param options Filter options
   * @returns Promise with service requests
   */
  async getServiceRequests(options: {
    status?: ServiceRequestStatus | ServiceRequestStatus[];
    priority?: ServiceRequestPriority | ServiceRequestPriority[];
    clientId?: string;
    assignedTo?: string;
    search?: string;
    limit?: number;
    offset?: number;
    orderBy?: string;
    orderDirection?: 'asc' | 'desc';
  } = {}): Promise<{ data: ServiceRequest[] | null; error: unknown }> {
    const supabase = createClient();

    // Start building the query
    let query = supabase
      .from('service_requests')
      .select(`
        *,
        client:client_id(*),
        assigned_user:assigned_to(id, email, full_name),
        work_order:work_order_id(id, title, status)
      `);

    // Apply filters
    if (options.status) {
      if (Array.isArray(options.status)) {
        query = query.in('status', options.status);
      } else {
        query = query.eq('status', options.status);
      }
    }

    if (options.priority) {
      if (Array.isArray(options.priority)) {
        query = query.in('priority', options.priority);
      } else {
        query = query.eq('priority', options.priority);
      }
    }

    if (options.clientId) {
      query = query.eq('client_id', options.clientId);
    }

    if (options.assignedTo) {
      query = query.eq('assigned_to', options.assignedTo);
    }

    if (options.search) {
      query = query.or(`title.ilike.%${options.search}%,description.ilike.%${options.search}%`);
    }

    // Apply ordering
    if (options.orderBy) {
      query = query.order(options.orderBy, {
        ascending: options.orderDirection === 'asc'
      });
    } else {
      // Default ordering by created_at descending (newest first)
      query = query.order('created_at', { ascending: false });
    }

    // Apply pagination
    if (options.limit) {
      query = query.limit(options.limit);
    }

    if (options.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
    }

    // Execute the query
    const { data, error } = await query;

    return { data, error };
  }

  /**
   * Get a service request by ID
   *
   * @param id Service request ID
   * @returns Promise with service request
   */
  async getServiceRequestById(id: string): Promise<{ data: ServiceRequest | null; error: unknown }> {
    const supabase = createClient();

    const { data, error } = await supabase
      .from('service_requests')
      .select(`
        *,
        client:client_id(*),
        assigned_user:assigned_to(id, email, full_name),
        work_order:work_order_id(id, title, status)
      `)
      .eq('id', id)
      .single();

    return { data, error };
  }

  /**
   * Create a new service request
   *
   * @param serviceRequest Service request data
   * @returns Promise with created service request
   */
  async createServiceRequest(serviceRequest: CreateServiceRequestInput): Promise<{ data: ServiceRequest | null; error: unknown }> {
    const supabase = createClient();

    // Get current user for created_by field
    // Pass null as the JWT parameter to make it work with the current Supabase version
    const { data: userData } = await supabase.auth.getUser();
    const userId = userData?.user?.id;

    // Prepare the data
    const newServiceRequest = {
      ...serviceRequest,
      status: serviceRequest.status || 'pending',
      created_by: userId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Insert the service request
    const { data, error } = await supabase
      .from('service_requests')
      .insert(newServiceRequest)
      .select(`
        *,
        client:client_id(*),
        assigned_user:assigned_to(id, email, full_name)
      `)
      .single();

    // If service request was created successfully and has an assigned technician,
    // create a notification for the technician
    if (!error && data && data.assigned_to) {
      await this.createAssignmentNotification(data);
    }

    return { data, error };
  }

  /**
   * Update a service request
   *
   * @param id Service request ID
   * @param updates Service request updates
   * @returns Promise with updated service request
   */
  async updateServiceRequest(id: string, updates: Partial<ServiceRequest>): Promise<{ data: ServiceRequest | null; error: unknown }> {
    const supabase = createClient();

    // Get the current service request to check for assignment changes
    const { data: currentServiceRequest } = await this.getServiceRequestById(id);

    // Prepare the updates
    const serviceRequestUpdates = {
      ...updates,
      updated_at: new Date().toISOString()
    };

    // Update the service request
    const { data, error } = await supabase
      .from('service_requests')
      .update(serviceRequestUpdates)
      .eq('id', id)
      .select(`
        *,
        client:client_id(*),
        assigned_user:assigned_to(id, email, full_name),
        work_order:work_order_id(id, title, status)
      `)
      .single();

    // If service request was updated successfully and the assigned technician has changed,
    // create a notification for the new technician
    if (!error && data && updates.assigned_to && currentServiceRequest?.assigned_to !== updates.assigned_to) {
      await this.createAssignmentNotification(data);
    }

    return { data, error };
  }

  /**
   * Delete a service request
   *
   * @param id Service request ID
   * @returns Promise with deletion result
   */
  async deleteServiceRequest(id: string): Promise<{ error: unknown }> {
    const supabase = createClient();

    const { error } = await supabase
      .from('service_requests')
      .delete()
      .eq('id', id);

    return { error };
  }

  /**
   * Create a notification for a technician when assigned to a service request
   *
   * @param serviceRequest Service request
   * @returns Promise with notification result
   */
  private async createAssignmentNotification(serviceRequest: ServiceRequest): Promise<{ error: unknown }> {
    const supabase = createClient();

    // Check if notifications table exists
    try {
      const { error } = await supabase
        .from('notifications')
        .insert({
          user_id: serviceRequest.assigned_to,
          title: 'New Service Request Assignment',
          description: `You have been assigned to service request: ${serviceRequest.title}`,
          type: 'service_request_assignment',
          read: false,
          link: `/dashboard/service-requests/${serviceRequest.id}`,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      return { error };
    } catch (error) {
      console.error('Error creating notification:', error);
      return { error };
    }
  }

  /**
   * Convert a service request to a work order
   *
   * @param serviceRequestId Service request ID
   * @returns Promise with work order result
   */
  async convertToWorkOrder(serviceRequestId: string): Promise<{ data: unknown; error: unknown }> {
    const supabase = createClient();

    // Get the service request
    const { data: serviceRequest, error: fetchError } = await this.getServiceRequestById(serviceRequestId);

    if (fetchError || !serviceRequest) {
      return { data: null, error: fetchError || new Error('Service request not found') };
    }

    // Start a transaction
    const { data, error } = await supabase.rpc('create_work_order_from_service_request', {
      service_request_id: serviceRequestId,
      work_order_title: serviceRequest.title,
      work_order_description: serviceRequest.description || '',
      work_order_priority: serviceRequest.priority,
      work_order_assigned_to: serviceRequest.assigned_to
    });

    return { data, error };
  }
}

// Export a singleton instance
export const serviceRequestService = new ServiceRequestService();
