"use client"

import { useState, useEffect } from "react"
import { createClient } from "@/lib/supabase/client"
import { CheckCircle, XCircle, AlertCircle, Wifi, WifiOff } from "lucide-react"

export function ConnectionStatus() {
  const [status, setStatus] = useState<'checking' | 'connected' | 'error'>('checking')
  const [isOnline, setIsOnline] = useState<boolean>(typeof navigator !== 'undefined' ? navigator.onLine : true)
  const [errorMessage, setErrorMessage] = useState<string | null>(null)

  const supabase = createClient()

  // Verificar la conexión a Supabase
  const checkConnection = async () => {
    if (!isOnline) {
      setStatus('error')
      setErrorMessage('Sin conexión a Internet')
      return
    }

    try {
      const { data, error } = await supabase.from('_health').select('count').maybeSingle()

      if (error) {
        setStatus('error')
        const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

        setErrorMessage(errorMessage)
      } else {
        setStatus('connected')
        setErrorMessage(null)
      }
    } catch (error) {
      setStatus('error')
      setErrorMessage(error instanceof Error ? error.message : 'Error desconocido')
    }
  }

  // Manejar eventos de conexión
  const handleOnline = () => {
    setIsOnline(true)
    checkConnection()
  }

  const handleOffline = () => {
    setIsOnline(false)
    setStatus('error')
    setErrorMessage('Sin conexión a Internet')
  }

  // Verificar la conexión al cargar el componente y cada 30 segundos
  useEffect(() => {
    checkConnection()
    window.addEventListener('online', handleOnline)
    return () => {
      window.removeEventListener('online', handleOnline)
    }
  }, [checkConnection, handleOnline])

  // Renderizar el indicador de estado
  return (
    <div className="flex items-center">
      <div
        className={`
          flex items-center px-2 py-1 rounded-full text-xs border cursor-help
          ${status === 'checking' ? 'animate-pulse border-gray-300 bg-gray-100' :
            status === 'connected' ? 'border-green-200 bg-green-50 text-green-700' :
            'border-red-200 bg-red-50 text-red-700'}
        `}
        title={status === 'checking' ? 'Verificando conexión...' :
              status === 'connected' ? 'Conectado a Supabase' :
              `Error: ${errorMessage || 'Error desconocido'}`}
      >
        {status === 'checking' ? (
          <>
            <AlertCircle className="h-3 w-3 mr-1" />
            <span>Verificando...</span>
          </>
        ) : status === 'connected' ? (
          <>
            <CheckCircle className="h-3 w-3 mr-1" />
            <span>Conectado</span>
          </>
        ) : (
          <>
            {isOnline ? <XCircle className="h-3 w-3 mr-1" /> : <WifiOff className="h-3 w-3 mr-1" />}
            <span>Error</span>
          </>
        )}
      </div>
    </div>
  )
}
