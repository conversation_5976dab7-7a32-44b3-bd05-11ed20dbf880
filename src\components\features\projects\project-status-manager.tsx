"use client"

import { useState } from "react"
import { Check, Chev<PERSON>UpDown, Loader2 } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Badge } from "@/components/ui/badge"
import { toast } from "@/hooks/use-toast"
import { createClient } from "@/lib/supabase/client"
import { ProjectStatus, getStatusColor, getStatusText } from "@/types/projects"

interface ProjectStatusManagerProps {
  projectId: string
  currentStatus: ProjectStatus
  onStatusChange?: (newStatus: ProjectStatus) => void
}

export function ProjectStatusManager({
  projectId,
  currentStatus,
  onStatusChange,
}: ProjectStatusManagerProps) {
  const [open, setOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const supabase = createClient()

  const statuses: ProjectStatus[] = [
    "pending",
    "planning",
    "in_progress",
    "completed",
    "cancelled",
    "on_hold",
  ]

  const handleStatusChange = async (status: ProjectStatus) => {
    if (status === currentStatus) {
      setOpen(false)
      return
    }

    setIsLoading(true)
    try {
      const { error } = await supabase
        .from("projects")
        .update({ status })
        .eq("id", projectId)

      if (error) throw error

      toast({
        title: "Estado actualizado",
        description: `El proyecto ha sido actualizado a "${getStatusText(status)}"`,
      })

      if (onStatusChange) {
        onStatusChange(status)
      }
    } catch (error: unknown) {
      console.error("Error al actualizar el estado:", error)
      toast({
        title: "Error",
        description: "No se pudo actualizar el estado del proyecto",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
      setOpen(false)
    }
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between md:w-[200px]"
          disabled={isLoading}
        >
          {isLoading ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <Badge variant={getStatusColor(currentStatus)} className="mr-2">
              {getStatusText(currentStatus)}
            </Badge>
          )}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0">
        <Command>
          <CommandInput placeholder="Buscar estado..." />
          <CommandEmpty>No se encontraron resultados.</CommandEmpty>
          <CommandGroup>
            {statuses.map((status) => (
              <CommandItem
                key={status}
                value={status}
                onSelect={() => handleStatusChange(status)}
              >
                <Check
                  className={cn(
                    "mr-2 h-4 w-4",
                    status === currentStatus ? "opacity-100" : "opacity-0"
                  )}
                />
                <Badge variant={getStatusColor(status)} className="mr-2">
                  {getStatusText(status)}
                </Badge>
              </CommandItem>
            ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
