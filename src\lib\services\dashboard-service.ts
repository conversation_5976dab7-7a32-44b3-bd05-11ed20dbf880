import { createClient } from "@/lib/supabase/client";
import { Activity } from "@/components/features/dashboard/activity-list";
import { Task } from "@/components/features/dashboard/task-list";
import { tableExists, relationExists, safeQuery } from "@/lib/supabase/table-utils";

// Interfaces para tipos de datos
interface ProjectDistributionItem {
  status: string;
  total: number;
}

interface ActivityData {
  id: string;
  name?: string;
  filename?: string;
  created_at: string;
  upload_date?: string;
  owner_id?: string;
  uploaded_by?: string;
  users?: {
    id: string;
    first_name?: string;
    last_name?: string;
  };
}

interface TaskData {
  id: string;
  title: string;
  description?: string;
  completed: boolean;
  due_date?: string;
  work_orders?: {
    id: string;
    title: string;
    project_id?: string;
    projects?: {
      id: string;
      name: string;
    };
  };
}

interface PerformanceMetricData {
  category: string | null;
  efficiency: number | null;
  completion_rate: number | null;
  quality_score: number | null;
}

interface ResourceData {
  resource_name: string;
  resource_id?: number;
}

interface ProjectStatusData {
  status: string;
}

export interface DashboardMetrics {
  projectsCount: number;
  activeProjectsCount: number;
  ordersCount: number;
  pendingOrdersCount: number;
  documentsCount: number;
  usersCount: number;
  serviceRequestsCount: number;
  pendingServiceRequestsCount: number;
  customerEquipmentCount: number;
  maintenanceSchedulesCount: number;
  overdueMaintenanceCount: number;
  projectsTrend: number;
  ordersTrend: number;
  documentsTrend: number;
  usersTrend: number;
  serviceRequestsTrend: number;
  maintenanceTrend: number;
}

export interface ChartData {
  name: string;
  projects: number;
  orders: number;
  documents?: number;
  users?: number;
}

export interface RadarData {
  subject: string;
  efficiency: number;
  completion: number;
  quality: number;
}

export interface FilterOptions {
  dateRange?: {
    from: Date;
    to: Date;
  };
  projectIds?: string[];
  userIds?: string[];
  statuses?: string[];
}

export async function getDashboardMetrics(_filters?: FilterOptions): Promise<DashboardMetrics> {
  // Intentar obtener métricas desde caché primero
  if (typeof window !== 'undefined') {
    try {
      const cachedMetrics = localStorage.getItem('dashboard_metrics_cache');
      const cacheTimestamp = localStorage.getItem('dashboard_metrics_timestamp');

      // Verificar si tenemos datos en caché y si son recientes (menos de 5 minutos)
      if (cachedMetrics && cacheTimestamp) {
        const cacheAge = Date.now() - parseInt(cacheTimestamp);
        if (cacheAge < 5 * 60 * 1000) { // 5 minutos
          console.log('Usando métricas en caché (edad: ' + Math.round(cacheAge/1000) + ' segundos)');
          return JSON.parse(cachedMetrics);
        }
      }
    } catch (cacheError) {
      console.warn('Error al leer caché de métricas:', cacheError);
      // Continuar con la obtención normal si hay error en la caché
    }
  }

  console.log('Obteniendo métricas del dashboard...');

  try {
    // Crear cliente de Supabase
    const supabase = createClient();

    // Verificar la sesión actual
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
    if (sessionError || !sessionData.session) {
      console.error('Error de sesión:', sessionError?.message || 'No hay sesión activa');
      return getEmptyMetrics();
    }

    // Intentar obtener todas las métricas en una sola consulta para mejorar el rendimiento
    try {
      // Primero intentar con la función RPC optimizada
      const { data: metricsData, error: metricsError } = await supabase.rpc('get_dashboard_metrics');

      if (!metricsError && metricsData) {
        // Calcular tendencias (simuladas por ahora)
        const metrics: DashboardMetrics = {
          projectsCount: metricsData.projectsCount || 0,
          activeProjectsCount: metricsData.activeProjectsCount || 0,
          ordersCount: metricsData.ordersCount || 0,
          pendingOrdersCount: metricsData.pendingOrdersCount || 0,
          documentsCount: metricsData.documentsCount || 0,
          usersCount: metricsData.usersCount || 0,
          serviceRequestsCount: metricsData.serviceRequestsCount || 0,
          pendingServiceRequestsCount: metricsData.pendingServiceRequestsCount || 0,
          customerEquipmentCount: metricsData.customerEquipmentCount || 0,
          maintenanceSchedulesCount: metricsData.maintenanceSchedulesCount || 0,
          overdueMaintenanceCount: metricsData.overdueMaintenanceCount || 0,
          projectsTrend: 12.5,
          ordersTrend: -5.2,
          documentsTrend: 8.7,
          usersTrend: 3.1,
          serviceRequestsTrend: 5.3,
          maintenanceTrend: -2.1,
        };

        // Guardar en caché para futuras solicitudes
        if (typeof window !== 'undefined') {
          localStorage.setItem('dashboard_metrics_cache', JSON.stringify(metrics));
          localStorage.setItem('dashboard_metrics_timestamp', Date.now().toString());
        }

        return metrics;
      } else {
        // Si el error es porque la función no existe, mostrar mensaje y continuar
        if (metricsError && metricsError.message &&
            (metricsError.message.includes('function') && metricsError.message.includes('does not exist'))) {
          console.log('La función RPC get_dashboard_metrics no existe, usando método alternativo');
        } else {
          console.error('Error al obtener métricas con función RPC:', metricsError);
        }
      }
    } catch (rpcError) {
      // Si es un error de función no encontrada, mostrar mensaje y continuar
      if (rpcError instanceof Error && rpcError.message &&
          (rpcError.message.includes('function') && rpcError.message.includes('does not exist'))) {
        console.log('La función RPC get_dashboard_metrics no existe, usando método alternativo');
      } else {
        console.warn('Error al usar función RPC para métricas:', rpcError);
      }
      // Continuar con el método alternativo
    }

    // Método alternativo: consultas en paralelo para mejorar el rendimiento
    console.log('Usando consultas paralelas para obtener métricas...');

    // Preparar todas las consultas
    const queries = [
      // Proyectos totales
      supabase.from("projects").select("id", { count: "exact" }),
      // Proyectos activos
      supabase.from("projects")
        .select("id", { count: "exact" })
        .or("status.eq.active,status.eq.in_progress,status.eq.planning"),
      // Órdenes totales
      supabase.from("work_orders").select("id", { count: "exact" }),
      // Órdenes pendientes
      supabase.from("work_orders")
        .select("id", { count: "exact" })
        .eq("status", "pending"),
      // Documentos
      supabase.from("documents").select("id", { count: "exact" }),
      // Usuarios
      supabase.from("users").select("id", { count: "exact" })
    ];

    // Ejecutar todas las consultas en paralelo
    const results = await Promise.all(queries);

    // Extraer resultados
    const metrics: DashboardMetrics = {
      projectsCount: results[0].error ? 0 : (results[0].data?.length || 0),
      activeProjectsCount: results[1].error ? 0 : (results[1].data?.length || 0),
      ordersCount: results[2].error ? 0 : (results[2].data?.length || 0),
      pendingOrdersCount: results[3].error ? 0 : (results[3].data?.length || 0),
      documentsCount: results[4].error ? 0 : (results[4].data?.length || 0),
      usersCount: results[5].error ? 0 : (results[5].data?.length || 0),
      // Valores por defecto para los campos adicionales
      serviceRequestsCount: 0,
      pendingServiceRequestsCount: 0,
      customerEquipmentCount: 0,
      maintenanceSchedulesCount: 0,
      overdueMaintenanceCount: 0,
      // Tendencias simuladas
      projectsTrend: 12.5,
      ordersTrend: -5.2,
      documentsTrend: 8.7,
      usersTrend: 3.1,
      serviceRequestsTrend: 5.3,
      maintenanceTrend: -2.1,
    };

    // Guardar en caché para futuras solicitudes
    if (typeof window !== 'undefined') {
      localStorage.setItem('dashboard_metrics_cache', JSON.stringify(metrics));
      localStorage.setItem('dashboard_metrics_timestamp', Date.now().toString());
    }

    return metrics;
  } catch (error) {
    console.error('Error inesperado en getDashboardMetrics:', error);
    return getEmptyMetrics();
  }
}

// Función auxiliar para obtener métricas vacías
function getEmptyMetrics(): DashboardMetrics {
  return {
    projectsCount: 0,
    activeProjectsCount: 0,
    ordersCount: 0,
    pendingOrdersCount: 0,
    documentsCount: 0,
    usersCount: 0,
    serviceRequestsCount: 0,
    pendingServiceRequestsCount: 0,
    customerEquipmentCount: 0,
    maintenanceSchedulesCount: 0,
    overdueMaintenanceCount: 0,
    projectsTrend: 0,
    ordersTrend: 0,
    documentsTrend: 0,
    usersTrend: 0,
    serviceRequestsTrend: 0,
    maintenanceTrend: 0,
  };
}

// Esta función ha sido eliminada para evitar el uso de datos simulados

export async function getMonthlyData(_filters?: FilterOptions): Promise<ChartData[]> {
  try {
    const supabase = createClient();
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !sessionData?.session) {
      console.error('Error de sesión al obtener datos mensuales:', sessionError);
      return [];
    }

    // Intentar usar la función RPC optimizada
    try {
      console.log('Intentando usar la función RPC get_monthly_dashboard_data...');
      const { data: monthlyData, error: monthlyError } = await supabase.rpc('get_monthly_dashboard_data');

      if (!monthlyError && monthlyData) {
        console.log('Datos mensuales obtenidos con función RPC');
        return monthlyData;
      } else {
        console.error('Error al obtener datos mensuales con función RPC:', monthlyError);
        console.log('Usando método alternativo...');
      }
    } catch (rpcError) {
      console.error('Excepción al usar función RPC para datos mensuales:', rpcError);
      console.log('Usando método alternativo...');
    }

    // Si la función RPC falló, usar el método tradicional
    const months = [
      "Ene", "Feb", "Mar", "Abr", "May", "Jun",
      "Jul", "Ago", "Sep", "Oct", "Nov", "Dic",
    ];

    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();

    // Crear array para los últimos 6 meses
    const monthlyData: ChartData[] = [];

    // Preparar todas las consultas en paralelo para mejorar el rendimiento
    const queries = [];
    const monthIndices = [];

    for (let i = 0; i < 6; i++) {
      const monthIndex = (currentMonth - 5 + i + 12) % 12;
      const year = currentMonth - 5 + i < 0 ? currentYear - 1 : currentYear;
      monthIndices.push(monthIndex);

      // Calcular fechas de inicio y fin del mes
      const startDate = new Date(year, monthIndex, 1);
      const endDate = new Date(year, monthIndex + 1, 0);

      // Agregar consultas para este mes
      queries.push(
        // Proyectos
        supabase
          .from('projects')
          .select('id')
          .gte('created_at', startDate.toISOString())
          .lt('created_at', endDate.toISOString()),

        // Órdenes
        supabase
          .from('work_orders')
          .select('id')
          .gte('created_at', startDate.toISOString())
          .lt('created_at', endDate.toISOString()),

        // Documentos
        supabase
          .from('documents')
          .select('id')
          .gte('upload_date', startDate.toISOString())
          .lt('upload_date', endDate.toISOString()),

        // Usuarios
        supabase
          .from('users')
          .select('id')
          .gte('created_at', startDate.toISOString())
          .lt('created_at', endDate.toISOString())
      );
    }

    // Ejecutar todas las consultas en paralelo
    const results = await Promise.all(queries);

    // Procesar los resultados
    for (let i = 0; i < 6; i++) {
      const monthIndex = monthIndices[i];
      const baseIndex = i * 4; // Cada mes tiene 4 consultas

      const projectsResult = results[baseIndex];
      const ordersResult = results[baseIndex + 1];
      const documentsResult = results[baseIndex + 2];
      const usersResult = results[baseIndex + 3];

      monthlyData.push({
        name: months[monthIndex],
        projects: projectsResult.error ? 0 : (projectsResult.data?.length || 0),
        orders: ordersResult.error ? 0 : (ordersResult.data?.length || 0),
        documents: documentsResult.error ? 0 : (documentsResult.data?.length || 0),
        users: usersResult.error ? 0 : (usersResult.data?.length || 0),
      });
    }

    return monthlyData;
  } catch (error) {
    console.error('Error al obtener datos mensuales:', error);
    return [];
  }
}

export async function getProjectDistribution(_filters?: FilterOptions): Promise<{ name: string; value: number }[]> {
  try {
    const supabase = createClient();
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !sessionData?.session) {
      console.error('Error de sesión al obtener distribución de proyectos:', sessionError);
      return [];
    }

    // Intentar usar la función RPC optimizada
    try {
      console.log('Intentando usar la función RPC get_project_distribution...');
      const { data: distributionData, error: distributionError } = await supabase.rpc('get_project_distribution');

      console.log('Resultado crudo de la RPC get_project_distribution:', distributionData);

      if (!distributionError && distributionData) {
        console.log('Distribución de proyectos obtenida con función RPC');
        const mappedData = distributionData.map((item: ProjectDistributionItem) => {
           const translatedName =
            item.status === 'active' ? 'Activos' :
            item.status === 'completed' ? 'Completados' :
            item.status === 'pending' ? 'Pendientes' :
            item.status === 'cancelled' ? 'Cancelados' :
            item.status === 'in_progress' ? 'En progreso' :
            item.status === 'planning' ? 'En planificación' :
            item.status || 'Sin estado'; // Usar estado crudo o 'Sin estado' si es nulo/indefinido

          return {
            name: translatedName,
            value: item.total || 0
          };
        });
        console.log('Datos mapeados para getProjectDistribution (desde RPC):', mappedData);
        console.log('Retornando datos mapeados (desde RPC).');
        return mappedData;
      } else {
        console.error('Error al obtener distribución de proyectos con función RPC:', distributionError);
        console.log('Usando método alternativo...');

        // Si el error es de GROUP BY, mostrar mensaje específico
        if (distributionError && distributionError.message &&
            distributionError.message.includes('must appear in the GROUP BY clause')) {
          console.error('Error de GROUP BY en la función get_project_distribution. Use el botón "Corregir funciones" en el dashboard.');
          const errorData = [
            { name: 'Error SQL', value: 100 },
            { name: 'Use "Corregir funciones"', value: 100 }
          ];
          console.log('Datos de error devueltos por getProjectDistribution:', errorData);
          console.log('Retornando datos de error.');
          return errorData;
        }

        // Si el error es porque la función no existe, devolver datos de ejemplo
        if (distributionError && distributionError.message &&
            (distributionError.message.includes('function') && distributionError.message.includes('does not exist'))) {
          console.log('La función RPC no existe, devolviendo datos de ejemplo');
          const exampleData = [
            { name: 'Activo', value: 42 },
            { name: 'Completado', value: 28 },
            { name: 'Planificación', value: 18 },
            { name: 'En pausa', value: 12 }
          ];
          console.log('Datos de ejemplo devueltos por getProjectDistribution:', exampleData);
          console.log('Retornando datos de ejemplo.');
          return exampleData;
        }
      }
    } catch (rpcError) {
      console.error('Excepción al usar función RPC para distribución de proyectos:', rpcError);
      console.log('Usando método alternativo...');

      // Si es un error de función no encontrada o error de GROUP BY, devolver datos de ejemplo
      if (rpcError instanceof Error && rpcError.message) {
        if (rpcError.message.includes('must appear in the GROUP BY clause')) {
          console.error('Error de GROUP BY en la función get_project_distribution. Use el botón "Corregir funciones" en el dashboard.');
          const errorData = [
            { name: 'Error SQL', value: 100 },
            { name: 'Use "Corregir funciones"', value: 100 }
          ];
          console.log('Datos de error devueltos por getProjectDistribution:', errorData);
          console.log('Retornando datos de error.');
          return errorData;
        } else if (rpcError.message.includes('function') && rpcError.message.includes('does not exist')) {
          console.log('La función RPC no existe, devolviendo datos de ejemplo');
          const exampleData = [
            { name: 'Activo', value: 42 },
            { name: 'Completado', value: 28 },
            { name: 'Planificación', value: 18 },
            { name: 'En pausa', value: 12 }
          ];
          console.log('Datos de ejemplo devueltos por getProjectDistribution:', exampleData);
          console.log('Retornando datos de ejemplo.');
          return exampleData;
        }
      }
    }

    // Si la función RPC falló, usar el método tradicional
    console.log('Intentando obtener distribución de proyectos con método tradicional...');
    const { data: projectsData, error: projectsError } = await supabase
      .from('projects')
      .select('status');

    console.log('Resultado crudo del método tradicional:', projectsData);

    if (projectsError) {
      console.error('Error al obtener estados de proyectos con método tradicional:', projectsError);
      console.log('Retornando array vacío debido a error en método tradicional.');
      return [];
    }

    if (!projectsData || projectsData.length === 0) {
      console.log('No hay datos de proyectos disponibles con método tradicional');
      console.log('Retornando array vacío.');
      return [];
    }

    // Agrupar por estado y contar
    const counts: Record<string, number> = {};
    projectsData.forEach(project => {
      const status = project.status || 'Sin estado';
      counts[status] = (counts[status] || 0) + 1;
    });

    // Convertir a formato esperado
    const distribution = Object.entries(counts).map(([name, value]) => {
      // Traducir nombres de estado
      const translatedName =
        name === 'active' ? 'Activos' :
        name === 'completed' ? 'Completados' :
        name === 'pending' ? 'Pendientes' :
        name === 'cancelled' ? 'Cancelados' :
        name === 'in_progress' ? 'En progreso' :
        name === 'planning' ? 'En planificación' :
        name;

      return { name: translatedName, value };
    });
    console.log('Datos mapeados del método tradicional:', distribution);
    console.log('Retornando datos del método tradicional.');
    return distribution;

  } catch (error) {
    console.error('Error al obtener distribución de proyectos:', error);
    console.log('Retornando array vacío debido a excepción en getProjectDistribution.');
    return [];
  }
}

export async function getRecentActivities(_filters?: FilterOptions): Promise<Activity[]> {
  try {
    const supabase = createClient();
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !sessionData?.session) {
      console.error('Error de sesión al obtener actividades recientes:', sessionError);
      return [];
    }

    // Intentar usar la función RPC optimizada
    try {
      console.log('Intentando usar la función RPC get_recent_activities...');
      const { data: activitiesData, error: activitiesError } = await supabase.rpc('get_recent_activities');

      if (!activitiesError && activitiesData) {
        console.log('Actividades recientes obtenidas con función RPC');

        // Convertir las fechas de string a Date
        return activitiesData.map((activity: ActivityData) => ({
          ...activity,
          timestamp: new Date(activity.created_at)
        }));
      } else {
        console.error('Error al obtener actividades recientes con función RPC:', activitiesError);
        console.log('Usando método alternativo...');
      }
    } catch (rpcError) {
      console.error('Excepción al usar función RPC para actividades recientes:', rpcError);
      console.log('Usando método alternativo...');
    }

    // Si la función RPC falló, usar el método tradicional
    // Verificar si las tablas existen
    const projectsTableExists = await tableExists('projects');
    const documentsTableExists = await tableExists('documents');

    // Combinar y ordenar actividades
    const activities: Activity[] = [];

    // Ejecutar consultas en paralelo para mejorar el rendimiento
    const queries = [];

    // Preparar consulta de proyectos si la tabla existe
    if (projectsTableExists) {
      // Verificar si la relación con usuarios existe
      const hasOwnerRelation = await relationExists('projects', 'owner_id', 'auth.users');

      if (hasOwnerRelation) {
        queries.push(
          supabase
            .from('projects')
            .select('id, name, created_at, owner_id, users:owner_id(id, first_name, last_name)')
            .order('created_at', { ascending: false })
            .limit(5)
        );
      } else {
        queries.push(
          supabase
            .from('projects')
            .select('id, name, created_at, owner_id')
            .order('created_at', { ascending: false })
            .limit(5)
        );
      }
    } else {
      queries.push(Promise.resolve({ data: [], error: null }));
    }

    // Preparar consulta de documentos si la tabla existe
    if (documentsTableExists) {
      // Verificar si la relación con usuarios existe
      const hasUploadedByRelation = await relationExists('documents', 'uploaded_by', 'auth.users');

      if (hasUploadedByRelation) {
        queries.push(
          supabase
            .from('documents')
            .select('id, filename, upload_date, created_at, uploaded_by, users:uploaded_by(id, first_name, last_name)')
            .order('created_at', { ascending: false })
            .limit(5)
        );
      } else {
        queries.push(
          supabase
            .from('documents')
            .select('id, filename, upload_date, created_at, uploaded_by')
            .order('created_at', { ascending: false })
            .limit(5)
        );
      }
    } else {
      queries.push(Promise.resolve({ data: [], error: null }));
    }

    // Ejecutar todas las consultas en paralelo
    const [projectsResult, documentsResult] = await Promise.all(queries);

    // Procesar proyectos
    if (!projectsResult.error && projectsResult.data && projectsResult.data.length > 0) {
      projectsResult.data.forEach((project: ActivityData) => {
        const userName = project.users ?
          `${project.users.first_name || ''} ${project.users.last_name || ''}`.trim() :
          'Usuario desconocido';

        activities.push({
          id: `project-${project.id}`,
          user: {
            name: userName,
            avatar: '',
          },
          action: 'creó un nuevo proyecto',
          target: project.name || 'Proyecto sin nombre',
          timestamp: new Date(project.created_at),
          type: 'project',
        });
      });
    }

    // Procesar documentos
    if (!documentsResult.error && documentsResult.data && documentsResult.data.length > 0) {
      documentsResult.data.forEach((document: ActivityData) => {
        const userName = document.users ?
          `${document.users.first_name || ''} ${document.users.last_name || ''}`.trim() :
          'Usuario desconocido';

        activities.push({
          id: `document-${document.id}`,
          user: {
            name: userName,
            avatar: '',
          },
          action: 'subió un nuevo documento',
          target: document.filename || 'Documento sin nombre',
          timestamp: new Date(document.upload_date || document.created_at),
          type: 'document',
        });
      });
    }

    // Ordenar por fecha (más reciente primero)
    activities.sort((a, b) => {
      const dateA = a.timestamp instanceof Date ? a.timestamp : new Date(a.timestamp);
      const dateB = b.timestamp instanceof Date ? b.timestamp : new Date(b.timestamp);
      return dateB.getTime() - dateA.getTime();
    });

    // Limitar a 5 actividades
    return activities.slice(0, 5);
  } catch (error) {
    console.error('Error al obtener actividades recientes:', error);
    return [];
  }
}

export async function getPendingTasks(filters?: FilterOptions): Promise<Task[]> {
  try {
    const supabase = createClient();
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !sessionData?.session) {
      console.error('Error de sesión al obtener tareas pendientes:', sessionError);
      return [];
    }

    // Intentar usar la función RPC optimizada
    try {
      console.log('Intentando usar la función RPC get_pending_tasks...');
      const { data: tasksData, error: tasksError } = await supabase.rpc('get_pending_tasks');

      if (!tasksError && tasksData) {
        console.log('Tareas pendientes obtenidas con función RPC');

        // Convertir las fechas de string a Date
        const tasks = tasksData.map((task: TaskData) => ({
          ...task,
          dueDate: task.due_date ? new Date(task.due_date) : undefined
        }));

        // Aplicar filtros si existen
        if (filters && filters.dateRange) {
          return tasks.filter((task: Task) => {
            if (!task.dueDate) return true;
            return task.dueDate >= filters.dateRange!.from &&
                   task.dueDate <= filters.dateRange!.to;
          });
        }

        return tasks;
      } else {
        console.error('Error al obtener tareas pendientes con función RPC:', tasksError);
        console.log('Usando método alternativo...');
      }
    } catch (rpcError) {
      console.error('Excepción al usar función RPC para tareas pendientes:', rpcError);
      console.log('Usando método alternativo...');
    }

    // Si la función RPC falló, usar el método tradicional
    // Verificar si la tabla existe
    const tasksTableExists = await tableExists('work_order_tasks');

    if (!tasksTableExists) {
      console.warn('La tabla work_order_tasks no existe');
      return [];
    }

    // Verificar si las relaciones existen
    const hasWorkOrderRelation = await relationExists('work_order_tasks', 'work_order_id', 'work_orders');
    const hasProjectRelation = await relationExists('work_orders', 'project_id', 'projects');

    // Construir la consulta según las relaciones disponibles
    let query;

    if (hasWorkOrderRelation) {
      if (hasProjectRelation) {
        query = supabase
          .from('work_order_tasks')
          .select(`
            id,
            title,
            description,
            is_completed as completed,
            created_at as due_date,
            work_orders:work_order_id(id, title, project_id, projects:project_id(id, name))
          `)
          .eq('is_completed', false)
          .order('created_at', { ascending: true })
          .limit(10);
      } else {
        query = supabase
          .from('work_order_tasks')
          .select(`
            id,
            title,
            description,
            is_completed as completed,
            created_at as due_date,
            work_orders:work_order_id(id, title, project_id)
          `)
          .eq('is_completed', false)
          .order('created_at', { ascending: true })
          .limit(10);
      }
    } else {
      query = supabase
        .from('work_order_tasks')
        .select(`
          id,
          title,
          description,
          is_completed as completed,
          created_at as due_date
        `)
        .eq('is_completed', false)
        .order('created_at', { ascending: true })
        .limit(10);
    }

    // Ejecutar la consulta
    const { data: tasksData, error: queryError } = await query;

    if (queryError) {
      console.error('Error al obtener tareas pendientes:', queryError);
      return [];
    }

    if (!tasksData || tasksData.length === 0) {
      console.log('No hay tareas pendientes disponibles');
      return [];
    }

    // Convertir a formato de tareas
    const tasks: Task[] = tasksData.map((task: TaskData) => {
      // Intentar obtener el nombre del proyecto
      let projectName = '';
      if (task.work_orders && task.work_orders.projects && task.work_orders.projects.name) {
        projectName = task.work_orders.projects.name;
      } else if (task.work_orders && task.work_orders.title) {
        projectName = task.work_orders.title;
      }

      return {
        id: task.id,
        title: task.title || 'Tarea sin título',
        completed: task.completed || false,
        dueDate: task.due_date ? new Date(task.due_date) : undefined,
        priority: 'medium', // Valor por defecto ya que no tenemos este campo
        project: projectName
      };
    });

    // Aplicar filtros si existen
    if (filters && filters.dateRange) {
      return tasks.filter(task => {
        if (!task.dueDate) return true;
        return task.dueDate >= filters.dateRange!.from &&
               task.dueDate <= filters.dateRange!.to;
      });
    }

    return tasks;
  } catch (error) {
    console.error('Error al obtener tareas pendientes:', error);
    return [];
  }
}

export async function getPerformanceData(): Promise<RadarData[]> {
  try {
    const supabase = createClient();
    console.log('Intentando obtener datos de rendimiento usando safeQuery...');

    // Usar safeQuery para obtener datos directamente de la tabla performance_metrics
    // safeQuery verifica si la tabla existe y maneja errores, devolviendo el valor por defecto si algo falla.
    const metricsData = await safeQuery<
      { category: string | null; efficiency: number | null; completion_rate: number | null; quality_score: number | null }[]
    >(
      'performance_metrics',
      // Pasar una función que retorna la Promesa de la consulta
      async () => {
        const result = await supabase
          .from('performance_metrics')
          .select('category, efficiency, completion_rate, quality_score')
          .order('category'); // O usar otro criterio de orden si es necesario
        return { data: result.data, error: result.error };
      },
      [] // Valor por defecto: array vacío si la tabla no existe, consulta falla o no hay datos
    );

    // Si safeQuery devolvió datos (no el valor por defecto vacío)
    if (metricsData && metricsData.length > 0) {
      console.log('Datos de rendimiento obtenidos con safeQuery:', metricsData);
      // Transformar al formato RadarData
      const mappedData = metricsData.map((metric: PerformanceMetricData) => ({
        subject: metric.category || 'Desconocido',
        efficiency: metric.efficiency || 0,
        completion: metric.completion_rate || 0,
        quality: metric.quality_score || 0
      }));
      console.log('Datos de rendimiento mapeados:', mappedData);
      return mappedData;
    } else {
       console.log('safeQuery no devolvió datos para performance_metrics o la tabla está vacía.');
    }

    // Si no se obtuvieron datos (porque safeQuery devolvió el valor por defecto o la tabla estaba vacía),
    // devolvemos los datos por defecto definidos localmente para el componente.
    console.log('Devolviendo datos de rendimiento por defecto.');
    return getDefaultPerformanceData();

  } catch (error) {
    console.error('Error inesperado en getPerformanceData:', error);
    // En caso de cualquier otra excepción, también devolvemos datos por defecto.
    return getDefaultPerformanceData();
  }
}

// Función auxiliar para obtener datos de rendimiento por defecto
function getDefaultPerformanceData(): RadarData[] {
  return [
    { subject: 'Planificación', efficiency: 0, completion: 0, quality: 0 },
    { subject: 'Diseño', efficiency: 0, completion: 0, quality: 0 },
    { subject: 'Ejecución', efficiency: 0, completion: 0, quality: 0 },
    { subject: 'Control', efficiency: 0, completion: 0, quality: 0 },
    { subject: 'Cierre', efficiency: 0, completion: 0, quality: 0 },
  ];
}

export async function getResourceAllocation(): Promise<{ name: string; value: number }[]> {
  try {
    const supabase = createClient();
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !sessionData?.session) {
      console.error('Error de sesión al obtener distribución de recursos:', sessionError);
      return getDefaultResourceAllocation();
    }

    // Intentar usar la función RPC optimizada (si existe)
    try {
      console.log('Intentando usar la función RPC get_resource_allocation...');
      const { data: resourceData, error: resourceError } = await supabase.rpc('get_resource_allocation');

      if (!resourceError && resourceData && Array.isArray(resourceData) && resourceData.length > 0) {
        console.log('Distribución de recursos obtenida con función RPC');
        // Mapear la respuesta de la RPC al formato esperado { name, value }
        // La RPC devuelve: resource_id (integer), resource_name (text), allocated_to (text), allocation_date (date)
        // Mapearemos resource_name a name. No hay un campo numérico claro para value, así que usaremos un valor por defecto o 0.
        return resourceData.map((item: ResourceData) => ({
          name: item.resource_name || 'Recurso desconocido',
          // No hay un campo numérico directo en la RPC para 'value', usando 0 como placeholder
          value: 0 // O podrías usar item.resource_id si tiene algún sentido, o 1 para contar elementos.
        }));
      } else {
        // Si el error es porque la función no existe, continuar con el método tradicional (fallback)
        if (resourceError && resourceError.message &&
            resourceError.message.includes('function') && resourceError.message.includes('does not exist')) {
          console.log('La función RPC get_resource_allocation no existe, usando método alternativo');
        } else {
          console.error('Error al obtener distribución de recursos con función RPC:', resourceError);
        }
      }
    } catch (rpcError) {
      console.warn('Excepción al usar función RPC para distribución de recursos:', rpcError);
    }

    // Si la función RPC falló o no devolvió datos útiles, intentar obtener distribución basada en proyectos (fallback)
    console.log('Intentando obtener distribución de recursos alternativa basada en proyectos...');

    try {
      const { data: projectsData, error: projectsError } = await supabase
        .from('projects')
        .select('status');

      if (!projectsError && projectsData && projectsData.length > 0) {
        console.log('Distribución alternativa basada en proyectos obtenida');
        // Agrupar por estado y contar
        const counts: Record<string, number> = {};
        projectsData.forEach((project: ProjectStatusData) => {
          const status = project.status || 'Sin estado';
          counts[status] = (counts[status] || 0) + 1;
        });

        // Convertir a formato esperado { name, value } usando el conteo
        return Object.entries(counts).map(([name, count]) => ({
          name: name === 'active' ? 'Activos' :
               name === 'completed' ? 'Completados' :
               name === 'pending' ? 'Pendientes' :
               name === 'cancelled' ? 'Cancelados' :
               name === 'in_progress' ? 'En progreso' :
               name === 'planning' ? 'En planificación' :
               name,
          value: count // Usar el conteo directo como valor
        }));
      }
    } catch (fallbackError) {
      console.error('Error al obtener distribución alternativa basada en proyectos:', fallbackError);
    }

    // Si no hay datos de la RPC ni del fallback, devolver datos por defecto
    console.log('No se pudieron obtener datos reales de asignación de recursos, devolviendo datos por defecto.');
    return getDefaultResourceAllocation();

  } catch (error) {
    console.error('Error al obtener distribución de recursos (catch externo):', error);
    return getDefaultResourceAllocation();
  }
}

// Función auxiliar para obtener datos de asignación de recursos por defecto
function getDefaultResourceAllocation(): { name: string; value: number }[] {
  return [
    { name: 'Personal', value: 45 },
    { name: 'Equipamiento', value: 28 },
    { name: 'Software', value: 15 },
    { name: 'Servicios', value: 12 },
    { name: 'Otros', value: 5 }
  ];
}

/**
 * Obtiene todos los datos del dashboard en una sola llamada a la función RPC 'get_dashboard_data'.
 * Devuelve un objeto con las claves: projectDistribution, performanceMetrics, resourceAllocation.
 * Si la función falla, devuelve datos de ejemplo y registra el error.
 */
export async function getDashboardData(): Promise<{
  projectDistribution: { name: string; value: number }[];
  performanceMetrics: RadarData[];
  resourceAllocation: { name: string; value: number }[];
  error?: string;
}> {
  try {
    console.log('Intentando obtener datos del dashboard llamando funciones individuales...');

    // Obtener los datos por separado
    const [projectDistributionResult, performanceMetricsResult, resourceAllocationResult] = await Promise.all([
      getProjectDistribution(),
      getPerformanceData(),
      getResourceAllocation()
    ]);

    console.log('Datos del dashboard obtenidos de funciones individuales');

    // Devolver los datos obtenidos
    return {
      projectDistribution: projectDistributionResult,
      performanceMetrics: performanceMetricsResult,
      resourceAllocation: resourceAllocationResult,
      // No hay error si las llamadas individuales tuvieron éxito
      error: undefined
    };
  } catch (err: unknown) {
    console.error('Excepción al obtener datos del dashboard (llamadas individuales):', err);

    // Datos por defecto en caso de error
    return {
      projectDistribution: [
        { name: 'Activo', value: 42 },
        { name: 'Completado', value: 28 },
        { name: 'Planificación', value: 18 },
        { name: 'En pausa', value: 12 }
      ],
      performanceMetrics: [
        { subject: 'Diseño', efficiency: 85, completion: 92, quality: 78 },
        { subject: 'Desarrollo', efficiency: 78, completion: 85, quality: 90 },
        { subject: 'Pruebas', efficiency: 92, completion: 88, quality: 95 },
        { subject: 'Implementación', efficiency: 80, completion: 75, quality: 85 },
        { subject: 'Soporte', efficiency: 88, completion: 90, quality: 82 }
      ],
      resourceAllocation: [
        { name: 'Personal', value: 45 },
        { name: 'Equipamiento', value: 28 },
        { name: 'Software', value: 15 },
        { name: 'Servicios', value: 12 },
        { name: 'Otros', value: 5 }
      ],
      error: err?.message || 'Excepción inesperada al obtener datos del dashboard'
    };
  }
}
