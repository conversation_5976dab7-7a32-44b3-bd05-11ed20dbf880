// Tipos e interfaces para contratos de servicio

// Contrato de servicio
export interface ServiceContract {
  id: string;
  contract_number: string | null;
  title: string;
  description: string | null;
  client_id: string | null;
  client_name?: string;
  start_date: string;
  end_date: string;
  status: ServiceContractStatus;
  contract_type: ServiceContractType;
  billing_cycle: BillingCycle | null;
  billing_amount: number | null;
  currency: string;
  auto_renew: boolean;
  renewal_reminder_days?: number;
  terms_conditions?: string | null;
  notes?: string | null;
  created_at: string;
  updated_at: string;
  created_by?: string | null;
  created_by_name?: string;
  updated_by?: string | null;
  document_url?: string | null;
  is_active: boolean;
  is_expired?: boolean;
  days_until_expiration?: number;
  items_count?: number;
  services_count?: number;
  requests_count?: number;
  equipment_count?: number;
  total_billed?: number | null;
  total_paid?: number | null;
}

// Detalles completos de un contrato de servicio
import type { CustomerEquipment } from './customer-equipment';

export interface ServiceContractDetails extends ServiceContract {
  items: ServiceContractItem[];
  recent_services: ServiceContractHistory[];
  covered_equipment: CustomerEquipment[];
  billing_history: ServiceContractBilling[];
}

// Estados de contrato de servicio
export type ServiceContractStatus = 
  | 'active'
  | 'expired'
  | 'cancelled'
  | 'pending';

// Tipos de contrato de servicio
export type ServiceContractType = 
  | 'basic'
  | 'standard'
  | 'premium'
  | 'custom';

// Ciclos de facturación
export type BillingCycle = 
  | 'monthly'
  | 'quarterly'
  | 'yearly'
  | 'one-time';

// Elemento de contrato de servicio
export interface ServiceContractItem {
  id: string;
  item_type: 'equipment' | 'service' | 'maintenance';
  equipment_id: string | null;
  equipment_name?: string;
  description: string;
  quantity: number;
  unit_price: number | null;
  total_price: number | null;
  included_hours: number | null;
  response_time_hours: number | null;
  is_unlimited: boolean;
  max_incidents: number | null;
  priority_level: string | null;
  created_at: string;
  updated_at: string;
}

// Historial de servicio de contrato
export interface ServiceContractHistory {
  id: string;
  service_date: string;
  hours_used: number | null;
  description: string;
  status: 'completed' | 'cancelled' | 'in_progress';
  is_billable: boolean;
  additional_charges: number | null;
  notes: string | null;
  service_request_id: string | null;
  service_request_title?: string | null;
  service_activity_id: string | null;
  service_activity_description?: string | null;
  technician_id: string | null;
  technician_name?: string;
}

// Facturación de contrato
export interface ServiceContractBilling {
  id: string;
  invoice_number: string | null;
  invoice_date: string;
  due_date: string;
  amount: number;
  status: 'pending' | 'paid' | 'overdue' | 'cancelled';
  payment_date: string | null;
  payment_method: string | null;
  payment_reference: string | null;
  billing_period_start: string | null;
  billing_period_end: string | null;
  notes: string | null;
  created_at: string;
  updated_at: string;
}

// Información de cobertura de contrato
export interface ServiceRequestCoverage {
  is_covered: boolean;
  contract_id: string | null;
  contract_title: string | null;
  contract_number: string | null;
  contract_type: string | null;
  client_id: string | null;
  client_name: string | null;
  equipment_covered: boolean;
  service_type_covered: boolean;
  hours_remaining: number | null;
  incidents_remaining: number | null;
}

// Métricas de contratos
export interface ContractMetrics {
  active_contracts: number;
  expiring_contracts: number;
  expired_contracts: number;
  total_contract_value: number;
  contracts_by_type: ChartData[];
  contracts_by_status: ChartData[];
  monthly_revenue: MonthlyRevenue[];
  top_clients: TopClient[];
}

// Datos para gráficos
export interface ChartData {
  name: string;
  value: number;
}

// Ingresos mensuales
export interface MonthlyRevenue {
  month: string;
  total: number;
  paid: number;
  pending: number;
  overdue: number;
}

// Cliente principal
export interface TopClient {
  client_name: string;
  contracts_count: number;
  total_value: number;
}

// Parámetros para crear un contrato de servicio
export interface CreateServiceContractParams {
  client_id: string;
  title: string;
  description?: string;
  start_date: string;
  end_date: string;
  contract_type: ServiceContractType;
  billing_cycle?: BillingCycle;
  billing_amount?: number;
  auto_renew?: boolean;
  terms_conditions?: string;
}

// Parámetros para añadir un elemento a un contrato
export interface AddContractItemParams {
  contract_id: string;
  item_type: 'equipment' | 'service' | 'maintenance';
  description: string;
  equipment_id?: string;
  quantity?: number;
  unit_price?: number;
  included_hours?: number;
  response_time_hours?: number;
  is_unlimited?: boolean;
  max_incidents?: number;
  priority_level?: string;
}

// Parámetros para registrar un servicio bajo un contrato
export interface RecordContractServiceParams {
  contract_id: string;
  service_request_id?: string;
  service_activity_id: string;
  hours_used: number;
  description: string;
  technician_id?: string;
  is_billable?: boolean;
  additional_charges?: number;
  notes?: string;
}

// Parámetros para generar facturación de contrato
export interface GenerateContractBillingParams {
  contract_id: string;
  invoice_date?: string;
  due_date?: string;
  amount?: number;
  billing_period_start?: string;
  billing_period_end?: string;
  notes?: string;
}
