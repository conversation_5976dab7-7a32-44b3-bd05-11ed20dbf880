#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔨 Testing Next.js 15+ build...\n');

function runCommand(command, description) {
  console.log(`📦 ${description}...`);
  try {
    const output = execSync(command, { 
      stdio: 'pipe', 
      cwd: process.cwd(),
      encoding: 'utf8'
    });
    console.log(`✅ ${description} completed`);
    if (output.trim()) {
      console.log('Output:', output.trim());
    }
    console.log('');
    return output;
  } catch (error) {
    console.error(`❌ Error during ${description}:`);
    console.error('STDOUT:', error.stdout?.toString() || 'No stdout');
    console.error('STDERR:', error.stderr?.toString() || 'No stderr');
    console.error('Error:', error.message);
    return null;
  }
}

function checkBuildOutput() {
  const nextDir = path.join(process.cwd(), '.next');
  const buildId = path.join(nextDir, 'BUILD_ID');
  
  console.log('🔍 Checking build output...');
  
  if (!fs.existsSync(nextDir)) {
    console.log('❌ .next directory does not exist');
    return false;
  }
  
  if (!fs.existsSync(buildId)) {
    console.log('❌ BUILD_ID file does not exist');
    return false;
  }
  
  console.log('✅ Build output looks good');
  return true;
}

async function testBuild() {
  try {
    // Step 1: Clean
    console.log('🧹 Cleaning previous build...');
    const nextDir = path.join(process.cwd(), '.next');
    if (fs.existsSync(nextDir)) {
      fs.rmSync(nextDir, { recursive: true, force: true });
      console.log('✅ Cleaned .next directory\n');
    }

    // Step 2: Type check
    const typeCheck = runCommand('npx tsc --noEmit', 'Type checking');
    if (!typeCheck && typeCheck !== '') {
      console.log('⚠️ Type check failed, but continuing...\n');
    }

    // Step 3: Build
    const buildOutput = runCommand('npx next build', 'Building application');
    if (!buildOutput && buildOutput !== '') {
      console.log('❌ Build failed');
      return false;
    }

    // Step 4: Check build output
    if (!checkBuildOutput()) {
      console.log('❌ Build output verification failed');
      return false;
    }

    console.log('🎉 Build test completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('1. Test production server: npm start');
    console.log('2. Deploy to Vercel: vercel --prod');
    
    return true;

  } catch (error) {
    console.error('❌ Build test failed:', error.message);
    return false;
  }
}

// Run the test
testBuild().then(success => {
  process.exit(success ? 0 : 1);
});
