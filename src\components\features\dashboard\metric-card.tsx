import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { ArrowUpIcon, ArrowDownIcon, ArrowRightIcon } from "lucide-react";
import Link from "next/link";

interface MetricCardProps {
  title: string;
  value: string | number;
  icon: React.ElementType;
  description?: string;
  trend?: number;
  href?: string;
  iconColor?: string;
  className?: string;
}

export function MetricCard({
  title,
  value,
  icon: Icon,
  description,
  trend,
  href,
  iconColor = "text-primary",
  className,
}: MetricCardProps) {
  // Determinar el color y el icono de la tendencia
  const getTrendColor = (trend?: number) => {
    if (trend === undefined) return "text-muted-foreground";
    if (trend > 0) return "text-green-500";
    if (trend < 0) return "text-red-500";
    return "text-muted-foreground";
  };

  const getTrendIcon = (trend?: number) => {
    if (trend === undefined) return ArrowRightIcon;
    if (trend > 0) return ArrowUpIcon;
    if (trend < 0) return ArrowDownIcon;
    return ArrowRightIcon;
  };

  const TrendIcon = getTrendIcon(trend);
  const trendColor = getTrendColor(trend);
  const trendText = trend !== undefined ? `${Math.abs(trend)}% respecto al período anterior` : "";

  // Contenido de la tarjeta
  const cardContent = (
    <>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className={cn("h-4 w-4", iconColor)} />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {(description || trend !== undefined) && (
          <div className="flex items-center text-xs text-muted-foreground">
            {trend !== undefined && (
              <>
                <TrendIcon className={cn("mr-1 h-3 w-3", trendColor)} />
                <span className={trendColor}>{trend > 0 ? "+" : ""}{trend}%</span>
                {description && <span className="mx-1">•</span>}
              </>
            )}
            {description && <span>{description}</span>}
          </div>
        )}
      </CardContent>
    </>
  );

  // Si hay un enlace, envolver en un Link
  if (href) {
    return (
      <Link href={href} className="block">
        <Card className={cn("transition-all hover:shadow-md", className)}>
          {cardContent}
        </Card>
      </Link>
    );
  }

  // Sin enlace
  return <Card className={className}>{cardContent}</Card>;
}
