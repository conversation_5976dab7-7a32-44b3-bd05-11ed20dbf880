"use client"

import { useState, useEffect } from "react"
import { createClient } from "@/lib/supabase/client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { CheckCircle, XCircle, AlertCircle, RefreshCw, Database, ShieldAlert, User, Table2 } from "lucide-react"
import { Skeleton } from "@/components/ui/skeleton"

export default function ProjectsDiagnosticsPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [supabaseStatus, setSupabaseStatus] = useState<'checking' | 'connected' | 'error'>('checking')
  const [authStatus, setAuthStatus] = useState<'checking' | 'authenticated' | 'unauthenticated'>('checking')
  const [projectsTableExists, setProjectsTableExists] = useState<boolean | null>(null)
  const [projectsCount, setProjectsCount] = useState<number | null>(null)
  const [projectsError, setProjectsError] = useState<string | null>(null)
  const [userProjects, setUserProjects] = useState<any[]>([])
  const [allProjects, setAllProjects] = useState<any[]>([])
  const [userDetails, setUserDetails] = useState<any>(null)
  const [rlsStatus, setRlsStatus] = useState<'checking' | 'enabled' | 'disabled' | 'error'>('checking')
  const [logs, setLogs] = useState<string[]>([])
  const [isCreatingDemo, setIsCreatingDemo] = useState(false)

  const supabase = createClient()

  // Función para añadir logs
  const addLog = (message: string) => {
    setLogs(prev => [...prev, `[${new Date().toISOString()}] ${message}`])
  }

  // Verificar la conexión a Supabase
  const checkSupabaseConnection = async () => {
    try {
      addLog("Verificando conexión a Supabase...")
      const { data, error } = await supabase.from('_health').select('count').maybeSingle()

      if (error) {
        const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

        addLog(`Error de conexión: ${errorMessage}`)
        setSupabaseStatus('error')
        return false
      }

      addLog("Conexión a Supabase establecida correctamente")
      setSupabaseStatus('connected')
      return true
    } catch (error) {
      addLog(`Excepción al verificar conexión: ${error instanceof Error ? error.message : String(error)}`)
      setSupabaseStatus('error')
      return false
    }
  }

  // Verificar la autenticación
  const checkAuthentication = async () => {
    try {
      addLog("Verificando autenticación...")
      const { data: sessionData, error } = await supabase.auth.getSession()

      if (error) {
        const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

        addLog(`Error al obtener sesión: ${errorMessage}`)
        setAuthStatus('unauthenticated')
        return false
      }

      if (!sessionData?.session) {
        addLog("No hay sesión activa")
        setAuthStatus('unauthenticated')
        return false
      }

      addLog(`Sesión activa para usuario: ${sessionData.session.user.email}`)
      setUserDetails(sessionData.session.user)
      setAuthStatus('authenticated')
      return true
    } catch (error) {
      addLog(`Excepción al verificar autenticación: ${error instanceof Error ? error.message : String(error)}`)
      setAuthStatus('unauthenticated')
      return false
    }
  }

  // Verificar si existe la tabla de proyectos
  const checkProjectsTable = async () => {
    try {
      addLog("Verificando existencia de tabla projects...")

      // Intentar una consulta simple para verificar si la tabla existe
      const { data, error } = await supabase.from('projects').select('count').limit(1)

      if (error) {
        const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

        if (errorMessage.includes("does not exist") || error.code === "42P01") {
          addLog("La tabla projects no existe")
          setProjectsTableExists(false)
          return false
        } else {
          const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

          addLog(`Error al verificar tabla: ${errorMessage}`)
          setProjectsTableExists(null)
          return null
        }
      }

      addLog("La tabla projects existe")
      setProjectsTableExists(true)
      return true
    } catch (error) {
      addLog(`Excepción al verificar tabla: ${error instanceof Error ? error.message : String(error)}`)
      setProjectsTableExists(null)
      return null
    }
  }

  // Verificar el estado de RLS
  const checkRLS = async () => {
    try {
      addLog("Verificando estado de RLS...")

      // Intentar una consulta para verificar si RLS está habilitado
      const { data, error } = await supabase.rpc('check_rls_enabled', { table_name: 'projects' })

      if (error) {
        const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

        addLog(`Error al verificar RLS: ${errorMessage}`)
        setRlsStatus('error')
        return null
      }

      if (data === true) {
        addLog("RLS está habilitado para la tabla projects")
        setRlsStatus('enabled')
      } else {
        addLog("RLS está deshabilitado para la tabla projects")
        setRlsStatus('disabled')
      }

      return data
    } catch (error) {
      addLog(`Excepción al verificar RLS: ${error instanceof Error ? error.message : String(error)}`)
      setRlsStatus('error')
      return null
    }
  }

  // Contar proyectos
  const countProjects = async () => {
    try {
      addLog("Contando proyectos...")

      // Intentar contar proyectos
      const { count, error } = await supabase.from('projects').select('*', { count: 'exact', head: true })

      if (error) {
        const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

        addLog(`Error al contar proyectos: ${errorMessage}`)
        setProjectsCount(null)
        setProjectsError(errorMessage)
        return null
      }

      addLog(`Total de proyectos: ${count}`)
      setProjectsCount(count || 0)
      return count
    } catch (error) {
      addLog(`Excepción al contar proyectos: ${error instanceof Error ? error.message : String(error)}`)
      setProjectsCount(null)
      setProjectsError(error instanceof Error ? error.message : String(error))
      return null
    }
  }

  // Obtener proyectos del usuario actual
  const getUserProjects = async () => {
    try {
      addLog("Obteniendo proyectos del usuario actual...")

      // Obtener ID del usuario actual
      const { data: sessionData } = await supabase.auth.getSession()
      if (!sessionData?.session) {
        addLog("No hay sesión activa para obtener proyectos")
        return []
      }

      const userId = sessionData.session.user.id

      // Intentar obtener proyectos donde el usuario es propietario
      const { data: ownerProjects, error: ownerError } = await supabase
        .from('projects')
        .select('id, name, status, created_at')
        .eq('owner_id', userId)

      if (ownerError) {
        addLog(`Error al obtener proyectos como propietario: ${ownerError.message}`)
      } else {
        addLog(`Proyectos como propietario: ${ownerProjects?.length || 0}`)
      }

      // Intentar obtener proyectos donde el usuario es miembro
      const { data: memberProjects, error: memberError } = await supabase
        .from('project_users')
        .select('project:project_id(id, name, status, created_at)')
        .eq('user_id', userId)

      if (memberError) {
        addLog(`Error al obtener proyectos como miembro: ${memberError.message}`)
      } else {
        addLog(`Proyectos como miembro: ${memberProjects?.length || 0}`)
      }

      // Combinar resultados
      const projects = [
        ...(ownerProjects || []),
        ...(memberProjects?.map(p => p.project) || [])
      ]

      setUserProjects(projects)
      return projects
    } catch (error) {
      addLog(`Excepción al obtener proyectos del usuario: ${error instanceof Error ? error.message : String(error)}`)
      return []
    }
  }

  // Obtener todos los proyectos (sin filtrar)
  const getAllProjects = async () => {
    try {
      addLog("Obteniendo todos los proyectos (sin filtrar)...")

      // Intentar obtener todos los proyectos
      const { data, error } = await supabase
        .from('projects')
        .select('id, name, status, created_at, owner_id')
        .limit(100)

      if (error) {
        const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

        addLog(`Error al obtener todos los proyectos: ${errorMessage}`)
        return []
      }

      addLog(`Total de proyectos obtenidos: ${data?.length || 0}`)
      setAllProjects(data || [])
      return data || []
    } catch (error) {
      addLog(`Excepción al obtener todos los proyectos: ${error instanceof Error ? error.message : String(error)}`)
      return []
    }
  }

  // Crear proyectos de demostración
  const createDemoProjects = async () => {
    try {
      setIsCreatingDemo(true)
      addLog("Creando proyectos de demostración...")

      // Obtener ID del usuario actual
      const { data: sessionData } = await supabase.auth.getSession()
      if (!sessionData?.session) {
        addLog("No hay sesión activa para crear proyectos")
        return false
      }

      const userId = sessionData.session.user.id

      // Crear 3 proyectos de ejemplo
      const demoProjects = [
        {
          name: "Proyecto Demo 1",
          description: "Proyecto de demostración creado automáticamente",
          status: "pending",
          owner_id: userId,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          name: "Proyecto Demo 2",
          description: "Proyecto de demostración en progreso",
          status: "in_progress",
          owner_id: userId,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          name: "Proyecto Demo 3",
          description: "Proyecto de demostración completado",
          status: "completed",
          owner_id: userId,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ]

      // Insertar proyectos
      const { data, error } = await supabase
        .from('projects')
        .insert(demoProjects)
        .select()

      if (error) {
        const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

        addLog(`Error al crear proyectos de demostración: ${errorMessage}`)
        return false
      }

      addLog(`Proyectos de demostración creados: ${data?.length || 0}`)

      // Actualizar datos
      await runDiagnostics()
      return true
    } catch (error) {
      addLog(`Excepción al crear proyectos de demostración: ${error instanceof Error ? error.message : String(error)}`)
      return false
    } finally {
      setIsCreatingDemo(false)
    }
  }

  // Ejecutar todos los diagnósticos
  const runDiagnostics = async () => {
    setIsLoading(true)
    setLogs([])

    addLog("Iniciando diagnóstico completo...")

    const isConnected = await checkSupabaseConnection()
    if (!isConnected) {
      setIsLoading(false)
      return
    }

    const isAuthenticated = await checkAuthentication()
    if (!isAuthenticated) {
      setIsLoading(false)
      return
    }

    const tableExists = await checkProjectsTable()
    if (tableExists === false) {
      setIsLoading(false)
      return
    }

    await checkRLS()
    await countProjects()
    await getUserProjects()
    await getAllProjects()

    addLog("Diagnóstico completo finalizado")
    setIsLoading(false)
  }

  // Ejecutar diagnóstico al cargar la página
  useEffect(() => {
    runDiagnostics()
  }, [])

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Diagnóstico de Proyectos</h1>
        <Button onClick={runDiagnostics} disabled={isLoading}>
          <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          Actualizar
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Estado de Supabase */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center">
              <Database className="mr-2 h-5 w-5" />
              Conexión a Supabase
            </CardTitle>
          </CardHeader>
          <CardContent>
            {supabaseStatus === 'checking' ? (
              <Skeleton className="h-6 w-24" />
            ) : (
              <Badge variant={supabaseStatus === 'connected' ? 'success' : 'destructive'}>
                {supabaseStatus === 'connected' ? (
                  <><CheckCircle className="mr-1 h-4 w-4" /> Conectado</>
                ) : (
                  <><XCircle className="mr-1 h-4 w-4" /> Error</>
                )}
              </Badge>
            )}
          </CardContent>
        </Card>

        {/* Estado de Autenticación */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center">
              <User className="mr-2 h-5 w-5" />
              Autenticación
            </CardTitle>
          </CardHeader>
          <CardContent>
            {authStatus === 'checking' ? (
              <Skeleton className="h-6 w-24" />
            ) : (
              <Badge variant={authStatus === 'authenticated' ? 'success' : 'destructive'}>
                {authStatus === 'authenticated' ? (
                  <><CheckCircle className="mr-1 h-4 w-4" /> Autenticado</>
                ) : (
                  <><XCircle className="mr-1 h-4 w-4" /> No autenticado</>
                )}
              </Badge>
            )}
            {userDetails && (
              <div className="mt-2 text-sm text-muted-foreground">
                Usuario: {userDetails.email}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Estado de la tabla de proyectos */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center">
              <Table2 className="mr-2 h-5 w-5" />
              Tabla de Proyectos
            </CardTitle>
          </CardHeader>
          <CardContent>
            {projectsTableExists === null ? (
              <Skeleton className="h-6 w-24" />
            ) : (
              <Badge variant={projectsTableExists ? 'success' : 'destructive'}>
                {projectsTableExists ? (
                  <><CheckCircle className="mr-1 h-4 w-4" /> Existe</>
                ) : (
                  <><XCircle className="mr-1 h-4 w-4" /> No existe</>
                )}
              </Badge>
            )}
            {projectsCount !== null && (
              <div className="mt-2 text-sm text-muted-foreground">
                Total de proyectos: {projectsCount}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Estado de RLS */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center">
              <ShieldAlert className="mr-2 h-5 w-5" />
              Row Level Security (RLS)
            </CardTitle>
          </CardHeader>
          <CardContent>
            {rlsStatus === 'checking' ? (
              <Skeleton className="h-6 w-24" />
            ) : (
              <Badge variant={rlsStatus === 'error' ? 'destructive' : (rlsStatus === 'enabled' ? 'outline' : 'secondary')}>
                {rlsStatus === 'enabled' ? (
                  <><CheckCircle className="mr-1 h-4 w-4" /> Habilitado</>
                ) : rlsStatus === 'disabled' ? (
                  <><AlertCircle className="mr-1 h-4 w-4" /> Deshabilitado</>
                ) : (
                  <><XCircle className="mr-1 h-4 w-4" /> Error</>
                )}
              </Badge>
            )}
          </CardContent>
        </Card>
      </div>

      {projectsError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error al obtener proyectos</AlertTitle>
          <AlertDescription>{projectsError}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="user-projects">
        <TabsList>
          <TabsTrigger value="user-projects">Proyectos del Usuario</TabsTrigger>
          <TabsTrigger value="all-projects">Todos los Proyectos</TabsTrigger>
          <TabsTrigger value="logs">Logs</TabsTrigger>
        </TabsList>

        <TabsContent value="user-projects" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Proyectos del Usuario Actual</CardTitle>
              <CardDescription>
                Proyectos donde el usuario actual es propietario o miembro
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-2">
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                </div>
              ) : userProjects.length > 0 ? (
                <div className="space-y-2">
                  {userProjects.map(project => (
                    <div key={project.id} className="p-3 border rounded-md flex justify-between items-center">
                      <div>
                        <div className="font-medium">{project.name}</div>
                        <div className="text-sm text-muted-foreground">
                          Creado: {new Date(project.created_at).toLocaleDateString()}
                        </div>
                      </div>
                      <Badge variant="outline">{project.status}</Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6 text-muted-foreground">
                  No se encontraron proyectos para el usuario actual
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Button onClick={createDemoProjects} disabled={isCreatingDemo || isLoading}>
                {isCreatingDemo && <RefreshCw className="mr-2 h-4 w-4 animate-spin" />}
                Crear Proyectos de Demostración
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="all-projects" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Todos los Proyectos</CardTitle>
              <CardDescription>
                Listado de todos los proyectos en la base de datos (máximo 100)
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-2">
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                </div>
              ) : allProjects.length > 0 ? (
                <div className="space-y-2">
                  {allProjects.map(project => (
                    <div key={project.id} className="p-3 border rounded-md flex justify-between items-center">
                      <div>
                        <div className="font-medium">{project.name}</div>
                        <div className="text-sm text-muted-foreground">
                          ID: {project.id} | Propietario: {project.owner_id}
                        </div>
                      </div>
                      <Badge variant="outline">{project.status}</Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6 text-muted-foreground">
                  No se encontraron proyectos en la base de datos
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="logs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Logs de Diagnóstico</CardTitle>
              <CardDescription>
                Registro de operaciones realizadas durante el diagnóstico
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-muted p-4 rounded-md h-[400px] overflow-y-auto font-mono text-sm">
                {logs.length > 0 ? (
                  logs.map((log, index) => (
                    <div key={index} className="py-1">
                      {log}
                    </div>
                  ))
                ) : (
                  <div className="text-center py-6 text-muted-foreground">
                    No hay logs disponibles
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
