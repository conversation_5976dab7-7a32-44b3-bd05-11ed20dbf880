'use client'

import { useState } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useRouter } from 'next/navigation'
import { Provider } from '@supabase/supabase-js'
import { validateOAuthConfig, getOAuthRedirectUrl } from '@/lib/supabase/oauth-config'

export function useAuth() {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()
  const supabase = createClient()

  const signIn = async (email: string, password: string) => {
    try {
      setIsLoading(true)
      setError(null)

      // Verificar si hay un bloqueo por rate limit
      const rateLimitUntil = typeof window !== 'undefined' ? localStorage.getItem('auth_rate_limit_until') : null
      const now = Date.now()

      if (rateLimitUntil && parseInt(rateLimitUntil) > now) {
        const waitMinutes = Math.round((parseInt(rateLimitUntil) - now) / 1000 / 60)
        throw new Error(`Demasiados intentos de inicio de sesión. Por favor, espera ${waitMinutes} minutos antes de intentar nuevamente.`)
      }

      // Intentar iniciar sesión
      console.log('Iniciando sesión con email:', email)
      const { data, error: signInError } = await supabase.auth.signInWithPassword({
        email,
        password,
        options: {
          // Configuración para mejorar la persistencia de la sesión
          persistSession: true,
          // Configuración de cookies
          cookieOptions: {
            // Tiempo de vida de la cookie en segundos (30 días)
            lifetime: 60 * 60 * 24 * 30,
            // Configuración de seguridad de la cookie
            sameSite: 'lax',
            secure: false, // Desactivar secure para desarrollo local en http
            path: '/',
          },
        }
      })

      if (signInError) {
        // Manejar el error de correo no confirmado
        if (signInError.message.includes('Email not confirmed')) {
          console.log("Error de correo no confirmado:", signInError.message)
          setError("Tu correo electrónico no ha sido verificado. Por favor, verifica tu correo o contacta a soporte.")
          return
        }

        // Manejar error de credenciales inválidas
        if (signInError.message.includes('Invalid login credentials')) {
          console.log("Credenciales inválidas:", signInError.message)
          setError("Correo electrónico o contraseña incorrectos. Por favor, verifica tus credenciales.")
          return
        }

        // Para cualquier otro error
        console.error("Error de inicio de sesión:", signInError.message)
        throw signInError
      }

      // Guardar timestamp de inicio de sesión
      localStorage.setItem('auth_signin_time', Date.now().toString())

      // Verificar si hay una URL de redirección guardada
      const redirectUrl = localStorage.getItem('auth_redirect_after_login')

      if (redirectUrl) {
        console.log('Redirigiendo a URL guardada:', redirectUrl)
        localStorage.removeItem('auth_redirect_after_login')
        router.push(redirectUrl)
      } else {
        console.log('Redirigiendo al dashboard')
        router.push('/dashboard')
      }

      // Refrescar la página para asegurar que se carguen los datos correctamente
      setTimeout(() => {
        router.refresh()
      }, 100)
    } catch (e: unknown) {
      // Manejar específicamente el error de rate limit
      const errorMessage = e instanceof Error ? e.message : 'Error desconocido';

      if (e.message.includes('too many requests') || errorMessage.includes('rate limit')) {
        const now = Date.now()
        const waitUntil = now + (15 * 60 * 1000) // 15 minutos
        localStorage.setItem('auth_rate_limit_until', waitUntil.toString())
        setError(`Demasiados intentos de inicio de sesión. Por favor, espera 15 minutos antes de intentar nuevamente.`)
      } else {
        console.error('Error en proceso de login:', e)
        const errorMessage = e instanceof Error ? e.message : 'Error desconocido';

        setError(errorMessage)
      }
    } finally {
      setIsLoading(false)
    }
  }

  const signUp = async (email: string, password: string) => {
    try {
      setIsLoading(true)
      setError(null)

      const { error: signUpError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/callback`,
        },
      })

      if (signUpError) throw signUpError

      return true
    } catch (e: unknown) {
      const errorMessage = e instanceof Error ? e.message : 'Error desconocido';

      setError(errorMessage)
      return false
    } finally {
      setIsLoading(false)
    }
  }

  const signOut = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const { error: signOutError } = await supabase.auth.signOut()

      if (signOutError) throw signOutError

      router.push('/auth/login')
      router.refresh()
    } catch (e: unknown) {
      const errorMessage = e instanceof Error ? e.message : 'Error desconocido';

      setError(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const signInWithGoogle = async () => {
    try {
      setIsLoading(true)
      setError(null)

      console.log('Iniciando autenticación con Google...');

      // URL de redirección absoluta
      const redirectUrl = `${window.location.origin}/auth/callback`;
      console.log('URL de redirección:', redirectUrl);

      // Verificar si las variables de entorno están configuradas
      const isOAuthConfigValid = validateOAuthConfig();
      if (!isOAuthConfigValid) {
        console.warn('La configuración de OAuth no es válida, usando la configuración de Supabase');
      }

      // Intentar iniciar sesión con Google con configuración explícita para persistencia
      const { data, error: oauthError } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: redirectUrl,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
          // Forzar persistencia de sesión
          persistSession: true,
          // Configuración de cookies
          cookieOptions: {
            // Tiempo de vida de la cookie en segundos (30 días)
            lifetime: 60 * 60 * 24 * 30,
            // Configuración de seguridad de la cookie
            sameSite: 'lax',
            secure: false, // Desactivar secure para desarrollo local en http
            path: '/',
          },
        },
      })

      if (oauthError) {
        console.error('Error en autenticación con Google:', oauthError);
        throw oauthError;
      }

      if (!data?.url) {
        console.error('No se recibió URL de autorización');
        throw new Error('No se pudo iniciar la autenticación con Google');
      }

      console.log('Autenticación con Google iniciada correctamente');
      console.log('URL de autorización:', data.url);

      // Redirigir manualmente a la URL de autorización de Google
      window.location.href = data.url;
    } catch (e: unknown) {
      console.error('Excepción en autenticación con Google:', e);
      const errorMessage = e instanceof Error ? e.message : 'Error al iniciar sesión con Google';

      setError(errorMessage)
      setIsLoading(false)
    }
  }

  const signInWithGitHub = async () => {
    try {
      setIsLoading(true)
      setError(null)

      console.log('Iniciando autenticación con GitHub...');

      // URL de redirección absoluta
      const redirectUrl = `${window.location.origin}/auth/callback`;
      console.log('URL de redirección:', redirectUrl);

      // Verificar si las variables de entorno están configuradas
      const isOAuthConfigValid = validateOAuthConfig('github');
      if (!isOAuthConfigValid) {
        console.warn('La configuración de OAuth para GitHub no es válida, usando la configuración de Supabase');
      }

      // Intentar iniciar sesión con GitHub con configuración explícita para persistencia
      const { data, error: oauthError } = await supabase.auth.signInWithOAuth({
        provider: 'github',
        options: {
          redirectTo: redirectUrl,
          scopes: 'read:user user:email repo',
          // Forzar persistencia de sesión
          persistSession: true,
          // Configuración de cookies
          cookieOptions: {
            // Tiempo de vida de la cookie en segundos (30 días)
            lifetime: 60 * 60 * 24 * 30,
            // Configuración de seguridad de la cookie
            sameSite: 'lax',
            secure: false, // Desactivar secure para desarrollo local en http
            path: '/',
          },
        },
      })

      if (oauthError) {
        console.error('Error en autenticación con GitHub:', oauthError);
        throw oauthError;
      }

      if (!data?.url) {
        console.error('No se recibió URL de autorización');
        throw new Error('No se pudo iniciar la autenticación con GitHub');
      }

      console.log('Autenticación con GitHub iniciada correctamente');
      console.log('URL de autorización:', data.url);

      // Redirigir manualmente a la URL de autorización de GitHub
      window.location.href = data.url;
    } catch (e: unknown) {
      console.error('Excepción en autenticación con GitHub:', e);
      const errorMessage = e instanceof Error ? e.message : 'Error al iniciar sesión con GitHub';

      setError(errorMessage)
      setIsLoading(false)
    }
  }

  return {
    signIn,
    signUp,
    signOut,
    signInWithGoogle,
    signInWithGitHub,
    isLoading,
    error,
  }
}