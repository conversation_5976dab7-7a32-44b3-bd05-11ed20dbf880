"use client"

import { useState, useRef, useEffect } from "react"
import { Upload, File, X, Loader2, FileText, Check, AlertCircle } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent } from "@/components/ui/card"
import { toast } from "@/hooks/use-toast"
import { createClient } from "@/lib/supabase/client"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { documentAnalysisClient } from "@/lib/document-analysis/document-analysis-client"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"

interface DocumentUploadWithAnalysisProps {
  projectId?: string
  workOrderId?: string
  onUploadComplete?: (fileData: unknown) => void
  onAnalysisComplete?: (analysisData: unknown) => void
  allowedFileTypes?: string[]
  maxFileSize?: number // en MB
}

export function DocumentUploadWithAnalysis({
  projectId,
  workOrderId,
  onUploadComplete,
  onAnalysisComplete,
  allowedFileTypes = [".pdf", ".doc", ".docx", ".txt"],
  maxFileSize = 10, // 10MB por defecto
}: DocumentUploadWithAnalysisProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [description, setDescription] = useState("")
  const [category, setCategory] = useState("")
  const fileInputRef = useRef<HTMLInputElement>(null)
  const supabase = createClient()

  // Estados para el análisis
  const [analyzeWithLMStudio, setAnalyzeWithLMStudio] = useState(false)
  const [selectedModel, setSelectedModel] = useState("")
  const [availableModels, setAvailableModels] = useState<any[]>([])
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [analysisId, setAnalysisId] = useState<string | null>(null)
  const [analysisResult, setAnalysisResult] = useState<any | null>(null)
  const [analysisError, setAnalysisError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState("upload")

  // Cargar modelos disponibles
  useEffect(() => {
    if (analyzeWithLMStudio) {
      loadAvailableModels()
    }
  }, [analyzeWithLMStudio])

  // Consultar estado del análisis
  useEffect(() => {
    let intervalId: NodeJS.Timeout

    if (analysisId && isAnalyzing) {
      intervalId = setInterval(async () => {
        try {
          const result = await documentAnalysisClient.getAnalysisResult(analysisId)

          if (result.status === 'completed') {
            setIsAnalyzing(false)
            setAnalysisResult(result)
            setActiveTab("results")

            if (onAnalysisComplete) {
              onAnalysisComplete(result)
            }

            toast({
              title: "Análisis completado",
              description: "El documento ha sido analizado correctamente.",
              variant: "default",
            })
          } else if (result.status === 'error') {
            setIsAnalyzing(false)
            const errorMessage = result.error instanceof Error ? result.error.message : 'Error desconocido'
            setAnalysisError(errorMessage)

            toast({
              title: "Error en el análisis",
              description: errorMessage,
              variant: "destructive",
            })
          }
        } catch (error) {
          console.error("Error al consultar el estado del análisis:", error)
        }
      }, 5000) // Consultar cada 5 segundos
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId)
      }
    }
  }, [analysisId, isAnalyzing, onAnalysisComplete])

  const loadAvailableModels = async () => {
    try {
      const response = await documentAnalysisClient.getAvailableModels()

      if (response && response.data) {
        setAvailableModels(response.data)

        // Seleccionar el primer modelo por defecto
        if (response.data.length > 0 && !selectedModel) {
          setSelectedModel(response.data[0].id)
        }
      }
    } catch (error) {
      console.error("Error al cargar los modelos disponibles:", error)
      toast({
        title: "Error al cargar modelos",
        description: "No se pudieron cargar los modelos disponibles. Verifica la conexión con LM Studio.",
        variant: "destructive",
      })
    }
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Validar tipo de archivo
    const fileExtension = "." + file.name.split(".").pop()?.toLowerCase()
    if (!allowedFileTypes.includes(fileExtension)) {
      toast({
        title: "Tipo de archivo no permitido",
        description: `Solo se permiten archivos: ${allowedFileTypes.join(", ")}`,
        variant: "destructive",
      })
      return
    }

    // Validar tamaño
    if (file.size > maxFileSize * 1024 * 1024) {
      toast({
        title: "Archivo demasiado grande",
        description: `El tamaño máximo permitido es ${maxFileSize}MB`,
        variant: "destructive",
      })
      return
    }

    setSelectedFile(file)
  }

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
  }

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()

    const file = e.dataTransfer.files?.[0]
    if (!file) return

    // Validar tipo de archivo
    const fileExtension = "." + file.name.split(".").pop()?.toLowerCase()
    if (!allowedFileTypes.includes(fileExtension)) {
      toast({
        title: "Tipo de archivo no permitido",
        description: `Solo se permiten archivos: ${allowedFileTypes.join(", ")}`,
        variant: "destructive",
      })
      return
    }

    // Validar tamaño
    if (file.size > maxFileSize * 1024 * 1024) {
      toast({
        title: "Archivo demasiado grande",
        description: `El tamaño máximo permitido es ${maxFileSize}MB`,
        variant: "destructive",
      })
      return
    }

    setSelectedFile(file)
  }

  const handleUpload = async () => {
    if (!selectedFile) return

    try {
      setIsUploading(true)
      setUploadProgress(10)

      // 1. Subir archivo a Storage
      const fileName = `${Date.now()}-${selectedFile.name}`
      const filePath = projectId
        ? `projects/${projectId}/${fileName}`
        : workOrderId
        ? `work_orders/${workOrderId}/${fileName}`
        : `documents/${fileName}`

      setUploadProgress(30)

      const { data: uploadData, error: uploadError } = await supabase.storage
        .from("documents")
        .upload(filePath, selectedFile, {
          cacheControl: "3600",
          upsert: false,
        })

      if (uploadError) {
        throw new Error(`Error al subir el archivo: ${uploadError.message}`)
      }

      setUploadProgress(60)

      // 2. Obtener URL pública
      const { data: urlData } = await supabase.storage
        .from("documents")
        .getPublicUrl(filePath)

      setUploadProgress(80)

      // 3. Crear registro en la base de datos
      const { data: userData } = await supabase.auth.getUser()

      const { data: documentData, error: documentError } = await supabase
        .from("documents")
        .insert({
          filename: selectedFile.name,
          file_path: filePath,
          file_url: urlData?.publicUrl,
          file_type: selectedFile.type,
          file_size: selectedFile.size,
          description: description,
          category: category,
          project_id: projectId || null,
          work_order_id: workOrderId || null,
          public_url: urlData?.publicUrl,
          uploaded_by: userData.user?.id,
        })
        .select()
        .single()

      if (documentError) {
        throw new Error(`Error al crear el registro: ${documentError.message}`)
      }

      setUploadProgress(100)

      // Notificar que la carga se completó
      if (onUploadComplete) {
        onUploadComplete(documentData)
      }

      // Si se seleccionó análisis con LM Studio, iniciar el análisis
      if (analyzeWithLMStudio && selectedModel) {
        await handleAnalyzeDocument(documentData)
      } else {
        // Limpiar el formulario
        setSelectedFile(null)
        setDescription("")
        setCategory("")
        setUploadProgress(0)

        toast({
          title: "Documento subido",
          description: "El documento se ha subido correctamente.",
          variant: "default",
        })
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido'
      toast({
        title: "Error al subir el documento",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsUploading(false)
    }
  }

  const handleAnalyzeDocument = async (documentData: unknown) => {
    try {
      setIsAnalyzing(true)
      setAnalysisError(null)
      setAnalysisResult(null)

      // Iniciar el análisis
      const response = await documentAnalysisClient.analyzeDocument({
        file: selectedFile!,
        model: selectedModel,
        userId: (await supabase.auth.getUser()).data.user?.id
      })

      setAnalysisId(response.analysis_id)

      toast({
        title: "Análisis iniciado",
        description: "El documento está siendo analizado. Esto puede tardar unos minutos.",
        variant: "default",
      })

      // Limpiar el formulario
      setSelectedFile(null)
      setDescription("")
      setCategory("")
      setUploadProgress(0)

    } catch (error: unknown) {
      setIsAnalyzing(false)
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido'
      setAnalysisError(errorMessage)

      toast({
        title: "Error al iniciar el análisis",
        description: errorMessage,
        variant: "destructive",
      })
    }
  }

  const handleCreateProject = async () => {
    if (!analysisResult) return

    try {
      const project = await documentAnalysisClient.createProjectFromAnalysis(analysisResult)

      toast({
        title: "Proyecto creado",
        description: "Se ha creado un nuevo proyecto a partir del análisis del documento.",
        variant: "default",
      })

      // Redirigir al proyecto creado
      window.location.href = `/dashboard/projects/${project.id}`
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido'
      toast({
        title: "Error al crear el proyecto",
        description: errorMessage,
        variant: "destructive",
      })
    }
  }

  return (
    <div className="space-y-4">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="upload">Subir Documento</TabsTrigger>
          <TabsTrigger value="results" disabled={!analysisResult}>Resultados</TabsTrigger>
        </TabsList>

        <TabsContent value="upload" className="space-y-4">
          <div
            className={`border-2 border-dashed rounded-lg p-6 text-center ${
              selectedFile ? "border-primary" : "border-muted-foreground/25"
            }`}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
          >
            {!selectedFile ? (
              <div className="flex flex-col items-center justify-center space-y-2">
                <Upload className="h-10 w-10 text-muted-foreground" />
                <h3 className="font-medium text-lg">Arrastra y suelta un archivo aquí</h3>
                <p className="text-sm text-muted-foreground">
                  o haz clic para seleccionar un archivo
                </p>
                <Button
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                >
                  Seleccionar archivo
                </Button>
                <input
                  ref={fileInputRef}
                  type="file"
                  className="hidden"
                  onChange={handleFileChange}
                  accept={allowedFileTypes.join(",")}
                />
                <p className="text-xs text-muted-foreground mt-2">
                  Tipos permitidos: {allowedFileTypes.join(", ")} | Tamaño máximo: {maxFileSize}MB
                </p>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center space-y-4">
                <div className="flex items-center space-x-2">
                  <File className="h-8 w-8 text-primary" />
                  <div className="text-left">
                    <p className="font-medium">{selectedFile.name}</p>
                    <p className="text-xs text-muted-foreground">
                      {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setSelectedFile(null)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </div>

          {selectedFile && (
            <Card>
              <CardContent className="pt-4 space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="description">Descripción</Label>
                  <Input
                    id="description"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    placeholder="Descripción del documento"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category">Categoría</Label>
                  <Input
                    id="category"
                    value={category}
                    onChange={(e) => setCategory(e.target.value)}
                    placeholder="Categoría del documento"
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="analyze">Analizar con LM Studio</Label>
                    <Switch
                      id="analyze"
                      checked={analyzeWithLMStudio}
                      onCheckedChange={setAnalyzeWithLMStudio}
                    />
                  </div>

                  {analyzeWithLMStudio && (
                    <div className="space-y-2 mt-2">
                      <Label htmlFor="model">Modelo</Label>
                      <Select value={selectedModel} onValueChange={setSelectedModel}>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecciona un modelo" />
                        </SelectTrigger>
                        <SelectContent>
                          {availableModels.length > 0 ? (
                            availableModels.map((model) => (
                              <SelectItem key={model.id} value={model.id}>
                                {model.id}
                              </SelectItem>
                            ))
                          ) : (
                            <SelectItem value="loading" disabled>
                              Cargando modelos...
                            </SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                </div>

                {isUploading && (
                  <div className="space-y-2">
                    <Progress value={uploadProgress} className="h-2" />
                    <p className="text-xs text-center text-muted-foreground">
                      Subiendo documento... {uploadProgress}%
                    </p>
                  </div>
                )}

                <div className="flex justify-end space-x-2">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setSelectedFile(null)
                      setDescription("")
                      setCategory("")
                    }}
                    disabled={isUploading}
                  >
                    Cancelar
                  </Button>
                  <Button
                    onClick={handleUpload}
                    disabled={isUploading || (analyzeWithLMStudio && !selectedModel)}
                  >
                    {isUploading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Subiendo...
                      </>
                    ) : (
                      <>
                        <Upload className="mr-2 h-4 w-4" />
                        Subir
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {isAnalyzing && (
            <Alert>
              <Loader2 className="h-4 w-4 animate-spin" />
              <AlertTitle>Analizando documento</AlertTitle>
              <AlertDescription>
                El documento está siendo analizado. Este proceso puede tardar unos minutos.
              </AlertDescription>
            </Alert>
          )}

          {analysisError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error en el análisis</AlertTitle>
              <AlertDescription>
                {analysisError}
              </AlertDescription>
            </Alert>
          )}
        </TabsContent>

        <TabsContent value="results">
          {analysisResult ? (
            <div className="space-y-4">
              <Alert variant="default">
                <Check className="h-4 w-4" />
                <AlertTitle>Análisis completado</AlertTitle>
                <AlertDescription>
                  El documento ha sido analizado correctamente.
                </AlertDescription>
              </Alert>

              <Card>
                <CardContent className="pt-4 space-y-4">
                  <div>
                    <h3 className="text-lg font-medium">Información del Proyecto</h3>
                    <div className="grid grid-cols-2 gap-4 mt-2">
                      <div>
                        <p className="text-sm font-medium">Nombre</p>
                        <p className="text-sm text-muted-foreground">{analysisResult.project_info.name}</p>
                      </div>

                      {analysisResult.project_info.start_date && (
                        <div>
                          <p className="text-sm font-medium">Fecha de inicio</p>
                          <p className="text-sm text-muted-foreground">{analysisResult.project_info.start_date}</p>
                        </div>
                      )}

                      {analysisResult.project_info.end_date && (
                        <div>
                          <p className="text-sm font-medium">Fecha de finalización</p>
                          <p className="text-sm text-muted-foreground">{analysisResult.project_info.end_date}</p>
                        </div>
                      )}

                      {analysisResult.project_info.budget && (
                        <div>
                          <p className="text-sm font-medium">Presupuesto</p>
                          <p className="text-sm text-muted-foreground">{analysisResult.project_info.budget}</p>
                        </div>
                      )}

                      {analysisResult.project_info.scope && (
                        <div className="col-span-2">
                          <p className="text-sm font-medium">Alcance</p>
                          <p className="text-sm text-muted-foreground">{analysisResult.project_info.scope}</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {analysisResult.project_info.deliverables && analysisResult.project_info.deliverables.length > 0 && (
                    <div>
                      <h3 className="text-lg font-medium">Entregables</h3>
                      <ul className="list-disc list-inside mt-2">
                        {analysisResult.project_info.deliverables.map((deliverable: string, index: number) => (
                          <li key={index} className="text-sm text-muted-foreground">{deliverable}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  <div>
                    <h3 className="text-lg font-medium">Confianza del Análisis</h3>
                    <div className="flex items-center mt-2">
                      <Progress value={analysisResult.project_info.confidence_score * 100} className="h-2 flex-1" />
                      <span className="ml-2 text-sm text-muted-foreground">
                        {Math.round(analysisResult.project_info.confidence_score * 100)}%
                      </span>
                    </div>
                  </div>

                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" onClick={() => setActiveTab("upload")}>
                      Volver
                    </Button>
                    <Button onClick={handleCreateProject}>
                      <FileText className="mr-2 h-4 w-4" />
                      Crear Proyecto
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            <div className="space-y-4">
              <Skeleton className="h-[200px] w-full" />
              <div className="space-y-2">
                <Skeleton className="h-4 w-[250px]" />
                <Skeleton className="h-4 w-[200px]" />
              </div>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
