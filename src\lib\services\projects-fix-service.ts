/**
 * @file Servicio para corregir problemas con proyectos
 * @description Proporciona funciones para verificar y corregir problemas con proyectos
 */

import { createClient } from '@/lib/supabase/client';
import { toast } from "@/hooks/use-toast";

/**
 * Servicio para corregir problemas con proyectos
 */
export class ProjectsFixService {
  private static instance: ProjectsFixService;
  private supabase = createClient();

  /**
   * Constructor privado para implementar el patrón Singleton
   */
  private constructor() {}

  /**
   * Obtiene la instancia única del servicio
   */
  public static getInstance(): ProjectsFixService {
    if (!ProjectsFixService.instance) {
      ProjectsFixService.instance = new ProjectsFixService();
    }
    return ProjectsFixService.instance;
  }

  /**
   * Verifica y corrige problemas con proyectos en la base de datos
   */
  public async verifyAndFixProjects(): Promise<boolean> {
    try {
      console.log('Verificando y corrigiendo proyectos...');

      // 1. Obtener todos los proyectos
      const { data: projects, error: projectsError } = await this.supabase
        .from('projects')
        .select('id, name, status');

      if (projectsError) {
        console.error('Error al obtener proyectos:', projectsError);
        return false;
      }

      if (!projects || projects.length === 0) {
        console.log('No hay proyectos para verificar');
        return true;
      }

      console.log(`Verificando ${projects.length} proyectos...`);

      // 2. Verificar y corregir campos obligatorios
      let projectsFixed = 0;
      for (const project of projects) {
        let needsUpdate = false;
        const updates: Record<string, unknown> = {};

        // Verificar status
        if (!project.status) {
          updates.status = 'pending';
          needsUpdate = true;
        }

        // Actualizar si es necesario
        if (needsUpdate) {
          const { error: updateError } = await this.supabase
            .from('projects')
            .update(updates)
            .eq('id', project.id);

          if (updateError) {
            console.error(`Error al actualizar proyecto ${project.id}:`, updateError);
          } else {
            console.log(`Proyecto ${project.id} actualizado correctamente`);
            projectsFixed++;
          }
        }
      }

      console.log(`Verificación completada. ${projectsFixed} proyectos corregidos.`);

      if (projectsFixed > 0) {
        toast({
          title: "Proyectos corregidos",
          description: `Se han corregido ${projectsFixed} proyectos con campos faltantes.`,
        });
      }

      return true;
    } catch (error) {
      console.error('Error inesperado al verificar proyectos:', error);
      return false;
    }
  }
}

// Exportar instancia única
export const projectsFixService = ProjectsFixService.getInstance();
