/**
 * @ai-file-description: "Supabase middleware utilities for SSR"
 * @ai-related-files: ["middleware.ts"]
 * @ai-owner: "Supabase Integration"
 */

import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'
import { type Database } from '@/lib/supabase/types'

/**
 * Updates the session in middleware
 *
 * @param request The Next.js request object
 * @returns The Next.js response object
 */
export async function updateSession(request: NextRequest) {
  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  })

  try {
    // Verify Supabase environment variables are set
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.error('Middleware - Missing Supabase environment variables')
      return response
    }

    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        cookies: {
          get(name: string) {
            return request.cookies.get(name)?.value
          },
          set(name: string, value: string, options: CookieOptions) {
            // If the cookie is updated, update the cookies for the request and response
            request.cookies.set(name, value)
            response = NextResponse.next({
              request: {
                headers: request.headers,
              },
            })
            response.cookies.set(name, value)
          },
          remove(name: string, options: CookieOptions) {
            // If the cookie is removed, update the cookies for the request and response
            request.cookies.delete(name)
            response = NextResponse.next({
              request: {
                headers: request.headers,
              },
            })
            response.cookies.delete(name)
          },
        },
      }
    )

    // Obtener la sesión actual sin intentar refrescarla automáticamente
    const { data } = await supabase.auth.getSession()
    const initialSession = data.session

    // Registrar información de la sesión inicial
    console.log('Middleware - Sesión inicial:', initialSession ? 'Activa' : 'No activa')
    if (initialSession) {
      console.log('Middleware - Usuario:', initialSession.user.email)

      // Verificar si la sesión tiene fecha de expiración
      if (initialSession.expires_at) {
        console.log('Middleware - Expira:', new Date(initialSession.expires_at * 1000).toLocaleString())

        // Verificar si la sesión necesita ser refrescada (solo si está a menos de 10 minutos de expirar)
        const expiresAt = initialSession.expires_at * 1000
        const now = Date.now()
        const timeUntilExpiry = expiresAt - now

        // Solo refrescar si está a menos de 10 minutos de expirar
        const shouldRefresh = timeUntilExpiry > 0 && timeUntilExpiry < 10 * 60 * 1000

        if (shouldRefresh) {
          console.log(`Middleware - Sesión expira en ${Math.round(timeUntilExpiry/1000/60)} minutos, refrescando`)

          try {
            const refreshResult = await supabase.auth.refreshSession()
            const refreshedSession = refreshResult.data.session
            const error = refreshResult.error

            if (error) {
              const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

              if (error.message.includes('too many requests') || errorMessage.includes('rate limit')) {
                console.warn('Middleware - Límite de tasa alcanzado, omitiendo refresco de sesión')
                // No hacer nada, seguir usando la sesión actual
              } else {
                const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

                console.error('Middleware - Error al refrescar sesión:', errorMessage)
              }
            } else if (refreshedSession) {
              console.log('Middleware - Sesión refrescada correctamente')
            }
          } catch (refreshError: unknown) {
            const errorMessage = refreshError instanceof Error
              ? refreshError.message
              : 'Unknown refresh error';
            console.error('Middleware - Error al refrescar sesión:', errorMessage)
          }
        } else {
          console.log(`Middleware - Sesión válida por ${Math.round(timeUntilExpiry/1000/60)} minutos más, no es necesario refrescar`)
        }
      }
    }

    // Registrar información de cookies para depuración
    if (process.env.NODE_ENV === 'development') {
      const cookiesList = request.cookies.getAll()
      console.log('Cookies disponibles:', cookiesList.map(c => c.name))
    }

    // Permitir siempre el acceso a la página de callback de autenticación
    if (request.nextUrl.pathname === '/auth/callback') {
      return response
    }

    // Registrar información de depuración
    console.log('Middleware - URL:', request.nextUrl.pathname);
    console.log('Middleware - Sesión:', initialSession ? 'Activa' : 'No activa');

    // Si el usuario no está autenticado y trata de acceder al dashboard, redirigir a login
    if (!initialSession && request.nextUrl.pathname.startsWith('/dashboard')) {
      console.log('Redirigiendo a login desde:', request.nextUrl.pathname);
      const redirectUrl = new URL('/auth/login', request.url)
      redirectUrl.searchParams.set('from', request.nextUrl.pathname)
      const redirectResponse = NextResponse.redirect(redirectUrl)

      // Copiar todas las cookies de la respuesta original a la respuesta de redirección
      response.cookies.getAll().forEach(cookie => {
        redirectResponse.cookies.set(cookie)
      })

      return redirectResponse
    }

    // Si el usuario está autenticado y trata de acceder a auth (excepto callback), redirigir al dashboard
    if (initialSession && request.nextUrl.pathname.startsWith('/auth') && !request.nextUrl.pathname.startsWith('/auth/callback')) {
      console.log('Usuario autenticado intentando acceder a auth, redirigiendo al dashboard');
      const redirectResponse = NextResponse.redirect(new URL('/dashboard', request.url))

      // Copiar todas las cookies de la respuesta original a la respuesta de redirección
      response.cookies.getAll().forEach(cookie => {
        redirectResponse.cookies.set(cookie)
      })

      return redirectResponse
    }

    // Si el usuario accede a la raíz, redirigir según autenticación
    if (request.nextUrl.pathname === '/') {
      if (initialSession) {
        console.log('Usuario autenticado en raíz, redirigiendo al dashboard');
        const redirectResponse = NextResponse.redirect(new URL('/dashboard', request.url))

        // Copiar todas las cookies de la respuesta original a la respuesta de redirección
        response.cookies.getAll().forEach(cookie => {
          redirectResponse.cookies.set(cookie)
        })

        return redirectResponse
      } else {
        console.log('Usuario no autenticado en raíz, redirigiendo a login');
        const redirectResponse = NextResponse.redirect(new URL('/auth/login', request.url))

        // Copiar todas las cookies de la respuesta original a la respuesta de redirección
        response.cookies.getAll().forEach(cookie => {
          redirectResponse.cookies.set(cookie)
        })

        return redirectResponse
      }
    }

    return response
  } catch (error: unknown) {
    console.error('Middleware - Unhandled error:', error)

    const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

    console.error('Middleware - Error details:', errorMessage);
    return response
  }
}
