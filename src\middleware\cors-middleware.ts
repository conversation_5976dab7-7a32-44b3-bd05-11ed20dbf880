/**
 * Middleware CORS para permitir solicitudes desde dominios específicos
 */
import { NextResponse, type NextRequest } from 'next/server'

// Dominios permitidos para CORS
const ALLOWED_ORIGINS = [
  'https://admin4humans.vercel.app',
  'https://document-analysis-service.vercel.app',
  'https://pdf-extractor-service.vercel.app',
  'https://lmstudio-connector-service.vercel.app',
]

// En desarrollo, permitir localhost
if (process.env.NODE_ENV !== 'production') {
  ALLOWED_ORIGINS.push(
    'http://localhost:3000', 
    'http://localhost:8000', 
    'http://localhost:8001', 
    'http://localhost:8002', 
    'http://localhost:8003'
  )
}

/**
 * Middleware CORS
 */
export function corsMiddleware(request: NextRequest, response: NextResponse) {
  const { pathname } = request.nextUrl
  
  // Manejar CORS solo para solicitudes API
  if (pathname.startsWith('/api')) {
    const origin = request.headers.get('origin')

    // Verificar si el origen está permitido
    if (origin && (ALLOWED_ORIGINS.includes(origin) || process.env.NODE_ENV !== 'production')) {
      // Configurar cabeceras CORS
      response.headers.set('Access-Control-Allow-Origin', origin)
      response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
      response.headers.set('Access-Control-Allow-Headers', 'X-Requested-With, Content-Type, Authorization, X-Client-Info, apikey')
      response.headers.set('Access-Control-Allow-Credentials', 'true')

      // Para solicitudes OPTIONS (preflight), devolver 200 OK
      if (request.method === 'OPTIONS') {
        return new NextResponse(null, {
          status: 200,
          headers: response.headers
        })
      }
    }
  }
  
  return response
}
