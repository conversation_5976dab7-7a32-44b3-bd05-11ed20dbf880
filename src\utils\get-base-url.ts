/**
 * @ai-file-description: "Utility function to get the base URL for the application"
 * @ai-related-files: ["../lib/utils.ts"]
 * @ai-owner: "Infrastructure"
 */

/**
 * Gets the base URL for the application
 * Works in both client and server environments
 * 
 * @returns The base URL of the application
 */
export function getBaseUrl(): string {
  // Client-side: use window.location.origin
  if (typeof window !== 'undefined') {
    return window.location.origin
  }

  // Server-side: use environment variable or fallback to localhost
  return process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'
}

// Default export for convenience
export default getBaseUrl
