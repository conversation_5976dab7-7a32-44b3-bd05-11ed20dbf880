"use client"

import { useState, useRef } from "react"
import { Upload, File, X, Loader2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent } from "@/components/ui/card"
import { useToast } from "@/components/ui/use-toast"
import { createClient } from "@/lib/supabase/client"
import { useQueryClient } from "@tanstack/react-query"
import type { Database } from "@/types/database.types"

type DocumentInsert = Database['public']['Tables']['documents']['Insert']
type StorageResponse = { data: { path: string } | null, error: Error | null }

interface UploadProgress {
  loaded: number
  total: number
}

interface DocumentUploadProps {
  projectId?: string
  workOrderId?: string
  onUploadComplete?: (fileData: unknown) => void
  allowedFileTypes?: string[]
  maxFileSize?: number // en MB
}

export function DocumentUpload({
  projectId,
  workOrderId,
  onUploadComplete,
  allowedFileTypes = [".pdf", ".doc", ".docx", ".xls", ".xlsx", ".jpg", ".jpeg", ".png", ".dwg", ".dxf"],
  maxFileSize = 10, // 10MB por defecto
}: DocumentUploadProps) {
  const { toast } = useToast()
  const queryClient = useQueryClient()
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [description, setDescription] = useState("")
  const [category, setCategory] = useState("")
  const fileInputRef = useRef<HTMLInputElement>(null)
  const supabase = createClient()

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0]

      // Check file size
      if (file.size > maxFileSize * 1024 * 1024) {
        toast({
          title: "Error",
          description: `The file exceeds the maximum allowed size (${maxFileSize}MB)`,
          variant: "destructive",
        })
        return
      }

      // Check file type
      const fileExtension = `.${file.name.split('.').pop()?.toLowerCase()}`
      if (!allowedFileTypes.includes(fileExtension)) {
        toast({
          title: "Error",
          description: `File type not allowed. Allowed types: ${allowedFileTypes.join(", ")}`,
          variant: "destructive",
        })
        return
      }

      setSelectedFile(file)
    }
  }

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
  }

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0]

      // Check file size
      if (file.size > maxFileSize * 1024 * 1024) {
        toast({
          title: "Error",
          description: `The file exceeds the maximum allowed size (${maxFileSize}MB)`,
          variant: "destructive",
        })
        return
      }

      // Check file type
      const fileExtension = `.${file.name.split('.').pop()?.toLowerCase()}`
      if (!allowedFileTypes.includes(fileExtension)) {
        toast({
          title: "Error",
          description: `File type not allowed. Allowed types: ${allowedFileTypes.join(", ")}`,
          variant: "destructive",
        })
        return
      }

      setSelectedFile(file)
    }
  }

  const clearSelectedFile = () => {
    setSelectedFile(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  const uploadFile = async () => {
    if (!selectedFile) return

    setIsUploading(true)
    setUploadProgress(0)

    try {
      // Generate a unique name for the file
      const fileExtension = selectedFile.name.split('.').pop()
      const fileName = `${Date.now()}_${Math.random().toString(36).substring(2, 15)}.${fileExtension}`
      const filePath = `documents/${fileName}`

      // Upload file to Supabase Storage with progress tracking
      const { data: storageData, error: storageError } = await new Promise<StorageResponse>((resolve, reject) => {
        const upload = supabase.storage
          .from('documents')
          .upload(filePath, selectedFile, {
            cacheControl: '3600',
            upsert: false
          })

        // Simulate progress since Supabase doesn't provide upload progress
        const interval = setInterval(() => {
          setUploadProgress(prev => Math.min(prev + 10, 90))
        }, 500)

        upload.then(result => {
          clearInterval(interval)
          setUploadProgress(100)
          resolve(result)
        }).catch(error => {
          clearInterval(interval)
          setUploadProgress(0)
          reject(error)
        })
      })

      if (storageError) throw storageError

      // Get public URL of the file
      const { data: publicUrlData } = await supabase.storage
        .from('documents')
        .getPublicUrl(filePath)

      // Insert document metadata into database
      // Get current user
      const { data: { user } } = await supabase.auth.getUser()

      // Prepare document data
      const documentInsert: DocumentInsert = {
        filename: selectedFile.name,
        file_path: filePath,
        file_size: selectedFile.size,
        file_type: selectedFile.type,
        description: description || null,
        category: category || null,
        project_id: projectId || null,
        work_order_id: workOrderId || null,
        file_url: publicUrlData.publicUrl,
        uploaded_by: user?.id || null
      }

      const { data: documentData, error: dbError } = await supabase
        .from('documents')
        .insert([documentInsert])
        .select()

      if (dbError) throw dbError

      if (documentData) {
        // Invalidate documents query to trigger refresh
        queryClient.invalidateQueries({ queryKey: ['documents'] })

        toast({
          title: "Document uploaded",
          description: "The document has been uploaded successfully",
        })
      }

      // Clear form
      setSelectedFile(null)
      setDescription("")
      setCategory("")
      if (fileInputRef.current) {
        fileInputRef.current.value = ""
      }

      // Notify parent component
      if (onUploadComplete && documentData) {
        onUploadComplete(documentData[0])
      }
    } catch (error: unknown) {
      console.error("Error uploading document:", error)
      const errorMessage = error instanceof Error ? error.message : "An error occurred while uploading the document";

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsUploading(false)
      setUploadProgress(0)
    }
  }

  return (
    <div className="space-y-4">
      <div
        className={`border-2 border-dashed rounded-lg p-6 text-center ${
          selectedFile ? "border-primary" : "border-muted-foreground/25"
        }`}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
      >
        {!selectedFile ? (
          <div className="flex flex-col items-center justify-center space-y-2">
            <Upload className="h-10 w-10 text-muted-foreground" />
            <h3 className="font-medium text-lg">Drag and drop a file here</h3>
            <p className="text-sm text-muted-foreground">
              or click to select a file
            </p>
            <Button
              variant="outline"
              onClick={() => fileInputRef.current?.click()}
            >
              Select file
            </Button>
            <input
              ref={fileInputRef}
              type="file"
              className="hidden"
              onChange={handleFileChange}
              accept={allowedFileTypes.join(",")}
            />
            <p className="text-xs text-muted-foreground mt-2">
              Allowed types: {allowedFileTypes.join(", ")} | Maximum size: {maxFileSize}MB
            </p>
          </div>
        ) : (
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <File className="h-8 w-8 text-primary" />
                  <div>
                    <p className="font-medium">{selectedFile.name}</p>
                    <p className="text-sm text-muted-foreground">
                      {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={clearSelectedFile}
                  disabled={isUploading}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {selectedFile && (
        <div className="space-y-4">
          <div className="grid gap-2">
            <Label htmlFor="description">Description</Label>
            <Input
              id="description"
              placeholder="Document description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              disabled={isUploading}
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="category">Category (optional)</Label>
            <Input
              id="category"
              placeholder="Document category"
              value={category}
              onChange={(e) => setCategory(e.target.value)}
              disabled={isUploading}
            />
          </div>

          <Button
            className="w-full"
            onClick={uploadFile}
            disabled={isUploading}
          >
            {isUploading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Uploading... {uploadProgress}%
              </>
            ) : (
              "Upload Document"
            )}
          </Button>
        </div>
      )}
    </div>
  )
}
