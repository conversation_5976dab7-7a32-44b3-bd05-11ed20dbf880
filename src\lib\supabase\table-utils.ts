import { createClient } from './client';

/**
 * Verifica si una tabla existe en la base de datos
 * @param tableName Nombre de la tabla a verificar
 * @returns Promise<boolean> true si la tabla existe, false en caso contrario
 */
export async function tableExists(tableName: string): Promise<boolean> {
  try {
    const supabase = createClient();

    // Intentar obtener un registro de la tabla
    const { data, error } = await supabase
      .from(tableName)
      .select('*')
      .limit(1);

    // Si hay un error 404, la tabla no existe
    if (error && error.code === '404') {
      console.warn(`Tabla ${tableName} no encontrada`);
      return false;
    }

    // Si hay otro tipo de error, asumimos que la tabla existe pero hay otro problema
    if (error) {
      console.error(`Error al verificar tabla ${tableName}:`, error);
      // Aún así devolvemos true porque el error podría ser de permisos, no de existencia
      return true;
    }

    return true;
  } catch (err) {
    console.error(`Excepción al verificar tabla ${tableName}:`, err);
    return false;
  }
}

/**
 * Verifica si una columna existe en una tabla
 * @param tableName Nombre de la tabla
 * @param columnName Nombre de la columna
 * @returns Promise<boolean> true si la columna existe, false en caso contrario
 */
export async function columnExists(tableName: string, columnName: string): Promise<boolean> {
  try {
    const supabase = createClient();

    // Intentar seleccionar específicamente la columna
    const { data, error } = await supabase
      .from(tableName)
      .select(columnName)
      .limit(1);

    const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

    if (error && (errorMessage.includes(`column "${columnName}" does not exist`) ||
        errorMessage.includes(`column ${columnName} does not exist`))) {
      console.warn(`Columna ${columnName} no encontrada en tabla ${tableName}`);
      return false;
    }

    if (error) {
      console.error(`Error al verificar columna ${columnName} en tabla ${tableName}:`, error);
      // Asumimos que la columna existe pero hay otro problema
      return true;
    }

    return true;
  } catch (err) {
    console.error(`Excepción al verificar columna ${columnName} en tabla ${tableName}:`, err);
    return false;
  }
}

/**
 * Verifica si una relación existe entre dos tablas
 * @param tableName Nombre de la tabla principal
 * @param foreignKey Nombre de la clave foránea
 * @param referencedTable Nombre de la tabla referenciada
 * @returns Promise<boolean> true si la relación existe, false en caso contrario
 */
export async function relationExists(
  tableName: string,
  foreignKey: string,
  referencedTable: string
): Promise<boolean> {
  try {
    const supabase = createClient();

    // Intentar seleccionar con la relación
    const { data, error } = await supabase
      .from(tableName)
      .select(`${referencedTable}:${foreignKey}(id)`)
      .limit(1);

    const errorMessage = error instanceof Error ? error.message : 'Error desconocido';


    if (error && errorMessage.includes('foreign key relationship')) {
      console.warn(`Relación entre ${tableName}.${foreignKey} y ${referencedTable} no encontrada`);
      return false;
    }

    if (error) {
      console.error(`Error al verificar relación entre ${tableName}.${foreignKey} y ${referencedTable}:`, error);
      // Asumimos que la relación existe pero hay otro problema
      return true;
    }

    return true;
  } catch (err) {
    console.error(`Excepción al verificar relación entre ${tableName}.${foreignKey} y ${referencedTable}:`, err);
    return false;
  }
}

/**
 * Realiza una consulta segura que verifica primero si la tabla existe
 * @param tableName Nombre de la tabla
 * @param query Función que realiza la consulta
 * @returns Promise con el resultado de la consulta o un valor por defecto
 */
export async function safeQuery<T>(
  tableName: string,
  query: () => Promise<{ data: T | null, error: unknown }>,
  defaultValue: T
): Promise<T> {
  try {
    // Verificar si la tabla existe
    const exists = await tableExists(tableName);

    if (!exists) {
      console.warn(`Tabla ${tableName} no existe, devolviendo valor por defecto`);
      return defaultValue;
    }

    // Realizar la consulta
    const { data, error } = await query();

    if (error) {
      console.error(`Error en consulta a ${tableName}:`, error);
      return defaultValue;
    }

    return (data as T) || defaultValue;
  } catch (err) {
    console.error(`Excepción en consulta a ${tableName}:`, err);
    return defaultValue;
  }
}
