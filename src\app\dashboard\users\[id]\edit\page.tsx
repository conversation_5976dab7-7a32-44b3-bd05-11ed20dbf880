"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { ArrowLeft } from "lucide-react"
import { But<PERSON> } from "@/components/shared/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/shared/ui/card"
import { UserForm } from "@/components/features/users/user-form"
import { createClient } from "@/lib/supabase/client"
import { getBaseUrl } from "@/lib/utils"
import { toast } from "@/components/shared/ui/use-toast"
import Link from "next/link"

interface EditUserPageProps {
  params: {
    id: string
  }
}

export default function EditUserPage({ params }: EditUserPageProps) {
  const [user, setUser] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()
  const supabase = createClient()

  useEffect(() => {
    const fetchUser = async () => {
      setIsLoading(true)
      setError(null)

      try {
        // Obtener usuario a través de la API
        const response = await fetch(`${getBaseUrl()}/api/users/${params.id}`, { cache: 'no-store' })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Error al obtener el usuario')
        }

        const userData = await response.json()

        setUser(userData)
      } catch (error: unknown) {
        console.error("Error al cargar el usuario:", error)
        const errorMessage = error instanceof Error ? error.message : "Ha ocurrido un error al cargar el usuario";

        setError(errorMessage)
      } finally {
        setIsLoading(false)
      }
    }

    fetchUser()
  }, [params.id, supabase])

  const handleSubmit = async (data: unknown) => {
    setIsSaving(true)
    try {
      // Actualizar usuario a través de la API
      const response = await fetch(`${getBaseUrl()}/api/users/${params.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          full_name: data.full_name,
          role: data.role,
          status: data.status
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Error al actualizar el usuario')
      }

      toast({
        title: "Usuario actualizado",
        description: "El usuario se ha actualizado correctamente",
      })

      // Redirigir a la página de detalle del usuario
      router.push(`/dashboard/users/${params.id}`)
      router.refresh()
    } catch (error: unknown) {
      console.error("Error al actualizar el usuario:", error)
      const errorMessage = error instanceof Error ? error.message : "Ha ocurrido un error al actualizar el usuario";

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex h-[500px] items-center justify-center">
        <div className="flex flex-col items-center">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          <p className="mt-2 text-sm text-muted-foreground">Cargando usuario...</p>
        </div>
      </div>
    )
  }

  if (error || !user) {
    return (
      <div className="flex h-[500px] flex-col items-center justify-center">
        <p className="text-center text-muted-foreground">{error || "Usuario no encontrado"}</p>
        <Button
          variant="outline"
          className="mt-4"
          asChild
        >
          <Link href="/dashboard/users">
            Volver a usuarios
          </Link>
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <Button variant="outline" size="icon" asChild>
          <Link href={`/dashboard/users/${params.id}`}>
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <h2 className="text-3xl font-bold tracking-tight">Editar Usuario</h2>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Editar usuario</CardTitle>
          <CardDescription>
            Actualiza la información del usuario.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <UserForm
            initialData={user}
            onSubmit={handleSubmit}
            isLoading={isSaving}
            isEditing={true}
          />
        </CardContent>
      </Card>
    </div>
  )
}
