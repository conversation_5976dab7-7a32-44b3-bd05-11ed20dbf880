/**
 * @file Type definitions for the Technical Service Management module
 * @description Defines interfaces for service requests, customer equipment, maintenance, and related entities
 */

import { Database } from './database.types';

// Service Request Status Types
export type ServiceRequestStatus = 'pending' | 'assigned' | 'in_progress' | 'on_hold' | 'resolved' | 'closed' | 'cancelled';

// Service Request Priority Types
export type ServiceRequestPriority = 'low' | 'medium' | 'high' | 'critical';

// Service Request Source Types
export type ServiceRequestSource = 'email' | 'phone' | 'web' | 'app' | 'in_person' | 'other';

// Service Activity Types
export type ServiceActivityType = 'diagnosis' | 'repair' | 'installation' | 'maintenance' | 'inspection' | 'training' | 'other';

// Service Activity Status Types
export type ServiceActivityStatus = 'pending' | 'in_progress' | 'completed' | 'cancelled';

// Maintenance Types
export type MaintenanceType = 'preventive' | 'corrective' | 'predictive' | 'condition-based';

// Maintenance Frequency Types
export type MaintenanceFrequency = 'daily' | 'weekly' | 'biweekly' | 'monthly' | 'quarterly' | 'biannual' | 'annual' | 'custom';

// Equipment Status Types
export type EquipmentStatus = 'active' | 'inactive' | 'under_repair' | 'decommissioned' | 'in_storage';

// Checklist Item Types
export type ChecklistItemType = 'checkbox' | 'text' | 'number' | 'photo' | 'signature' | 'select';

// Service Request Interface
export interface ServiceRequest {
  id: string;
  title: string;
  description?: string;
  client_id?: string;
  status: ServiceRequestStatus;
  priority: ServiceRequestPriority;
  source?: ServiceRequestSource;
  created_by?: string;
  assigned_to?: string;
  due_date?: string;
  created_at: string;
  updated_at: string;
  resolution_notes?: string;
  resolution_date?: string;
  work_order_id?: string;
  location?: string;
  estimated_hours?: number;
  actual_hours?: number;
  is_billable: boolean;
  external_reference?: string;
  
  // Joined fields
  client?: {
    id: string;
    name: string;
    email?: string;
    phone?: string;
  };
  assigned_user?: {
    id: string;
    email: string;
    full_name?: string;
  };
  work_order?: {
    id: string;
    title: string;
    status?: string;
  };
}

// Customer Equipment Interface
export interface CustomerEquipment {
  id: string;
  name: string;
  model?: string;
  serial_number?: string;
  client_id: string;
  location?: string;
  installation_date?: string;
  warranty_start_date?: string;
  warranty_end_date?: string;
  status: EquipmentStatus;
  notes?: string;
  created_at: string;
  updated_at: string;
  created_by?: string;
  inventory_item_id?: string;
  manufacturer?: string;
  purchase_date?: string;
  purchase_price?: number;
  expected_lifetime_months?: number;
  last_service_date?: string;
  qr_code?: string;
  image_url?: string;
  
  // Joined fields
  client?: {
    id: string;
    name: string;
    email?: string;
    phone?: string;
  };
  inventory_item?: {
    id: string;
    name: string;
    description?: string;
  };
}

// Maintenance Schedule Interface
export interface MaintenanceSchedule {
  id: string;
  equipment_id: string;
  maintenance_type: MaintenanceType;
  frequency?: MaintenanceFrequency;
  last_maintenance_date?: string;
  next_maintenance_date?: string;
  description?: string;
  created_at: string;
  updated_at: string;
  created_by?: string;
  is_active: boolean;
  estimated_duration_hours?: number;
  checklist_template_id?: string;
  notification_days_before?: number;
  repeat_count?: number;
  
  // Joined fields
  equipment?: CustomerEquipment;
}

// Service Activity Interface
export interface ServiceActivity {
  id: string;
  service_request_id: string;
  equipment_id?: string;
  activity_type: ServiceActivityType;
  description?: string;
  start_time?: string;
  end_time?: string;
  technician_id?: string;
  status: ServiceActivityStatus;
  notes?: string;
  created_at: string;
  updated_at: string;
  location_coordinates?: string;
  travel_time_minutes?: number;
  is_remote: boolean;
  diagnostic_results?: string;
  resolution_code?: string;
  follow_up_required: boolean;
  
  // Joined fields
  service_request?: ServiceRequest;
  equipment?: CustomerEquipment;
  technician?: {
    id: string;
    email: string;
    full_name?: string;
  };
}

// Service Parts Used Interface
export interface ServicePartsUsed {
  id: string;
  service_activity_id: string;
  inventory_item_id?: string;
  quantity: number;
  unit_cost?: number;
  created_at: string;
  updated_at: string;
  is_billable: boolean;
  is_warranty_covered: boolean;
  notes?: string;
  batch_number?: string;
  source_location?: string;
  
  // Joined fields
  inventory_item?: {
    id: string;
    name: string;
    description?: string;
    unit?: string;
    unit_cost?: number;
  };
  service_activity?: ServiceActivity;
}

// Service Signature Interface
export interface ServiceSignature {
  id: string;
  service_activity_id: string;
  signature_data: string;
  signed_by: string;
  signed_at: string;
  client_id?: string;
  ip_address?: string;
  device_info?: string;
  geo_location?: string;
  signature_type: string;
  
  // Joined fields
  service_activity?: ServiceActivity;
  client?: {
    id: string;
    name: string;
  };
}

// Service Attachment Interface
export interface ServiceAttachment {
  id: string;
  service_request_id?: string;
  service_activity_id?: string;
  file_name: string;
  file_type?: string;
  file_size?: number;
  file_url: string;
  uploaded_by?: string;
  uploaded_at: string;
  description?: string;
  is_before_photo?: boolean;
  is_after_photo?: boolean;
  
  // Joined fields
  uploader?: {
    id: string;
    email: string;
    full_name?: string;
  };
}

// Service Checklist Interface
export interface ServiceChecklist {
  id: string;
  name: string;
  description?: string;
  created_by?: string;
  created_at: string;
  updated_at: string;
  is_active: boolean;
  equipment_type?: string;
  maintenance_type?: MaintenanceType;
  
  // Joined fields
  items?: ServiceChecklistItem[];
}

// Service Checklist Item Interface
export interface ServiceChecklistItem {
  id: string;
  checklist_id: string;
  description: string;
  order_number?: number;
  is_required: boolean;
  item_type: ChecklistItemType;
  created_at: string;
  updated_at: string;
}

// Service Checklist Response Interface
export interface ServiceChecklistResponse {
  id: string;
  service_activity_id: string;
  checklist_item_id: string;
  response: string;
  completed_by?: string;
  completed_at: string;
  notes?: string;
  attachment_url?: string;
  
  // Joined fields
  checklist_item?: ServiceChecklistItem;
  completed_by_user?: {
    id: string;
    email: string;
    full_name?: string;
  };
}

// Service Request Creation Input
export interface CreateServiceRequestInput {
  title: string;
  description?: string;
  client_id?: string;
  priority: ServiceRequestPriority;
  source?: ServiceRequestSource;
  assigned_to?: string;
  due_date?: string;
  location?: string;
  estimated_hours?: number;
  is_billable?: boolean;
  external_reference?: string;
  equipment_id?: string;
}

// Customer Equipment Creation Input
export interface CreateCustomerEquipmentInput {
  name: string;
  model?: string;
  serial_number?: string;
  client_id: string;
  location?: string;
  installation_date?: string;
  warranty_start_date?: string;
  warranty_end_date?: string;
  status?: EquipmentStatus;
  notes?: string;
  inventory_item_id?: string;
  manufacturer?: string;
  purchase_date?: string;
  purchase_price?: number;
  expected_lifetime_months?: number;
  image_url?: string;
}

// Maintenance Schedule Creation Input
export interface CreateMaintenanceScheduleInput {
  equipment_id: string;
  maintenance_type: MaintenanceType;
  frequency?: MaintenanceFrequency;
  next_maintenance_date?: string;
  description?: string;
  is_active?: boolean;
  estimated_duration_hours?: number;
  checklist_template_id?: string;
  notification_days_before?: number;
}

// Service Activity Creation Input
export interface CreateServiceActivityInput {
  service_request_id: string;
  equipment_id?: string;
  activity_type: ServiceActivityType;
  description?: string;
  start_time?: string;
  end_time?: string;
  technician_id?: string;
  notes?: string;
  location_coordinates?: string;
  travel_time_minutes?: number;
  is_remote?: boolean;
  diagnostic_results?: string;
  resolution_code?: string;
  follow_up_required?: boolean;
}
