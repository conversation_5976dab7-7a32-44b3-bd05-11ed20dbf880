import { getProjectsWithRelations, getBasicProjects } from '@/lib/supabase/projects-client'
import { validateSupabaseConfig } from '@/lib/supabase/config'
import { createClient } from '@/lib/supabase/client'
import { supabaseAdmin } from '@/lib/supabase/admin-client'

export async function getServerSideProjects() {
  try {
    // Validar la configuración de Supabase
    const isConfigValid = validateSupabaseConfig()

    console.log('Supabase config validation result for projects:', isConfigValid)

    if (!isConfigValid) {
      console.error('Invalid Supabase configuration for projects')
      return [] // Devolver array vacío en lugar de datos mock
    }

    // Verificar si hay una sesión activa
    const supabase = createClient()
    const { data: sessionData } = await supabase.auth.getSession()

    if (!sessionData?.session) {
      console.log('No active session found, returning empty projects array')
      return [] // Devolver array vacío en lugar de datos mock
    }

    console.log('Active session found for user:', sessionData.session.user.email)

    // Intentar obtener proyectos con todas las relaciones
    console.log('Attempting to get projects with relations')
    try {
      const projectsWithRelations = await getProjectsWithRelations()

      if (projectsWithRelations && projectsWithRelations.length > 0) {
        console.log('Successfully retrieved projects with relations:', projectsWithRelations.length)
        const { transformProjects } = await import('@/lib/supabase/projects-client');
        return transformProjects(projectsWithRelations);
      }
    } catch (error) {
      console.error('Error getting projects with relations:', error)
    }

    // Si no se pudieron obtener proyectos con relaciones, intentar con proyectos básicos
    console.log('Attempting to get basic projects')
    try {
      const basicProjects = await getBasicProjects()

      if (basicProjects && basicProjects.length > 0) {
        console.log('Successfully retrieved basic projects:', basicProjects.length)
        const { transformProjects } = await import('@/lib/supabase/projects-client');
        return transformProjects(basicProjects);
      }
    } catch (error) {
      console.error('Error getting basic projects:', error)
    }

    // Intentar obtener proyectos directamente con el cliente actual
    console.log('Attempting direct query with current client')
    try {
      const { data: directProjects, error: directError } = await supabase
        .from('projects')
        .select(`
          id, name, description, status, created_at, updated_at,
          owner_id, progress_percent, client_id, budget, currency,
          start_date, end_date
        `)
        .limit(10)
        .order('created_at', { ascending: false })

      if (!directError && directProjects && directProjects.length > 0) {
        console.log('Successfully retrieved projects with direct query:', directProjects.length)
        const { transformProjects } = await import('@/lib/supabase/projects-client');
        return transformProjects(directProjects);
      }

      if (directError) {
        console.error('Error with direct query:', directError.message)
      }
    } catch (directQueryError) {
      console.error('Exception with direct query:', directQueryError)
    }

    console.log('Failed to retrieve projects from all sources')
    console.log('This could be due to RLS policies or authentication issues')

    // Intentar verificar si la tabla existe y si hay proyectos en general
    try {
      console.log('Checking if projects table exists and has data')

      // Intentar con el cliente admin para evitar restricciones de RLS
      const { data: tableInfo, error: tableError } = await supabaseAdmin.rpc('diagnose_projects')

      if (tableError) {
        console.error('Error calling diagnose_projects function:', tableError.message)
      } else {
        console.log('Diagnostic results:', tableInfo)

        // Si hay proyectos pero el usuario no puede verlos, es un problema de permisos
        if (tableInfo.total_projects > 0 && tableInfo.user_projects === 0) {
          console.log('Projects exist but user has no access. This is likely an RLS issue.')
          console.log('User ID:', tableInfo.user_id)
          console.log('Total projects:', tableInfo.total_projects)
          console.log('RLS enabled:', tableInfo.rls_enabled)
        } else if (tableInfo.total_projects === 0) {
          console.log('No projects exist in the database at all.')

          // Intentar crear proyectos de demostración automáticamente
          try {
            console.log('Attempting to create demo projects automatically')
            const { data: demoResult, error: demoError } = await supabaseAdmin.rpc('create_demo_projects')

            if (demoError) {
              console.error('Error creating demo projects:', demoError.message)
            } else {
              console.log('Demo projects creation result:', demoResult)

              if (demoResult.success && demoResult.count > 0) {
                // Intentar obtener los proyectos recién creados
                const { data: newProjects, error: newError } = await supabase
                  .from('projects')
                  .select(`
                    id, name, description, status, created_at, updated_at,
                    owner_id, progress_percent, client_id, budget, currency,
                    start_date, end_date
                  `)
                  .order('created_at', { ascending: false })

                if (!newError && newProjects && newProjects.length > 0) {
                  console.log('Successfully retrieved newly created demo projects:', newProjects.length)
                  const { transformProjects } = await import('@/lib/supabase/projects-client');
                  return transformProjects(newProjects);
                }
              }
            }
          } catch (demoError) {
            console.error('Exception creating demo projects:', demoError)
          }
        }
      }
    } catch (diagnosticError) {
      console.error('Error during diagnostic checks:', diagnosticError)
    }

    // Devolver un array vacío en lugar de datos mock
    return []
  } catch (error) {
    console.error('Unexpected error in getServerSideProjects:', error)
    console.error('Error details:', JSON.stringify(error))

    // Devolver un array vacío en lugar de datos mock
    return []
  }
}


