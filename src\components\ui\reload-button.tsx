"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { RefreshCw } from "lucide-react"
import { ButtonProps } from "@/components/ui/button"

interface ReloadButtonProps extends ButtonProps {
  label?: string
}

export function ReloadButton({ label = "Recargar página", ...props }: ReloadButtonProps) {
  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={() => window.location.reload()}
      {...props}
    >
      <RefreshCw className="mr-2 h-4 w-4" /> {label}
    </Button>
  )
}
