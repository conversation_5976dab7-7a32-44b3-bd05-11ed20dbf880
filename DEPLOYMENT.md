# Deployment Guide

This document outlines the deployment process for the AdminCore application.

## Prerequisites

- Node.js 18.x or later
- npm 8.x or later
- Vercel CLI (for manual deployments)
- Vercel account with project access

## Environment Variables

Create a `.env.local` file in the root directory with the following variables:

```
NEXT_PUBLIC_API_URL=your_api_url
VERCEL_TOKEN=your_vercel_token
# Add other environment variables here
```

## Automated Deployment (GitHub Actions)

The application is automatically deployed to production when changes are pushed to the `main` branch. The deployment process includes:

1. Installing dependencies
2. Running tests
3. Building the application
4. Deploying to Vercel

### Required GitHub Secrets

Set up the following secrets in your GitHub repository settings:

- `VERCEL_TOKEN`: Your Vercel authentication token
- `NEXT_PUBLIC_API_URL`: Your API URL
- Add other required environment variables

## Manual Deployment

### Local Deployment

1. Install dependencies:
   ```bash
   npm ci
   ```

2. Run tests:
   ```bash
   npm test
   ```

3. Build the application:
   ```bash
   npm run build
   ```

4. Deploy to Vercel:
   ```bash
   vercel --prod
   ```

### Using Deployment Script

1. Make the script executable:
   ```bash
   chmod +x scripts/deploy.sh
   ```

2. Run the deployment script:
   ```bash
   VERCEL_TOKEN=your_vercel_token ./scripts/deploy.sh
   ```

## Rollback

If you need to rollback to a previous deployment:

1. Go to your Vercel dashboard
2. Navigate to the project
3. Go to the "Deployments" tab
4. Find the deployment you want to rollback to
5. Click the "..." menu and select "Redeploy"

## Monitoring

Monitor your deployment in the following places:

- Vercel Dashboard: [Vercel](https://vercel.com)
- GitHub Actions: [Actions tab](https://github.com/your-username/admincore/actions)

## Troubleshooting

### Common Issues

1. **Build Failures**: Check the GitHub Actions logs for specific error messages
2. **Environment Variables**: Ensure all required environment variables are set in both GitHub Secrets and Vercel
3. **Dependency Issues**: Try deleting `node_modules` and `package-lock.json`, then run `npm ci` again

### Getting Help

If you encounter any issues during deployment, please check the following:

1. GitHub Actions workflow logs
2. Vercel deployment logs
3. Application logs in your hosting environment

For additional support, please contact the development team.
