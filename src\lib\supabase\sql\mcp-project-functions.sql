-- Funciones MCP (Model Context Protocol) para gestión de proyectos
-- Estas funciones utilizan el protocolo MCP para operaciones más eficientes y seguras

-- Función para eliminar un proyecto con MCP
CREATE OR REPLACE FUNCTION public.mcp_delete_project(project_id UUID, options JSONB DEFAULT '{}'::JSONB)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSONB;
  deleted_count INTEGER := 0;
  document_count INTEGER := 0;
  task_count INTEGER := 0;
  order_count INTEGER := 0;
  storage_count INTEGER := 0;
  project_exists BOOLEAN;
  current_user_id UUID;
  is_admin BOOLEAN := FALSE;
  cascade_delete BOOLEAN := COALESCE((options->>'cascade')::BOOLEAN, TRUE);
  soft_delete BOOLEAN := COALESCE((options->>'soft_delete')::BOOLEAN, FALSE);
  project_record RECORD;
BEGIN
  -- Obtener el ID del usuario actual
  current_user_id := auth.uid();
  
  -- Verificar si el usuario es administrador
  SELECT EXISTS (
    SELECT 1 FROM auth.users
    WHERE id = current_user_id
    AND raw_app_meta_data->>'is_admin' = 'true'
  ) INTO is_admin;
  
  -- Verificar si el proyecto existe
  SELECT EXISTS (
    SELECT 1 FROM public.projects WHERE id = project_id
  ) INTO project_exists;
  
  IF NOT project_exists THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'El proyecto no existe',
      'project_id', project_id,
      'code', 'PROJECT_NOT_FOUND'
    );
  END IF;
  
  -- Obtener información del proyecto para verificar permisos
  SELECT * INTO project_record FROM public.projects WHERE id = project_id;
  
  -- Verificar permisos (el usuario debe ser propietario o administrador)
  IF NOT is_admin AND project_record.owner_id != current_user_id THEN
    -- Verificar si el usuario tiene rol de administrador en el proyecto
    IF NOT EXISTS (
      SELECT 1 FROM public.project_users
      WHERE project_id = project_id
      AND user_id = current_user_id
      AND role = 'admin'
    ) THEN
      RETURN jsonb_build_object(
        'success', false,
        'message', 'No tienes permisos para eliminar este proyecto',
        'project_id', project_id,
        'code', 'PERMISSION_DENIED'
      );
    END IF;
  END IF;
  
  -- Si es soft delete, simplemente marcar como eliminado
  IF soft_delete THEN
    UPDATE public.projects
    SET 
      status = 'deleted',
      updated_at = NOW(),
      deleted_at = NOW()
    WHERE id = project_id;
    
    IF FOUND THEN
      deleted_count := 1;
    END IF;
    
    RETURN jsonb_build_object(
      'success', true,
      'message', 'Proyecto marcado como eliminado',
      'project_id', project_id,
      'deleted_count', deleted_count,
      'soft_delete', true,
      'code', 'SOFT_DELETE_SUCCESS'
    );
  END IF;
  
  -- Si cascade_delete es true, eliminar datos relacionados
  IF cascade_delete THEN
    -- Eliminar documentos asociados al proyecto
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'documents') THEN
      WITH deleted AS (
        DELETE FROM public.documents
        WHERE project_id = project_id
        RETURNING id
      )
      SELECT COUNT(*) INTO document_count FROM deleted;
    END IF;
    
    -- Eliminar tareas asociadas al proyecto a través de órdenes de trabajo
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'work_order_tasks') AND
       EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'work_orders') THEN
      WITH deleted AS (
        DELETE FROM public.work_order_tasks
        WHERE work_order_id IN (
          SELECT id FROM public.work_orders WHERE project_id = project_id
        )
        RETURNING id
      )
      SELECT COUNT(*) INTO task_count FROM deleted;
    END IF;
    
    -- Eliminar órdenes de trabajo asociadas al proyecto
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'work_orders') THEN
      WITH deleted AS (
        DELETE FROM public.work_orders
        WHERE project_id = project_id
        RETURNING id
      )
      SELECT COUNT(*) INTO order_count FROM deleted;
    END IF;
    
    -- Eliminar usuarios del proyecto
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'project_users') THEN
      DELETE FROM public.project_users
      WHERE project_id = project_id;
    END IF;
    
    -- Eliminar etapas del proyecto
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'project_stages') THEN
      DELETE FROM public.project_stages
      WHERE project_id = project_id;
    END IF;
    
    -- Eliminar archivos de almacenamiento asociados al proyecto
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'storage' AND table_name = 'objects') THEN
      WITH deleted AS (
        DELETE FROM storage.objects
        WHERE bucket_id = 'projects' AND path LIKE project_id || '/%'
        RETURNING id
      )
      SELECT COUNT(*) INTO storage_count FROM deleted;
    END IF;
  END IF;
  
  -- Finalmente, eliminar el proyecto
  DELETE FROM public.projects
  WHERE id = project_id;
  
  -- Incrementar el contador de eliminados si se eliminó el proyecto
  IF FOUND THEN
    deleted_count := 1;
  END IF;
  
  -- Construir el resultado
  result := jsonb_build_object(
    'success', true,
    'message', 'Proyecto eliminado correctamente',
    'project_id', project_id,
    'deleted_count', deleted_count,
    'related_data', jsonb_build_object(
      'documents', document_count,
      'tasks', task_count,
      'orders', order_count,
      'storage_files', storage_count
    ),
    'code', 'DELETE_SUCCESS',
    'user_id', current_user_id,
    'is_admin', is_admin,
    'timestamp', extract(epoch from now())
  );
  
  -- Registrar la operación en el log de auditoría si existe la tabla
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'audit_log') THEN
    INSERT INTO public.audit_log (
      user_id,
      action,
      resource_type,
      resource_id,
      details,
      ip_address
    ) VALUES (
      current_user_id,
      'delete',
      'project',
      project_id,
      result,
      COALESCE(nullif(current_setting('request.headers', true)::json->>'x-forwarded-for', ''), 'unknown')
    );
  END IF;
  
  RETURN result;
END;
$$;
