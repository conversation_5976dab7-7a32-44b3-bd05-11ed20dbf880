-- Función para verificar si existe una función en la base de datos
CREATE OR REPLACE FUNCTION public.function_exists(function_name TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  func_exists BOOLEAN;
BEGIN
  -- Verificar si la función existe
  SELECT EXISTS (
    SELECT 1
    FROM pg_proc p
    JOIN pg_namespace n ON p.pronamespace = n.oid
    WHERE n.nspname = 'public'
    AND p.proname = function_name
  ) INTO func_exists;

  RETURN func_exists;
END;
$$;

-- Función para ejecutar SQL dinámico
CREATE OR REPLACE FUNCTION public.exec_sql(sql TEXT)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  EXECUTE sql;
END;
$$;

-- Crear tabla de métricas de rendimiento si no existe
CREATE TABLE IF NOT EXISTS public.performance_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  category TEXT NOT NULL,
  efficiency NUMERIC NOT NULL DEFAULT 0,
  completion_rate NUMERIC NOT NULL DEFAULT 0,
  quality_score NUMERIC NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Crear tabla de asignación de recursos si no existe
CREATE TABLE IF NOT EXISTS public.resource_allocation (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  category TEXT NOT NULL,
  amount NUMERIC NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Crear tabla de log de auditoría si no existe
CREATE TABLE IF NOT EXISTS public.audit_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id),
  action TEXT NOT NULL,
  resource_type TEXT NOT NULL,
  resource_id UUID NOT NULL,
  details JSONB,
  ip_address TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Crear función para obtener distribución de proyectos
-- CORREGIDO: Añadido COALESCE(status, 'Sin estado') al GROUP BY
CREATE OR REPLACE FUNCTION public.get_project_distribution()
RETURNS TABLE (name TEXT, value NUMERIC)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Verificar si existe la tabla de proyectos
  IF EXISTS (
    SELECT 1 FROM information_schema.tables
    WHERE table_schema = 'public' AND table_name = 'projects'
  ) THEN
    -- Devolver distribución real de proyectos por estado
    RETURN QUERY
    SELECT
      COALESCE(status, 'Sin estado') AS name,
      COUNT(*)::NUMERIC AS value
    FROM public.projects
    GROUP BY COALESCE(status, 'Sin estado')
    ORDER BY value DESC;
  ELSE
    -- Si no existe la tabla, devolver datos de ejemplo
    RETURN QUERY
    SELECT name, value FROM (
      VALUES
        ('Activo', 42),
        ('Completado', 28),
        ('Planificación', 18),
        ('En pausa', 12)
    ) AS t(name, value);
  END IF;
END;
$$;

-- Crear función para obtener métricas del dashboard
CREATE OR REPLACE FUNCTION public.get_dashboard_metrics()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSON;
  projects_count INTEGER := 0;
  active_projects_count INTEGER := 0;
  orders_count INTEGER := 0;
  pending_orders_count INTEGER := 0;
  documents_count INTEGER := 0;
  users_count INTEGER := 0;
BEGIN
  -- Verificar y contar proyectos
  IF EXISTS (
    SELECT 1 FROM information_schema.tables
    WHERE table_schema = 'public' AND table_name = 'projects'
  ) THEN
    SELECT COUNT(*) INTO projects_count FROM public.projects;

    SELECT COUNT(*) INTO active_projects_count
    FROM public.projects
    WHERE status IN ('active', 'in_progress', 'planning');
  END IF;

  -- Verificar y contar órdenes de trabajo
  IF EXISTS (
    SELECT 1 FROM information_schema.tables
    WHERE table_schema = 'public' AND table_name = 'work_orders'
  ) THEN
    SELECT COUNT(*) INTO orders_count FROM public.work_orders;

    SELECT COUNT(*) INTO pending_orders_count
    FROM public.work_orders
    WHERE status = 'pending';
  END IF;

  -- Verificar y contar documentos
  IF EXISTS (
    SELECT 1 FROM information_schema.tables
    WHERE table_schema = 'public' AND table_name = 'documents'
  ) THEN
    SELECT COUNT(*) INTO documents_count FROM public.documents;
  END IF;

  -- Contar usuarios
  SELECT COUNT(*) INTO users_count FROM auth.users;

  -- Construir objeto JSON con los resultados
  result := json_build_object(
    'projectsCount', projects_count,
    'activeProjectsCount', active_projects_count,
    'ordersCount', orders_count,
    'pendingOrdersCount', pending_orders_count,
    'documentsCount', documents_count,
    'usersCount', users_count
  );

  RETURN result;
END;
$$;

-- Crear función robusta para obtener métricas de rendimiento
-- NUEVA FUNCIÓN: Versión robusta que maneja diferentes estructuras de tabla
CREATE OR REPLACE FUNCTION public.get_performance_metrics()
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSONB;
  table_exists BOOLEAN;
  has_new_structure BOOLEAN;
  has_old_structure BOOLEAN;
BEGIN
  -- Verificar si la tabla existe
  SELECT EXISTS (
    SELECT FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_name = 'performance_metrics'
  ) INTO table_exists;

  IF NOT table_exists THEN
    -- Si la tabla no existe, devolver datos de ejemplo
    result := jsonb_build_array(
      jsonb_build_object('subject', 'Diseño', 'efficiency', 85, 'completion', 92, 'quality', 78),
      jsonb_build_object('subject', 'Desarrollo', 'efficiency', 78, 'completion', 85, 'quality', 90),
      jsonb_build_object('subject', 'Pruebas', 'efficiency', 92, 'completion', 88, 'quality', 95),
      jsonb_build_object('subject', 'Implementación', 'efficiency', 80, 'completion', 75, 'quality', 85),
      jsonb_build_object('subject', 'Soporte', 'efficiency', 88, 'completion', 90, 'quality', 82)
    );
    RETURN result;
  END IF;

  -- Verificar si la tabla tiene la estructura nueva (category, efficiency, etc.)
  SELECT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'performance_metrics'
    AND column_name = 'category'
  ) INTO has_new_structure;

  -- Verificar si la tabla tiene la estructura antigua (metric_name, metric_type, etc.)
  SELECT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'performance_metrics'
    AND column_name = 'metric_name'
  ) INTO has_old_structure;

  -- Obtener datos según la estructura de la tabla
  IF has_new_structure THEN
    -- Estructura nueva: category, efficiency, completion_rate, quality_score
    WITH metrics AS (
      SELECT
        category,
        efficiency,
        completion_rate,
        quality_score
      FROM public.performance_metrics
      ORDER BY category
    )
    SELECT jsonb_agg(
      jsonb_build_object(
        'subject', category,
        'efficiency', efficiency,
        'completion', completion_rate,
        'quality', quality_score
      )
    ) INTO result
    FROM metrics;
  ELSIF has_old_structure THEN
    -- Estructura antigua: metric_name, metric_type, metric_value
    WITH grouped_metrics AS (
      SELECT
        metric_name,
        jsonb_object_agg(metric_type, metric_value) AS values
      FROM public.performance_metrics
      GROUP BY metric_name
    )
    SELECT jsonb_agg(
      jsonb_build_object(
        'subject', metric_name,
        'efficiency', COALESCE((values->>'efficiency')::numeric, 0),
        'completion', COALESCE((values->>'completion_rate')::numeric, 0),
        'quality', COALESCE((values->>'quality_score')::numeric, 0)
      )
    ) INTO result
    FROM grouped_metrics;
  ELSE
    -- Estructura desconocida, devolver datos de ejemplo
    result := jsonb_build_array(
      jsonb_build_object('subject', 'Diseño', 'efficiency', 85, 'completion', 92, 'quality', 78),
      jsonb_build_object('subject', 'Desarrollo', 'efficiency', 78, 'completion', 85, 'quality', 90),
      jsonb_build_object('subject', 'Pruebas', 'efficiency', 92, 'completion', 88, 'quality', 95),
      jsonb_build_object('subject', 'Implementación', 'efficiency', 80, 'completion', 75, 'quality', 85),
      jsonb_build_object('subject', 'Soporte', 'efficiency', 88, 'completion', 90, 'quality', 82)
    );
  END IF;

  -- Si no hay datos, devolver datos de ejemplo
  IF result IS NULL OR jsonb_array_length(result) = 0 THEN
    result := jsonb_build_array(
      jsonb_build_object('subject', 'Diseño', 'efficiency', 85, 'completion', 92, 'quality', 78),
      jsonb_build_object('subject', 'Desarrollo', 'efficiency', 78, 'completion', 85, 'quality', 90),
      jsonb_build_object('subject', 'Pruebas', 'efficiency', 92, 'completion', 88, 'quality', 95),
      jsonb_build_object('subject', 'Implementación', 'efficiency', 80, 'completion', 75, 'quality', 85),
      jsonb_build_object('subject', 'Soporte', 'efficiency', 88, 'completion', 90, 'quality', 82)
    );
  END IF;

  RETURN result;
END;
$$;

-- Crear una función específica para devolver JSONB (para evitar problemas de tipo)
CREATE OR REPLACE FUNCTION public.get_performance_metrics_jsonb()
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Simplemente llamar a la función existente
  RETURN public.get_performance_metrics();
END;
$$;

-- Insertar datos de ejemplo en la tabla de métricas de rendimiento si está vacía
INSERT INTO public.performance_metrics (category, efficiency, completion_rate, quality_score)
SELECT * FROM (
  VALUES
    ('Diseño', 85, 92, 78),
    ('Desarrollo', 78, 85, 90),
    ('Pruebas', 92, 88, 95),
    ('Implementación', 80, 75, 85),
    ('Soporte', 88, 90, 82)
) AS t(category, efficiency, completion_rate, quality_score)
WHERE NOT EXISTS (SELECT 1 FROM public.performance_metrics LIMIT 1);

-- Insertar datos de ejemplo en la tabla de asignación de recursos si está vacía
INSERT INTO public.resource_allocation (category, amount)
SELECT * FROM (
  VALUES
    ('Personal', 45000),
    ('Equipamiento', 28000),
    ('Software', 15000),
    ('Servicios', 12000),
    ('Otros', 5000)
) AS t(category, amount)
WHERE NOT EXISTS (SELECT 1 FROM public.resource_allocation LIMIT 1);

-- Habilitar RLS en las tablas
ALTER TABLE public.performance_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.resource_allocation ENABLE ROW LEVEL SECURITY;

-- Crear políticas para la tabla de métricas de rendimiento
DROP POLICY IF EXISTS "Allow authenticated users to read performance_metrics" ON public.performance_metrics;
CREATE POLICY "Allow authenticated users to read performance_metrics"
  ON public.performance_metrics FOR SELECT
  USING (auth.role() = 'authenticated');

-- Crear políticas para la tabla de asignación de recursos
DROP POLICY IF EXISTS "Allow authenticated users to read resource_allocation" ON public.resource_allocation;
CREATE POLICY "Allow authenticated users to read resource_allocation"
  ON public.resource_allocation FOR SELECT
  USING (auth.role() = 'authenticated');

-- Crear función robusta para obtener todos los datos del dashboard
CREATE OR REPLACE FUNCTION public.get_dashboard_data()
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  project_distribution JSONB;
  performance_metrics JSONB;
  resource_allocation JSONB;
  result JSONB;
BEGIN
  -- Obtener distribución de proyectos
  SELECT COALESCE(
    (SELECT jsonb_agg(pd) FROM public.get_project_distribution() pd),
    jsonb_build_array(
      jsonb_build_object('name', 'Activo', 'value', 42),
      jsonb_build_object('name', 'Completado', 'value', 28),
      jsonb_build_object('name', 'Planificación', 'value', 18),
      jsonb_build_object('name', 'En pausa', 'value', 12)
    )
  ) INTO project_distribution;

  -- Obtener métricas de rendimiento
  SELECT public.get_performance_metrics() INTO performance_metrics;

  -- Obtener asignación de recursos
  SELECT COALESCE(
    (SELECT jsonb_agg(ra) FROM (
      SELECT * FROM (
        VALUES
          ('Personal', 45),
          ('Equipamiento', 28),
          ('Software', 15),
          ('Servicios', 12),
          ('Otros', 5)
      ) AS t(name, value)
    ) ra),
    jsonb_build_array(
      jsonb_build_object('name', 'Personal', 'value', 45),
      jsonb_build_object('name', 'Equipamiento', 'value', 28),
      jsonb_build_object('name', 'Software', 'value', 15),
      jsonb_build_object('name', 'Servicios', 'value', 12),
      jsonb_build_object('name', 'Otros', 'value', 5)
    )
  ) INTO resource_allocation;

  -- Construir el resultado final
  SELECT jsonb_build_object(
    'projectDistribution', project_distribution,
    'performanceMetrics', performance_metrics,
    'resourceAllocation', resource_allocation
  ) INTO result;

  RETURN result;
END;
$$;
