import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { getGitHubConnection, getGitHubRepositories, syncUserRepositories } from '@/lib/services/server/github-service.server';

// Disable caching for this route
export const dynamic = 'force-dynamic';



export async function GET() {
  try {
    const connection = await getGitHubConnection();
    const repositories = connection ? await getGitHubRepositories() : [];
    return NextResponse.json({
      success: true,
      connection,
      repositories,
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Failed to fetch GitHub data';
    console.error('Failed to fetch GitHub data', error);
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

export async function POST() {
  try {
    const success = await syncUserRepositories();
    if (!success) {
      return NextResponse.json(
        { success: false, error: 'Failed to sync repositories' },
        { status: 500 }
      );
    }
    // Get updated data after sync
    const connection = await getGitHubConnection();
    const repositories = connection ? await getGitHubRepositories() : [];
    return NextResponse.json({
      success: true,
      connection,
      repositories,
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Failed to sync repositories';
    console.error('Failed to sync repositories', error);
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
