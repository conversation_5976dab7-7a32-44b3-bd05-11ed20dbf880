/**
 * @ai-file-description: "Interface definition for AI providers used in document analysis"
 * @ai-related-files: ["base-provider.ts", "provider-factory.ts", "document-analyzer.ts"]
 * @ai-owner: "File-Based Projects"
 */

/**
 * Result of document analysis containing extracted project information
 */
export interface DocumentAnalysisResult {
  // Project basic information
  project_name: string;
  description: string | null;
  start_date: string | null; // ISO format date string
  end_date: string | null; // ISO format date string

  // Financial information
  budget: string | null; // String to handle various formats
  currency: string | null;

  // Client information
  client_name: string | null;

  // Project details
  deliverables: string[] | null;
  scope: string | null;
  team_requirements: string[] | null;

  // Metadata
  tags: string[] | null;
  confidence_score: number; // 0-1 value indicating confidence in the analysis

  // Error information (optional)
  error_message?: string; // Present only if there was an error during analysis
}

/**
 * Configuration for AI providers
 */
export interface AIProviderConfig {
  apiKey: string;
  modelName: string;
  maxTokens?: number;
  temperature?: number;
}

/**
 * Interface that all AI providers must implement
 *
 * @ai-responsibility: "Defines the contract that all AI providers must follow"
 */
export interface AIProvider {
  /**
   * Analyzes document text and extracts project information
   *
   * @param documentText The text content of the document
   * @returns Structured project information
   */
  analyzeDocument(documentText: string): Promise<DocumentAnalysisResult>;

  /**
   * Validates the provider configuration
   *
   * @returns True if configuration is valid, false otherwise
   */
  validateConfig(): boolean;

  /**
   * Gets the name of the provider
   *
   * @returns Provider name
   */
  getProviderName(): string;

  /**
   * Gets the display name of the provider for UI
   *
   * @returns Provider display name
   */
  getProviderDisplayName(): string;
}
