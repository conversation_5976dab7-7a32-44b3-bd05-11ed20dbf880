"use client"

import React, { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { CalendarIcon, Loader2, DollarSign, UserPlus, X, Check, ChevronsUpDown, AlertCircle } from "lucide-react"
import { format } from "date-fns"
import { es } from "date-fns/locale"

import { Button } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { <PERSON>ert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { EnhancedFormValidationAlert, createValidationError } from "@/components/features/projects/enhanced-form-validation-alert"
import { createBrowserSupabaseClient } from "@/lib/supabase/client"
import { useSupabaseSession } from "@/hooks/use-supabase-session"
import { dataValidator } from "@/lib/services/data-validator-service"
import { validate as validateUUID } from 'uuid'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command"
import { cn } from "@/lib/utils"
import { getStatusText, Currency } from "@/types/projects"



// Definición de la interfaz para usuarios
interface User {
  id: string
  email: string
  first_name?: string | null
  last_name?: string | null
  full_name?: string
}

// Definición de la interfaz para usuarios del proyecto
interface ProjectUser {
  id: string
  user_id: string
  project_id: string
  role: string
  user?: User
}

interface UserSummary {
  id: string;
  email: string;
  first_name?: string | null;
  last_name?: string | null;
  full_name?: string;
}

interface ProjectUserInProject {
  id?: string;
  user_id: string;
  project_id?: string;
  role: string;
  users?: UserSummary | UserSummary[];
  user?: UserSummary;
}

// Robust transformer for project users array
function transformProjectUsers(raw: unknown[]): ProjectUser[] {
  return Array.isArray(raw)
    ? raw.map((puRaw: unknown) => {
        const pu = puRaw as ProjectUserInProject;
        let userData: UserSummary | null | undefined;
        if (Array.isArray(pu.users)) {
          userData = pu.users[0] || null;
        } else if (pu.users !== undefined) {
          userData = pu.users;
        } else {
          userData = pu.user;
        }
        const user: User | undefined = userData
          ? {
              id: userData.id,
              email: userData.email,
              first_name: userData.first_name,
              last_name: userData.last_name,
              full_name: userData.full_name || ((userData.first_name || "") + " " + (userData.last_name || "")).trim() || undefined,
            }
          : undefined;
        return {
          id: pu.id || '',
          user_id: pu.user_id,
          project_id: pu.project_id || '',
          role: pu.role,
          user: user,
        };
      })
    : [];
}

// Definición de la interfaz para clientes
interface Client {
  id: string
  name: string
  contact_name?: string
  contact_email?: string
}

// Esquema de validación para el formulario
const projectFormSchema = z.object({
  id: z.string().optional(),
  name: z.string({
    required_error: "El nombre del proyecto es obligatorio",
  }).min(3, {
    message: "El nombre debe tener al menos 3 caracteres.",
  }).refine(
    (val) => val && val.trim() !== '',
    {
      message: "El nombre del proyecto no puede estar vacío."
    }
  ),
  description: z.string().optional()
    .transform(val => val === '' ? null : val), // Convertir string vacío a null
  status: z.enum(["pending", "in_progress", "completed", "cancelled"] as const, {
    required_error: "El estado del proyecto es obligatorio",
  }),
  start_date: z.date().optional(),
  end_date: z.date().optional(),
  client_id: z.string()
    .optional()
    .refine(
      (val) => {
        // Si está vacío o es undefined, es válido
        if (!val || val === '') return true;
        // Si tiene valor, debe ser un UUID válido
        return validateUUID(val);
      },
      {
        message: "El cliente seleccionado no tiene un formato válido. Por favor, seleccione un cliente de la lista o deje el campo vacío."
      }
    )
    .transform(val => val === '' ? null : val), // Convertir string vacío a null
  budget: z.string().optional()
    .transform(val => val === '' ? null : val) // Convertir string vacío a null
    .refine(
      (val) => val === null || val === undefined || val === '' || !isNaN(parseFloat(val)),
      {
        message: "El presupuesto debe ser un número válido"
      }
    ),
  currency: z.enum(["USD", "CLP"] as const, {
    required_error: "La divisa es obligatoria",
  }).default("CLP"),
  project_type: z.string().optional()
    .transform(val => val === '' ? null : val), // Convertir string vacío a null
  project_users: z.array(
    z.object({
      user_id: z.string()
        .refine(
          (val) => val && val.trim() !== '' && validateUUID(val),
          {
            message: "El usuario seleccionado no tiene un formato válido o está vacío."
          }
        ),
      role: z.string({
        required_error: "El rol del usuario es obligatorio",
      }).refine(
        (val) => val && val.trim() !== '',
        {
          message: "El rol del usuario no puede estar vacío."
        }
      ),
      user: z.object({
        id: z.string(),
        email: z.string(),
        full_name: z.string().optional(),
      }).optional(),
    })
  ).default([]),
})

type ProjectFormValues = z.infer<typeof projectFormSchema>

// Valores por defecto para un nuevo proyecto
const defaultValues: Partial<ProjectFormValues> = {
  name: "",
  description: "",
  status: "pending",
  budget: "",
  currency: "CLP",
  client_id: "",
  project_users: [],
  start_date: undefined,
  end_date: undefined,
}

interface ProjectFormProps {
  initialData?: Partial<ProjectFormValues>
  onSubmit: (data: ProjectFormValues) => void
  isLoading?: boolean
}

export function ProjectForm({
  initialData,
  onSubmit,
  isLoading = false,
}: ProjectFormProps) {
  const supabase = createBrowserSupabaseClient()
  const { session, loading: isLoadingSession } = useSupabaseSession()

  // Definir el tipo de sesión para TypeScript
  type SessionType = {
    user: {
      id: string
      email: string
      user_metadata?: {
        name?: string
        avatar_url?: string
      }
    }
  }

  const currentSession = session as SessionType | null

  // Transform project_users before initializing the form
  const transformedInitialData = initialData && initialData.project_users
    ? { ...initialData, project_users: transformProjectUsers(initialData.project_users) }
    : initialData;
  const [startDate, setStartDate] = useState<Date | undefined>(
    initialData?.start_date
  )
  const [endDate, setEndDate] = useState<Date | undefined>(
    initialData?.end_date
  )

  const [availableUsers, setAvailableUsers] = useState<User[]>([])
  const [availableClients, setAvailableClients] = useState<Client[]>([])
  const [selectedUserId, setSelectedUserId] = useState<string>("")
  const [selectedRole, setSelectedRole] = useState<string>("member")
  const [userSelectOpen, setUserSelectOpen] = useState(false)
  const [clientSelectOpen, setClientSelectOpen] = useState(false)
  const [validationErrors, setValidationErrors] = useState<any[]>([])

  const form = useForm<ProjectFormValues>({
    resolver: zodResolver(projectFormSchema),
    defaultValues: transformedInitialData ? {
      ...defaultValues,
      ...transformedInitialData,
    } : defaultValues,
    mode: "onChange",
  })

  // Cargar usuarios disponibles
  useEffect(() => {
    const loadUsers = async () => {
      try {
        const { data, error } = await supabase
          .from("users")
          .select("id, email, first_name, last_name")

        if (error) throw error

        // Transformar los datos para incluir el nombre completo
        const usersWithFullName = (data || []).map(user => ({
          ...user,
          full_name: `${user.first_name || ''} ${user.last_name || ''}`.trim() || undefined
        }))

        setAvailableUsers(usersWithFullName)
      } catch (error) {
        console.error("Error al cargar usuarios:", error)
      }
    }

    if (currentSession) {
      loadUsers()
    }
  }, [currentSession, supabase])

  // Cargar clientes disponibles
  useEffect(() => {
    const loadClients = async () => {
      try {
        const { data, error } = await supabase
          .from("clients")
          .select("id, name, contact_name, contact_email")

        if (error) throw error

        setAvailableClients(data || [])
      } catch (error) {
        console.error("Error al cargar clientes:", error)
      }
    }

    if (currentSession) {
      loadClients()
    }
  }, [currentSession, supabase])

  // Si está cargando la sesión, mostrar spinner
  if (isLoadingSession) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    )
  }

  // Si no hay sesión, mostrar mensaje de error
  if (!currentSession) {
    return (
      <div className="space-y-4 p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Acceso no autorizado</AlertTitle>
          <AlertDescription>
            Debes iniciar sesión para acceder a esta página.
            <div className="mt-4">
              <Button
                onClick={() => window.location.href = '/auth/signin'}
                variant="outline"
              >
                Ir a Iniciar Sesión
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  // Función para agregar un usuario al proyecto
  const addUserToProject = () => {
    if (!selectedUserId) return

    const selectedUser = availableUsers.find(user => user.id === selectedUserId)
    if (!selectedUser) return

    const newProjectUser: ProjectUser = {
      id: crypto.randomUUID(), // Generate a new UUID for the project user entry
      project_id: initialData?.id || '', // Use existing project ID or empty string for new projects
      user_id: selectedUserId,
      role: selectedRole,
      user: selectedUser
    }

    const currentUsers = form.getValues("project_users") || []

    // Verificar si el usuario ya está en la lista
    if (!currentUsers.some(u => u.user_id === selectedUserId)) {
      form.setValue("project_users", [...currentUsers, newProjectUser])
    }

    // Limpiar selección
    setSelectedUserId("")
    setSelectedRole("member")
  }

  // Función para eliminar un usuario del proyecto
  const removeUserFromProject = (userId: string) => {
    const currentUsers = form.getValues("project_users") || []
    form.setValue(
      "project_users",
      currentUsers.filter(u => u.user_id !== userId)
    )
  }

  const handleSubmit = (data: ProjectFormValues) => {
    // Limpiar errores previos
    setValidationErrors([]);

    const clientValidationErrors: unknown[] = [];

    // Validar campos requeridos
    if (!data.name || data.name.trim() === '') {
      clientValidationErrors.push(createValidationError(
        'El nombre del proyecto es obligatorio',
        'name',
        'error',
        'Ingrese un nombre descriptivo para el proyecto'
      ));
    } else if (data.name.length < 3) {
      clientValidationErrors.push(createValidationError(
        'El nombre debe tener al menos 3 caracteres',
        'name',
        'error',
        'Agregue más caracteres al nombre del proyecto'
      ));
    }

    // Validar estado
    if (!data.status) {
      clientValidationErrors.push(createValidationError(
        'El estado del proyecto es obligatorio',
        'status',
        'error',
        'Seleccione un estado de la lista desplegable'
      ));
    }

    // Validar client_id si se proporciona
    if (data.client_id && data.client_id.trim() !== '' && !dataValidator.isValidUUID(data.client_id)) {
      clientValidationErrors.push(createValidationError(
        'El ID del cliente no tiene un formato válido',
        'client_id',
        'error',
        'Seleccione un cliente de la lista desplegable o deje el campo vacío'
      ));
    }

    // Validar presupuesto si se proporciona
    if (data.budget && data.budget.trim() !== '' && isNaN(parseFloat(data.budget))) {
      clientValidationErrors.push(createValidationError(
        'El presupuesto debe ser un número válido',
        'budget',
        'error',
        'Ingrese solo números, sin símbolos de moneda'
      ));
    }

    // Validar que los usuarios tengan IDs válidos
    if (data.project_users && data.project_users.length > 0) {
      data.project_users.forEach((user, index) => {
        if (!user.user_id || !dataValidator.isValidUUID(user.user_id)) {
          clientValidationErrors.push(createValidationError(
            `El usuario #${index + 1} tiene un ID inválido`,
            'project_users',
            'error',
            'Seleccione usuarios válidos de la lista'
          ));
        }
        if (!user.role || user.role.trim() === '') {
          clientValidationErrors.push(createValidationError(
            `El usuario #${index + 1} debe tener un rol asignado`,
            'project_users',
            'error',
            'Asigne un rol a cada usuario del proyecto'
          ));
        }
      });
    }

    // Si hay errores de validación del cliente, mostrarlos y no continuar
    if (clientValidationErrors.length > 0) {
      setValidationErrors(clientValidationErrors);
      return;
    }

    // Sanitizar datos antes del envío
    const uuidFields = ['client_id'];
    const sanitizedData = dataValidator.sanitizeObject(data, uuidFields);
    const textFields = ['description', 'budget'];
    const fullySanitizedData = dataValidator.sanitizeTextFields(sanitizedData, textFields);

    // Continuar con el envío del formulario
    onSubmit(fullySanitizedData as ProjectFormValues);
  }

  return (
    <Form {...form}>
      <EnhancedFormValidationAlert
        errors={validationErrors}
        showSuggestions={true}
        maxVisibleErrors={5}
        onDismiss={() => setValidationErrors([])}
      />
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <div className="text-sm text-muted-foreground mb-4">
          Los campos marcados con <span className="text-red-500">*</span> son obligatorios.
        </div>
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center">
                Nombre del proyecto
                <span className="text-red-500 ml-1">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  placeholder="Ingrese el nombre del proyecto"
                  {...field}
                  className={form.formState.errors.name ? "border-red-500" : ""}
                  required
                />
              </FormControl>
              <FormDescription>
                Nombre descriptivo para identificar el proyecto.
              </FormDescription>
              <FormMessage className="text-red-500" />
            </FormItem>
          )}
        />

<FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Descripción</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Descripción detallada del proyecto"
                  className="min-h-[120px]"
                  {...field}
                  value={field.value || ""}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center">
                  Estado
                  <span className="text-red-500 ml-1">*</span>
                </FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger className={form.formState.errors.status ? "border-red-500" : ""}>
                      <SelectValue placeholder="Seleccione un estado" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {(["pending", "in_progress", "completed", "cancelled"] as const).map(
                      (status) => (
                        <SelectItem key={status} value={status}>
                          {getStatusText(status)}
                        </SelectItem>
                      )
                    )}
                  </SelectContent>
                </Select>
                <FormMessage className="text-red-500" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="client_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Cliente</FormLabel>
                <Popover open={clientSelectOpen} onOpenChange={setClientSelectOpen}>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={clientSelectOpen}
                        className="w-full justify-between"
                      >
                        {field.value
                          ? availableClients.find(client => client.id === field.value)?.name || "Seleccionar cliente"
                          : "Seleccionar cliente"}
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-full p-0">
                    <Command>
                      <CommandInput placeholder="Buscar cliente..." />
                      <CommandEmpty>No se encontraron clientes.</CommandEmpty>
                      <CommandGroup>
                        {availableClients.map(client => (
                          <CommandItem
                            key={client.id}
                            value={client.id}
                            onSelect={(currentValue) => {
                              field.onChange(currentValue === field.value ? "" : currentValue)
                              setClientSelectOpen(false)
                            }}
                          >
                            <Check
                              className={cn(
                                "mr-2 h-4 w-4",
                                field.value === client.id ? "opacity-100" : "opacity-0"
                              )}
                            />
                            <div className="flex flex-col">
                              <span>{client.name}</span>
                              {client.contact_name && (
                                <span className="text-xs text-muted-foreground">{client.contact_name}</span>
                              )}
                            </div>
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </Command>
                  </PopoverContent>
                </Popover>
                <FormDescription>
                  Seleccione un cliente para el proyecto.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="start_date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Fecha de inicio</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={"outline"}
                        className={`w-full pl-3 text-left font-normal ${
                          !field.value && "text-muted-foreground"
                        }`}
                      >
                        {field.value ? (
                          format(field.value, "PPP", { locale: es })
                        ) : (
                          <span>Seleccionar fecha</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={(date) => {
                        field.onChange(date)
                        setStartDate(date)
                      }}
                      disabled={(date) =>
                        endDate ? date > endDate : false
                      }
                      initialFocus
                      locale={es}
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="end_date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Fecha de finalización</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={"outline"}
                        className={`w-full pl-3 text-left font-normal ${
                          !field.value && "text-muted-foreground"
                        }`}
                      >
                        {field.value ? (
                          format(field.value, "PPP", { locale: es })
                        ) : (
                          <span>Seleccionar fecha</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={(date) => {
                        field.onChange(date)
                        setEndDate(date)
                      }}
                      disabled={(date) =>
                        startDate ? date < startDate : false
                      }
                      initialFocus
                      locale={es}
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="space-y-6">
            <FormField
              control={form.control}
              name="currency"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center">
                    Divisa
                    <span className="text-red-500 ml-1">*</span>
                  </FormLabel>
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value)
                      form.setValue("currency", value as Currency)
                    }}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className={form.formState.errors.currency ? "border-red-500" : ""}>
                        <SelectValue placeholder="Seleccione una divisa" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="USD">USD (Dólar estadounidense)</SelectItem>
                      <SelectItem value="CLP">CLP (Peso chileno)</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Divisa en la que se expresa el presupuesto.
                  </FormDescription>
                  <FormMessage className="text-red-500" />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="budget"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Presupuesto</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <DollarSign className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        type="text"
                        placeholder="Ej: 10000"
                        className="pl-8"
                        {...field}
                        value={field.value || ""}
                      />
                    </div>
                  </FormControl>
                  <FormDescription>
                    Presupuesto estimado en {form.watch("currency") === "USD" ? "dólares" : "pesos chilenos"}.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-4">Equipo del Proyecto</h3>
            <div className="flex flex-col space-y-4">
              <div className="flex items-end space-x-2">
                <div className="flex-1">
                  <label className="text-sm font-medium mb-1 block">Usuario</label>
                  <Popover open={userSelectOpen} onOpenChange={setUserSelectOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={userSelectOpen}
                        className="w-full justify-between"
                      >
                        {selectedUserId
                          ? availableUsers.find(user => user.id === selectedUserId)?.full_name ||
                            availableUsers.find(user => user.id === selectedUserId)?.email ||
                            "Seleccionar usuario"
                          : "Seleccionar usuario"}
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0">
                      <Command>
                        <CommandInput placeholder="Buscar usuario..." />
                        <CommandEmpty>No se encontraron usuarios.</CommandEmpty>
                        <CommandGroup>
                          {availableUsers
                            .filter(user => {
                              // Filtrar usuarios que ya están en el proyecto
                              const projectUsers = form.getValues("project_users") || []
                              return !projectUsers.some(pu => pu.user_id === user.id)
                            })
                            .map(user => (
                              <CommandItem
                                key={user.id}
                                value={user.id}
                                onSelect={(currentValue) => {
                                  setSelectedUserId(currentValue === selectedUserId ? "" : currentValue)
                                  setUserSelectOpen(false)
                                }}
                              >
                                <Check
                                  className={cn(
                                    "mr-2 h-4 w-4",
                                    selectedUserId === user.id ? "opacity-100" : "opacity-0"
                                  )}
                                />
                                <div className="flex flex-col">
                                  <span>{user.full_name || "Usuario sin nombre"}</span>
                                  <span className="text-xs text-muted-foreground">{user.email}</span>
                                </div>
                              </CommandItem>
                            ))}
                        </CommandGroup>
                      </Command>
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="w-40">
                  <label className="text-sm font-medium mb-1 block">Rol</label>
                  <Select
                    value={selectedRole}
                    onValueChange={setSelectedRole}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Seleccionar rol" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="admin">Administrador</SelectItem>
                      <SelectItem value="manager">Gerente</SelectItem>
                      <SelectItem value="member">Miembro</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Button
                  type="button"
                  onClick={addUserToProject}
                  disabled={!selectedUserId}
                >
                  <UserPlus className="h-4 w-4 mr-2" />
                  Agregar
                </Button>
              </div>

              <div>
                {form.watch("project_users")?.length > 0 ? (
                  <div className="space-y-2 mt-2">
                    {form.watch("project_users").map((projectUser) => (
                      <div
                        key={projectUser.user_id}
                        className="flex items-center justify-between p-2 border rounded-md"
                      >
                        <div className="flex items-center space-x-2">
                          <Avatar className="h-8 w-8">
                            <AvatarFallback>
                              {projectUser.user?.full_name
                                ? projectUser.user.full_name
                                    .split(" ")
                                    .map((n) => n[0])
                                    .join("")
                                    .toUpperCase()
                                    .substring(0, 2)
                                : projectUser.user?.email?.substring(0, 2).toUpperCase() || "??"}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">
                              {projectUser.user?.full_name || "Usuario sin nombre"}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {projectUser.user?.email}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline">
                            {projectUser.role === "admin" ? "Administrador" :
                             projectUser.role === "manager" ? "Gerente" :
                             "Miembro"}
                          </Badge>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeUserFromProject(projectUser.user_id)}
                          >
                            <X className="h-4 w-4 text-red-500" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center p-4 border border-dashed rounded-md">
                    <p className="text-muted-foreground">No hay usuarios asignados a este proyecto.</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-4">
            <Button variant="outline" type="button">
              Cancelar
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {initialData ? "Actualizar proyecto" : "Crear proyecto"}
            </Button>
          </div>
        </div>
      </form>
    </Form>
  )
}
