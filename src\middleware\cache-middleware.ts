/**
 * @file Middleware para implementar caché y reducir peticiones al servidor
 * @description Proporciona funciones para implementar caché en el cliente y reducir peticiones al servidor
 */

import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Rutas que se pueden cachear
const CACHEABLE_ROUTES = [
  '/api/clients',
  '/api/users',
  '/api/projects',
  '/api/dashboard/stats',
];

// Tiempo de caché en segundos para cada ruta
const CACHE_TIMES: Record<string, number> = {
  '/api/clients': 60 * 5, // 5 minutos
  '/api/users': 60 * 5, // 5 minutos
  '/api/projects': 60 * 2, // 2 minutos
  '/api/dashboard/stats': 60 * 1, // 1 minuto
};

// Tiempo de caché por defecto (2 minutos)
const DEFAULT_CACHE_TIME = 60 * 2;

/**
 * Middleware para implementar caché y reducir peticiones al servidor
 */
export async function middleware(request: NextRequest) {
  // Obtener la ruta de la solicitud
  const path = request.nextUrl.pathname;
  
  // Verificar si la ruta es cacheable
  const isCacheable = CACHEABLE_ROUTES.some(route => path.startsWith(route));
  
  // Si no es cacheable, continuar con la solicitud normal
  if (!isCacheable) {
    return NextResponse.next();
  }
  
  // Determinar el tiempo de caché para esta ruta
  const cacheTime = CACHE_TIMES[path] || DEFAULT_CACHE_TIME;
  
  // Crear la respuesta
  const response = NextResponse.next();
  
  // Configurar encabezados de caché
  response.headers.set('Cache-Control', `public, max-age=${cacheTime}, s-maxage=${cacheTime}`);
  response.headers.set('CDN-Cache-Control', `public, max-age=${cacheTime}`);
  response.headers.set('Vercel-CDN-Cache-Control', `public, max-age=${cacheTime}`);
  
  // Agregar encabezado para indicar que la respuesta fue cacheada
  response.headers.set('X-Cache-Status', 'HIT');
  
  return response;
}

/**
 * Configuración del middleware
 */
export const config = {
  // Aplicar solo a rutas de API
  matcher: '/api/:path*',
};
