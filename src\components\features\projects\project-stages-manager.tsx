"use client"

import { useState } from "react"
import { Loader2, Plus, Trash2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { toast } from "@/hooks/use-toast"
import { createClient } from "@/lib/supabase/client"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

interface ProjectStage {
  id: string
  name: string
  description?: string
  stage_order: number
  completed: boolean
  project_id: string
}

interface ProjectStagesManagerProps {
  projectId: string
  initialStages?: ProjectStage[]
}

export function ProjectStagesManager({
  projectId,
  initialStages = [],
}: ProjectStagesManagerProps) {
  const [stages, setStages] = useState<ProjectStage[]>(initialStages)
  const [isLoading, setIsLoading] = useState(false)
  const [newStageName, setNewStageName] = useState("")
  const [newStageDescription, setNewStageDescription] = useState("")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const supabase = createClient()

  const loadStages = async () => {
    setIsLoading(true)
    try {
      const { data, error } = await supabase
        .from("project_stages")
        .select("*")
        .eq("project_id", projectId)
        .order("stage_order", { ascending: true })

      if (error) throw error
      setStages(data || [])
    } catch (error) {
      console.error("Error al cargar las etapas:", error)
      toast({
        title: "Error",
        description: "No se pudieron cargar las etapas del proyecto",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const addStage = async () => {
    if (!newStageName.trim()) {
      toast({
        title: "Error",
        description: "El nombre de la etapa es obligatorio",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)
    try {
      const newOrder = stages.length > 0
        ? Math.max(...stages.map(s => s.stage_order)) + 1
        : 0

      const { data, error } = await supabase
        .from("project_stages")
        .insert({
          name: newStageName,
          description: newStageDescription || null,
          stage_order: newOrder,
          completed: false,
          project_id: projectId,
        })
        .select()

      if (error) throw error

      setStages([...stages, data[0]])
      setNewStageName("")
      setNewStageDescription("")
      setIsAddDialogOpen(false)

      toast({
        title: "Etapa añadida",
        description: "La etapa ha sido añadida correctamente",
      })
    } catch (error) {
      console.error("Error al añadir la etapa:", error)
      toast({
        title: "Error",
        description: "No se pudo añadir la etapa",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const toggleStageCompletion = async (stageId: string, completed: boolean) => {
    setIsLoading(true)
    try {
      const { error } = await supabase
        .from("project_stages")
        .update({ completed })
        .eq("id", stageId)

      if (error) throw error

      setStages(
        stages.map((stage) =>
          stage.id === stageId ? { ...stage, completed } : stage
        )
      )

      toast({
        title: completed ? "Etapa completada" : "Etapa pendiente",
        description: `La etapa ha sido marcada como ${
          completed ? "completada" : "pendiente"
        }`,
      })
    } catch (error) {
      console.error("Error al actualizar la etapa:", error)
      toast({
        title: "Error",
        description: "No se pudo actualizar el estado de la etapa",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const deleteStage = async (stageId: string) => {
    setIsLoading(true)
    try {
      const { error } = await supabase
        .from("project_stages")
        .delete()
        .eq("id", stageId)

      if (error) throw error

      setStages(stages.filter((stage) => stage.id !== stageId))

      toast({
        title: "Etapa eliminada",
        description: "La etapa ha sido eliminada correctamente",
      })
    } catch (error) {
      console.error("Error al eliminar la etapa:", error)
      toast({
        title: "Error",
        description: "No se pudo eliminar la etapa",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Cargar etapas si no se proporcionaron inicialmente
  useState(() => {
    if (initialStages.length === 0) {
      loadStages()
    }
  })

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Etapas del Proyecto</h3>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button size="sm">
              <Plus className="mr-2 h-4 w-4" /> Añadir Etapa
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Añadir Nueva Etapa</DialogTitle>
              <DialogDescription>
                Crea una nueva etapa para este proyecto.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <label htmlFor="name" className="text-sm font-medium">
                  Nombre
                </label>
                <Input
                  id="name"
                  value={newStageName}
                  onChange={(e) => setNewStageName(e.target.value)}
                  placeholder="Nombre de la etapa"
                />
              </div>
              <div className="grid gap-2">
                <label htmlFor="description" className="text-sm font-medium">
                  Descripción (opcional)
                </label>
                <Input
                  id="description"
                  value={newStageDescription}
                  onChange={(e) => setNewStageDescription(e.target.value)}
                  placeholder="Descripción breve"
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsAddDialogOpen(false)}
              >
                Cancelar
              </Button>
              <Button onClick={addStage} disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Añadir
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {isLoading && stages.length === 0 ? (
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      ) : stages.length === 0 ? (
        <Card>
          <CardContent className="pt-6 text-center">
            <p className="text-muted-foreground">
              No hay etapas definidas para este proyecto.
            </p>
            <Button
              className="mt-4"
              onClick={() => setIsAddDialogOpen(true)}
              variant="outline"
            >
              <Plus className="mr-2 h-4 w-4" /> Añadir Primera Etapa
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-3">
          {stages.map((stage) => (
            <Card key={stage.id} className="relative">
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-base flex items-center">
                    <span>{stage.name}</span>
                    {stage.completed && (
                      <Badge className="ml-2 bg-green-500" variant="default">
                        Completada
                      </Badge>
                    )}
                  </CardTitle>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        toggleStageCompletion(stage.id, !stage.completed)
                      }
                      disabled={isLoading}
                    >
                      {stage.completed ? "Marcar Pendiente" : "Completar"}
                    </Button>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-red-500 hover:text-red-600"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>
                            ¿Eliminar esta etapa?
                          </AlertDialogTitle>
                          <AlertDialogDescription>
                            Esta acción no se puede deshacer. ¿Estás seguro de
                            que quieres eliminar esta etapa del proyecto?
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancelar</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => deleteStage(stage.id)}
                            className="bg-red-500 hover:bg-red-600"
                          >
                            Eliminar
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </div>
              </CardHeader>
              {stage.description && (
                <CardContent className="pt-0">
                  <p className="text-sm text-muted-foreground">
                    {stage.description}
                  </p>
                </CardContent>
              )}
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
