/**
 * @file Service Activity Service
 * @description Service for managing service activities, parts used, and signatures
 */

import { createClient } from '@/lib/supabase/client';
import {
  ServiceActivity,
  CreateServiceActivityInput,
  ServiceActivityType,
  ServiceActivityStatus,
  ServicePartsUsed,
  ServiceSignature
} from '@/types/service-management';

/**
 * Service for managing service activities, parts used, and signatures
 */
export class ServiceActivityService {
  /**
   * Get all service activities with optional filtering
   *
   * @param options Filter options
   * @returns Promise with service activities
   */
  async getServiceActivities(options: {
    serviceRequestId?: string;
    equipmentId?: string;
    technicianId?: string;
    activityType?: ServiceActivityType | ServiceActivityType[];
    status?: ServiceActivityStatus | ServiceActivityStatus[];
    startDate?: Date;
    endDate?: Date;
    limit?: number;
    offset?: number;
    orderBy?: string;
    orderDirection?: 'asc' | 'desc';
  } = {}): Promise<{ data: ServiceActivity[] | null; error: unknown }> {
    const supabase = createClient();

    // Start building the query
    let query = supabase
      .from('service_activities')
      .select(`
        *,
        service_request:service_request_id(*),
        equipment:equipment_id(*),
        technician:technician_id(id, email, full_name)
      `);

    // Apply filters
    if (options.serviceRequestId) {
      query = query.eq('service_request_id', options.serviceRequestId);
    }

    if (options.equipmentId) {
      query = query.eq('equipment_id', options.equipmentId);
    }

    if (options.technicianId) {
      query = query.eq('technician_id', options.technicianId);
    }

    if (options.activityType) {
      if (Array.isArray(options.activityType)) {
        query = query.in('activity_type', options.activityType);
      } else {
        query = query.eq('activity_type', options.activityType);
      }
    }

    if (options.status) {
      if (Array.isArray(options.status)) {
        query = query.in('status', options.status);
      } else {
        query = query.eq('status', options.status);
      }
    }

    if (options.startDate) {
      query = query.gte('start_time', options.startDate.toISOString());
    }

    if (options.endDate) {
      query = query.lte('end_time', options.endDate.toISOString());
    }

    // Apply ordering
    if (options.orderBy) {
      query = query.order(options.orderBy, {
        ascending: options.orderDirection === 'asc'
      });
    } else {
      // Default ordering by start_time descending (newest first)
      query = query.order('start_time', { ascending: false });
    }

    // Apply pagination
    if (options.limit) {
      query = query.limit(options.limit);
    }

    if (options.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
    }

    // Execute the query
    const { data, error } = await query;

    return { data, error };
  }

  /**
   * Get a service activity by ID
   *
   * @param id Service activity ID
   * @returns Promise with service activity
   */
  async getServiceActivityById(id: string): Promise<{ data: ServiceActivity | null; error: unknown }> {
    const supabase = createClient();

    const { data, error } = await supabase
      .from('service_activities')
      .select(`
        *,
        service_request:service_request_id(*),
        equipment:equipment_id(*),
        technician:technician_id(id, email, full_name)
      `)
      .eq('id', id)
      .single();

    return { data, error };
  }

  /**
   * Create a new service activity
   *
   * @param activity Service activity data
   * @returns Promise with created service activity
   */
  async createServiceActivity(activity: CreateServiceActivityInput): Promise<{ data: ServiceActivity | null; error: unknown }> {
    const supabase = createClient();

    // Get current user for technician_id if not provided
    if (!activity.technician_id) {
      // No pasar parámetros a getUser() para compatibilidad con todas las versiones de Supabase
      const { data: userData } = await supabase.auth.getUser();
      activity.technician_id = userData?.user?.id;
    }

    // Prepare the data
    const newActivity = {
      ...activity,
      status: activity.status || 'pending',
      is_remote: activity.is_remote !== undefined ? activity.is_remote : false,
      follow_up_required: activity.follow_up_required !== undefined ? activity.follow_up_required : false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Insert the service activity
    const { data, error } = await supabase
      .from('service_activities')
      .insert(newActivity)
      .select(`
        *,
        service_request:service_request_id(*),
        equipment:equipment_id(*),
        technician:technician_id(id, email, full_name)
      `)
      .single();

    // If the activity was created successfully, update the service request status if needed
    if (!error && data && data.service_request) {
      await this.updateServiceRequestStatus(data.service_request_id);
    }

    return { data, error };
  }

  /**
   * Update a service activity
   *
   * @param id Service activity ID
   * @param updates Service activity updates
   * @returns Promise with updated service activity
   */
  async updateServiceActivity(id: string, updates: Partial<ServiceActivity>): Promise<{ data: ServiceActivity | null; error: unknown }> {
    const supabase = createClient();

    // Prepare the updates
    const activityUpdates = {
      ...updates,
      updated_at: new Date().toISOString()
    };

    // Update the service activity
    const { data, error } = await supabase
      .from('service_activities')
      .update(activityUpdates)
      .eq('id', id)
      .select(`
        *,
        service_request:service_request_id(*),
        equipment:equipment_id(*),
        technician:technician_id(id, email, full_name)
      `)
      .single();

    // If the activity was updated successfully and status changed, update the service request status
    if (!error && data && updates.status && data.service_request) {
      await this.updateServiceRequestStatus(data.service_request_id);
    }

    return { data, error };
  }

  /**
   * Delete a service activity
   *
   * @param id Service activity ID
   * @returns Promise with deletion result
   */
  async deleteServiceActivity(id: string): Promise<{ error: unknown }> {
    const supabase = createClient();

    // Get the service request ID before deleting
    const { data: activity } = await this.getServiceActivityById(id);
    const serviceRequestId = activity?.service_request_id;

    // Delete the service activity
    const { error } = await supabase
      .from('service_activities')
      .delete()
      .eq('id', id);

    // If deletion was successful and we have a service request ID, update its status
    if (!error && serviceRequestId) {
      await this.updateServiceRequestStatus(serviceRequestId);
    }

    return { error };
  }

  /**
   * Start a service activity (set start time and status to in_progress)
   *
   * @param id Service activity ID
   * @returns Promise with updated service activity
   */
  async startActivity(id: string): Promise<{ data: ServiceActivity | null; error: unknown }> {
    const now = new Date().toISOString();

    return this.updateServiceActivity(id, {
      start_time: now,
      status: 'in_progress',
      updated_at: now
    });
  }

  /**
   * Complete a service activity (set end time and status to completed)
   *
   * @param id Service activity ID
   * @param notes Optional completion notes
   * @returns Promise with updated service activity
   */
  async completeActivity(id: string, notes?: string): Promise<{ data: ServiceActivity | null; error: unknown }> {
    const now = new Date().toISOString();

    return this.updateServiceActivity(id, {
      end_time: now,
      status: 'completed',
      notes: notes || undefined,
      updated_at: now
    });
  }

  /**
   * Get parts used for a service activity
   *
   * @param activityId Service activity ID
   * @returns Promise with parts used
   */
  async getPartsUsed(activityId: string): Promise<{ data: ServicePartsUsed[] | null; error: unknown }> {
    const supabase = createClient();

    const { data, error } = await supabase
      .from('service_parts_used')
      .select(`
        *,
        inventory_item:inventory_item_id(id, name, description, unit, unit_cost)
      `)
      .eq('service_activity_id', activityId)
      .order('created_at', { ascending: true });

    return { data, error };
  }

  /**
   * Add a part used to a service activity
   *
   * @param activityId Service activity ID
   * @param partData Part used data
   * @returns Promise with created part used
   */
  async addPartUsed(
    activityId: string,
    partData: {
      inventory_item_id: string;
      quantity: number;
      unit_cost?: number;
      is_billable?: boolean;
      is_warranty_covered?: boolean;
      notes?: string;
      batch_number?: string;
      source_location?: string;
    }
  ): Promise<{ data: ServicePartsUsed | null; error: unknown }> {
    const supabase = createClient();

    // Prepare the data
    const newPart = {
      service_activity_id: activityId,
      ...partData,
      is_billable: partData.is_billable !== undefined ? partData.is_billable : true,
      is_warranty_covered: partData.is_warranty_covered !== undefined ? partData.is_warranty_covered : false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Insert the part used
    const { data, error } = await supabase
      .from('service_parts_used')
      .insert(newPart)
      .select(`
        *,
        inventory_item:inventory_item_id(id, name, description, unit, unit_cost)
      `)
      .single();

    // If the part was added successfully, update the inventory
    if (!error && data && data.inventory_item_id) {
      await this.updateInventory(data.inventory_item_id, data.quantity, activityId);
    }

    return { data, error };
  }

  /**
   * Update a part used
   *
   * @param id Part used ID
   * @param updates Part used updates
   * @returns Promise with updated part used
   */
  async updatePartUsed(id: string, updates: Partial<ServicePartsUsed>): Promise<{ data: ServicePartsUsed | null; error: unknown }> {
    const supabase = createClient();

    // Get the current part used to calculate inventory adjustment
    const { data: currentPart } = await supabase
      .from('service_parts_used')
      .select('*')
      .eq('id', id)
      .single();

    // Prepare the updates
    const partUpdates = {
      ...updates,
      updated_at: new Date().toISOString()
    };

    // Update the part used
    const { data, error } = await supabase
      .from('service_parts_used')
      .update(partUpdates)
      .eq('id', id)
      .select(`
        *,
        inventory_item:inventory_item_id(id, name, description, unit, unit_cost)
      `)
      .single();

    // If the part was updated successfully and quantity changed, update the inventory
    if (!error && data && currentPart && updates.quantity !== undefined &&
        data.inventory_item_id && currentPart.quantity !== updates.quantity) {
      // Calculate the difference in quantity
      const quantityDiff = updates.quantity - currentPart.quantity;
      await this.updateInventory(data.inventory_item_id, quantityDiff, data.service_activity_id);
    }

    return { data, error };
  }

  /**
   * Delete a part used
   *
   * @param id Part used ID
   * @returns Promise with deletion result
   */
  async deletePartUsed(id: string): Promise<{ error: unknown }> {
    const supabase = createClient();

    // Get the part used before deleting to restore inventory
    const { data: part } = await supabase
      .from('service_parts_used')
      .select('*')
      .eq('id', id)
      .single();

    // Delete the part used
    const { error } = await supabase
      .from('service_parts_used')
      .delete()
      .eq('id', id);

    // If deletion was successful and we have inventory data, restore the inventory
    if (!error && part && part.inventory_item_id) {
      // Restore the inventory by adding back the quantity (negative of what was used)
      await this.updateInventory(part.inventory_item_id, -part.quantity, part.service_activity_id);
    }

    return { error };
  }

  /**
   * Get signature for a service activity
   *
   * @param activityId Service activity ID
   * @returns Promise with signature
   */
  async getSignature(activityId: string): Promise<{ data: ServiceSignature | null; error: unknown }> {
    const supabase = createClient();

    const { data, error } = await supabase
      .from('service_signatures')
      .select(`
        *,
        client:client_id(id, name)
      `)
      .eq('service_activity_id', activityId)
      .maybeSingle(); // Use maybeSingle to handle case where no signature exists

    return { data, error };
  }

  /**
   * Save a signature for a service activity
   *
   * @param activityId Service activity ID
   * @param signatureData Signature data
   * @returns Promise with created/updated signature
   */
  async saveSignature(
    activityId: string,
    signatureData: {
      signature_data: string;
      signed_by: string;
      client_id?: string;
      ip_address?: string;
      device_info?: string;
      geo_location?: string;
      signature_type?: string;
    }
  ): Promise<{ data: ServiceSignature | null; error: unknown }> {
    const supabase = createClient();

    // Check if a signature already exists
    const { data: existingSignature } = await this.getSignature(activityId);

    // Prepare the signature data
    const signatureRecord = {
      service_activity_id: activityId,
      ...signatureData,
      signature_type: signatureData.signature_type || 'customer',
      signed_at: new Date().toISOString()
    };

    let result;

    if (existingSignature) {
      // Update existing signature
      result = await supabase
        .from('service_signatures')
        .update(signatureRecord)
        .eq('id', existingSignature.id)
        .select(`
          *,
          client:client_id(id, name)
        `)
        .single();
    } else {
      // Create new signature
      result = await supabase
        .from('service_signatures')
        .insert(signatureRecord)
        .select(`
          *,
          client:client_id(id, name)
        `)
        .single();
    }

    return result;
  }

  /**
   * Update inventory when parts are used
   *
   * @param inventoryItemId Inventory item ID
   * @param quantity Quantity used (negative for removal, positive for addition)
   * @param serviceActivityId Service activity ID
   * @returns Promise with inventory transaction result
   */
  private async updateInventory(
    inventoryItemId: string,
    quantity: number,
    serviceActivityId: string
  ): Promise<{ data: unknown; error: unknown }> {
    const supabase = createClient();

    try {
      // First get the current inventory item
      const { data: inventoryItem, error: fetchError } = await supabase
        .from('inventory_items')
        .select('quantity_available')
        .eq('id', inventoryItemId)
        .single();

      if (fetchError || !inventoryItem) {
        throw fetchError || new Error('Inventory item not found');
      }

      // Calculate new quantity
      const newQuantity = inventoryItem.quantity_available - quantity;

      // Update the inventory item
      const { error: updateError } = await supabase
        .from('inventory_items')
        .update({ quantity_available: newQuantity })
        .eq('id', inventoryItemId);

      if (updateError) throw updateError;

      // Create an inventory transaction record
      const { data, error: transactionError } = await supabase
        .from('inventory_transactions')
        .insert({
          item_id: inventoryItemId,
          quantity: Math.abs(quantity),
          transaction_type: quantity > 0 ? 'remove' : 'add', // If quantity is positive, we're removing from inventory
          notes: `Service activity ${serviceActivityId}`,
          service_activity_id: serviceActivityId,
          is_service_related: true
        });

      if (transactionError) throw transactionError;

      return { data, error: null };
    } catch (error) {
      console.error('Error updating inventory:', error);
      return { data: null, error };
    }
  }

  /**
   * Update service request status based on activities
   *
   * @param serviceRequestId Service request ID
   * @returns Promise with update result
   */
  private async updateServiceRequestStatus(serviceRequestId: string): Promise<{ error: unknown }> {
    const supabase = createClient();

    try {
      // Get all activities for this service request
      const { data: activities, error: fetchError } = await supabase
        .from('service_activities')
        .select('status')
        .eq('service_request_id', serviceRequestId);

      if (fetchError) throw fetchError;

      // Determine the appropriate status for the service request
      let newStatus: string;

      if (!activities || activities.length === 0) {
        newStatus = 'pending';
      } else if (activities.every(a => a.status === 'completed')) {
        newStatus = 'resolved';
      } else if (activities.some(a => a.status === 'in_progress')) {
        newStatus = 'in_progress';
      } else if (activities.some(a => a.status === 'cancelled')) {
        // If some activities are cancelled but others are completed, still consider in progress
        if (activities.some(a => a.status === 'completed')) {
          newStatus = 'in_progress';
        } else {
          newStatus = 'on_hold';
        }
      } else {
        newStatus = 'assigned';
      }

      // Update the service request
      const { error: updateError } = await supabase
        .from('service_requests')
        .update({
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', serviceRequestId);

      return { error: updateError };
    } catch (error) {
      console.error('Error updating service request status:', error);
      return { error };
    }
  }
}

// Export a singleton instance
export const serviceActivityService = new ServiceActivityService();
