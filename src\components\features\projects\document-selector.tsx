import { useState, useEffect } from "react";
import { createClient } from "@/lib/supabase/client";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { format } from "date-fns";
import { es } from "date-fns/locale";

interface Document {
  id: string;
  filename: string;
  file_type: string;
  upload_date: string | null;
  file_path?: string;
  file_url?: string;
  file_size?: number;
}

interface DocumentSelectorProps {
  onSelect: (document: Document) => void;
}

interface RawDocument {
  id: string;
  filename: string | null;
  file_type: string | null;
  upload_date: string | null;
  file_size?: number | null;
}

export function DocumentSelector({ onSelect }: DocumentSelectorProps) {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const supabase = createClient();

  useEffect(() => {
    loadDocuments();
  }, [loadDocuments]);

  const loadDocuments = async () => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from("documents")
        .select("id, filename, file_type, upload_date, file_size")
        .order("upload_date", { ascending: false })
        .returns<RawDocument[]>();

      if (error) throw error;

      if (data) {
        const typedDocuments: Document[] = data.map(doc => ({
          id: doc.id,
          filename: doc.filename || "",
          file_type: doc.file_type || "application/octet-stream",
          upload_date: doc.upload_date,
          file_size: doc.file_size || 0
        }));
        setDocuments(typedDocuments);
      }
    } catch (error) {
      console.error("Error cargando documentos:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSelect = async (document: Document) => {
    // Verificar que el documento existe antes de seleccionarlo
    try {
      // Primero verificar en la base de datos
      const { data, error } = await supabase
        .from("documents")
        .select("id, filename, file_path, file_url, file_size")
        .eq("id", document.id)
        .single();

      if (error) {
        console.error("Error verificando documento:", error);
        const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

        throw new Error(`Error al verificar el documento: ${errorMessage}`);
      }

      if (!data) {
        console.error("Documento no encontrado en la base de datos:", document.id);
        throw new Error(`El documento con ID ${document.id} ya no existe en la base de datos`);
      }

      // Verificar que el archivo existe en el almacenamiento
      if (data.file_path) {
        console.log('Verificando que el archivo existe en el almacenamiento...');
        try {
          // Extraer la ruta del archivo (sin el bucket)
          const filePath = data.file_path;

          // Verificar si el archivo existe en el bucket
          const { data: fileData, error: fileError } = await supabase
            .storage
            .from('documents')
            .download(filePath);

          if (fileError) {
            console.error('Error al verificar el archivo en el almacenamiento:', fileError);
            throw new Error(`El archivo no se encuentra en el almacenamiento. Por favor, intente subir el documento nuevamente.`);
          }

          if (!fileData) {
            console.error('Archivo no encontrado en el almacenamiento:', filePath);
            throw new Error(`El archivo no se encuentra en el almacenamiento. Por favor, intente subir el documento nuevamente.`);
          }

          console.log('Archivo verificado en el almacenamiento:', filePath);
        } catch (storageError) {
          console.error('Error al verificar el archivo en el almacenamiento:', storageError);
          // No lanzar error aquí, intentar continuar con la URL firmada
        }
      }

      // Generar una nueva URL firmada para el documento
      let signedUrl = data.file_url;

      if (data.file_path) {
        try {
          console.log('Generando nueva URL firmada para el documento...');
          const { data: urlData, error: urlError } = await supabase
            .storage
            .from('documents')
            .createSignedUrl(data.file_path, 3600); // URL válida por 1 hora

          if (!urlError && urlData?.signedUrl) {
            signedUrl = urlData.signedUrl;
            console.log('Nueva URL firmada generada correctamente');
          } else {
            console.error('Error al generar URL firmada:', urlError);
          }
        } catch (urlError) {
          console.error('Error al generar URL firmada:', urlError);
          // Continuar con la URL original
        }
      }

      // Si el documento existe, actualizar con los datos más recientes
      const verifiedDocument = {
        ...document,
        file_path: data.file_path,
        file_url: signedUrl || data.file_url,
        file_size: data.file_size || 0
      };

      // Log the file size for debugging
      console.log("Documento verificado con tamaño:", data.file_size, "bytes");

      console.log("Documento verificado antes de seleccionar:", verifiedDocument);
      onSelect(verifiedDocument);
      setIsOpen(false);
    } catch (error) {
      console.error("Error al seleccionar documento:", error);
      // Mostrar toast de error
      const { toast } = await import("@/hooks/use-toast");
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Error al seleccionar el documento",
        variant: "destructive"
      });
    }
  };

  const formatDate = (date: string | null) => {
    if (!date) return "-";
    return format(new Date(date), "PPP", { locale: es });
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">Seleccionar Documento Existente</Button>
      </DialogTrigger>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle>Seleccionar Documento</DialogTitle>
          <DialogDescription>
            Elige un documento existente para analizarlo
          </DialogDescription>
        </DialogHeader>
        <ScrollArea className="h-[400px]">
          <div className="p-4">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nombre</TableHead>
                  <TableHead>Tipo</TableHead>
                  <TableHead>Fecha</TableHead>
                  <TableHead></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center">
                      Cargando documentos...
                    </TableCell>
                  </TableRow>
                ) : documents.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center">
                      No hay documentos disponibles
                    </TableCell>
                  </TableRow>
                ) : (
                  documents.map((doc) => (
                    <TableRow key={doc.id}>
                      <TableCell>{doc.filename || "Sin nombre"}</TableCell>
                      <TableCell>{doc.file_type || "Desconocido"}</TableCell>
                      <TableCell>{formatDate(doc.upload_date)}</TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleSelect(doc)}
                        >
                          Seleccionar
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}