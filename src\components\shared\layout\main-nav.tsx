import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { useEffect, useState } from "react"
import { createClient } from "@/lib/supabase/client"
import { useI18n } from "@/i18n/i18n-context"
import type { SupabaseClient } from '@supabase/supabase-js'
import type { Database } from '@/lib/supabase/types'
import {
  LayoutDashboard,
  FolderOpen,
  ClipboardList,
  FileText,
  Users,
  Settings,
  Package,
  Bug,
  KeyRound,
  Activity,
  Gauge,
  Database,
  Microscope,
  Wrench,
  CalendarClock,
} from "@/components/ui/icons"

// Definimos las rutas con claves de traducción
const routeKeys = [
  {
    labelKey: "navigation.dashboard",
    icon: LayoutDashboard,
    href: "/dashboard",
  },
  {
    labelKey: "navigation.projects",
    icon: FolderOpen,
    href: "/dashboard/projects",
  },
  {
    labelKey: "navigation.workOrders",
    icon: ClipboardList,
    href: "/dashboard/work-orders",
  },
  {
    labelKey: "navigation.serviceRequests",
    icon: Wrench,
    href: "/dashboard/service-requests",
  },
  {
    labelKey: "navigation.customerEquipment",
    icon: Wrench,
    href: "/dashboard/customer-equipment",
  },
  {
    labelKey: "navigation.maintenanceSchedules",
    icon: CalendarClock,
    href: "/dashboard/maintenance",
  },
  {
    labelKey: "navigation.inventory",
    icon: Package,
    href: "/dashboard/inventory",
  },
  {
    labelKey: "navigation.documents",
    icon: FileText,
    href: "/dashboard/documents",
  },
  {
    labelKey: "navigation.users",
    icon: Users,
    href: "/dashboard/users",
  },
  {
    labelKey: "navigation.settings",
    icon: Settings,
    href: "/dashboard/settings",
  },
]

export function MainNav() {
  const pathname = usePathname()
  const [isAdmin, setIsAdmin] = useState(false)
  const supabase = createClient() as SupabaseClient<Database>
  const { t } = useI18n()

  useEffect(() => {
    async function checkAdminStatus() {
      try {
        // No pasar parámetros a getUser() para compatibilidad con todas las versiones de Supabase
        const { data: { user } } = await supabase.auth.getUser()

        if (!user) return

        // Verificar si el usuario es administrador
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('role')
          .eq('id', user.id)
          .single()

        if (!profileError && profile?.role === 'admin') {
          setIsAdmin(true)
        }
      } catch (error) {
        console.error('Error al verificar estado de administrador:', error)
      }
    }

    checkAdminStatus()
  }, [])

  // Rutas de administrador con claves de traducción
  const adminRouteKeys = [
    {
      labelKey: "navigation.documentAnalysis",
      icon: Microscope,
      href: "/dashboard/admin/document-analysis",
    },
    {
      labelKey: "navigation.rateLimiter",
      icon: Gauge,
      href: "/dashboard/admin/rate-limiter",
    },
    {
      labelKey: "navigation.localSync",
      icon: Database,
      href: "/dashboard/admin/sync",
    },
    {
      labelKey: "navigation.aiProviders",
      icon: Settings,
      href: "/dashboard/settings/ai-providers",
    },
    {
      labelKey: "navigation.sessionDiagnostic",
      icon: KeyRound,
      href: "/dashboard/test-session",
    },
    {
      labelKey: "navigation.debugging",
      icon: Bug,
      href: "/dashboard/debug",
    }
  ]

  return (
    <nav className="flex flex-col space-y-1">
      {routeKeys.map((route) => (
        <Link
          key={route.href}
          href={route.href}
          className={cn(
            "flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground",
            pathname === route.href ? "bg-accent text-accent-foreground" : "text-muted-foreground"
          )}
        >
          <route.icon className="h-4 w-4" />
          {t(route.labelKey)}
        </Link>
      ))}

      {isAdmin && (
        <>
          <div className="mt-6 mb-2 px-3">
            <h3 className="text-xs font-semibold text-muted-foreground tracking-wider uppercase">{t('navigation.administration')}</h3>
          </div>
          {adminRouteKeys.map((route) => (
            <Link
              key={route.href}
              href={route.href}
              className={cn(
                "flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground",
                pathname === route.href ? "bg-accent text-accent-foreground" : "text-muted-foreground"
              )}
            >
              <route.icon className="h-4 w-4" />
              {t(route.labelKey)}
            </Link>
          ))}
        </>
      )}
    </nav>
  )
}