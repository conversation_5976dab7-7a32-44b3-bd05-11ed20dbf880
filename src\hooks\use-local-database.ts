/**
 * @file Hook para usar la base de datos local
 * @description Proporciona acceso a la base de datos local y funciones de sincronización
 */

import { useState, useEffect, useCallback } from 'react';
import { useLiveQuery } from 'dexie-react-hooks';
import { localDb, CachedProject, CachedDocument, CachedUser, SyncConfig } from '@/lib/db/local-database';
import { syncService, SYNC_EVENTS } from '@/lib/services/sync-service';

/**
 * Hook para acceder a proyectos en la base de datos local
 */
export function useLocalProjects(options: { userId?: string; limit?: number; orderBy?: string } = {}) {
  const { userId, limit = 50, orderBy = 'updated_at' } = options;
  
  // Consulta en vivo que se actualiza automáticamente cuando cambian los datos
  const projects = useLiveQuery(
    async () => {
      let query = localDb.projects;
      
      // Filtrar por usuario si se proporciona
      if (userId) {
        query = query.where('user_id').equals(userId);
      }
      
      // Ordenar y limitar
      return query
        .orderBy(orderBy)
        .reverse()
        .limit(limit)
        .toArray();
    },
    [userId, limit, orderBy],
    []
  );
  
  const addProject = useCallback(async (project: Omit<CachedProject, '_synced' | '_local_updated_at'>) => {
    return syncService.addRecord('projects', project);
  }, []);
  
  const updateProject = useCallback(async (id: string, data: Partial<CachedProject>) => {
    return syncService.updateRecord('projects', id, data);
  }, []);
  
  const deleteProject = useCallback(async (id: string) => {
    return syncService.deleteRecord('projects', id);
  }, []);
  
  return {
    projects,
    addProject,
    updateProject,
    deleteProject,
    isLoading: projects === undefined
  };
}

/**
 * Hook para acceder a documentos en la base de datos local
 */
export function useLocalDocuments(options: { userId?: string; limit?: number; orderBy?: string } = {}) {
  const { userId, limit = 50, orderBy = 'updated_at' } = options;
  
  const documents = useLiveQuery(
    async () => {
      let query = localDb.documents;
      
      if (userId) {
        query = query.where('user_id').equals(userId);
      }
      
      return query
        .orderBy(orderBy)
        .reverse()
        .limit(limit)
        .toArray();
    },
    [userId, limit, orderBy],
    []
  );
  
  const addDocument = useCallback(async (document: Omit<CachedDocument, '_synced' | '_local_updated_at'>) => {
    return syncService.addRecord('documents', document);
  }, []);
  
  const updateDocument = useCallback(async (id: string, data: Partial<CachedDocument>) => {
    return syncService.updateRecord('documents', id, data);
  }, []);
  
  const deleteDocument = useCallback(async (id: string) => {
    return syncService.deleteRecord('documents', id);
  }, []);
  
  return {
    documents,
    addDocument,
    updateDocument,
    deleteDocument,
    isLoading: documents === undefined
  };
}

/**
 * Hook para acceder a usuarios en la base de datos local
 */
export function useLocalUsers(options: { limit?: number; orderBy?: string } = {}) {
  const { limit = 50, orderBy = 'email' } = options;
  
  const users = useLiveQuery(
    async () => {
      return localDb.users
        .orderBy(orderBy)
        .limit(limit)
        .toArray();
    },
    [limit, orderBy],
    []
  );
  
  const addUser = useCallback(async (user: Omit<CachedUser, '_synced' | '_local_updated_at'>) => {
    return syncService.addRecord('users', user);
  }, []);
  
  const updateUser = useCallback(async (id: string, data: Partial<CachedUser>) => {
    return syncService.updateRecord('users', id, data);
  }, []);
  
  const deleteUser = useCallback(async (id: string) => {
    return syncService.deleteRecord('users', id);
  }, []);
  
  return {
    users,
    addUser,
    updateUser,
    deleteUser,
    isLoading: users === undefined
  };
}

/**
 * Hook para gestionar la sincronización
 */
export function useSyncManager() {
  const [isSyncing, setIsSyncing] = useState(false);
  const [lastSyncTime, setLastSyncTime] = useState<number | null>(null);
  const [syncError, setSyncError] = useState<string | null>(null);
  const [syncStats, setSyncStats] = useState<any | null>(null);
  
  const config = useLiveQuery(
    () => localDb.syncConfig.get(1),
    [],
    null
  );
  
  // Cargar estadísticas iniciales
  useEffect(() => {
    const loadStats = async () => {
      const stats = await syncService.getSyncStats();
      setSyncStats(stats);
      setLastSyncTime(stats.lastSync);
    };
    
    loadStats();
  }, []);
  
  // Configurar listeners de eventos de sincronización
  useEffect(() => {
    const handleSyncEvent = (event: string, data: unknown) => {
      switch (event) {
        case SYNC_EVENTS.SYNC_STARTED:
          setIsSyncing(true);
          setSyncError(null);
          break;
        
        case SYNC_EVENTS.SYNC_COMPLETED:
          setIsSyncing(false);
          setLastSyncTime(data.timestamp);
          // Actualizar estadísticas
          syncService.getSyncStats().then(setSyncStats);
          break;
        
        case SYNC_EVENTS.SYNC_ERROR:
          setIsSyncing(false);
          setSyncError(data.error instanceof Error ? data.error.message : String(data.error));
          break;
      }
    };
    
    // Registrar listener
    const unsubscribe = syncService.addSyncListener(handleSyncEvent);
    
    // Limpiar al desmontar
    return unsubscribe;
  }, []);
  
  // Funciones para gestionar la sincronización
  const syncNow = useCallback(async () => {
    return syncService.forceSyncNow();
  }, []);
  
  const updateSyncConfig = useCallback(async (newConfig: Partial<SyncConfig>) => {
    return syncService.updateConfig(newConfig);
  }, []);
  
  const cleanupLogs = useCallback(async () => {
    return syncService.cleanupOldLogs();
  }, []);
  
  return {
    isSyncing,
    lastSyncTime,
    syncError,
    syncStats,
    config,
    syncNow,
    updateSyncConfig,
    cleanupLogs
  };
}

/**
 * Hook para obtener estadísticas de la base de datos local
 */
export function useLocalDbStats() {
  const [stats, setStats] = useState<{
    projectsCount: number;
    documentsCount: number;
    usersCount: number;
    pendingOperationsCount: number;
    syncLogsCount: number;
    dbSize: string;
  } | null>(null);
  
  const refreshStats = useCallback(async () => {
    const projectsCount = await localDb.projects.count();
    const documentsCount = await localDb.documents.count();
    const usersCount = await localDb.users.count();
    const pendingOperationsCount = await localDb.pendingOperations.count();
    const syncLogsCount = await localDb.syncLogs.count();
    
    // Estimar tamaño de la base de datos (no es exacto)
    let dbSize = 'Desconocido';
    try {
      if ('storage' in navigator && 'estimate' in navigator.storage) {
        const estimate = await navigator.storage.estimate();
        if (estimate.usage) {
          dbSize = `${Math.round(estimate.usage / 1024 / 1024)} MB`;
        }
      }
    } catch (e) {
      console.error('Error al estimar tamaño de almacenamiento:', e);
    }
    
    setStats({
      projectsCount,
      documentsCount,
      usersCount,
      pendingOperationsCount,
      syncLogsCount,
      dbSize
    });
  }, []);
  
  // Cargar estadísticas iniciales
  useEffect(() => {
    refreshStats();
  }, [refreshStats]);
  
  return {
    stats,
    refreshStats
  };
}
