/**
 * @ai-file-description: "Supabase server client for server components"
 * @ai-related-files: ["client.ts", "server.ts"]
 * @ai-owner: "Supabase Integration"
 */

import { createServerClient } from '@supabase/ssr'
import { type CookieOptions } from '@supabase/ssr'
import { type cookies } from 'next/headers'
import { Database } from '@/lib/supabase/types'

/**
 * Creates a Supabase client for server components
 *
 * @param cookieStore - The cookie store from next/headers (can be Promise or direct)
 * @returns Supabase client
 */
export function createClient(cookieStore: ReturnType<typeof cookies> | any) {
  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          try {
            // Handle both Promise and direct cookie store
            if (cookieStore && typeof cookieStore.get === 'function') {
              return cookieStore.get(name)?.value
            }
            return undefined
          } catch (error) {
            console.warn('Error getting cookie:', error)
            return undefined
          }
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
            if (cookieStore && typeof cookieStore.set === 'function') {
              cookieStore.set({ name, value, ...options })
            }
          } catch (error) {
            // This will throw in middleware, but we can safely ignore it since we handle
            // setting cookies in the middleware separately
          }
        },
        remove(name: string, options: CookieOptions) {
          try {
            if (cookieStore && typeof cookieStore.set === 'function') {
              cookieStore.set({ name, value: '', ...options })
            }
          } catch (error) {
            // This will throw in middleware, but we can safely ignore it since we handle
            // setting cookies in the middleware separately
          }
        },
      },
      auth: {
        persistSession: true,
        autoRefreshToken: true,
        detectSessionInUrl: true,
      },
      cookieOptions: {
        // Configuración de seguridad de la cookie
        sameSite: 'lax',
        secure: process.env.NODE_ENV === 'production', // Permitir cookies en desarrollo (http)
        path: '/',
      },
      global: {
        // Headers para evitar problemas de caché
        headers: {
          'Cache-Control': 'no-store, max-age=0',
        },
      }
    }
  )
}
