"use client"

import { ProjectDeleteButton } from "./project-delete-button"
import { Archive } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

interface ProjectArchiveButtonProps {
  projectId: string
  projectName: string
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
  size?: "default" | "sm" | "lg" | "icon"
  className?: string
}

/**
 * Botón para archivar un proyecto (soft delete)
 * Internamente usa ProjectDeleteButton con softDelete=true
 */
export function ProjectArchiveButton({
  projectId,
  projectName,
  variant = "outline",
  size = "default",
  className = "",
}: ProjectArchiveButtonProps) {
  return (
    <ProjectDeleteButton
      projectId={projectId}
      projectName={projectName}
      variant={variant}
      size={size}
      className={className}
      softDelete={true}
      cascadeDelete={false}
    >
      <Archive className="mr-2 h-4 w-4" /> Archivar Proyecto
    </ProjectDeleteButton>
  )
}
