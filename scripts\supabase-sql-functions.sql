-- Script para crear las funciones SQL necesarias en Supabase
-- Ejecutar este script directamente en el Editor SQL de Supabase

-- Función para ejecutar SQL dinámico
CREATE OR REPLACE FUNCTION public.pgsql(query text)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSONB;
BEGIN
  EXECUTE query;
  result := jsonb_build_object('success', true, 'message', 'SQL executed successfully');
  RETURN result;
EXCEPTION WHEN OTHERS THEN
  result := jsonb_build_object(
    'success', false,
    'message', SQLERRM,
    'detail', SQLSTATE,
    'query', query
  );
  RETURN result;
END;
$$;

-- Función para verificar si existe una función en la base de datos
CREATE OR REPLACE FUNCTION public.function_exists(function_name TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  func_exists BOOLEAN;
BEGIN
  -- Verificar si la función existe
  SELECT EXISTS (
    SELECT 1
    FROM pg_proc p
    JOIN pg_namespace n ON p.pronamespace = n.oid
    WHERE n.nspname = 'public'
    AND p.proname = function_name
  ) INTO func_exists;
  
  RETURN func_exists;
END;
$$;

-- Función para ejecutar SQL dinámico (alias para compatibilidad)
CREATE OR REPLACE FUNCTION public.exec_sql(sql TEXT)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  EXECUTE sql;
END;
$$;

-- Verificar que las funciones se hayan creado correctamente
SELECT proname, pronamespace::regnamespace as schema
FROM pg_proc
WHERE proname IN ('pgsql', 'function_exists', 'exec_sql')
AND pronamespace = 'public'::regnamespace;
