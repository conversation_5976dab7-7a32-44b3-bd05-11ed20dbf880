import { useState, useEffect } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { CustomerEquipment } from '@/lib/types/service-management';
import { ServiceManagementService } from '@/lib/services/service-management-service';
import { formatDate } from '@/lib/utils';
import { useRouter } from 'next/navigation';
import { 
  Calendar, 
  AlertTriangle, 
  CheckCircle2, 
  Clock, 
  Wrench,
  ShieldCheck,
  ShieldAlert,
  MapPin
} from 'lucide-react';

interface CustomerEquipmentTableProps {
  clientId?: string;
  limit?: number;
  onRowClick?: (equipment: CustomerEquipment) => void;
}

export function CustomerEquipmentTable({
  clientId,
  limit = 10,
  onRowClick,
}: CustomerEquipmentTableProps) {
  const [equipment, setEquipment] = useState<CustomerEquipment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    const fetchEquipment = async () => {
      try {
        setLoading(true);
        const data = await ServiceManagementService.getClientEquipment(
          clientId || null,
          true,
          limit,
          0
        );
        setEquipment(data);
        setError(null);
      } catch (err) {
        console.error('Error al cargar equipos:', err);
        setError('Error al cargar equipos. Intente nuevamente.');
      } finally {
        setLoading(false);
      }
    };

    fetchEquipment();
  }, [clientId, limit]);

  const handleRowClick = (equipment: CustomerEquipment) => {
    if (onRowClick) {
      onRowClick(equipment);
    } else {
      router.push(`/dashboard/customer-equipment/${equipment.id}`);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return (
          <Badge variant="success" className="flex gap-1 items-center">
            <CheckCircle2 className="h-3 w-3" />
            Activo
          </Badge>
        );
      case 'inactive':
        return (
          <Badge variant="secondary" className="flex gap-1 items-center">
            <Clock className="h-3 w-3" />
            Inactivo
          </Badge>
        );
      case 'maintenance':
        return (
          <Badge variant="warning" className="flex gap-1 items-center">
            <Wrench className="h-3 w-3" />
            En mantenimiento
          </Badge>
        );
      case 'retired':
        return (
          <Badge variant="destructive" className="flex gap-1 items-center">
            <AlertTriangle className="h-3 w-3" />
            Retirado
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            {status}
          </Badge>
        );
    }
  };

  const getWarrantyBadge = (isUnderWarranty?: boolean, daysRemaining?: number) => {
    if (isUnderWarranty) {
      return (
        <Badge variant="outline" className="flex gap-1 items-center bg-green-50">
          <ShieldCheck className="h-3 w-3 text-green-500" />
          <span className="text-green-700">
            {daysRemaining && daysRemaining > 0 
              ? `${daysRemaining} días` 
              : 'Activa'}
          </span>
        </Badge>
      );
    } else {
      return (
        <Badge variant="outline" className="flex gap-1 items-center bg-red-50">
          <ShieldAlert className="h-3 w-3 text-red-500" />
          <span className="text-red-700">Vencida</span>
        </Badge>
      );
    }
  };

  const getMaintenanceBadge = (
    maintenanceOverdue?: boolean, 
    daysToNextMaintenance?: number | null
  ) => {
    if (maintenanceOverdue) {
      return (
        <Badge variant="destructive" className="flex gap-1 items-center">
          <AlertTriangle className="h-3 w-3" />
          Vencido
        </Badge>
      );
    } else if (daysToNextMaintenance !== undefined && daysToNextMaintenance !== null) {
      if (daysToNextMaintenance <= 7) {
        return (
          <Badge variant="warning" className="flex gap-1 items-center">
            <Clock className="h-3 w-3" />
            {daysToNextMaintenance} días
          </Badge>
        );
      } else {
        return (
          <Badge variant="outline" className="flex gap-1 items-center">
            <Calendar className="h-3 w-3" />
            {daysToNextMaintenance} días
          </Badge>
        );
      }
    } else {
      return (
        <Badge variant="outline" className="flex gap-1 items-center">
          <Calendar className="h-3 w-3" />
          No programado
        </Badge>
      );
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Equipos de Clientes</CardTitle>
          <CardDescription>
            Equipos registrados en el sistema
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {Array(5).fill(0).map((_, i) => (
              <Skeleton key={i} className="h-12 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Equipos de Clientes</CardTitle>
          <CardDescription>
            Equipos registrados en el sistema
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-destructive/10 p-4 rounded-md text-destructive">
            {error}
          </div>
          <Button 
            variant="outline" 
            className="mt-4"
            onClick={() => window.location.reload()}
          >
            Reintentar
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Equipos de Clientes</CardTitle>
        <CardDescription>
          {clientId ? 'Equipos del cliente seleccionado' : 'Todos los equipos registrados'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {equipment.length === 0 ? (
          <div className="text-center py-6 text-muted-foreground">
            No hay equipos para mostrar.
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nombre</TableHead>
                  <TableHead>Cliente</TableHead>
                  <TableHead>Ubicación</TableHead>
                  <TableHead>Estado</TableHead>
                  <TableHead>Garantía</TableHead>
                  <TableHead>Próximo mantenimiento</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {equipment.map((item) => (
                  <TableRow 
                    key={item.id}
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleRowClick(item)}
                  >
                    <TableCell className="font-medium">
                      <div>
                        {item.name}
                        {item.model && (
                          <div className="text-xs text-muted-foreground">
                            Modelo: {item.model}
                          </div>
                        )}
                        {item.serial_number && (
                          <div className="text-xs text-muted-foreground">
                            S/N: {item.serial_number}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{item.client_name || 'Sin cliente'}</TableCell>
                    <TableCell>
                      {item.location ? (
                        <div className="flex items-center gap-1">
                          <MapPin className="h-3 w-3" />
                          {item.location}
                        </div>
                      ) : (
                        <span className="text-muted-foreground">Sin ubicación</span>
                      )}
                    </TableCell>
                    <TableCell>{getStatusBadge(item.status)}</TableCell>
                    <TableCell>{getWarrantyBadge(item.is_under_warranty, item.warranty_days_remaining)}</TableCell>
                    <TableCell>
                      {getMaintenanceBadge(item.maintenance_overdue, item.days_to_next_maintenance)}
                      {item.next_maintenance_date && (
                        <div className="text-xs text-muted-foreground mt-1">
                          {formatDate(item.next_maintenance_date)}
                        </div>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
        <div className="mt-4 flex justify-end">
          <Button 
            variant="outline" 
            onClick={() => router.push('/dashboard/customer-equipment')}
          >
            Ver todos
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
