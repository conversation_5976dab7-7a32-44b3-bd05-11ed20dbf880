-- <PERSON><PERSON>t para configurar las funciones y tablas del dashboard
-- Ejecutar este script directamente en el Editor SQL de Supabase

-- 1. Crear tabla de métricas de rendimiento si no existe
CREATE TABLE IF NOT EXISTS public.performance_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  category TEXT NOT NULL,
  efficiency NUMERIC NOT NULL DEFAULT 0,
  completion_rate NUMERIC NOT NULL DEFAULT 0,
  quality_score NUMERIC NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Crear tabla de asignación de recursos si no existe
CREATE TABLE IF NOT EXISTS public.resource_allocation (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  category TEXT NOT NULL,
  amount NUMERIC NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Crear función para obtener distribución de proyectos
CREATE OR REPLACE FUNCTION public.get_project_distribution()
RETURNS TABLE (name TEXT, value NUMERIC)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Verificar si existe la tabla de proyectos
  IF EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = 'public' AND table_name = 'projects'
  ) THEN
    -- Devolver distribución real de proyectos por estado
    RETURN QUERY
    SELECT 
      COALESCE(status, 'Sin estado') AS name,
      COUNT(*)::NUMERIC AS value
    FROM public.projects
    GROUP BY status
    ORDER BY value DESC;
  ELSE
    -- Si no existe la tabla, devolver datos de ejemplo
    RETURN QUERY
    SELECT name, value FROM (
      VALUES 
        ('Activo', 42),
        ('Completado', 28),
        ('Planificación', 18),
        ('En pausa', 12)
    ) AS t(name, value);
  END IF;
END;
$$;

-- 4. Crear función para obtener métricas de rendimiento
CREATE OR REPLACE FUNCTION public.get_performance_metrics()
RETURNS TABLE (category TEXT, efficiency NUMERIC, completion_rate NUMERIC, quality_score NUMERIC)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Verificar si existe la tabla de métricas
  IF EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = 'public' AND table_name = 'performance_metrics'
  ) THEN
    -- Devolver datos reales
    RETURN QUERY
    SELECT 
      category,
      efficiency,
      completion_rate,
      quality_score
    FROM public.performance_metrics
    ORDER BY created_at DESC
    LIMIT 5;
  ELSE
    -- Si no existe la tabla, devolver datos de ejemplo
    RETURN QUERY
    SELECT * FROM (
      VALUES 
        ('Diseño', 85, 92, 78),
        ('Desarrollo', 78, 85, 90),
        ('Pruebas', 92, 88, 95),
        ('Implementación', 80, 75, 85),
        ('Soporte', 88, 90, 82)
    ) AS t(category, efficiency, completion_rate, quality_score);
  END IF;
END;
$$;

-- 5. Crear función para obtener métricas del dashboard
CREATE OR REPLACE FUNCTION public.get_dashboard_metrics()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSON;
  projects_count INTEGER := 0;
  active_projects_count INTEGER := 0;
  orders_count INTEGER := 0;
  pending_orders_count INTEGER := 0;
  documents_count INTEGER := 0;
  users_count INTEGER := 0;
BEGIN
  -- Verificar y contar proyectos
  IF EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = 'public' AND table_name = 'projects'
  ) THEN
    SELECT COUNT(*) INTO projects_count FROM public.projects;
    
    SELECT COUNT(*) INTO active_projects_count 
    FROM public.projects
    WHERE status IN ('active', 'in_progress', 'planning');
  END IF;
  
  -- Verificar y contar órdenes de trabajo
  IF EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = 'public' AND table_name = 'work_orders'
  ) THEN
    SELECT COUNT(*) INTO orders_count FROM public.work_orders;
    
    SELECT COUNT(*) INTO pending_orders_count 
    FROM public.work_orders
    WHERE status = 'pending';
  END IF;
  
  -- Verificar y contar documentos
  IF EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = 'public' AND table_name = 'documents'
  ) THEN
    SELECT COUNT(*) INTO documents_count FROM public.documents;
  END IF;
  
  -- Contar usuarios
  SELECT COUNT(*) INTO users_count FROM auth.users;
  
  -- Construir objeto JSON con los resultados
  result := json_build_object(
    'projectsCount', projects_count,
    'activeProjectsCount', active_projects_count,
    'ordersCount', orders_count,
    'pendingOrdersCount', pending_orders_count,
    'documentsCount', documents_count,
    'usersCount', users_count
  );
  
  RETURN result;
END;
$$;

-- 6. Crear función para eliminar un proyecto y sus datos relacionados
CREATE OR REPLACE FUNCTION public.delete_project(project_id UUID)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSONB;
  deleted_count INTEGER := 0;
  document_count INTEGER := 0;
  task_count INTEGER := 0;
  order_count INTEGER := 0;
  storage_count INTEGER := 0;
  project_exists BOOLEAN;
BEGIN
  -- Verificar si el proyecto existe
  SELECT EXISTS (
    SELECT 1 FROM public.projects WHERE id = project_id
  ) INTO project_exists;
  
  IF NOT project_exists THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'El proyecto no existe',
      'project_id', project_id
    );
  END IF;
  
  -- Eliminar documentos asociados al proyecto
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'documents') THEN
    WITH deleted AS (
      DELETE FROM public.documents
      WHERE project_id = project_id
      RETURNING id
    )
    SELECT COUNT(*) INTO document_count FROM deleted;
  END IF;
  
  -- Eliminar tareas asociadas al proyecto a través de órdenes de trabajo
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'work_order_tasks') AND
     EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'work_orders') THEN
    WITH deleted AS (
      DELETE FROM public.work_order_tasks
      WHERE work_order_id IN (
        SELECT id FROM public.work_orders WHERE project_id = project_id
      )
      RETURNING id
    )
    SELECT COUNT(*) INTO task_count FROM deleted;
  END IF;
  
  -- Eliminar órdenes de trabajo asociadas al proyecto
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'work_orders') THEN
    WITH deleted AS (
      DELETE FROM public.work_orders
      WHERE project_id = project_id
      RETURNING id
    )
    SELECT COUNT(*) INTO order_count FROM deleted;
  END IF;
  
  -- Eliminar archivos de almacenamiento asociados al proyecto
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'storage' AND table_name = 'objects') THEN
    WITH deleted AS (
      DELETE FROM storage.objects
      WHERE bucket_id = 'projects' AND path LIKE project_id || '/%'
      RETURNING id
    )
    SELECT COUNT(*) INTO storage_count FROM deleted;
  END IF;
  
  -- Finalmente, eliminar el proyecto
  DELETE FROM public.projects
  WHERE id = project_id;
  
  -- Incrementar el contador de eliminados si se eliminó el proyecto
  IF FOUND THEN
    deleted_count := 1;
  END IF;
  
  -- Construir el resultado
  result := jsonb_build_object(
    'success', true,
    'message', 'Proyecto eliminado correctamente',
    'project_id', project_id,
    'deleted_count', deleted_count,
    'related_data', jsonb_build_object(
      'documents', document_count,
      'tasks', task_count,
      'orders', order_count,
      'storage_files', storage_count
    )
  );
  
  RETURN result;
END;
$$;

-- 7. Insertar datos de ejemplo en la tabla de métricas de rendimiento si está vacía
INSERT INTO public.performance_metrics (category, efficiency, completion_rate, quality_score)
SELECT * FROM (
  VALUES 
    ('Diseño', 85, 92, 78),
    ('Desarrollo', 78, 85, 90),
    ('Pruebas', 92, 88, 95),
    ('Implementación', 80, 75, 85),
    ('Soporte', 88, 90, 82)
) AS t(category, efficiency, completion_rate, quality_score)
WHERE NOT EXISTS (SELECT 1 FROM public.performance_metrics LIMIT 1);

-- 8. Insertar datos de ejemplo en la tabla de asignación de recursos si está vacía
INSERT INTO public.resource_allocation (category, amount)
SELECT * FROM (
  VALUES 
    ('Personal', 45000),
    ('Equipamiento', 28000),
    ('Software', 15000),
    ('Servicios', 12000),
    ('Otros', 5000)
) AS t(category, amount)
WHERE NOT EXISTS (SELECT 1 FROM public.resource_allocation LIMIT 1);

-- 9. Habilitar RLS en las tablas
ALTER TABLE public.performance_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.resource_allocation ENABLE ROW LEVEL SECURITY;

-- 10. Crear políticas para la tabla de métricas de rendimiento
DROP POLICY IF EXISTS "Allow authenticated users to read performance_metrics" ON public.performance_metrics;
CREATE POLICY "Allow authenticated users to read performance_metrics"
  ON public.performance_metrics FOR SELECT
  USING (auth.role() = 'authenticated');

-- 11. Crear políticas para la tabla de asignación de recursos
DROP POLICY IF EXISTS "Allow authenticated users to read resource_allocation" ON public.resource_allocation;
CREATE POLICY "Allow authenticated users to read resource_allocation"
  ON public.resource_allocation FOR SELECT
  USING (auth.role() = 'authenticated');
