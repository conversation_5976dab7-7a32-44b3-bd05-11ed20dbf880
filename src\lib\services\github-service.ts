/**
 * @file Servicio para gestionar la integración con GitHub
 * @description Implementa un servicio para gestionar la conexión con GitHub y sincronizar repositorios
 *
 * @example
 * // En client components (páginas con 'use client')
 * const githubService = new GitHubService(); // Usa cliente del navegador automáticamente
 * // O explícitamente:
 * const githubService = GitHubService.createClientSide();
 *
 * @example
 * // Detección automática de contexto
 * const githubService = await GitHubService.create();
 */

import { createClient as createBrowserClient } from '@/lib/supabase/client';

// Tipos para la integración con GitHub
export interface GitHubConnection {
  id: string;
  user_id: string;
  access_token: string;
  refresh_token?: string;
  token_expires_at?: string;
  github_user_id: string;
  github_username: string;
  github_email?: string;
  github_avatar_url?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  last_synced_at?: string;
}

export interface GitHubRepository {
  id: string;
  connection_id: string;
  repository_id: string;
  repository_name: string;
  repository_full_name: string;
  repository_url: string;
  repository_description?: string;
  is_private: boolean;
  default_branch: string;
  created_at: string;
  updated_at: string;
  last_synced_at?: string;
}

export interface GitHubProjectSync {
  id: string;
  project_id: string;
  repository_id: string;
  sync_enabled: boolean;
  auto_sync: boolean;
  sync_interval_minutes: number;
  sync_branch: string;
  sync_path: string;
  created_at: string;
  updated_at: string;
  last_synced_at?: string;
}

// Clase para el servicio de GitHub
export class GitHubService {
  private supabase: unknown;

  /**
   * Constructor que maneja tanto uso público como privado
   * Sin argumentos: crea cliente del navegador para compatibilidad
   * Con argumento: constructor privado para métodos estáticos
   */
  constructor(supabase?: unknown) {
    if (supabase) {
      // Constructor privado usado por métodos estáticos
      this.supabase = supabase;
    } else {
      // Constructor público para compatibilidad con código existente
      // Crear cliente del lado del navegador por defecto
      this.supabase = createBrowserClient();
    }
  }

  /**
   * Crea una instancia para uso en client components
   * Seguro para usar en cualquier contexto del navegador
   */
  static create() {
    const supabase = createBrowserClient();
    return new GitHubService(supabase);
  }

  /**
   * Obtiene la conexión de GitHub del usuario actual
   */
  async getUserConnection(): Promise<GitHubConnection | null> {
    try {
      // No pasar parámetros a getUser() para compatibilidad con todas las versiones de Supabase
      const { data: authData, error: authError } = await this.supabase.auth.getUser();

      if (authError || !authData.user) {
        console.error('Error al obtener usuario autenticado:', authError);
        return null;
      }

      const { data, error } = await this.supabase
        .from('github_connections')
        .select('*')
        .eq('user_id', authData.user.id)
        .eq('is_active', true)
        .single();

      if (error) {
        console.error('Error al obtener conexión de GitHub:', error);
        return null;
      }

      return data as GitHubConnection;
    } catch (error) {
      console.error('Error en getUserConnection:', error);
      return null;
    }
  }

  /**
   * Obtiene los repositorios de GitHub del usuario
   */
  async getUserRepositories(): Promise<GitHubRepository[]> {
    try {
      const connection = await this.getUserConnection();

      if (!connection) {
        return [];
      }

      const { data, error } = await this.supabase
        .from('github_repositories')
        .select('*')
        .eq('connection_id', connection.id)
        .order('repository_name', { ascending: true });

      if (error) {
        console.error('Error al obtener repositorios de GitHub:', error);
        return [];
      }

      return data as GitHubRepository[];
    } catch (error) {
      console.error('Error en getUserRepositories:', error);
      return [];
    }
  }

  /**
   * Sincroniza los repositorios de GitHub del usuario
   */
  async syncUserRepositories(): Promise<boolean> {
    try {
      const connection = await this.getUserConnection();

      if (!connection) {
        return false;
      }

      // Llamar a la API de GitHub para obtener los repositorios
      const response = await fetch('https://api.github.com/user/repos', {
        headers: {
          'Authorization': `token ${connection.access_token}`,
          'Accept': 'application/vnd.github.v3+json'
        }
      });

      if (!response.ok) {
        console.error('Error al obtener repositorios de GitHub API:', response.statusText);
        return false;
      }

      const repositories = await response.json();

      // Actualizar la conexión con la última fecha de sincronización
      await this.supabase
        .from('github_connections')
        .update({ last_synced_at: new Date().toISOString() })
        .eq('id', connection.id);

      // Insertar o actualizar los repositorios en la base de datos
      for (const repo of repositories) {
        const { data: existingRepo } = await this.supabase
          .from('github_repositories')
          .select('id')
          .eq('connection_id', connection.id)
          .eq('repository_id', repo.id.toString())
          .single();

        const repoData = {
          connection_id: connection.id,
          repository_id: repo.id.toString(),
          repository_name: repo.name,
          repository_full_name: repo.full_name,
          repository_url: repo.html_url,
          repository_description: repo.description,
          is_private: repo.private,
          default_branch: repo.default_branch,
          last_synced_at: new Date().toISOString()
        };

        if (existingRepo) {
          await this.supabase
            .from('github_repositories')
            .update(repoData)
            .eq('id', existingRepo.id);
        } else {
          await this.supabase
            .from('github_repositories')
            .insert(repoData);
        }
      }

      return true;
    } catch (error) {
      console.error('Error en syncUserRepositories:', error);
      return false;
    }
  }
}
