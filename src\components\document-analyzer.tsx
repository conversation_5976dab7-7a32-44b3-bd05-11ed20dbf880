import { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { createClient } from '@/lib/supabase/client'
import { Loader2, FileText, AlertCircle } from 'lucide-react'
import { toast } from '@/hooks/use-toast'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Database } from '@/lib/supabase/types'

// Interfaces para tipos de datos
interface DocumentData {
  id: string;
  filename: string;
  file_path: string;
  file_url: string;
  file_type: string;
  file_size: number;
  uploaded_by: string;
}

interface AnalysisData {
  id: string;
  document_id: string;
  provider: string;
  status: string;
  started_at: string;
}

interface AnalysisResult {
  success: boolean;
  message: string;
  analysisId?: string;
  status?: string;
}

export function DocumentAnalyzer() {
  const [file, setFile] = useState<File | null>(null)
  const [analyzing, setAnalyzing] = useState(false)
  const [result, setResult] = useState<AnalysisResult | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [provider, setProvider] = useState('gemini')

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0])
      setError(null)
      setResult(null)
    }
  }

  const analyzeDocument = async () => {
    try {
      setAnalyzing(true)
      setError(null)
      setResult(null)

      if (!file) {
        throw new Error('Por favor selecciona un archivo')
      }

      // Crear cliente de Supabase
      const supabase = createClient()

      // 1. Subir el archivo
      const fileName = `${Date.now()}-${file.name}`
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('documents')
        .upload(fileName, file)

      if (uploadError) {
        throw new Error('Error al subir el archivo: ' + uploadError.message)
      }

      // 2. Crear registro del documento
      // Generar URL del archivo
      const fileUrl = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/documents/${fileName}`;

      console.log('Creando registro de documento con ruta:', fileName);
      console.log('URL del archivo:', fileUrl);

      // Obtener el ID del usuario actual
      const { data: userData } = await supabase.auth.getUser();
      const userId = userData?.user?.id;

      // Insertar documento con tipos apropiados
      const { data, error: docError } = await supabase
        .from('documents')
        .insert([
          {
            filename: file.name,
            file_path: fileName,
            file_url: fileUrl, // Agregar file_url que es obligatorio
            file_type: file.type,
            file_size: file.size,
            uploaded_by: userId // Agregar el ID del usuario que sube el documento
          }
        ])
        .select()
        .single()

      // Usar tipo específico para el documento
      const docData = data as DocumentData

      if (docError || !docData) {
        throw new Error('Error al crear el registro del documento: ' + docError?.message)
      }

      console.log('Documento creado exitosamente:', {
        id: docData.id,
        filename: docData.filename,
        file_path: docData.file_path
      });

      // 3. Llamar a la función de análisis
      // Obtener la sesión actual
      const { data: sessionData } = await supabase.auth.getSession();
      const session = sessionData.session;

      if (!session || !session.access_token) {
        throw new Error('No hay sesión activa. Por favor inicia sesión nuevamente.');
      }

      console.log('Enviando solicitud de análisis para el documento:', docData.id);
      console.log('Usando proveedor:', provider);
      console.log('Token de autorización disponible:', !!session.access_token);

      let analysisResult;

      // Intentar usar la función Edge directamente
      try {
        console.log('Usando token de acceso de la sesión para autenticación');
        // Refrescar la sesión para obtener un token válido
        const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();

        if (refreshError || !refreshData.session) {
          console.error('Error al refrescar la sesión:', refreshError?.message || 'No se pudo obtener una sesión válida');
          throw new Error('Error de autenticación. Por favor, inicia sesión nuevamente.');
        }

        // Usar el token de acceso actualizado
        const accessToken = refreshData.session.access_token;

        // Verificar que el documento existe antes de enviarlo para análisis
        const { data: docCheck, error: docCheckError } = await supabase
          .from('documents')
          .select('id, filename, file_path')
          .eq('id', docData.id)
          .single();

        if (docCheckError || !docCheck) {
          console.error('Error al verificar el documento antes del análisis:', docCheckError?.message || 'Documento no encontrado');
          throw new Error(`El documento con ID ${docData.id} no se encuentra en la base de datos. Por favor, intente nuevamente.`);
        }

        console.log('Documento verificado antes del análisis:', docCheck);

        const response = await fetch('https://xdboxokpjubowptrcytl.supabase.co/functions/v1/analyze-document', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${accessToken}`,
            // Incluir el ID de usuario explícitamente
            'X-User-ID': refreshData.session.user.id
          },
          body: JSON.stringify({
            documentId: docData.id,
            provider: provider,
            userId: refreshData.session.user.id, // Incluir el ID de usuario en el cuerpo también
            config: {
              temperature: 0.7,
              // Usar max_completion_tokens para OpenAI
              ...(provider === 'openai' ? { max_completion_tokens: 2000 } : { maxTokens: 2000 })
            }
          })
        });

        if (!response.ok) {
          let errorMessage = 'Error al analizar el documento';
          try {
            const errorData = await response.json();
            console.error('Error detallado:', errorData);

            // Manejar específicamente el error de documento no encontrado
            if (errorData.message && errorData.message.includes('Document not found')) {
              console.error('Error de documento no encontrado. ID del documento:', docData.id);
              errorMessage = 'No se pudo encontrar el documento en la base de datos. Por favor, intente subir el documento nuevamente.';

              // Registrar información adicional para diagnóstico
              console.log('Detalles del documento que causó el error:', {
                id: docData.id,
                filename: docData.filename,
                file_path: docData.file_path,
                file_url: docData.file_url
              });
            } else if (errorData.message && errorData.message.includes('Multiple documents')) {
              // Error de integridad de la base de datos (múltiples documentos con el mismo ID)
              errorMessage = 'Error de integridad en la base de datos. Por favor, contacte al administrador.';
            } else {
              // Otros errores
              errorMessage += ': ' + (errorData.error || errorData.message || response.statusText);
            }
          } catch (jsonError) {
            console.error('Error al parsear la respuesta de error:', jsonError);
            errorMessage += ': ' + response.statusText + ' (Status: ' + response.status + ')';
          }
          throw new Error(errorMessage);
        }

        analysisResult = await response.json();
        console.log('Respuesta de la función Edge:', analysisResult);

        // Verificar si la respuesta incluye el ID del análisis
        if (analysisResult.analysisId) {
          console.log('ID del análisis recibido de la función Edge:', analysisResult.analysisId);
        } else {
          console.warn('ADVERTENCIA: La respuesta de la función Edge no incluye el ID del análisis');
          console.log('Respuesta completa:', JSON.stringify(analysisResult, null, 2));
        }
      } catch (fetchError) {
        console.error('Error al llamar a la función Edge:', fetchError);

        // Plan B: Usar la API de Supabase directamente
        console.log('Intentando alternativa: Usar la API de Supabase directamente');

        // 1. Crear un registro de análisis pendiente
        const { data: analysisData, error: analysisError } = await supabase
          .from('ai_document_analyses')
          .insert([
            {
              document_id: docData.id,
              provider: provider,
              status: 'pending',
              started_at: new Date().toISOString()
            }
          ])
          .select()
          .single()

        const analysisDataTyped = analysisData as AnalysisData;

        if (analysisError) {
          throw new Error('Error al crear el registro de análisis: ' + analysisError.message);
        }

        // Usar el resultado alternativo
        analysisResult = {
          success: true,
          message: 'Análisis iniciado correctamente (modo alternativo)',
          analysisId: analysisDataTyped.id,
          status: 'pending'
        };
      }

      // Depurar el resultado antes de establecerlo
      console.log('Tipo de resultado:', typeof analysisResult);
      console.log('Propiedades del resultado:', Object.keys(analysisResult));
      console.log('Valor de analysisResult.analysisId:', analysisResult.analysisId);

      // Establecer el resultado
      setResult(analysisResult);

      // Verificar si el resultado incluye el ID del análisis
      if (analysisResult.analysisId) {
        console.log(`Análisis iniciado correctamente. ID: ${analysisResult.analysisId}`);
      } else {
        console.warn('ADVERTENCIA: El ID del análisis es undefined. Respuesta completa:', JSON.stringify(analysisResult, null, 2));
      }

      // Verificar si el resultado incluye el ID del análisis para la notificación
      const analysisId = analysisResult.analysisId || 'No disponible';
      console.log(`ID del análisis para la notificación: ${analysisId}`);

      toast({
        title: "Análisis completado",
        description: `El documento ha sido analizado exitosamente. ID: ${analysisId}`,
      })

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error inesperado')
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : 'Error inesperado',
        variant: "destructive"
      })
    } finally {
      setAnalyzing(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Analizador de Documentos</CardTitle>
        <CardDescription>
          Sube un documento para probarlo con el servicio de análisis de IA
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label>Proveedor de IA</Label>
          <Select value={provider} onValueChange={setProvider}>
            <SelectTrigger>
              <SelectValue placeholder="Selecciona un proveedor" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="openai">OpenAI</SelectItem>
              <SelectItem value="gemini">Google Gemini</SelectItem>
              <SelectItem value="deepseek">DeepSeek</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="document">Documento</Label>
          <input
            id="document"
            type="file"
            onChange={handleFileChange}
            accept=".pdf,.doc,.docx,.txt"
            className="mt-2 block w-full text-sm text-slate-500
              file:mr-4 file:py-2 file:px-4
              file:rounded-full file:border-0
              file:text-sm file:font-semibold
              file:bg-violet-50 file:text-violet-700
              hover:file:bg-violet-100"
          />
        </div>

        <Button
          onClick={analyzeDocument}
          disabled={!file || analyzing}
          className="w-full"
        >
          {analyzing ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Analizando...
            </>
          ) : (
            <>
              <FileText className="mr-2 h-4 w-4" />
              Analizar Documento
            </>
          )}
        </Button>

        {error && (
          <div className="p-4 text-red-500 bg-red-50 rounded border border-red-200">
            <div className="flex items-start">
              <AlertCircle className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-semibold mb-1">Error al analizar el documento</h4>
                <p>{error}</p>
                {error.includes('No se pudo encontrar el documento') && (
                  <p className="mt-2 text-sm">
                    Sugerencia: Intente subir el documento nuevamente o seleccione otro documento.
                  </p>
                )}
              </div>
            </div>
          </div>
        )}

        {result && (
          <div className="mt-4 p-4 bg-green-50 rounded">
            <h3 className="font-semibold mb-2">Resultados del análisis:</h3>
            <pre className="whitespace-pre-wrap text-sm">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        )}
      </CardContent>
    </Card>
  )
}