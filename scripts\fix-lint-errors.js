#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * Script para corregir automáticamente errores comunes de ESLint
 */

// Configuración de patrones de corrección
const FIXES = {
  // Variables no utilizadas
  UNUSED_VARS: {
    pattern: /Error: '([^']+)' is (defined but never used|assigned a value but never used)\./g,
    fix: (match, varName, type) => {
      console.log(`🔧 Eliminando variable no utilizada: ${varName}`);
      return null; // Se manejará en el procesamiento de archivos
    }
  },
  
  // Tipos any explícitos
  EXPLICIT_ANY: {
    pattern: /Error: Unexpected any\. Specify a different type\./g,
    fix: (match) => {
      console.log(`🔧 Reemplazando tipo 'any' explícito`);
      return 'unknown'; // Reemplazar any con unknown
    }
  },
  
  // Entidades HTML no escapadas
  UNESCAPED_ENTITIES: {
    pattern: /Error: `([^`]+)` can be escaped with `([^`]+)`/g,
    fix: (match, entity, escaped) => {
      console.log(`🔧 Escapando entidad HTML: ${entity} -> ${escaped}`);
      return escaped;
    }
  }
};

/**
 * Obtiene la lista de errores de ESLint
 */
function getLintErrors() {
  try {
    console.log('📋 Obteniendo errores de ESLint...');
    const output = execSync('npm run lint', { 
      encoding: 'utf8',
      cwd: process.cwd()
    });
    return output;
  } catch (error) {
    // ESLint devuelve código de salida != 0 cuando hay errores
    return error.stdout || error.message;
  }
}

/**
 * Parsea los errores de ESLint y los agrupa por archivo
 */
function parseErrors(lintOutput) {
  const errors = {};
  const lines = lintOutput.split('\n');
  let currentFile = null;
  
  for (const line of lines) {
    // Detectar archivo actual
    const fileMatch = line.match(/^\.\/(.+\.tsx?)$/);
    if (fileMatch) {
      currentFile = fileMatch[1];
      errors[currentFile] = [];
      continue;
    }
    
    // Detectar errores
    const errorMatch = line.match(/^(\d+):(\d+)\s+Error:\s+(.+)$/);
    if (errorMatch && currentFile) {
      const [, lineNum, colNum, message] = errorMatch;
      errors[currentFile].push({
        line: parseInt(lineNum),
        column: parseInt(colNum),
        message: message.trim()
      });
    }
  }
  
  return errors;
}

/**
 * Corrige variables no utilizadas en un archivo
 */
function fixUnusedVariables(filePath, errors) {
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  let modified = false;
  
  // Obtener variables no utilizadas
  const unusedVars = errors
    .filter(error => error.message.includes('is defined but never used') || 
                    error.message.includes('is assigned a value but never used'))
    .map(error => {
      const match = error.message.match(/'([^']+)'/);
      return match ? match[1] : null;
    })
    .filter(Boolean);
  
  if (unusedVars.length === 0) return false;
  
  console.log(`🔧 Corrigiendo variables no utilizadas en ${filePath}:`, unusedVars);
  
  // Procesar líneas
  for (let i = 0; i < lines.length; i++) {
    let line = lines[i];
    
    // Eliminar importaciones no utilizadas
    for (const varName of unusedVars) {
      // Importación individual: import { varName } from '...'
      const singleImportRegex = new RegExp(`import\\s*{\\s*${varName}\\s*}\\s*from`, 'g');
      if (singleImportRegex.test(line)) {
        lines[i] = `// ${line} // Removed unused import`;
        modified = true;
        continue;
      }
      
      // Importación múltiple: import { var1, varName, var2 } from '...'
      const multiImportRegex = new RegExp(`(import\\s*{[^}]*),\\s*${varName}\\s*([^}]*})`, 'g');
      if (multiImportRegex.test(line)) {
        lines[i] = line.replace(multiImportRegex, '$1$2');
        modified = true;
        continue;
      }
      
      // Variable al inicio de importación múltiple
      const firstVarRegex = new RegExp(`(import\\s*{)\\s*${varName}\\s*,([^}]*})`, 'g');
      if (firstVarRegex.test(line)) {
        lines[i] = line.replace(firstVarRegex, '$1$2');
        modified = true;
        continue;
      }
      
      // Declaración de variable: const varName = ...
      const varDeclRegex = new RegExp(`^\\s*(const|let|var)\\s+${varName}\\s*=`, 'g');
      if (varDeclRegex.test(line)) {
        lines[i] = `// ${line} // Removed unused variable`;
        modified = true;
        continue;
      }
    }
  }
  
  if (modified) {
    fs.writeFileSync(filePath, lines.join('\n'));
    console.log(`✅ Archivo ${filePath} actualizado`);
  }
  
  return modified;
}

/**
 * Corrige tipos 'any' explícitos
 */
function fixExplicitAny(filePath, errors) {
  const content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  const anyErrors = errors.filter(error => 
    error.message.includes('Unexpected any. Specify a different type')
  );
  
  if (anyErrors.length === 0) return false;
  
  console.log(`🔧 Corrigiendo tipos 'any' en ${filePath}`);
  
  // Reemplazar 'any' con 'unknown' en contextos seguros
  let newContent = content
    .replace(/:\s*any\b/g, ': unknown')
    .replace(/\<any\>/g, '<unknown>')
    .replace(/as any\b/g, 'as unknown');
  
  if (newContent !== content) {
    fs.writeFileSync(filePath, newContent);
    console.log(`✅ Archivo ${filePath} actualizado`);
    modified = true;
  }
  
  return modified;
}

/**
 * Función principal
 */
function main() {
  console.log('🚀 Iniciando corrección automática de errores de ESLint...\n');
  
  // Obtener errores
  const lintOutput = getLintErrors();
  const errorsByFile = parseErrors(lintOutput);
  
  const fileCount = Object.keys(errorsByFile).length;
  console.log(`📁 Archivos con errores encontrados: ${fileCount}\n`);
  
  if (fileCount === 0) {
    console.log('✅ No se encontraron errores de ESLint para corregir.');
    return;
  }
  
  let totalFixed = 0;
  
  // Procesar cada archivo
  for (const [filePath, errors] of Object.entries(errorsByFile)) {
    if (errors.length === 0) continue;
    
    console.log(`\n📄 Procesando: ${filePath} (${errors.length} errores)`);
    
    let fileFixed = false;
    
    // Corregir variables no utilizadas
    if (fixUnusedVariables(filePath, errors)) {
      fileFixed = true;
    }
    
    // Corregir tipos any
    if (fixExplicitAny(filePath, errors)) {
      fileFixed = true;
    }
    
    if (fileFixed) {
      totalFixed++;
    }
  }
  
  console.log(`\n🎉 Corrección completada!`);
  console.log(`📊 Archivos modificados: ${totalFixed}/${fileCount}`);
  
  // Ejecutar lint nuevamente para ver el progreso
  console.log('\n🔍 Ejecutando ESLint nuevamente para verificar el progreso...');
  try {
    execSync('npm run lint', { stdio: 'inherit' });
  } catch (error) {
    console.log('\n⚠️  Aún quedan errores por corregir manualmente.');
  }
}

// Ejecutar si es llamado directamente
if (require.main === module) {
  main();
}

module.exports = { main, parseErrors, fixUnusedVariables, fixExplicitAny };
