import { createClient } from '@supabase/supabase-js'
import { supabaseConfig, validateSupabaseConfig } from './config'

// Validar la configuración antes de crear el cliente
const isConfigValid = validateSupabaseConfig()

if (!isConfigValid) {
  console.error('Supabase configuration is invalid. Admin client may not work properly.')
  console.error('URL:', supabaseConfig.url ? 'Configured' : 'Missing')
  console.error('Anon Key:', supabaseConfig.anonKey ? 'Configured' : 'Missing')
  console.error('Service Role Key:', supabaseConfig.serviceRoleKey ? 'Configured' : 'Missing')
}

// Crear un cliente de Supabase con la clave de servicio para operaciones de administración
export const supabaseAdmin = supabaseConfig.url && supabaseConfig.serviceRoleKey
  ? createClient(
      supabaseConfig.url,
      supabaseConfig.serviceRoleKey,
      {
        auth: {
          autoRefreshToken: true, // Cambiar a true para mantener la sesión activa
          persistSession: true,    // Cambiar a true para persistir la sesión
        },
      }
    )
  : null

// Función para obtener usuarios desde la tabla pública como alternativa
export async function getUsersFromPublicTable() {
  try {
    if (!supabaseAdmin) {
      console.error('Admin client not available. Using regular client instead.')
      const regularClient = createClient(
        supabaseConfig.url,
        supabaseConfig.anonKey
      )
      const { data, error } = await regularClient
        .from('users')
        .select('*')

      if (error) {
        console.error('Error fetching users from public table:', error)
        return []
      }

      return data || []
    }

    const { data, error } = await supabaseAdmin
      .from('users')
      .select('*')

    if (error) {
      console.error('Error fetching users from public table:', error)
      return []
    }

    return data || []
  } catch (error) {
    console.error('Exception fetching users from public table:', error)
    return []
  }
}

// Función para obtener usuarios desde la API de administración
export async function getUsersFromAdminApi() {
  try {
    if (!supabaseAdmin) {
      console.error('Admin client not available. Falling back to public table.')
      return getUsersFromPublicTable()
    }

    const { data, error } = await supabaseAdmin.auth.admin.listUsers()

    if (error) {
      console.error('Error fetching users from admin API:', error)
      return getUsersFromPublicTable() // Fallback a la tabla pública
    }

    // Transformar los datos para adaptarlos al formato esperado
    return data.users.map(user => ({
      id: user.id,
      email: user.email || '',
      full_name: user.user_metadata?.full_name || '',
      role: user.user_metadata?.role || ['user'],
      status: user.banned ? 'suspended' : (user.confirmed_at ? 'active' : 'inactive'),
      created_at: user.created_at,
      last_sign_in_at: user.last_sign_in_at,
    }))
  } catch (error) {
    console.error('Exception fetching users from admin API:', error)
    return getUsersFromPublicTable() // Fallback a la tabla pública en caso de error
  }
}
