"use client"

import { useState, lazy, Suspense } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { createClient } from "@/lib/supabase/client"
import { toast } from "@/components/shared/ui/use-toast"

// Lazy load WorkOrderForm component
const WorkOrderForm = lazy(() => import("@/components/features/work-orders/work-order-form").then(mod => ({ default: mod.WorkOrderForm })))

function NewWorkOrderContentInner() {
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()
  const searchParams = useSearchParams()
  const projectId = searchParams.get('project')
  const supabase = createClient()

  const handleSubmit = async (data: unknown) => {
    setIsLoading(true)
    try {
      // Insertar la nueva orden de trabajo en la base de datos
      const { data: workOrderData, error: workOrderError } = await supabase.from("work_orders").insert({
        title: data.title,
        description: data.description,
        status: data.status,
        priority: data.priority,
        project_id: data.project_id,
        assigned_to: data.assigned_to, // Mantenemos para compatibilidad
        due_date: data.due_date,
      }).select()

      if (workOrderError) throw workOrderError

      // Si hay usuarios asignados, insertarlos en la tabla work_order_users
      if (data.assigned_users && data.assigned_users.length > 0 && workOrderData && workOrderData[0]) {
        const workOrderId = workOrderData[0].id

        // Preparar los datos para insertar en work_order_users
        const workOrderUsersData = data.assigned_users.map((userId: string) => ({
          work_order_id: workOrderId,
          user_id: userId,
          role: 'assignee'
        }))

        const { error: usersError } = await supabase.from("work_order_users").insert(workOrderUsersData)

        if (usersError) {
          console.error("Error al asignar usuarios a la orden de trabajo:", usersError)
          // No lanzamos error para que no impida la creación de la orden de trabajo
        }
      }

      toast({
        title: "Orden de trabajo creada",
        description: "La orden de trabajo se ha creado correctamente.",
      })

      // Redirigir a la lista de órdenes de trabajo
      router.push("/dashboard/work-orders")
      router.refresh()
    } catch (error: unknown) {
      console.error("Error al crear la orden de trabajo:", error)
      const errorMessage = error instanceof Error ? error.message : "Ha ocurrido un error al crear la orden de trabajo.";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <WorkOrderForm
      onSubmit={handleSubmit}
      isLoading={isLoading}
      projectId={projectId || undefined}
    />
  )
}

export function NewWorkOrderContent() {
  return (
    <Suspense fallback={<div className="p-8 text-center">Cargando...</div>}>
      <NewWorkOrderContentInner />
    </Suspense>
  )
}
