import { NextResponse } from 'next/server'

export async function GET() {
  try {
    // Simulamos la obtención de logs
    // En una implementación real, esto obtendría los logs del servicio
    
    // Generamos algunos logs de ejemplo
    const currentDate = new Date()
    const logs = [
      `[${formatDate(currentDate)}] [INFO] Servicio de análisis de documentos iniciado`,
      `[${formatDate(new Date(currentDate.getTime() - 60000))}] [INFO] Conexión establecida con PDF Extractor`,
      `[${formatDate(new Date(currentDate.getTime() - 120000))}] [INFO] Conexión establecida con LM Studio`,
      `[${formatDate(new Date(currentDate.getTime() - 180000))}] [INFO] Caché inicializada correctamente`,
      `[${formatDate(new Date(currentDate.getTime() - 240000))}] [INFO] Servicio listo para procesar documentos`,
      `[${formatDate(new Date(currentDate.getTime() - 300000))}] [DEBUG] Verificando conexiones con servicios externos`,
      `[${formatDate(new Date(currentDate.getTime() - 360000))}] [INFO] Todas las conexiones OK`,
      `[${formatDate(new Date(currentDate.getTime() - 420000))}] [DEBUG] Limpieza de caché programada para medianoche`,
      `[${formatDate(new Date(currentDate.getTime() - 480000))}] [INFO] Modelos cargados: gemini-1.5-flash, llama3-8b`,
      `[${formatDate(new Date(currentDate.getTime() - 540000))}] [DEBUG] Verificación de estado completada`,
    ]
    
    return NextResponse.json({ logs })
  } catch (error) {
    console.error('Error al obtener los logs:', error)
    return NextResponse.json(
      { error: 'Error al obtener los logs' },
      { status: 500 }
    )
  }
}

// Función auxiliar para formatear fechas
function formatDate(date: Date): string {
  return date.toISOString().replace('T', ' ').substring(0, 19)
}
