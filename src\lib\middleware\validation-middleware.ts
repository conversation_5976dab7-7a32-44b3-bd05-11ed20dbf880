/**
 * @file Validation Middleware
 * @description Middleware for validating request data using Zod schemas
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { dataValidator } from '@/lib/services/data-validator-service';
import { errorHandler } from '@/lib/services/error-handler-service';
import { logger } from '@/lib/services/logger-service';

/**
 * Interface for validation error response
 */
export interface ValidationErrorResponse {
  success: false;
  errors: {
    field?: string;
    message: string;
  }[];
}

/**
 * Middleware for validating request data using Zod schemas
 *
 * @param schema Zod schema to validate against
 * @param handler Request handler function
 * @returns Handler function with validation
 */
export function withValidation<T extends z.ZodTypeAny>(
  schema: T,
  handler: (req: NextRequest, data: { data: z.infer<T> }) => Promise<NextResponse> | NextResponse
) {
  return async (req: NextRequest): Promise<NextResponse> => {
    try {
      // Parse request body as JSON
      const body = await req.json();

      // Validate against schema
      const result = schema.safeParse(body);

      if (!result.success) {
        // Format validation errors
        const errors = result.error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message,
        }));

        // Log validation errors
        logger.warn('Validation error', {
          path: req.nextUrl.pathname,
          errors
        });

        // Return validation error response
        return NextResponse.json(
          {
            success: false,
            errors
          } as ValidationErrorResponse,
          { status: 400 }
        );
      }

      // If validation passes, call the handler with validated data
      return handler(req, { data: result.data });
    } catch (error) {
      // Handle parsing errors
      const errorDetails = errorHandler.handleError(error);

      logger.error('Request validation error', {
        path: req.nextUrl.pathname,
        error: errorDetails
      });

      return NextResponse.json(
        {
          success: false,
          errors: [{ message: 'Invalid request data' }]
        } as ValidationErrorResponse,
        { status: 400 }
      );
    }
  };
}

/**
 * Middleware for validating required fields in request data
 *
 * @param requiredFields Array of required field names
 * @param handler Request handler function
 * @returns Handler function with required field validation
 */
export function withRequiredFields(
  requiredFields: string[],
  handler: (req: NextRequest, data: unknown) => Promise<NextResponse> | NextResponse
) {
  return async (req: NextRequest): Promise<NextResponse> => {
    try {
      // Parse request body as JSON
      const body = await req.json();

      // Validate required fields
      const { isValid, errors } = dataValidator.validateRequiredFields(body, requiredFields);

      if (!isValid) {
        // Format validation errors
        const formattedErrors = Object.entries(errors).map(([field, message]) => ({
          field,
          message,
        }));

        // Log validation errors
        logger.warn('Required field validation error', {
          path: req.nextUrl.pathname,
          errors: formattedErrors
        });

        // Return validation error response
        return NextResponse.json(
          {
            success: false,
            errors: formattedErrors
          } as ValidationErrorResponse,
          { status: 400 }
        );
      }

      // If validation passes, call the handler with validated data
      return handler(req, body);
    } catch (error) {
      // Handle parsing errors
      const errorDetails = errorHandler.handleError(error);

      logger.error('Request validation error', {
        path: req.nextUrl.pathname,
        error: errorDetails
      });

      return NextResponse.json(
        {
          success: false,
          errors: [{ message: 'Invalid request data' }]
        } as ValidationErrorResponse,
        { status: 400 }
      );
    }
  };
}
