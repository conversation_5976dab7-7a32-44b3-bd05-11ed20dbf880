import { createClient } from '@/lib/supabase/client';
import {
  ServiceContract,
  ServiceContractDetails,
  ServiceContractItem,
  ServiceContractHistory,
  ServiceContractBilling,
  ServiceRequestCoverage,
  ContractMetrics,
  CreateServiceContractParams,
  AddContractItemParams,
  RecordContractServiceParams,
  GenerateContractBillingParams
} from '@/lib/types/service-contracts';

// Initialize Supabase client
const supabase = createClient();

/**
 * Servicio para la gestión de contratos de servicio
 */
export const ServiceContractService = {
  /**
   * Obtiene los contratos de servicio activos
   * @param clientId ID del cliente (opcional)
   * @param includeExpired Incluir contratos vencidos
   * @param limit Límite de resultados
   * @param offset Desplazamiento para paginación
   * @returns Lista de contratos de servicio
   */
  async getActiveServiceContracts(
    clientId: string | null = null,
    includeExpired = false,
    limit = 100,
    offset = 0
  ): Promise<ServiceContract[]> {
    const { data, error } = await supabase
      .rpc('get_active_service_contracts', {
        p_client_id: clientId,
        p_include_expired: includeExpired,
        p_limit: limit,
        p_offset: offset
      });

    if (error) {
      console.error('Error al obtener contratos de servicio activos:', error);
      throw error;
    }

    return data || [];
  },

  /**
   * Obtiene los detalles de un contrato de servicio
   * @param contractId ID del contrato de servicio
   * @returns Detalles del contrato de servicio
   */
  async getServiceContractDetails(contractId: string): Promise<ServiceContractDetails> {
    const { data, error } = await supabase
      .rpc('get_service_contract_details', {
        p_contract_id: contractId
      });

    if (error) {
      console.error(`Error al obtener detalles del contrato ${contractId}:`, error);
      throw error;
    }

    if (!data || data.length === 0) {
      throw new Error(`No se encontró el contrato de servicio con ID ${contractId}`);
    }

    return data[0];
  },

  /**
   * Crea un nuevo contrato de servicio
   * @param params Parámetros para crear el contrato
   * @returns ID del contrato creado
   */
  async createServiceContract(params: CreateServiceContractParams): Promise<string> {
    const { data, error } = await supabase
      .rpc('create_service_contract', {
        p_client_id: params.client_id,
        p_title: params.title,
        p_description: params.description || null,
        p_start_date: params.start_date,
        p_end_date: params.end_date,
        p_contract_type: params.contract_type,
        p_billing_cycle: params.billing_cycle || null,
        p_billing_amount: params.billing_amount || null,
        p_auto_renew: params.auto_renew || false,
        p_terms_conditions: params.terms_conditions || null
      });

    if (error) {
      console.error('Error al crear contrato de servicio:', error);
      throw error;
    }

    return data;
  },

  /**
   * Añade un elemento a un contrato de servicio
   * @param params Parámetros para añadir el elemento
   * @returns ID del elemento creado
   */
  async addContractItem(params: AddContractItemParams): Promise<string> {
    const { data, error } = await supabase
      .rpc('add_contract_item', {
        p_contract_id: params.contract_id,
        p_item_type: params.item_type,
        p_description: params.description,
        p_equipment_id: params.equipment_id || null,
        p_quantity: params.quantity || 1,
        p_unit_price: params.unit_price || null,
        p_included_hours: params.included_hours || null,
        p_response_time_hours: params.response_time_hours || null,
        p_is_unlimited: params.is_unlimited || false,
        p_max_incidents: params.max_incidents || null,
        p_priority_level: params.priority_level || null
      });

    if (error) {
      console.error('Error al añadir elemento al contrato:', error);
      throw error;
    }

    return data;
  },

  /**
   * Registra un servicio bajo un contrato
   * @param params Parámetros para registrar el servicio
   * @returns ID del registro de historial creado
   */
  async recordContractService(params: RecordContractServiceParams): Promise<string> {
    const { data, error } = await supabase
      .rpc('record_contract_service', {
        p_contract_id: params.contract_id,
        p_service_request_id: params.service_request_id || null,
        p_service_activity_id: params.service_activity_id,
        p_hours_used: params.hours_used,
        p_description: params.description,
        p_technician_id: params.technician_id || null,
        p_is_billable: params.is_billable || false,
        p_additional_charges: params.additional_charges || 0,
        p_notes: params.notes || null
      });

    if (error) {
      console.error('Error al registrar servicio bajo contrato:', error);
      throw error;
    }

    return data;
  },

  /**
   * Genera facturación para un contrato
   * @param params Parámetros para generar la facturación
   * @returns ID de la facturación creada
   */
  async generateContractBilling(params: GenerateContractBillingParams): Promise<string> {
    const { data, error } = await supabase
      .rpc('generate_contract_billing', {
        p_contract_id: params.contract_id,
        p_invoice_date: params.invoice_date || null,
        p_due_date: params.due_date || null,
        p_amount: params.amount || null,
        p_billing_period_start: params.billing_period_start || null,
        p_billing_period_end: params.billing_period_end || null,
        p_notes: params.notes || null
      });

    if (error) {
      console.error('Error al generar facturación de contrato:', error);
      throw error;
    }

    return data;
  },

  /**
   * Verifica si una solicitud de servicio está cubierta por un contrato
   * @param serviceRequestId ID de la solicitud de servicio
   * @returns Información de cobertura
   */
  async isServiceRequestCoveredByContract(serviceRequestId: string): Promise<ServiceRequestCoverage> {
    const { data, error } = await supabase
      .rpc('is_service_request_covered_by_contract', {
        p_service_request_id: serviceRequestId
      });

    if (error) {
      console.error('Error al verificar cobertura de contrato:', error);
      throw error;
    }

    if (!data || data.length === 0) {
      return {
        is_covered: false,
        contract_id: null,
        contract_title: null,
        contract_number: null,
        contract_type: null,
        client_id: null,
        client_name: null,
        equipment_covered: false,
        service_type_covered: false,
        hours_remaining: null,
        incidents_remaining: null
      };
    }

    return data[0];
  },

  /**
   * Obtiene métricas de contratos para el dashboard
   * @returns Métricas de contratos
   */
  async getContractMetrics(): Promise<ContractMetrics> {
    const { data, error } = await supabase
      .rpc('get_contract_metrics');

    if (error) {
      console.error('Error al obtener métricas de contratos:', error);
      throw error;
    }

    return data;
  },

  /**
   * Actualiza un contrato de servicio existente
   * @param id ID del contrato
   * @param contract Datos actualizados del contrato
   * @returns El contrato actualizado
   */
  async updateServiceContract(id: string, contract: Partial<ServiceContract>): Promise<ServiceContract> {
    const { data, error } = await supabase
      .from('service_contracts')
      .update({
        ...contract,
        updated_at: new Date().toISOString(),
        updated_by: (await supabase.auth.getUser()).data.user?.id
      })
      .eq('id', id)
      .select();

    if (error) {
      console.error(`Error al actualizar contrato de servicio ${id}:`, error);
      throw error;
    }

    return data![0];
  },

  /**
   * Elimina un contrato de servicio
   * @param id ID del contrato
   * @returns Verdadero si se eliminó correctamente
   */
  async deleteServiceContract(id: string): Promise<boolean> {
    const { error } = await supabase
      .from('service_contracts')
      .delete()
      .eq('id', id);

    if (error) {
      console.error(`Error al eliminar contrato de servicio ${id}:`, error);
      throw error;
    }

    return true;
  },

  /**
   * Actualiza un elemento de contrato
   * @param id ID del elemento
   * @param item Datos actualizados del elemento
   * @returns El elemento actualizado
   */
  async updateContractItem(id: string, item: Partial<ServiceContractItem>): Promise<ServiceContractItem> {
    const { data, error } = await supabase
      .from('service_contract_items')
      .update({
        ...item,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select();

    if (error) {
      console.error(`Error al actualizar elemento de contrato ${id}:`, error);
      throw error;
    }

    return data![0];
  },

  /**
   * Elimina un elemento de contrato
   * @param id ID del elemento
   * @returns Verdadero si se eliminó correctamente
   */
  async deleteContractItem(id: string): Promise<boolean> {
    const { error } = await supabase
      .from('service_contract_items')
      .delete()
      .eq('id', id);

    if (error) {
      console.error(`Error al eliminar elemento de contrato ${id}:`, error);
      throw error;
    }

    return true;
  },

  /**
   * Actualiza un registro de facturación
   * @param id ID del registro de facturación
   * @param billing Datos actualizados del registro
   * @returns El registro actualizado
   */
  async updateContractBilling(id: string, billing: Partial<ServiceContractBilling>): Promise<ServiceContractBilling> {
    const { data, error } = await supabase
      .from('service_contract_billing')
      .update({
        ...billing,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select();

    if (error) {
      console.error(`Error al actualizar facturación de contrato ${id}:`, error);
      throw error;
    }

    return data![0];
  }
};
