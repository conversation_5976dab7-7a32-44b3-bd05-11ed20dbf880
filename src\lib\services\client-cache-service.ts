/**
 * @file Servicio de caché en el cliente
 * @description Proporciona funciones para implementar caché en el cliente y reducir peticiones al servidor
 */

// Interfaz para los elementos de caché
interface CacheItem<T> {
  data: T;
  timestamp: number;
  expiry: number;
}

// Interfaz para las opciones de caché
interface CacheOptions {
  ttl?: number; // Tiempo de vida en segundos
  forceRefresh?: boolean; // Forzar actualización de caché
}

/**
 * Servicio de caché en el cliente
 */
class ClientCacheService {
  private static instance: ClientCacheService;
  private cache: Map<string, CacheItem<any>> = new Map();
  
  // Tiempos de caché por defecto para diferentes tipos de datos (en segundos)
  private defaultTTL: Record<string, number> = {
    'clients': 60 * 5, // 5 minutos
    'users': 60 * 5, // 5 minutos
    'projects': 60 * 2, // 2 minutos
    'dashboard': 60 * 1, // 1 minuto
    'default': 60 * 2, // 2 minutos por defecto
  };
  
  /**
   * Constructor privado para implementar el patrón Singleton
   */
  private constructor() {
    // Inicializar el servicio
    this.loadCacheFromStorage();
    
    // Guardar caché en almacenamiento al cerrar la ventana
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', () => {
        this.saveCacheToStorage();
      });
    }
  }
  
  /**
   * Obtiene la instancia única del servicio
   */
  public static getInstance(): ClientCacheService {
    if (!ClientCacheService.instance) {
      ClientCacheService.instance = new ClientCacheService();
    }
    return ClientCacheService.instance;
  }
  
  /**
   * Obtiene un elemento de la caché
   * @param key Clave del elemento
   * @param options Opciones de caché
   * @returns Elemento de caché o null si no existe o ha expirado
   */
  public get<T>(key: string, options: CacheOptions = {}): T | null {
    // Si se fuerza la actualización, ignorar la caché
    if (options.forceRefresh) {
      return null;
    }
    
    // Obtener el elemento de la caché
    const item = this.cache.get(key);
    
    // Si no existe, devolver null
    if (!item) {
      return null;
    }
    
    // Si ha expirado, eliminar de la caché y devolver null
    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      return null;
    }
    
    // Devolver los datos
    return item.data;
  }
  
  /**
   * Establece un elemento en la caché
   * @param key Clave del elemento
   * @param data Datos a almacenar
   * @param options Opciones de caché
   */
  public set<T>(key: string, data: T, options: CacheOptions = {}): void {
    // Determinar el tipo de datos para el TTL por defecto
    const dataType = key.split(':')[0] || 'default';
    
    // Determinar el tiempo de vida
    const ttl = options.ttl || this.defaultTTL[dataType] || this.defaultTTL.default;
    
    // Calcular el tiempo de expiración
    const expiry = Date.now() + (ttl * 1000);
    
    // Almacenar en la caché
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      expiry
    });
  }
  
  /**
   * Elimina un elemento de la caché
   * @param key Clave del elemento
   */
  public delete(key: string): void {
    this.cache.delete(key);
  }
  
  /**
   * Limpia toda la caché
   */
  public clear(): void {
    this.cache.clear();
  }
  
  /**
   * Limpia los elementos expirados de la caché
   */
  public clearExpired(): void {
    const now = Date.now();
    
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiry) {
        this.cache.delete(key);
      }
    }
  }
  
  /**
   * Guarda la caché en el almacenamiento local
   */
  private saveCacheToStorage(): void {
    if (typeof localStorage === 'undefined') {
      return;
    }
    
    try {
      // Limpiar elementos expirados antes de guardar
      this.clearExpired();
      
      // Convertir la caché a un objeto para almacenar
      const cacheObject: Record<string, CacheItem<any>> = {};
      
      for (const [key, value] of this.cache.entries()) {
        cacheObject[key] = value;
      }
      
      // Guardar en localStorage
      localStorage.setItem('app_cache', JSON.stringify(cacheObject));
    } catch (error) {
      console.error('Error al guardar caché en localStorage:', error);
    }
  }
  
  /**
   * Carga la caché desde el almacenamiento local
   */
  private loadCacheFromStorage(): void {
    if (typeof localStorage === 'undefined') {
      return;
    }
    
    try {
      // Obtener la caché del localStorage
      const cacheJson = localStorage.getItem('app_cache');
      
      if (!cacheJson) {
        return;
      }
      
      // Parsear la caché
      const cacheObject = JSON.parse(cacheJson) as Record<string, CacheItem<any>>;
      
      // Restaurar la caché
      for (const [key, value] of Object.entries(cacheObject)) {
        this.cache.set(key, value);
      }
      
      // Limpiar elementos expirados
      this.clearExpired();
    } catch (error) {
      console.error('Error al cargar caché desde localStorage:', error);
    }
  }
}

// Exportar instancia única
export const clientCache = ClientCacheService.getInstance();
