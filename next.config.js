/** @type {import('next').NextConfig} */
const path = require('path');
const { withSentryConfig } = require('@sentry/nextjs');
const nextConfig = {

  webpack: (config, { isServer }) => {
    // Handle external dependencies differently based on server/client
    if (isServer) {
      // For server-side, mark these as external
      config.externals = [...(config.externals || []), 'canvas', 'pdfjs-dist'];
    } else {
      // For client-side, provide fallbacks
      config.resolve.fallback = {
        ...(config.resolve.fallback || {}),
        canvas: false,
        fs: false,
        path: false,
      };
    }

    // Resolve aliases for better imports
    config.resolve.alias = {
      ...(config.resolve.alias || {}),
      // Add explicit path aliases
      '@/components': path.resolve(__dirname, 'src/components'),
      '@/lib': path.resolve(__dirname, 'src/lib'),
      '@/hooks': path.resolve(__dirname, 'src/hooks'),
      '@/contexts': path.resolve(__dirname, 'src/contexts'),
      '@/types': path.resolve(__dirname, 'src/types'),
    };

    // Increase memory limit for webpack
    config.performance = {
      ...config.performance,
      maxEntrypointSize: 512000,
      maxAssetSize: 512000,
    };

    // Optimize for production builds
    if (!isServer && process.env.NODE_ENV === 'production') {
      // Enable tree shaking and minification
      config.optimization = {
        ...config.optimization,
        usedExports: true,
        sideEffects: true,
      };
    }

    return config;
  },
  // Habilitar verificación de tipos en producción para mayor seguridad
  typescript: {
    // Verificar errores de TypeScript durante la compilación
    ignoreBuildErrors: false,
  },
  eslint: {
    // Habilitar ESLint durante la compilación para detectar problemas
    ignoreDuringBuilds: false,
  },
  // Set output directory
  distDir: '.next',
  // Habilitar React strict mode incluso en producción para detectar problemas
  reactStrictMode: true,
  // Enable static optimization where possible
  // optimizeFonts is now enabled by default in Next.js 15+
  // Compress images
  images: {
    formats: ['image/avif', 'image/webp'],
    minimumCacheTTL: 60,
    // Configurar dominios permitidos para imágenes externas
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'xdboxokpjubowptrcytl.supabase.co',
        pathname: '/storage/v1/object/public/**',
      },
    ],
  },
  // Configuración para serverless functions
  serverRuntimeConfig: {
    PROJECT_ROOT: __dirname,
  },
  // Disable x-powered-by header por seguridad
  poweredByHeader: false,

  // External packages for server components (moved from experimental)
  serverExternalPackages: ['canvas', 'pdfjs-dist'],

  // Turbopack configuration (moved from experimental)
  turbopack: {
    rules: {
      '*.svg': {
        loaders: ['@svgr/webpack'],
        as: '*.js',
      },
    },
  },

  // Next.js 15+ optimizations
  experimental: {
    // Optimize for Vercel - disable barrel optimization for problematic packages
    optimizePackageImports: ['@radix-ui/react-icons', 'lucide-react'],
    // Reduce bundle size
    optimizeCss: true,
    // Enable React 19 features
    reactCompiler: false, // Disable for now until stable
  },
  // Configuración de headers de seguridad
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()',
          },
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=63072000; includeSubDomains; preload',
          },
        ],
      },
      {
        source: '/api/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-store, max-age=0',
          },
          {
            key: 'Access-Control-Allow-Origin',
            value: '*',
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, OPTIONS',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'X-Requested-With, Content-Type, Authorization',
          },
        ],
      },
      {
        source: '/_next/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },

};

module.exports = withSentryConfig(nextConfig, {
  // For all available options, see:
  // https://www.npmjs.com/package/@sentry/webpack-plugin#options

  org: "personal-rgx",
  project: "javascript-nextjs",

  // Only print logs for uploading source maps in CI
  silent: !process.env.CI,

  // For all available options, see:
  // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

  // Upload a larger set of source maps for prettier stack traces (increases build time)
  widenClientFileUpload: true,

  // Uncomment to route browser requests to Sentry through a Next.js rewrite to circumvent ad-blockers.
  // This can increase your server load as well as your hosting bill.
  // Note: Check that the configured route will not match with your Next.js middleware, otherwise reporting of client-
  // side errors will fail.
  // tunnelRoute: "/monitoring",

  // Automatically tree-shake Sentry logger statements to reduce bundle size
  disableLogger: true,

  // Enables automatic instrumentation of Vercel Cron Monitors. (Does not yet work with App Router route handlers.)
  // See the following for more information:
  // https://docs.sentry.io/product/crons/
  // https://vercel.com/docs/crons/
  automaticVercelMonitors: true,
});
