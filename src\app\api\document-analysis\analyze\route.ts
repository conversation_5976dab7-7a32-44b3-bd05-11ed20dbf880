import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server-client';
import { cookies } from 'next/headers';

export async function POST(request: NextRequest) {
  try {
    // Verificar autenticación
    const cookieStore = cookies();
    const supabase = createClient(cookieStore);
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json(
        {
          error: 'No autorizado',
          message: 'Debes iniciar sesión para acceder a este recurso',
          code: 'not_authenticated'
        },
        { status: 401 }
      );
    }

    // Obtener la configuración del servicio
    let config;
    try {
      const configResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/document-analysis/config`);
      if (!configResponse.ok) {
        throw new Error(`Error HTTP! estado: ${configResponse.status}`);
      }
      config = await configResponse.json();
    } catch (configError) {
      console.error('Error al obtener la configuración del servicio:', configError);
      return NextResponse.json(
        {
          error: 'Error de configuración',
          message: 'No se pudo obtener la configuración del servicio',
          code: 'config_error',
          details: process.env.NODE_ENV === 'development' ? configError : undefined
        },
        { status: 500 }
      );
    }

    // Obtener los datos de la solicitud
    const formData = await request.formData();

    // Obtener el archivo
    const file = formData.get('file') as File;
    if (!file) {
      return NextResponse.json(
        {
          error: 'Solicitud incorrecta',
          message: 'No se proporcionó ningún archivo',
          code: 'missing_file'
        },
        { status: 400 }
      );
    }

    // Verificar el tamaño del archivo
    const maxSizeBytes = config.max_file_size_mb * 1024 * 1024;
    if (file.size > maxSizeBytes) {
      return NextResponse.json(
        {
          error: 'Archivo demasiado grande',
          message: 'El archivo excede el tamaño máximo permitido',
          code: 'file_too_large',
          max_size_mb: config.max_file_size_mb,
          file_size_mb: Math.round(file.size / 1024 / 1024 * 100) / 100
        },
        { status: 400 }
      );
    }

    // Verificar el tipo de archivo
    const allowedTypes = config.allowed_file_types.split(',');
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!allowedTypes.includes(fileExtension)) {
      return NextResponse.json(
        {
          error: 'Tipo de archivo no permitido',
          message: 'El tipo de archivo proporcionado no está permitido',
          code: 'invalid_file_type',
          allowed_types: allowedTypes,
          file_type: fileExtension
        },
        { status: 400 }
      );
    }

    // Obtener el modelo a utilizar
    const model = formData.get('model') as string || undefined;

    // Crear un nuevo FormData para enviar al servicio
    const serviceFormData = new FormData();
    serviceFormData.append('file', file);
    if (model) {
      serviceFormData.append('model', model);
    }

    // Agregar el ID de usuario
    serviceFormData.append('user_id', session.user.id);

    // Enviar la solicitud al servicio de análisis de documentos
    const response = await fetch(`${config.document_analyzer_url}/analyze`, {
      method: 'POST',
      body: serviceFormData,
    });

    // Devolver la respuesta del servicio
    const data = await response.json();

    return NextResponse.json(data, { status: response.status });

  } catch (error: unknown) {
    console.error('Error al analizar documento:', error);
    const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

    // Manejar errores específicos de fetch
    if (error instanceof TypeError && errorMessage.includes('fetch')) {
      return NextResponse.json(
        {
          error: 'Error de conexión',
          message: 'No se pudo conectar con el servicio de análisis',
          code: 'service_unavailable',
          details: process.env.NODE_ENV === 'development' ? errorMessage : undefined
        },
        { status: 503 }
      );
    }

    // Manejar errores de validación
    if (error instanceof Error && error.name === 'ValidationError') {
      return NextResponse.json(
        {
          error: 'Error de validación',
          message: errorMessage,
          code: 'validation_error',
          details: process.env.NODE_ENV === 'development' ? error : undefined
        },
        { status: 400 }
      );
    }

    // Error genérico del servidor
    return NextResponse.json(
      {
        error: 'Error del servidor',
        message: errorMessage,
        code: 'internal_server_error',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Verificar autenticación
    const cookieStore = cookies();
    const supabase = createClient(cookieStore);
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError) {
      console.error('Error al verificar la sesión:', sessionError);
      return NextResponse.json(
        {
          error: 'Error de autenticación',
          message: 'No se pudo verificar la sesión del usuario',
          code: 'authentication_error',
          details: process.env.NODE_ENV === 'development' ? sessionError : undefined
        },
        { status: 500 }
      );
    }

    if (!session?.user) {
      return NextResponse.json(
        {
          error: 'No autorizado',
          message: 'Debes iniciar sesión para acceder a este recurso',
          code: 'not_authenticated'
        },
        { status: 401 }
      );
    }

    // Obtener la configuración del servicio
    let config;
    try {
      const configResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/document-analysis/config`);
      if (!configResponse.ok) {
        throw new Error(`Error HTTP! estado: ${configResponse.status}`);
      }
      config = await configResponse.json();
    } catch (configError) {
      console.error('Error al obtener la configuración del servicio:', configError);
      return NextResponse.json(
        {
          error: 'Error de configuración',
          message: 'No se pudo obtener la configuración del servicio',
          code: 'config_error',
          details: process.env.NODE_ENV === 'development' ? configError : undefined
        },
        { status: 500 }
      );
    }

    // Obtener el ID del análisis
    const url = new URL(request.url);
    const analysisId = url.searchParams.get('id');

    if (!analysisId) {
      return NextResponse.json(
        {
          error: 'Solicitud incorrecta',
          message: 'No se proporcionó un ID de análisis',
          code: 'missing_analysis_id'
        },
        { status: 400 }
      );
    }

    // Enviar la solicitud al servicio de análisis de documentos
    const response = await fetch(`${config.document_analyzer_url}/analyze/${analysisId}`, {
      method: 'GET',
      headers: {
        'X-User-ID': session.user.id,
      },
    });

    // Si hay un error en la respuesta del servicio, manejarlo
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      return NextResponse.json(
        {
          error: 'Error en el servicio de análisis',
          message: errorData.message || 'El servicio de análisis devolvió un error',
          code: 'analysis_service_error',
          details: process.env.NODE_ENV === 'development' ? errorData : undefined
        },
        { status: response.status }
      );
    }

    // Devolver la respuesta del servicio
    const data = await response.json();
    return NextResponse.json(data, { status: response.status });

  } catch (error: unknown) {
    console.error('Error al obtener resultado de análisis:', error);
    const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

    // Manejar errores específicos de fetch
    if (error instanceof TypeError && errorMessage.includes('fetch')) {
      return NextResponse.json(
        {
          error: 'Error de conexión',
          message: 'No se pudo conectar con el servicio de análisis',
          code: 'service_unavailable',
          details: process.env.NODE_ENV === 'development' ? errorMessage : undefined
        },
        { status: 503 }
      );
    }

    // Manejar errores de validación
    if (error instanceof Error && error.name === 'ValidationError') {
      return NextResponse.json(
        {
          error: 'Error de validación',
          message: errorMessage,
          code: 'validation_error',
          details: process.env.NODE_ENV === 'development' ? error : undefined
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: 'Error del servidor',
        message: errorMessage,
        code: 'internal_server_error',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    );
  }
}
