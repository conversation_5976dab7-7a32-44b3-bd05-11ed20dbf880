/**
 * @ai-file-description: "Enhanced form validation alert with progressive error disclosure"
 * @ai-related-files: ["project-form.tsx", "../../shared/form-validation-alert.tsx"]
 * @ai-owner: "Projects"
 */

"use client"

import { useState } from "react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { AlertCircle, ChevronDown, ChevronUp, X, CheckCircle, AlertTriangle } from "lucide-react"
import { cn } from "@/lib/utils"

interface ValidationError {
  field?: string
  message: string
  type?: 'error' | 'warning' | 'info'
  suggestion?: string
}

interface EnhancedFormValidationAlertProps {
  errors: (string | ValidationError)[]
  onDismiss?: () => void
  className?: string
  maxVisibleErrors?: number
  showSuggestions?: boolean
}

/**
 * Enhanced form validation alert with progressive error disclosure
 * 
 * @ai-responsibility: "Displays validation errors with improved UX and suggestions"
 */
export function EnhancedFormValidationAlert({
  errors,
  onDismiss,
  className,
  maxVisibleErrors = 3,
  showSuggestions = true
}: EnhancedFormValidationAlertProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [dismissedErrors, setDismissedErrors] = useState<Set<number>>(new Set())

  // Normalize errors to ValidationError objects
  const normalizedErrors: ValidationError[] = errors.map(error => 
    typeof error === 'string' 
      ? { message: error, type: 'error' as const }
      : error
  ).filter((_, index) => !dismissedErrors.has(index))

  if (normalizedErrors.length === 0) {
    return null
  }

  // Categorize errors by type
  const errorsByType = {
    error: normalizedErrors.filter(e => e.type === 'error' || !e.type),
    warning: normalizedErrors.filter(e => e.type === 'warning'),
    info: normalizedErrors.filter(e => e.type === 'info')
  }

  const visibleErrors = isExpanded 
    ? normalizedErrors 
    : normalizedErrors.slice(0, maxVisibleErrors)

  const hiddenErrorCount = normalizedErrors.length - maxVisibleErrors

  const getIcon = (type: ValidationError['type']) => {
    switch (type) {
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case 'info':
        return <CheckCircle className="h-4 w-4 text-blue-500" />
      default:
        return <AlertCircle className="h-4 w-4 text-red-500" />
    }
  }

  const getAlertVariant = () => {
    if (errorsByType.error.length > 0) return 'destructive'
    if (errorsByType.warning.length > 0) return 'default'
    return 'default'
  }

  const dismissError = (index: number) => {
    setDismissedErrors(prev => new Set([...prev, index]))
  }

  const getMostRelevantErrors = () => {
    // Prioritize errors over warnings over info
    const prioritized = [
      ...errorsByType.error.slice(0, 2),
      ...errorsByType.warning.slice(0, 1),
      ...errorsByType.info.slice(0, 1)
    ]
    return prioritized.slice(0, maxVisibleErrors)
  }

  const getErrorSuggestion = (error: ValidationError): string | undefined => {
    if (error.suggestion) return error.suggestion

    // Auto-generate suggestions based on common error patterns
    const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

    const message = errorMessage.toLowerCase()
    
    if (message.includes('obligatorio') || message.includes('requerido')) {
      return 'Complete este campo para continuar'
    }
    
    if (message.includes('formato') || message.includes('válido')) {
      return 'Verifique el formato del dato ingresado'
    }
    
    if (message.includes('uuid') || message.includes('id')) {
      return 'Seleccione una opción de la lista desplegable'
    }
    
    if (message.includes('número') || message.includes('presupuesto')) {
      return 'Ingrese solo números, sin símbolos de moneda'
    }
    
    if (message.includes('fecha')) {
      return 'Seleccione una fecha válida del calendario'
    }
    
    return undefined
  }

  return (
    <Alert variant={getAlertVariant()} className={cn("mb-4", className)}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle className="text-sm font-medium">
              {errorsByType.error.length > 0 && "Errores de validación"}
              {errorsByType.error.length === 0 && errorsByType.warning.length > 0 && "Advertencias"}
              {errorsByType.error.length === 0 && errorsByType.warning.length === 0 && "Información"}
            </AlertTitle>
            <div className="flex gap-1">
              {errorsByType.error.length > 0 && (
                <Badge variant="destructive" className="text-xs">
                  {errorsByType.error.length} errores
                </Badge>
              )}
              {errorsByType.warning.length > 0 && (
                <Badge variant="secondary" className="text-xs">
                  {errorsByType.warning.length} advertencias
                </Badge>
              )}
            </div>
          </div>
          
          <AlertDescription>
            <div className="space-y-2">
              {(isExpanded ? normalizedErrors : getMostRelevantErrors()).map((error, index) => (
                <div key={index} className="flex items-start justify-between gap-2 p-2 rounded bg-background/50">
                  <div className="flex items-start gap-2 flex-1">
                    {getIcon(error.type)}
                    <div className="flex-1 min-w-0">
                      <div className="text-sm">
                        {error.field && (
                          <span className="font-medium text-muted-foreground">
                            {error.field}:{" "}
                          </span>
                        )}
                        const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

                        {errorMessage}
                      </div>
                      {showSuggestions && getErrorSuggestion(error) && (
                        <div className="text-xs text-muted-foreground mt-1 italic">
                          💡 {getErrorSuggestion(error)}
                        </div>
                      )}
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 opacity-50 hover:opacity-100"
                    onClick={() => dismissError(index)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              ))}
              
              {hiddenErrorCount > 0 && !isExpanded && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs text-muted-foreground"
                  onClick={() => setIsExpanded(true)}
                >
                  <ChevronDown className="h-3 w-3 mr-1" />
                  Ver {hiddenErrorCount} error{hiddenErrorCount > 1 ? 'es' : ''} más
                </Button>
              )}
              
              {isExpanded && hiddenErrorCount > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs text-muted-foreground"
                  onClick={() => setIsExpanded(false)}
                >
                  <ChevronUp className="h-3 w-3 mr-1" />
                  Mostrar menos
                </Button>
              )}
            </div>
          </AlertDescription>
        </div>
        
        {onDismiss && (
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 opacity-50 hover:opacity-100"
            onClick={onDismiss}
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>
    </Alert>
  )
}

/**
 * Helper function to create structured validation errors
 */
export function createValidationError(
  message: string,
  field?: string,
  type: ValidationError['type'] = 'error',
  suggestion?: string
): ValidationError {
  return {
    message,
    field,
    type,
    suggestion
  }
}
