"use client"

import { useState, useEffect } from "react"
import { useQuery, useQueryClient } from "@tanstack/react-query"
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import { ArrowUpDown, ChevronDown, MoreHorizontal, FileText, Download, Eye, Trash2, Building2 } from "lucide-react"
import { format } from "date-fns"
import { es } from "date-fns/locale"

import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { toast } from "@/hooks/use-toast"
import { createClient } from "@/lib/supabase/client"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

import { Document } from "@/types/documents"

export const getColumns = (queryClient: any): ColumnDef<Document>[] => [

  {
    id: "filename",
    accessorFn: (row) => row.filename,
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting()}
        >
          Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => {
      const document = row.original
      return (
        <div className="flex items-center space-x-2">
          <FileText className="h-4 w-4 text-muted-foreground" />
          <span>{document.filename}</span>
        </div>
      )
    },
  },
  {
    id: "description",
    accessorFn: (row) => row.description || "-",
    header: "Description",
    cell: ({ row }) => <div>{row.getValue("description")}</div>,
  },
  {
    id: "category",
    accessorFn: (row) => row.category || "",
    header: "Category",
    cell: ({ row }) => {
      const category = row.getValue("category") as string
      return category ? (
        <Badge variant="outline">{category}</Badge>
      ) : (
        <span className="text-muted-foreground">No category</span>
      )
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
  },
  {
    id: "file_type",
    accessorFn: (row) => row.file_type,
    header: "Type",
    cell: ({ row }) => {
      const fileType = row.getValue("file_type") as string
      const type = fileType.split("/")[1]?.toUpperCase() || fileType
      return <div className="text-xs">{type}</div>
    },
  },
  {
    id: "file_size",
    accessorFn: (row) => row.file_size,
    header: "Size",
    cell: ({ row }) => {
      const size = row.getValue("file_size") as number
      const formattedSize = size < 1024 * 1024
        ? `${(size / 1024).toFixed(2)} KB`
        : `${(size / 1024 / 1024).toFixed(2)} MB`
      return <div className="text-xs">{formattedSize}</div>
    },
  },
  {
    id: "project",
    accessorFn: (row) => row.project?.name || "N/A",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting()}
        >
          Project
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => {
      const project = row.original.project
      return (
        <div className="flex items-center space-x-2">
          <Building2 className="h-4 w-4 text-muted-foreground" />
          <span>{project?.name || "N/A"}</span>
        </div>
      )
    },
  },
  {
    id: "created_at",
    accessorFn: (row) => row.created_at,
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting()}
        >
          Date
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => {
      const date = row.getValue("created_at") as string
      return (
        <div className="text-xs">
          {format(new Date(date), "dd/MM/yyyy", { locale: es })}
        </div>
      )
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const document = row.original

      const handlePreview = () => {
        if (document.public_url) {
          window.open(document.public_url, "_blank")
        } else {
          toast({
            title: "Error",
            description: "Cannot preview this document",
            variant: "destructive",
          })
        }
      }

      const handleDownload = () => {
        if (document.public_url) {
          const link = window.document.createElement("a")
          link.href = document.public_url
          link.download = document.filename
          window.document.body.appendChild(link)
          link.click()
          window.document.body.removeChild(link)
        } else {
          toast({
            title: "Error",
            description: "Cannot download this document",
            variant: "destructive",
          })
        }
      }


      const handleDelete = async () => {
        try {
          const supabase = createClient()
          const { error } = await supabase
            .from("documents")
            .delete()
            .match({ id: document.id })

          if (error) {
            throw error
          }

          toast({
            title: "Document deleted",
            description: "The document has been deleted successfully",
          })

          // Invalidate documents query to trigger refresh
          queryClient.invalidateQueries({ queryKey: ['documents', projectId] })
        } catch (error) {
          console.error("Error deleting document:", error)
          toast({
            title: "Error",
            description: "An error occurred while deleting the document",
            variant: "destructive",
          })
        }
      }

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem onClick={handlePreview}>
              <Eye className="mr-2 h-4 w-4" />
              Preview
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleDownload}>
              <Download className="mr-2 h-4 w-4" />
              Download
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <DropdownMenuItem
                  onSelect={(e) => e.preventDefault()}
                  className="text-red-600"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </DropdownMenuItem>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Delete document?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action cannot be undone. Are you sure you want to delete this document?
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleDelete}
                    className="bg-red-600 hover:bg-red-700"
                  >
                    Delete
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    },
  },
]

import { useDocuments } from '@/hooks/useDocuments'

export type DocumentsListProps = {
  projectId?: string
}

export function DocumentsList({ projectId }: DocumentsListProps) {
  const queryClient = useQueryClient()
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const { data = [], isLoading, isError, refetch } = useDocuments()
  const documents = data as Document[]

  const [sorting, setSorting] = useState<SortingState>([])
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({})
  const [rowSelection, setRowSelection] = useState({})
  const [categoryFilter, setCategoryFilter] = useState<string[]>([])

  // Obtener categorías únicas
  const uniqueCategories = Array.from(
    new Set(
      documents
        .filter((doc): doc is Document => doc.category !== null)
        .map((doc) => doc.category)
    )
  ).filter((category): category is string => category !== null)

  const table = useReactTable({
    columns: getColumns(queryClient),
    data: documents,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
    },
  })

  // Aplicar filtro de categoría
  const handleCategoryFilterChange = (category: string) => {
    setSelectedCategories(
      selectedCategories.includes(category)
        ? selectedCategories.filter((c) => c !== category)
        : [...selectedCategories, category]
    )
  }

  // Actualizar el filtro de la tabla cuando cambie el filtro de categoría
  useEffect(() => {
    const categoryColumn = table.getColumn("category")
    if (categoryColumn) {
      if (selectedCategories.length > 0) {
        categoryColumn.setFilterValue(selectedCategories)
      } else {
        categoryColumn.setFilterValue(undefined)
      }
    }
  }, [selectedCategories, table])

  return (
    <div className="w-full">
      {isLoading ? (
        <div className="rounded-md border p-8 text-center">
          <span className="text-muted-foreground">Loading documents...</span>
        </div>
      ) : isError ? (
        <div className="rounded-md border p-8 text-center">
          <span className="text-destructive">Error loading documents.</span>
          <button onClick={() => queryClient.invalidateQueries({ queryKey: ['documents'] })} className="ml-2 underline">Retry</button>
        </div>
      ) : (
        <>
          <div className="flex items-center py-4 gap-2">
            <Input
              placeholder="Filter documents..."
              value={(table.getColumn("filename")?.getFilterValue() as string) ?? ""}
              onChange={(event) =>
                table.getColumn("filename")?.setFilterValue(event.target.value)
              }
              className="max-w-sm"
            />
            {uniqueCategories.length > 0 && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    Category <ChevronDown className="ml-2 h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Filter by category</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {uniqueCategories.map((category) => (
                    <DropdownMenuCheckboxItem
                      key={category}
                      checked={selectedCategories.includes(category)}
                      onCheckedChange={(value) =>
                        handleCategoryFilterChange(category)
                      }
                    >
                      {category}
                    </DropdownMenuCheckboxItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            )}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  Columns <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {table
                  .getAllColumns()
                  .filter((column) => column.getCanHide())
                  .map((column) => {
                    return (
                      <DropdownMenuCheckboxItem
                        key={column.id}
                        className="capitalize"
                        checked={column.getIsVisible()}
                        onCheckedChange={(value) =>
                          column.toggleVisibility(!!value)
                        }
                      >
                        {column.id === "filename"
                          ? "Name"
                          : column.id === "description"
                          ? "Description"
                          : column.id === "category"
                          ? "Category"
                          : column.id === "file_type"
                          ? "Type"
                          : column.id === "file_size"
                          ? "Size"
                          : column.id === "project"
                          ? "Project"
                          : column.id === "created_at"
                          ? "Date"
                          : column.id}
                      </DropdownMenuCheckboxItem>
                    )
                  })}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => {
                      return (
                        <TableHead key={header.id}>
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                                header.column.columnDef.header,
                                header.getContext()
                              )}
                        </TableHead>
                      )
                    })}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id}>
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length}
                      className="h-24 text-center"
                    >
                      No documents found.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
          <div className="flex items-center justify-end space-x-2 py-4">
            <div className="flex-1 text-sm text-muted-foreground">
              {table.getFilteredRowModel().rows.length} document(s) found.
            </div>
            <div className="space-x-2">
              <Button
                variant="outline"
                onClick={() => table.previousPage()}
                disabled={!table.getCanPreviousPage()}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                onClick={() => table.nextPage()}
                disabled={!table.getCanNextPage()}
              >
                Next
              </Button>
            </div>
          </div>
        </>
      )}
    </div>
  )
}
