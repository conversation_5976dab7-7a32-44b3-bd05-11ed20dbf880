/**
 * Vercel Build Script
 *
 * This script runs before the Next.js build on Vercel to ensure all
 * necessary setup steps are completed.
 */

const { execSync } = require('child_process');

console.log('🚀 Running Vercel build script...');

// Set environment for production
process.env.NODE_ENV = 'production';

// Verificar y limpiar caché si es necesario (simplificado para Vercel)
try {
  console.log('🧹 Limpiando caché de Next.js...');
  const fs = require('fs');

  // Usar fs para limpiar en lugar de comandos del sistema
  if (fs.existsSync('.next')) {
    console.log('🗑️ Eliminando directorio .next existente...');
    fs.rmSync('.next', { recursive: true, force: true });
  }

  console.log('✅ Limpieza de caché completada');
} catch (error) {
  console.warn('⚠️ No se pudo limpiar la caché:', error.message);
  // Continuar a pesar del error
}

// Verificar dependencias (simplificado para Vercel)
try {
  console.log('🔍 Verificando dependencias...');
  const fs = require('fs');

  // Verificar que el archivo de iconos existe
  const iconsPath = './src/components/ui/icons.ts';

  if (fs.existsSync(iconsPath)) {
    console.log('✅ Archivo de iconos encontrado:', iconsPath);
    const iconsContent = fs.readFileSync(iconsPath, 'utf8');

    // Verificar que Tool está exportado
    if (iconsContent.includes('Tool,')) {
      console.log('✅ Icono Tool encontrado en el archivo de iconos');
    } else {
      console.warn('⚠️ Icono Tool no encontrado en el archivo de iconos');
    }
  } else {
    console.error('❌ Archivo de iconos no encontrado:', iconsPath);
  }
} catch (error) {
  console.warn('⚠️ Advertencia en dependencias:', error.message);
  // Continuar a pesar de advertencias
}

// Configurar opciones de Node para evitar problemas de memoria
process.env.NODE_OPTIONS = '--max-old-space-size=4096';

// Run the Next.js build
console.log('📦 Building Next.js application...');
try {
  execSync('next build', {
    stdio: 'inherit',
    env: {
      ...process.env,
      NEXT_TELEMETRY_DISABLED: '1'
    }
  });
  console.log('✅ Vercel build completed successfully!');
} catch (error) {
  console.error('❌ Error during build:', error.message);
  process.exit(1);
}
