"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { setupServiceManagement } from "@/lib/actions/setup-service-management"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { ArrowLeft, Database, AlertTriangle, CheckCircle2 } from "lucide-react"
import { toast } from "@/components/ui/use-toast"

export default function SetupServiceManagementPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<{ success: boolean; message: string } | null>(null)
  const router = useRouter()

  const handleSetup = async () => {
    setIsLoading(true)
    try {
      const setupResult = await setupServiceManagement()
      setResult(setupResult)
      
      if (setupResult.success) {
        toast({
          title: "Setup Successful",
          description: setupResult.message,
        })
      } else {
        toast({
          title: "Setup Failed",
          description: setupResult.message,
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error setting up service management:", error)
      setResult({
        success: false,
        message: `An unexpected error occurred: ${error instanceof Error ? error.message : String(error)}`
      })
      
      toast({
        title: "Setup Error",
        description: "An unexpected error occurred during setup.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <Button variant="outline" size="sm" asChild>
            <Link href="/dashboard/service-requests">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Service Requests
            </Link>
          </Button>
          <h2 className="text-3xl font-bold tracking-tight mt-2">Setup Service Management</h2>
          <p className="text-muted-foreground mt-1">
            Set up the database tables required for the technical service management module.
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Database Setup</CardTitle>
          <CardDescription>
            This will create all the necessary tables for the technical service management module.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-start space-x-4">
            <Database className="h-6 w-6 mt-1 text-blue-500" />
            <div>
              <h3 className="text-lg font-medium">Service Management Tables</h3>
              <p className="text-sm text-muted-foreground">
                The following tables will be created:
              </p>
              <ul className="mt-2 text-sm list-disc pl-5 space-y-1">
                <li>service_requests - For tracking service requests</li>
                <li>customer_equipment - For managing customer equipment</li>
                <li>maintenance_schedules - For scheduling maintenance</li>
                <li>service_activities - For tracking service activities</li>
                <li>service_parts_used - For tracking parts used in service</li>
                <li>service_signatures - For capturing signatures</li>
                <li>service_attachments - For storing attachments</li>
                <li>service_checklists - For service checklists</li>
                <li>service_checklist_items - For checklist items</li>
                <li>service_checklist_responses - For checklist responses</li>
              </ul>
            </div>
          </div>

          <Alert variant="warning">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Important</AlertTitle>
            <AlertDescription>
              This action will modify your database schema. Make sure you have a backup before proceeding.
              If the tables already exist, no changes will be made.
            </AlertDescription>
          </Alert>

          {result && (
            <Alert variant={result.success ? "default" : "destructive"}>
              {result.success ? (
                <CheckCircle2 className="h-4 w-4" />
              ) : (
                <AlertTriangle className="h-4 w-4" />
              )}
              <AlertTitle>{result.success ? "Success" : "Error"}</AlertTitle>
              <AlertDescription>
                {result.message}
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" asChild>
            <Link href="/dashboard/service-requests">Cancel</Link>
          </Button>
          <Button onClick={handleSetup} disabled={isLoading}>
            {isLoading ? "Setting up..." : "Set Up Service Management"}
          </Button>
        </CardFooter>
      </Card>

      {result?.success && (
        <div className="flex justify-center mt-6">
          <Button asChild>
            <Link href="/dashboard/service-requests">
              Go to Service Requests
            </Link>
          </Button>
        </div>
      )}
    </div>
  )
}
