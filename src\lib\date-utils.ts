/**
 * @file Utilidades para formateo de fechas y horas
 * @description Funciones para formatear fechas y horas en diferentes formatos
 */

import { format, formatDistanceToNow } from 'date-fns';
import { es } from 'date-fns/locale';

/**
 * Formatea una fecha en formato legible
 * @param date Fecha a formatear (string o Date)
 * @param formatStr Formato a utilizar (opcional)
 * @returns Fecha formateada
 */
export function formatDate(date: string | Date | null | undefined, formatStr: string = 'dd/MM/yyyy'): string {
  if (!date) return '-';
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return format(dateObj, formatStr, { locale: es });
  } catch (error) {
    console.error('Error al formatear fecha:', error);
    return '-';
  }
}

/**
 * Formatea una hora en formato legible
 * @param date Fecha/hora a formatear (string o Date)
 * @param formatStr Formato a utilizar (opcional)
 * @returns Hora formateada
 */
export function formatTime(date: string | Date | null | undefined, formatStr: string = 'HH:mm'): string {
  if (!date) return '-';
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return format(dateObj, formatStr, { locale: es });
  } catch (error) {
    console.error('Error al formatear hora:', error);
    return '-';
  }
}

/**
 * Formatea una fecha y hora en formato legible
 * @param date Fecha/hora a formatear (string o Date)
 * @param formatStr Formato a utilizar (opcional)
 * @returns Fecha y hora formateada
 */
export function formatDateTime(date: string | Date | null | undefined, formatStr: string = 'dd/MM/yyyy HH:mm'): string {
  if (!date) return '-';
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return format(dateObj, formatStr, { locale: es });
  } catch (error) {
    console.error('Error al formatear fecha y hora:', error);
    return '-';
  }
}

/**
 * Formatea una fecha en formato relativo (ej: "hace 5 minutos")
 * @param date Fecha a formatear (string o Date)
 * @returns Texto con tiempo relativo
 */
export function formatRelativeTime(date: string | Date | null | undefined): string {
  if (!date) return '-';
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return formatDistanceToNow(dateObj, { addSuffix: true, locale: es });
  } catch (error) {
    console.error('Error al formatear tiempo relativo:', error);
    return '-';
  }
}
