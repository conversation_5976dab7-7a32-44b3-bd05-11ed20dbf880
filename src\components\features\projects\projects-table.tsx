"use client"
import React, { useState, useCallback } from "react"
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import { ArrowUpDown, ChevronDown, MoreHorizontal, Loader2, RefreshCw, Shield, Plus, AlertCircle } from "lucide-react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { formatCurrency } from "@/lib/currency"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { format } from "date-fns"
import { es } from "date-fns/locale"
import { Project, ProjectStatus, getStatusText, getStatusColor } from "@/types/projects"
import { toast } from "@/hooks/use-toast"
import { createClient } from "@/lib/supabase/client"
import { useProjects } from "@/hooks/use-projects"
import { sessionManager } from "@/lib/supabase/session-manager"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { AuthDiagnostics } from "@/components/features/auth/auth-diagnostics"
interface ProjectsTableProps {
  // Props opcionales para futuras extensiones
  className?: string;
}
export function ProjectsTable(_props: ProjectsTableProps = {}) {
  const supabase = createClient()

  // Usar el hook de proyectos
  const {
    projects: projectsData,
    isLoading,
    error,
    refreshProjects,
    deleteProject: deleteProjectFromHook
  } = useProjects({ limit: 100 });

  // Estados locales
  const [sorting, setSorting] = useState<SortingState>([])
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({})
  const [rowSelection, setRowSelection] = useState({})
  const [statusFilter, setStatusFilter] = useState<ProjectStatus[]>([])
  const [priorityFilter, setPriorityFilter] = useState<string[]>([])
  const [projectToDelete, setProjectToDelete] = useState<Project | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)
  const columns: ColumnDef<Project>[] = [
    {
      accessorKey: "name",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Nombre
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
      cell: ({ row }) => (
        <div className="font-medium">
          <Link href={`/dashboard/projects/${row.original.id}`} className="hover:underline">
            {row.getValue("name")}
          </Link>
        </div>
      ),
    },
    {
      accessorKey: "status",
      header: "Estado",
      cell: ({ row }) => {
        const status = row.getValue("status") as ProjectStatus
        return (
          <Badge variant="outline" className={getStatusColor(status)}>
            {getStatusText(status)}
          </Badge>
        )
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id))
      },
    },
    {
      accessorKey: "clients",
      header: "Cliente",
      cell: ({ row }) => {
        const client = row.getValue("clients") as { name: string } | null;
        return <div>{client?.name || row.original.client || "-"}</div>;
      },
    },
    {
      accessorKey: "start_date",
      header: "Fecha de inicio",
      cell: ({ row }) => {
        const date = row.getValue("start_date") as string | null
        return date ? format(new Date(date), "dd/MM/yyyy", { locale: es }) : "-"
      },
    },
    {
      accessorKey: "end_date",
      header: "Fecha de fin",
      cell: ({ row }) => {
        const date = row.getValue("end_date") as string | null
        return date ? format(new Date(date), "dd/MM/yyyy", { locale: es }) : "-"
      },
    },
    {
      accessorKey: "priority",
      header: "Prioridad",
      cell: ({ row }) => {
        const priority = row.getValue("priority") as string;
        return (
          <Badge variant="outline" className={
            priority === "high" ? "text-red-500 border-red-500" :
            priority === "medium" ? "text-yellow-500 border-yellow-500" :
            "text-green-500 border-green-500"
          }>
            {priority === "high" ? "Alta" :
             priority === "medium" ? "Media" :
             priority === "low" ? "Baja" : "Normal"}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id))
      },
    },
    {
      accessorKey: "budget",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Presupuesto
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
      cell: ({ row }) => {
        const budget = row.getValue("budget") as number | null
        const currency = row.original.currency || "CLP"
        return (
          <div className="flex items-center">
            {budget ? (
              <span>{formatCurrency(budget, currency)}</span>
            ) : (
              <span className="text-muted-foreground">-</span>
            )}
          </div>
        )
      },
    },
    {
      accessorKey: "progress_percent",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Progreso
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
      cell: ({ row }) => {
        const progress = row.getValue("progress_percent") as number
        return (
          <div className="flex items-center">
            <div className="w-full bg-gray-200 rounded-full h-2.5 mr-2">
              <div
                className="bg-blue-600 h-2.5 rounded-full"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            <span>{progress}%</span>
          </div>
        )
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const project = row.original
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Abrir menú</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Acciones</DropdownMenuLabel>
              <DropdownMenuItem
                onClick={() => navigator.clipboard.writeText(project.id)}
              >
                Copiar ID
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Link href={`/dashboard/projects/${project.id}`}>
                  Ver detalles
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Link href={`/dashboard/projects/${project.id}/edit`}>
                  Editar
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="text-red-600"
                onClick={() => setProjectToDelete(project)}
              >
                Eliminar
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
    },
  ]
  const table = useReactTable({
    data: projectsData,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  })
  // Aplicar filtro de estado
  const handleStatusFilterChange = (status: ProjectStatus) => {
    setStatusFilter((prev) => {
      if (prev.includes(status)) {
        return prev.filter((s) => s !== status)
      } else {
        return [...prev, status]
      }
    })
  }
  // Aplicar filtro de prioridad
  const handlePriorityFilterChange = (priority: string) => {
    setPriorityFilter((prev) => {
      if (prev.includes(priority)) {
        return prev.filter((p) => p !== priority)
      } else {
        return [...prev, priority]
      }
    })
  }
  // Función para manejar la eliminación de proyectos
  const handleDeleteProject = useCallback(async () => {
    if (!projectToDelete) return;
    setIsDeleting(true);

    try {
      console.log(`Deleting project with ID: ${projectToDelete.id}`);

      // Verificar sesión antes de eliminar
      const sessionValid = await sessionManager.ensureValidSession();

      if (!sessionValid) {
        console.error('Session validation failed during project deletion');
        toast({
          title: 'Error de sesión',
          description: 'Su sesión ha expirado. Por favor, inicie sesión nuevamente.',
          variant: 'destructive'
        });
        setIsDeleting(false);
        setProjectToDelete(null);
        return;
      }

      // Verificar si el proyecto existe antes de intentar eliminarlo
      try {
        const { data: projectData, error: projectError } = await supabase
          .from('projects')
          .select('id, name')
          .eq('id', projectToDelete.id)
          .maybeSingle();

        if (projectError) {
          console.error("Error al verificar el proyecto:", projectError);
          throw new Error("Error al verificar el proyecto: " + projectError.message);
        }

        if (!projectData) {
          console.warn("El proyecto no existe o ya fue eliminado");
          toast({
            title: "Advertencia",
            description: "El proyecto no existe o ya fue eliminado.",
            variant: "warning",
          });
          setIsDeleting(false);
          setProjectToDelete(null);

          // Actualizar la lista de proyectos
          refreshProjects(true);
          return;
        }
      } catch (verifyError) {
        console.error("Error al verificar el proyecto:", verifyError);
        toast({
          title: "Error",
          description: "No se pudo verificar el proyecto. Es posible que ya haya sido eliminado.",
          variant: "destructive",
        });
        setIsDeleting(false);
        setProjectToDelete(null);
        return;
      }

      // Usar el hook para eliminar el proyecto
      const success = await deleteProjectFromHook(projectToDelete.id);

      if (success) {
        // Cerrar el diálogo
        setProjectToDelete(null);

        // Refrescar la lista de proyectos
        refreshProjects(true);
      } else {
        console.error("Error al eliminar el proyecto");
        toast({
          title: "Error",
          description: "No se pudo eliminar el proyecto. Intente nuevamente.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error al eliminar el proyecto:", error);
      // Mostrar mensaje de error específico si está disponible
      const errorMessage = error instanceof Error ? error.message : "No se pudo eliminar el proyecto. Intente nuevamente.";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  }, [projectToDelete, deleteProjectFromHook, refreshProjects, supabase]);
  // Actualizar los filtros de la tabla cuando cambien
  React.useEffect(() => {
    // Filtro de estado
    if (statusFilter.length > 0) {
      table.getColumn("status")?.setFilterValue(statusFilter)
    } else {
      table.getColumn("status")?.setFilterValue(undefined)
    }
    // Filtro de prioridad
    if (priorityFilter.length > 0) {
      table.getColumn("priority")?.setFilterValue(priorityFilter)
    } else {
      table.getColumn("priority")?.setFilterValue(undefined)
    }
  }, [statusFilter, priorityFilter, table])

  return (
    <div className="w-full">
      <div className="flex items-center py-4 gap-2">
        <Input
          placeholder="Filtrar proyectos..."
          value={(table.getColumn("name")?.getFilterValue() as string) ?? ""}
          onChange={(event) =>
            table.getColumn("name")?.setFilterValue(event.target.value)
          }
          className="max-w-sm"
        />
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="ml-auto">
              Estado <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Filtrar por estado</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {["pending", "in_progress", "completed", "cancelled"].map(
              (status) => (
                <DropdownMenuCheckboxItem
                  key={status}
                  checked={statusFilter.includes(status as ProjectStatus)}
                  onCheckedChange={() =>
                    handleStatusFilterChange(status as ProjectStatus)
                  }
                >
                  {getStatusText(status as ProjectStatus)}
                </DropdownMenuCheckboxItem>
              )
            )}
          </DropdownMenuContent>
        </DropdownMenu>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline">
              Prioridad <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Filtrar por prioridad</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {["high", "medium", "low"].map(
              (priority) => (
                <DropdownMenuCheckboxItem
                  key={priority}
                  checked={priorityFilter.includes(priority)}
                  onCheckedChange={() =>
                    handlePriorityFilterChange(priority)
                  }
                >
                  {priority === "high" ? "Alta" :
                   priority === "medium" ? "Media" :
                   "Baja"}
                </DropdownMenuCheckboxItem>
              )
            )}
          </DropdownMenuContent>
        </DropdownMenu>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline">
              Columnas <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {table
              .getAllColumns()
              .filter((column) => column.getCanHide())
              .map((column) => {
                return (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) =>
                      column.toggleVisibility(!!value)
                    }
                  >
                    {column.id === "name"
                      ? "Nombre"
                      : column.id === "status"
                      ? "Estado"
                      : column.id === "clients"
                      ? "Cliente"
                      : column.id === "start_date"
                      ? "Fecha de inicio"
                      : column.id === "end_date"
                      ? "Fecha de fin"
                      : column.id === "priority"
                      ? "Prioridad"
                      : column.id === "budget"
                      ? "Presupuesto"
                      : column.id === "progress_percent"
                      ? "Progreso"
                      : column.id}
                  </DropdownMenuCheckboxItem>
                )
              })}
          </DropdownMenuContent>
        </DropdownMenu>
        <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => refreshProjects(true)}
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="mr-2 h-4 w-4" />
              )}
              Actualizar
            </Button>

            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="outline" size="icon">
                  <Shield className="h-4 w-4" />
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Diagnóstico de Autenticación</AlertDialogTitle>
                  <AlertDialogDescription>
                    Verifique el estado de su sesión y solucione problemas de autenticación.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <div className="py-4">
                  <AuthDiagnostics />
                </div>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cerrar</AlertDialogCancel>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>

            <Button asChild>
              <Link href="/dashboard/projects/new">
                <Plus className="mr-2 h-4 w-4" /> Nuevo Proyecto
              </Link>
            </Button>
        </div>
      </div>

      {/* Mostrar mensaje de error si existe */}
      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  <div className="flex flex-col items-center justify-center">
                    <Loader2 className="h-8 w-8 animate-spin mb-2" />
                    <p className="text-sm text-muted-foreground">Cargando proyectos...</p>
                  </div>
                </TableCell>
              </TableRow>
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No se encontraron resultados.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredRowModel().rows.length} proyecto(s) encontrado(s).
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Anterior
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Siguiente
          </Button>
        </div>
      </div>
      {/* Diálogo de confirmación para eliminar proyecto */}
      <AlertDialog open={!!projectToDelete} onOpenChange={(open) => !open && setProjectToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>¿Eliminar este proyecto?</AlertDialogTitle>
            <AlertDialogDescription>
              Esta acción no se puede deshacer. ¿Estás seguro de que quieres eliminar el proyecto
              {projectToDelete ? ` "${projectToDelete.name}"` : ''}?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteProject}
              disabled={isDeleting}
              className="bg-red-500 hover:bg-red-600"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Eliminando...
                </>
              ) : (
                "Eliminar"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
