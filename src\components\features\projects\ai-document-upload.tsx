/**
 * @ai-file-description: "Component for uploading and analyzing documents with AI for project creation"
 * @ai-related-files: ["project-form.tsx", "../../shared/file-upload.tsx"]
 * @ai-owner: "File-Based Projects"
 */

"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { createClient } from "@/lib/supabase/client";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { FileUpload } from "@/components/shared/file-upload";
import { Loader2, FileText, Check, AlertCircle } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { ProviderFactory } from "@/lib/ai-providers/provider-factory";

/**
 * Component for uploading and analyzing documents with AI for project creation
 *
 * @ai-responsibility: "Handles document upload, AI provider selection, and analysis initiation"
 */
export function AIDocumentUpload() {
  const [isUploading, setIsUploading] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [uploadedDocument, setUploadedDocument] = useState<any>(null);
  const [selectedProvider, setSelectedProvider] = useState<string>("gemini");
  const [availableProviders, setAvailableProviders] = useState<{id: string, name: string}[]>([]);
  const [analysisId, setAnalysisId] = useState<string | null>(null);
  const [analysisStatus, setAnalysisStatus] = useState<string | null>(null);
  const [analysisError, setAnalysisError] = useState<string | null>(null);

  const router = useRouter();
  const supabase = createClient();

  // Fetch available providers
  useEffect(() => {
    async function fetchProviders() {
      try {
        const response = await fetch('/api/ai/providers?active=true');
        if (response.ok) {
          const data = await response.json();
          if (data && data.length > 0) {
            setAvailableProviders(data);
            setSelectedProvider(data[0].id);
          } else {
            // If no providers are available from API, use default list
            setAvailableProviders(ProviderFactory.getSupportedProvidersWithDisplayNames());
          }
        } else {
          // If API fails, use default list
          setAvailableProviders(ProviderFactory.getSupportedProvidersWithDisplayNames());
        }
      } catch (error) {
        console.error('Error fetching AI providers:', error);
        // Fallback to default providers
        setAvailableProviders(ProviderFactory.getSupportedProvidersWithDisplayNames());
      }
    }

    fetchProviders();
  }, []);

  // Poll for analysis status if analysis is in progress
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (analysisId && analysisStatus === 'pending') {
      interval = setInterval(checkAnalysisStatus, 3000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [analysisId, analysisStatus]);

  /**
   * Check the status of an ongoing analysis
   */
  const checkAnalysisStatus = async () => {
    if (!analysisId) {
      console.warn('Analysis ID is null, cannot check status.');
      setIsAnalyzing(false); // Stop analyzing if ID is null
      setAnalysisStatus('failed'); // Set status to failed if no ID
      setAnalysisError('Analysis could not be started.');
      return;
    }

    try {
      // Assign analysisId to a local variable after the null check
      const currentAnalysisId = analysisId;
      if (!currentAnalysisId) {
        // This should theoretically not happen due to the check outside the try block,
        // but it satisfies TypeScript's flow analysis.
        throw new Error('Analysis ID became null unexpectedly.');
      }
      const { data, error } = await supabase
        .from('ai_document_analyses')
        .select('status, error_message')
        .eq('id', currentAnalysisId!) // Use non-null assertion
        .single();

      if (error) {
        console.error('Error checking analysis status:', error);
        setAnalysisStatus('failed');
        const errorMessage = error instanceof Error ? error.message : 'Failed to check analysis status';

        setAnalysisError(errorMessage);
        setIsAnalyzing(false);
        toast({
          title: "Analysis Status Error",
          description: errorMessage,
          variant: "destructive",
        });
        return;
      }

      // Check if data is null or undefined before accessing properties
      if (!data) {
         console.warn('No data returned when checking analysis status.');
         setAnalysisStatus('failed');
         setAnalysisError('No analysis data found.');
         setIsAnalyzing(false);
         toast({
           title: "Analysis Status Error",
           description: "No analysis data found.",
           variant: "destructive",
         });
         return;
      }

      // Ensure data is not an error object before accessing properties
      if ('status' in data) {
        setAnalysisStatus(data.status);

        if (data.status === 'failed') {
          setAnalysisError(data.error_message || 'Analysis failed');
          setIsAnalyzing(false);
          toast({
            title: "Analysis Failed",
            description: data.error_message || "Failed to analyze document",
            variant: "destructive",
          });
        } else if (data.status === 'completed') {
          setIsAnalyzing(false);
          toast({
            title: "Analysis Complete",
            description: "Document analyzed successfully",
          });

          // Navigate to create project from analysis
          router.push(`/dashboard/projects/new/from-analysis/${analysisId}`);
        }
      } else {
         console.error('Unexpected data format when checking analysis status:', data);
         setAnalysisStatus('failed');
         setAnalysisError('Unexpected data format from analysis status check.');
         setIsAnalyzing(false);
         toast({
           title: "Analysis Status Error",
           description: "Unexpected data format from analysis status check.",
           variant: "destructive",
         });
      }

    } catch (error) {
      console.error('Error checking analysis status:', error);
      setAnalysisStatus('failed');
      setAnalysisError((error as Error).message || 'An unexpected error occurred');
      setIsAnalyzing(false);
      toast({
        title: "Analysis Error",
        description: (error as Error).message || "An unexpected error occurred",
        variant: "destructive",
      });
    }
  };

  /**
   * Handle document upload completion
   */
  const handleUploadComplete = (document: unknown) => { // document ahora es DocumentRow
    setUploadedDocument(document);
    toast({
      title: "Upload Complete",
      description: "Document uploaded successfully",
    });
  };

  /**
   * Start document analysis with selected provider
   */
  const startAnalysis = async () => {
    if (!uploadedDocument) {
      toast({
        title: "No Document",
        description: "Please upload a document first",
        variant: "destructive",
      });
      return;
    }

    setIsAnalyzing(true);
    setAnalysisError(null);

    try {
      const response = await fetch('/api/ai/analyze-document', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          documentId: uploadedDocument.id, // Usar el ID del documento insertado
          providerId: selectedProvider,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to start analysis');
      }

      setAnalysisId(data.id);
      setAnalysisStatus('pending');

      toast({
        title: "Analysis Started",
        description: "Document analysis has started",
      });
    } catch (error) {
      console.error('Error starting analysis:', error);
      setIsAnalyzing(false);
      setAnalysisError((error as Error).message);

      toast({
        title: "Analysis Error",
        description: (error as Error).message || "Failed to start analysis",
        variant: "destructive",
      });
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Create Project from Document</CardTitle>
        <CardDescription>
          Upload a document and use AI to extract project information
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Document Upload */}
        <div className="space-y-2">
          <Label>Upload Document</Label>
          <FileUpload
            bucket="documents"
            folder="projects"
            acceptedFileTypes={['.pdf', '.docx', '.txt']}
            maxFileSizeMB={10}
            onUploadComplete={handleUploadComplete}
            onUploadStart={() => setIsUploading(true)}
            onUploadError={(error) => {
              setIsUploading(false);
              toast({
                title: "Upload Error",
                description: error,
                variant: "destructive",
              });
            }}
          />

          {uploadedDocument && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground mt-2">
              <FileText className="h-4 w-4" />
              <span>{uploadedDocument.filename}</span>
              <Check className="h-4 w-4 text-green-500" />
            </div>
          )}
        </div>

        {/* AI Provider Selection */}
        <div className="space-y-2">
          <Label htmlFor="ai-provider">AI Provider</Label>
          <Select
            value={selectedProvider}
            onValueChange={setSelectedProvider}
            disabled={isAnalyzing || availableProviders.length === 0}
          >
            <SelectTrigger id="ai-provider">
              <SelectValue placeholder="Select AI Provider" />
            </SelectTrigger>
            <SelectContent>
              {availableProviders.map((provider) => (
                <SelectItem key={provider.id} value={provider.id}>
                  {provider.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <p className="text-sm text-muted-foreground">
            Select the AI provider to analyze your document
          </p>
        </div>

        {/* Analysis Status */}
        {analysisStatus === 'pending' && (
          <Alert>
            <Loader2 className="h-4 w-4 animate-spin" />
            <AlertTitle>Analyzing Document</AlertTitle>
            <AlertDescription>
              Please wait while we analyze your document. This may take a few moments.
            </AlertDescription>
          </Alert>
        )}

        {analysisError && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Analysis Error</AlertTitle>
            <AlertDescription>{analysisError}</AlertDescription>
          </Alert>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          onClick={() => router.push('/dashboard/projects/new')}
          disabled={isUploading || isAnalyzing}
        >
          Cancel
        </Button>
        <Button
          onClick={startAnalysis}
          disabled={!uploadedDocument || isUploading || isAnalyzing}
        >
          {isAnalyzing ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Analyzing...
            </>
          ) : (
            'Analyze Document'
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
