/**
 * @file Gestor de caché para análisis de documentos
 * @description Implementa un sistema de caché para almacenar los resultados de análisis de documentos
 */

import { localDb, CachedDocumentAnalysis } from '@/lib/db/local-database';
import { documentAnalysisClient, AnalysisResult } from './document-analysis-client';

/**
 * Clase para gestionar la caché de análisis de documentos
 */
export class DocumentAnalysisCache {
  /**
   * Guarda un resultado de análisis en la caché local
   * 
   * @param analysisResult Resultado del análisis
   * @returns ID del análisis guardado
   */
  async saveAnalysisToCache(analysisResult: AnalysisResult): Promise<string> {
    try {
      const cachedAnalysis: CachedDocumentAnalysis = {
        id: analysisResult.analysis_id,
        document_id: analysisResult.document_id,
        provider: 'lmstudio',
        status: analysisResult.status,
        analysis_data: analysisResult,
        confidence_score: analysisResult.project_info.confidence_score,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        _synced: false,
        _local_updated_at: Date.now()
      };
      
      await localDb.documentAnalyses.put(cachedAnalysis);
      
      // Agregar operación pendiente para sincronizar con Supabase
      await localDb.addPendingOperation({
        table: 'documentAnalyses',
        operation: 'insert',
        record_id: analysisResult.analysis_id,
        data: cachedAnalysis,
        priority: 3 // Prioridad media
      });
      
      return analysisResult.analysis_id;
    } catch (error) {
      console.error('Error al guardar análisis en caché:', error);
      throw error;
    }
  }
  
  /**
   * Obtiene un resultado de análisis de la caché local
   * 
   * @param analysisId ID del análisis
   * @returns Resultado del análisis o null si no existe
   */
  async getAnalysisFromCache(analysisId: string): Promise<AnalysisResult | null> {
    try {
      const cachedAnalysis = await localDb.documentAnalyses.get(analysisId);
      
      if (cachedAnalysis) {
        return cachedAnalysis.analysis_data;
      }
      
      return null;
    } catch (error) {
      console.error('Error al obtener análisis de caché:', error);
      return null;
    }
  }
  
  /**
   * Obtiene un resultado de análisis, primero de la caché y si no existe, del servidor
   * 
   * @param analysisId ID del análisis
   * @returns Resultado del análisis
   */
  async getAnalysis(analysisId: string): Promise<AnalysisResult> {
    try {
      // Intentar obtener de la caché
      const cachedAnalysis = await this.getAnalysisFromCache(analysisId);
      
      if (cachedAnalysis) {
        console.log('Análisis obtenido de caché:', analysisId);
        
        // Si el análisis está en proceso, verificar con el servidor
        if (cachedAnalysis.status === 'processing') {
          try {
            const serverAnalysis = await documentAnalysisClient.getAnalysisResult(analysisId);
            
            // Si el estado ha cambiado, actualizar la caché
            if (serverAnalysis.status !== cachedAnalysis.status) {
              await this.saveAnalysisToCache(serverAnalysis);
              return serverAnalysis;
            }
          } catch (error) {
            console.error('Error al verificar estado con el servidor:', error);
          }
        }
        
        return cachedAnalysis;
      }
      
      // Si no está en caché, obtener del servidor
      console.log('Análisis no encontrado en caché, obteniendo del servidor:', analysisId);
      const serverAnalysis = await documentAnalysisClient.getAnalysisResult(analysisId);
      
      // Guardar en caché
      await this.saveAnalysisToCache(serverAnalysis);
      
      return serverAnalysis;
    } catch (error) {
      console.error('Error al obtener análisis:', error);
      throw error;
    }
  }
  
  /**
   * Obtiene todos los análisis de documentos de la caché local
   * 
   * @param limit Límite de resultados
   * @returns Lista de análisis
   */
  async getAllAnalysesFromCache(limit: number = 50): Promise<CachedDocumentAnalysis[]> {
    try {
      return await localDb.documentAnalyses
        .orderBy('created_at')
        .reverse()
        .limit(limit)
        .toArray();
    } catch (error) {
      console.error('Error al obtener análisis de caché:', error);
      return [];
    }
  }
  
  /**
   * Obtiene los análisis de un documento específico
   * 
   * @param documentId ID del documento
   * @returns Lista de análisis
   */
  async getAnalysesByDocumentId(documentId: string): Promise<CachedDocumentAnalysis[]> {
    try {
      return await localDb.documentAnalyses
        .where('document_id')
        .equals(documentId)
        .toArray();
    } catch (error) {
      console.error('Error al obtener análisis por documento:', error);
      return [];
    }
  }
  
  /**
   * Elimina un análisis de la caché local
   * 
   * @param analysisId ID del análisis
   */
  async deleteAnalysisFromCache(analysisId: string): Promise<void> {
    try {
      await localDb.documentAnalyses.delete(analysisId);
      
      // Agregar operación pendiente para sincronizar con Supabase
      await localDb.addPendingOperation({
        table: 'documentAnalyses',
        operation: 'delete',
        record_id: analysisId,
        data: { id: analysisId },
        priority: 3 // Prioridad media
      });
    } catch (error) {
      console.error('Error al eliminar análisis de caché:', error);
      throw error;
    }
  }
  
  /**
   * Limpia la caché de análisis antiguos (más de 30 días)
   * 
   * @returns Número de registros eliminados
   */
  async cleanupOldAnalyses(): Promise<number> {
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      const oldAnalyses = await localDb.documentAnalyses
        .where('created_at')
        .below(thirtyDaysAgo.toISOString())
        .toArray();
      
      // Eliminar los análisis antiguos
      await Promise.all(oldAnalyses.map(analysis => 
        localDb.documentAnalyses.delete(analysis.id)
      ));
      
      return oldAnalyses.length;
    } catch (error) {
      console.error('Error al limpiar análisis antiguos:', error);
      return 0;
    }
  }
}

// Exportar una instancia única del gestor de caché
export const documentAnalysisCache = new DocumentAnalysisCache();
