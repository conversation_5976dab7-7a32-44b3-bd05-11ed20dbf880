import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"

export default function HomePage() {
  return (
    <div className="container flex h-screen w-screen flex-col items-center justify-center">
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px] md:w-[450px]">
        <div className="flex flex-col space-y-2 text-center">
          <Image
            src="/logo.svg"
            width={80}
            height={80}
            alt="AdminCore Logo"
            className="mx-auto"
          />
          <h1 className="text-2xl font-semibold tracking-tight">
            Bienvenido a AdminCore
          </h1>
          <p className="text-sm text-muted-foreground">
            Por favor, inicia sesión para continuar
          </p>
        </div>

        <div className="flex flex-col space-y-4">
          <Button asChild className="w-full">
            <Link href="/auth/login">
              Iniciar sesión
            </Link>
          </Button>

          <Button asChild variant="outline" className="w-full">
            <Link href="/auth/register">
              Crear una cuenta
            </Link>
          </Button>
        </div>
      </div>
    </div>
  )
}
