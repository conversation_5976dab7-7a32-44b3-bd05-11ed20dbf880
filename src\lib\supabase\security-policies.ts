/**
 * @ai-file-description: "Configuración de políticas de seguridad para Supabase"
 * @ai-owner: "Supabase Integration"
 */

import { createClient } from './client';

/**
 * Configura las políticas de seguridad para los buckets de almacenamiento
 * y las tablas de la base de datos.
 */
export async function setupSecurityPolicies() {
  try {
    const supabase = createClient();

    // Verificar si el usuario tiene permisos de administrador
    // No pasar parámetros a getUser() para compatibilidad con todas las versiones de Supabase
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    // Verificar si el usuario es administrador
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (profileError) {
      console.error('Error al verificar permisos:', profileError);
      throw new Error('Error al verificar permisos de usuario');
    }

    if (profile.role !== 'admin') {
      throw new Error('Se requieren permisos de administrador para configurar políticas de seguridad');
    }

    // Configurar políticas para el bucket de documentos
    // Nota: Esto requiere una función RPC en Supabase que configure las políticas
    const { data: policiesResult, error: policiesError } = await supabase.rpc(
      'configure_storage_policies',
      { bucket_name: 'documents' }
    );

    if (policiesError) {
      console.error('Error al configurar políticas de almacenamiento:', policiesError);
      throw policiesError;
    }

    console.log('Políticas de seguridad configuradas correctamente');
    return { success: true, message: 'Políticas de seguridad configuradas correctamente' };
  } catch (error) {
    console.error('Error al configurar políticas de seguridad:', error);
    return { success: false, error };
  }
}

/**
 * Función para verificar si un usuario tiene acceso a un bucket específico
 */
export async function checkBucketAccess(bucketName: string) {
  try {
    const supabase = createClient();

    // Intentar listar archivos en el bucket para verificar acceso
    const { data, error } = await supabase.storage
      .from(bucketName)
      .list('', { limit: 1 });

    if (error) {
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

      if (errorMessage.includes('row level security')) {
        return { hasAccess: false, error: 'No tienes permisos para acceder a este bucket' };
      }
      throw error;
    }

    return { hasAccess: true };
  } catch (error) {
    console.error(`Error al verificar acceso al bucket ${bucketName}:`, error);
    return { hasAccess: false, error };
  }
}
