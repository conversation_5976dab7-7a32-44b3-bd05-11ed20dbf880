"use client"

import { useState } from 'react'
import Image, { ImageProps } from 'next/image'
import { cn } from '@/lib/utils'
import { supabaseImageLoader, generatePlaceholder } from '@/lib/image-config'

interface OptimizedImageProps extends Omit<ImageProps, 'src'> {
  src: string
  fallbackSrc?: string
  aspectRatio?: string
  containerClassName?: string
}

/**
 * Componente de imagen optimizado para Vercel
 * 
 * Características:
 * - Carga perezosa por defecto
 * - Manejo de errores con imagen de respaldo
 * - Soporte para relación de aspecto personalizada
 * - Optimización automática de imágenes de Supabase
 * - Placeholder durante la carga
 * 
 * @param props Propiedades del componente
 * @returns Componente de imagen optimizado
 */
export function OptimizedImage({
  src,
  fallbackSrc,
  alt,
  width,
  height,
  aspectRatio = 'auto',
  containerClassName,
  className,
  ...props
}: OptimizedImageProps) {
  const [error, setError] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  
  // Determinar la fuente de la imagen
  const imageSrc = error && fallbackSrc ? fallbackSrc : src
  
  // Generar un placeholder si no se proporciona uno
  const placeholder = props.placeholder || 'blur'
  const blurDataURL = props.blurDataURL || generatePlaceholder(
    typeof width === 'number' ? width : 200,
    typeof height === 'number' ? height : 200,
    alt?.slice(0, 10) || 'Loading'
  )
  
  return (
    <div 
      className={cn(
        'overflow-hidden', 
        isLoading && 'animate-pulse bg-muted/30',
        containerClassName
      )}
      style={{ aspectRatio }}
    >
      <Image
        src={imageSrc}
        alt={alt}
        width={width}
        height={height}
        loader={supabaseImageLoader}
        className={cn(
          'transition-opacity duration-300',
          isLoading ? 'opacity-0' : 'opacity-100',
          className
        )}
        onError={() => {
          if (!error && fallbackSrc) {
            setError(true)
          }
        }}
        onLoad={() => setIsLoading(false)}
        placeholder={placeholder}
        blurDataURL={blurDataURL}
        loading={props.priority ? 'eager' : 'lazy'}
        {...props}
      />
    </div>
  )
}
