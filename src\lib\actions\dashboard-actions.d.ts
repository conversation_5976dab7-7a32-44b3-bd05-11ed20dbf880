export interface ProjectDistributionItem {
  name: string
  value: number
}

export interface PerformanceMetricsItem {
  name: string
  value: number
  target: number
}

export interface ResourceAllocationItem {
  name: string
  value: number
  capacity: number
}

export interface DashboardData {
  projectDistribution: ProjectDistributionItem[]
  performanceMetrics: PerformanceMetricsItem[]
  resourceAllocation: ResourceAllocationItem[]
}

export function getDashboardData(): Promise<DashboardData>