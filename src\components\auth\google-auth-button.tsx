'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { createClient } from '@/lib/supabase/client'
import { validateOAuthConfig } from '@/lib/supabase/oauth-config'
import { FcGoogle } from 'react-icons/fc'

interface GoogleAuthButtonProps {
  mode?: 'signin' | 'signup'
  className?: string
  fullWidth?: boolean
}

export function GoogleAuthButton({
  mode = 'signin',
  className = '',
  fullWidth = false,
}: GoogleAuthButtonProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const supabase = createClient()

  const handleGoogleAuth = async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      console.log('Iniciando autenticación con Google...')
      
      // Verificar configuración de OAuth
      const isConfigValid = validateOAuthConfig()
      if (!isConfigValid) {
        console.warn('La configuración de OAuth no es válida, pero se intentará usar la configuración de Supabase')
      }
      
      // Iniciar flujo de autenticación con Google
      const { data, error: authError } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
        },
      })
      
      if (authError) {
        console.error('Error en autenticación con Google:', authError)
        throw authError
      }
      
      if (!data?.url) {
        console.error('No se recibió URL de autorización')
        throw new Error('No se pudo iniciar la autenticación con Google')
      }
      
      console.log('Autenticación con Google iniciada correctamente')
      console.log('URL de autorización:', data.url)
      
      // Redirigir al usuario a la URL de autorización de Google
      window.location.href = data.url
      
    } catch (e: unknown) {
      console.error('Error en autenticación con Google:', e)
      const errorMessage = e instanceof Error ? e.message : 'Error al iniciar sesión con Google';

      setError(errorMessage)
      setIsLoading(false)
    }
  }

  return (
    <div className="w-full space-y-2">
      <Button
        variant="outline"
        onClick={handleGoogleAuth}
        disabled={isLoading}
        className={`relative ${fullWidth ? 'w-full' : ''} ${className}`}
      >
        <FcGoogle className="mr-2 h-4 w-4" />
        {mode === 'signin' ? 'Iniciar sesión con Google' : 'Registrarse con Google'}
      </Button>
      {error && (
        <p className="text-sm text-red-500">{error}</p>
      )}
    </div>
  )
}
