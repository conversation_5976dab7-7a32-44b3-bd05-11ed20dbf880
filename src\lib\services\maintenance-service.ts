/**
 * @file Maintenance Service
 * @description Service for managing maintenance schedules and activities
 */

import { createClient } from '@/lib/supabase/client';
import { 
  MaintenanceSchedule, 
  CreateMaintenanceScheduleInput,
  MaintenanceType,
  MaintenanceFrequency
} from '@/types/service-management';

/**
 * Service for managing maintenance schedules and activities
 */
export class MaintenanceService {
  /**
   * Get all maintenance schedules with optional filtering
   * 
   * @param options Filter options
   * @returns Promise with maintenance schedules
   */
  async getMaintenanceSchedules(options: {
    equipmentId?: string;
    clientId?: string;
    maintenanceType?: MaintenanceType | MaintenanceType[];
    isActive?: boolean;
    isDue?: boolean;
    search?: string;
    limit?: number;
    offset?: number;
    orderBy?: string;
    orderDirection?: 'asc' | 'desc';
  } = {}): Promise<{ data: MaintenanceSchedule[] | null; error: unknown }> {
    const supabase = createClient();
    
    // Start building the query
    let query = supabase
      .from('maintenance_schedules')
      .select(`
        *,
        equipment:equipment_id(
          id, 
          name, 
          model, 
          serial_number, 
          client_id, 
          client:client_id(id, name)
        )
      `);
    
    // Apply filters
    if (options.equipmentId) {
      query = query.eq('equipment_id', options.equipmentId);
    }
    
    if (options.maintenanceType) {
      if (Array.isArray(options.maintenanceType)) {
        query = query.in('maintenance_type', options.maintenanceType);
      } else {
        query = query.eq('maintenance_type', options.maintenanceType);
      }
    }
    
    if (options.isActive !== undefined) {
      query = query.eq('is_active', options.isActive);
    }
    
    if (options.isDue) {
      const today = new Date().toISOString();
      query = query.lte('next_maintenance_date', today);
    }
    
    if (options.search) {
      query = query.or(`description.ilike.%${options.search}%`);
    }
    
    // Apply ordering
    if (options.orderBy) {
      query = query.order(options.orderBy, { 
        ascending: options.orderDirection === 'asc' 
      });
    } else {
      // Default ordering by next_maintenance_date ascending (soonest first)
      query = query.order('next_maintenance_date', { ascending: true });
    }
    
    // Apply pagination
    if (options.limit) {
      query = query.limit(options.limit);
    }
    
    if (options.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
    }
    
    // Execute the query
    const { data, error } = await query;
    
    // If clientId filter is provided, filter the results after fetching
    // (since we need to access the nested equipment.client_id)
    if (!error && data && options.clientId) {
      const filteredData = data.filter(schedule => 
        schedule.equipment && schedule.equipment.client_id === options.clientId
      );
      return { data: filteredData, error };
    }
    
    return { data, error };
  }

  /**
   * Get a maintenance schedule by ID
   * 
   * @param id Maintenance schedule ID
   * @returns Promise with maintenance schedule
   */
  async getMaintenanceScheduleById(id: string): Promise<{ data: MaintenanceSchedule | null; error: unknown }> {
    const supabase = createClient();
    
    const { data, error } = await supabase
      .from('maintenance_schedules')
      .select(`
        *,
        equipment:equipment_id(
          id, 
          name, 
          model, 
          serial_number, 
          client_id, 
          client:client_id(id, name)
        )
      `)
      .eq('id', id)
      .single();
    
    return { data, error };
  }

  /**
   * Create a new maintenance schedule
   * 
   * @param schedule Maintenance schedule data
   * @returns Promise with created maintenance schedule
   */
  async createMaintenanceSchedule(schedule: CreateMaintenanceScheduleInput): Promise<{ data: MaintenanceSchedule | null; error: unknown }> {
    const supabase = createClient();
    
    // Get current user for created_by field
    const { data: userData } = await supabase.auth.getUser();
    const userId = userData?.user?.id;
    
    // Calculate next maintenance date if not provided
    let nextMaintenanceDate = schedule.next_maintenance_date;
    if (!nextMaintenanceDate && schedule.frequency) {
      nextMaintenanceDate = this.calculateNextMaintenanceDate(new Date(), schedule.frequency);
    }
    
    // Prepare the data
    const newSchedule = {
      ...schedule,
      next_maintenance_date: nextMaintenanceDate,
      is_active: schedule.is_active !== undefined ? schedule.is_active : true,
      created_by: userId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      repeat_count: 0
    };
    
    // Insert the maintenance schedule
    const { data, error } = await supabase
      .from('maintenance_schedules')
      .insert(newSchedule)
      .select(`
        *,
        equipment:equipment_id(
          id, 
          name, 
          model, 
          serial_number, 
          client_id, 
          client:client_id(id, name)
        )
      `)
      .single();
    
    return { data, error };
  }

  /**
   * Update a maintenance schedule
   * 
   * @param id Maintenance schedule ID
   * @param updates Maintenance schedule updates
   * @returns Promise with updated maintenance schedule
   */
  async updateMaintenanceSchedule(id: string, updates: Partial<MaintenanceSchedule>): Promise<{ data: MaintenanceSchedule | null; error: unknown }> {
    const supabase = createClient();
    
    // Prepare the updates
    const scheduleUpdates = {
      ...updates,
      updated_at: new Date().toISOString()
    };
    
    // Update the maintenance schedule
    const { data, error } = await supabase
      .from('maintenance_schedules')
      .update(scheduleUpdates)
      .eq('id', id)
      .select(`
        *,
        equipment:equipment_id(
          id, 
          name, 
          model, 
          serial_number, 
          client_id, 
          client:client_id(id, name)
        )
      `)
      .single();
    
    return { data, error };
  }

  /**
   * Delete a maintenance schedule
   * 
   * @param id Maintenance schedule ID
   * @returns Promise with deletion result
   */
  async deleteMaintenanceSchedule(id: string): Promise<{ error: unknown }> {
    const supabase = createClient();
    
    const { error } = await supabase
      .from('maintenance_schedules')
      .delete()
      .eq('id', id);
    
    return { error };
  }

  /**
   * Complete a maintenance and schedule the next one
   * 
   * @param scheduleId Maintenance schedule ID
   * @param completionDate Completion date (defaults to current date)
   * @returns Promise with updated maintenance schedule
   */
  async completeMaintenanceAndScheduleNext(
    scheduleId: string, 
    completionDate: Date = new Date()
  ): Promise<{ data: MaintenanceSchedule | null; error: unknown }> {
    const supabase = createClient();
    
    // Get the current schedule
    const { data: currentSchedule, error: fetchError } = await this.getMaintenanceScheduleById(scheduleId);
    
    if (fetchError || !currentSchedule) {
      return { data: null, error: fetchError || new Error('Maintenance schedule not found') };
    }
    
    // Calculate the next maintenance date based on frequency
    let nextMaintenanceDate: string | null = null;
    if (currentSchedule.frequency) {
      nextMaintenanceDate = this.calculateNextMaintenanceDate(completionDate, currentSchedule.frequency);
    }
    
    // Update the maintenance schedule
    const { data, error } = await supabase
      .from('maintenance_schedules')
      .update({
        last_maintenance_date: completionDate.toISOString(),
        next_maintenance_date: nextMaintenanceDate,
        repeat_count: (currentSchedule.repeat_count || 0) + 1,
        updated_at: new Date().toISOString()
      })
      .eq('id', scheduleId)
      .select(`
        *,
        equipment:equipment_id(
          id, 
          name, 
          model, 
          serial_number, 
          client_id, 
          client:client_id(id, name)
        )
      `)
      .single();
    
    // Update the equipment's last_service_date
    if (!error && data && data.equipment) {
      await supabase
        .from('customer_equipment')
        .update({
          last_service_date: completionDate.toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', data.equipment.id);
    }
    
    return { data, error };
  }

  /**
   * Generate work orders for due maintenance schedules
   * 
   * @param options Options for generating work orders
   * @returns Promise with generated work orders
   */
  async generateWorkOrdersForDueMaintenance(options: {
    daysAhead?: number;
    clientId?: string;
    maintenanceType?: MaintenanceType | MaintenanceType[];
    assignToDefault?: boolean;
  } = {}): Promise<{ data: { generated: number; schedules: string[] } | null; error: unknown }> {
    const supabase = createClient();
    
    // Calculate the date range for due maintenance
    const today = new Date();
    const endDate = new Date();
    endDate.setDate(today.getDate() + (options.daysAhead || 0));
    
    // Get due maintenance schedules
    const { data: dueSchedules, error: fetchError } = await this.getMaintenanceSchedules({
      isActive: true,
      maintenanceType: options.maintenanceType,
      clientId: options.clientId
    });
    
    if (fetchError || !dueSchedules) {
      return { data: null, error: fetchError || new Error('Failed to fetch maintenance schedules') };
    }
    
    // Filter schedules that are due within the date range
    const filteredSchedules = dueSchedules.filter(schedule => {
      if (!schedule.next_maintenance_date) return false;
      
      const nextDate = new Date(schedule.next_maintenance_date);
      return nextDate <= endDate;
    });
    
    if (filteredSchedules.length === 0) {
      return { data: { generated: 0, schedules: [] }, error: null };
    }
    
    // Generate work orders for each due schedule
    const generatedWorkOrders: string[] = [];
    let errorOccurred = null;
    
    for (const schedule of filteredSchedules) {
      try {
        // Call a stored procedure or function to create work orders
        // This is a placeholder - you would implement this with a Supabase function
        const { data: workOrder, error } = await supabase.rpc('create_work_order_from_maintenance', {
          maintenance_schedule_id: schedule.id,
          assign_to_default: options.assignToDefault || false
        });
        
        if (error) throw error;
        if (workOrder && workOrder.id) {
          generatedWorkOrders.push(workOrder.id);
        }
      } catch (error) {
        console.error('Error generating work order for schedule:', schedule.id, error);
        errorOccurred = error;
      }
    }
    
    return { 
      data: { 
        generated: generatedWorkOrders.length, 
        schedules: generatedWorkOrders 
      }, 
      error: errorOccurred 
    };
  }

  /**
   * Calculate the next maintenance date based on frequency
   * 
   * @param baseDate Base date to calculate from
   * @param frequency Maintenance frequency
   * @returns ISO string date for next maintenance
   */
  private calculateNextMaintenanceDate(baseDate: Date, frequency: MaintenanceFrequency): string {
    const nextDate = new Date(baseDate);
    
    switch (frequency) {
      case 'daily':
        nextDate.setDate(nextDate.getDate() + 1);
        break;
      case 'weekly':
        nextDate.setDate(nextDate.getDate() + 7);
        break;
      case 'biweekly':
        nextDate.setDate(nextDate.getDate() + 14);
        break;
      case 'monthly':
        nextDate.setMonth(nextDate.getMonth() + 1);
        break;
      case 'quarterly':
        nextDate.setMonth(nextDate.getMonth() + 3);
        break;
      case 'biannual':
        nextDate.setMonth(nextDate.getMonth() + 6);
        break;
      case 'annual':
        nextDate.setFullYear(nextDate.getFullYear() + 1);
        break;
      default:
        // For custom frequency, default to monthly
        nextDate.setMonth(nextDate.getMonth() + 1);
    }
    
    return nextDate.toISOString();
  }
}

// Export a singleton instance
export const maintenanceService = new MaintenanceService();
