import { NextResponse } from 'next/server';
import { getGitHubConnection, getGitHubRepositories, syncUserRepositories } from '@/lib/services/server/github-service.server';

// Disable caching for this route
export const dynamic = 'force-dynamic';



export async function POST() {
  try {
    // Get current connection to verify authentication
    const connection = await getGitHubConnection();
    if (!connection) {
      return NextResponse.json(
        { success: false, error: 'No GitHub connection found' },
        { status: 401 }
      );
    }
    // Perform the sync
    const success = await syncUserRepositories();
    if (!success) {
      return NextResponse.json(
        { success: false, error: 'Failed to sync repositories' },
        { status: 500 }
      );
    }
    // Get updated data after sync
    const updatedConnection = await getGitHubConnection();
    const repositories = updatedConnection ? await getGitHubRepositories() : [];
    return NextResponse.json({
      success: true,
      connection: updatedConnection,
      repositories,
      lastSynced: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Failed to sync repositories', error);
    const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
    return NextResponse.json(
      { 
        success: false, 
        error: 'Error del servidor',
        message: errorMessage,
        details: process.env.NODE_ENV === 'development' ? error : undefined
      }, 
      { status: 500 }
    );
  }
}
