'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { supabaseAdmin } from '@/lib/supabase/admin-client'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, Bug, RefreshCw } from "lucide-react"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export function AuthDebug() {
  const [session, setSession] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)
  const [showDebug, setShowDebug] = useState(false)
  const [envVars, setEnvVars] = useState<Record<string, string>>({})
  const supabase = createClient()

  useEffect(() => {
    async function getSession() {
      try {
        setLoading(true)
        const { data, error } = await supabase.auth.getSession()

        if (error) {
          throw error
        }

        setSession(data.session)

        // Obtener información sobre las variables de entorno
        setEnvVars({
          'NEXT_PUBLIC_SUPABASE_URL': process.env.NEXT_PUBLIC_SUPABASE_URL || 'No configurado',
          'NEXT_PUBLIC_SUPABASE_ANON_KEY': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Configurado' : 'No configurado',
          'SUPABASE_SERVICE_ROLE_KEY': process.env.SUPABASE_SERVICE_ROLE_KEY ? 'Configurado' : 'No configurado',
          'NEXT_PUBLIC_GOOGLE_CLIENT_ID': process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || 'No configurado',
          'Admin Client': supabaseAdmin ? 'Disponible' : 'No disponible',
        })
      } catch (e: unknown) {
        const errorMessage = e instanceof Error ? e.message : 'Error desconocido';

        setError(errorMessage)
      } finally {
        setLoading(false)
      }
    }

    getSession()
  }, [supabase])

  if (!showDebug) {
    return (
      <div className="fixed bottom-4 right-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowDebug(true)}
          className="flex items-center gap-2"
        >
          <Bug className="h-4 w-4" />
          Depuración
        </Button>
      </div>
    )
  }

  return (
    <div className="fixed bottom-4 right-4 w-96 z-50">
      <Card>
        <CardHeader className="pb-2">
          <div className="flex justify-between items-center">
            <CardTitle className="text-lg">Depuración de Autenticación</CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowDebug(false)}
            >
              Cerrar
            </Button>
          </div>
          <CardDescription>Información de la sesión actual</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="session">
            <TabsList className="w-full">
              <TabsTrigger value="session">Sesión</TabsTrigger>
              <TabsTrigger value="env">Entorno</TabsTrigger>
            </TabsList>
            <TabsContent value="session" className="pt-4">
              {loading ? (
                <p>Cargando información de sesión...</p>
              ) : error ? (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              ) : session ? (
                <div className="space-y-2 text-sm">
                  <div>
                    <strong>Usuario:</strong> {session.user?.email}
                  </div>
                  <div>
                    <strong>ID:</strong> {session.user?.id}
                  </div>
                  <div>
                    <strong>Proveedor:</strong> {session.user?.app_metadata?.provider || 'email'}
                  </div>
                  <div>
                    <strong>Expira:</strong> {new Date(session.expires_at * 1000).toLocaleString()}
                  </div>
                </div>
              ) : (
                <p>No hay sesión activa</p>
              )}
            </TabsContent>
            <TabsContent value="env" className="pt-4">
              <div className="space-y-2 text-sm">
                {Object.entries(envVars).map(([key, value]) => (
                  <div key={key}>
                    <strong>{key}:</strong> {value}
                  </div>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
        <CardFooter className="flex justify-between pt-2">
          <Button
            variant="outline"
            size="sm"
            onClick={async () => {
              try {
                const { error } = await supabase.auth.signOut()
                if (error) throw error
                setSession(null)
              } catch (e: unknown) {
                const errorMessage = e instanceof Error ? e.message : 'Error desconocido';

                setError(errorMessage)
              }
            }}
            disabled={!session}
          >
            Cerrar Sesión
          </Button>
          <Button
            variant="default"
            size="sm"
            onClick={async () => {
              try {
                setLoading(true)
                const { data, error } = await supabase.auth.getSession()
                if (error) throw error
                setSession(data.session)
                setError(null)

                // Actualizar información de entorno
                setEnvVars({
                  'NEXT_PUBLIC_SUPABASE_URL': process.env.NEXT_PUBLIC_SUPABASE_URL || 'No configurado',
                  'NEXT_PUBLIC_SUPABASE_ANON_KEY': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Configurado' : 'No configurado',
                  'SUPABASE_SERVICE_ROLE_KEY': process.env.SUPABASE_SERVICE_ROLE_KEY ? 'Configurado' : 'No configurado',
                  'NEXT_PUBLIC_GOOGLE_CLIENT_ID': process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || 'No configurado',
                  'Admin Client': supabaseAdmin ? 'Disponible' : 'No disponible',
                })
              } catch (e: unknown) {
                const errorMessage = e instanceof Error ? e.message : 'Error desconocido';

                setError(errorMessage)
              } finally {
                setLoading(false)
              }
            }}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Actualizar
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}
