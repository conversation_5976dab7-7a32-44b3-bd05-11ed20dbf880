'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/lib/supabase/client';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Loader2, FileText, Database, Table, Tag, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

interface AIProjectViewProps {
  projectId: string;
}

export function AIProjectView({ projectId }: AIProjectViewProps) {
  const [project, setProject] = useState<any>(null);
  const [aiProjectData, setAIProjectData] = useState<any>(null);
  const [dynamicData, setDynamicData] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const supabase = createClient();

  useEffect(() => {
    const fetchProjectData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Obtener datos del proyecto
        const { data: projectData, error: projectError } = await supabase
          .from('projects')
          .select('*, source_document:source_document_id(*)')
          .eq('id', projectId)
          .single();

        if (projectError) {
          throw new Error(`Error al obtener datos del proyecto: ${projectError.message}`);
        }

        setProject(projectData);

        // Obtener datos de AI Project
        const { data: aiProject, error: aiProjectError } = await supabase
          .from('ai_projects')
          .select('*')
          .eq('project_id', projectId)
          .single();

        if (!aiProjectError && aiProject) {
          setAIProjectData(aiProject);
        }

        // Obtener estructuras dinámicas
        const { data: dynamicStructures, error: dynamicError } = await supabase
          .from('ai_project_data')
          .select('*')
          .eq('project_id', projectId)
          .order('data_type', { ascending: true })
          .order('data_key', { ascending: true });

        if (dynamicError) {
          console.error('Error al obtener estructuras dinámicas:', dynamicError);
        } else {
          setDynamicData(dynamicStructures || []);
        }
      } catch (error) {
        console.error('Error al cargar datos del proyecto:', error);
        setError(error instanceof Error ? error.message : 'Error inesperado');
      } finally {
        setIsLoading(false);
      }
    };

    if (projectId) {
      fetchProjectData();
    }
  }, [projectId, supabase]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Cargando datos del proyecto...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (!project) {
    return (
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Proyecto no encontrado</AlertTitle>
        <AlertDescription>No se pudo encontrar el proyecto solicitado</AlertDescription>
      </Alert>
    );
  }

  // Agrupar datos dinámicos por tipo
  const customFields = dynamicData.filter(item => item.data_type === 'custom_field');
  const relatedTables = dynamicData.filter(item => item.data_type === 'related_table');
  const metadata = dynamicData.find(item => item.data_type === 'metadata' && item.data_key === 'ai_analysis');

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>{project.name}</CardTitle>
              <CardDescription>
                Proyecto generado por IA {project.ai_provider && `(${project.ai_provider})`}
              </CardDescription>
            </div>
            <Badge variant="outline" className="ml-2">
              IA
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium">Descripción</h3>
              <p className="text-sm text-muted-foreground mt-1">{project.description || 'Sin descripción'}</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="text-sm font-medium">Fecha de inicio</h4>
                <p className="text-sm">{project.start_date ? new Date(project.start_date).toLocaleDateString() : 'No especificada'}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium">Fecha de finalización</h4>
                <p className="text-sm">{project.end_date ? new Date(project.end_date).toLocaleDateString() : 'No especificada'}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium">Presupuesto</h4>
                <p className="text-sm">{project.budget ? `${project.budget} ${project.currency || 'CLP'}` : 'No especificado'}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium">Estado</h4>
                <p className="text-sm">{project.status || 'No especificado'}</p>
              </div>
            </div>

            {project.tags && project.tags.length > 0 && (
              <div>
                <h4 className="text-sm font-medium">Etiquetas</h4>
                <div className="flex flex-wrap gap-2 mt-1">
                  {project.tags.map((tag: string, index: number) => (
                    <Badge key={index} variant="secondary">
                      <Tag className="h-3 w-3 mr-1" />
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {project.source_document && (
              <div>
                <h4 className="text-sm font-medium">Documento fuente</h4>
                <div className="flex items-center mt-1">
                  <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span className="text-sm">{project.source_document.filename}</span>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="ai-data">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="ai-data">Datos de IA</TabsTrigger>
          <TabsTrigger value="custom-fields">Campos Personalizados</TabsTrigger>
          <TabsTrigger value="related-tables">Tablas Relacionadas</TabsTrigger>
        </TabsList>

        <TabsContent value="ai-data" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Datos del Análisis de IA</CardTitle>
              <CardDescription>
                Información extraída por IA del documento fuente
              </CardDescription>
            </CardHeader>
            <CardContent>
              {aiProjectData ? (
                <div className="space-y-4">
                  {aiProjectData.client_name && (
                    <div>
                      <h4 className="text-sm font-medium">Cliente</h4>
                      <p className="text-sm">{aiProjectData.client_name}</p>
                    </div>
                  )}

                  {aiProjectData.scope && (
                    <div>
                      <h4 className="text-sm font-medium">Alcance</h4>
                      <p className="text-sm">{aiProjectData.scope}</p>
                    </div>
                  )}

                  {aiProjectData.deliverables && aiProjectData.deliverables.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium">Entregables</h4>
                      <ul className="list-disc pl-5 mt-1">
                        {aiProjectData.deliverables.map((item: string, index: number) => (
                          <li key={index} className="text-sm">{item}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {aiProjectData.team_requirements && aiProjectData.team_requirements.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium">Requisitos de Equipo</h4>
                      <ul className="list-disc pl-5 mt-1">
                        {aiProjectData.team_requirements.map((item: string, index: number) => (
                          <li key={index} className="text-sm">{item}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {metadata && (
                    <div>
                      <h4 className="text-sm font-medium">Metadatos</h4>
                      <div className="bg-muted p-3 rounded-md mt-1">
                        <pre className="text-xs whitespace-pre-wrap">
                          {JSON.stringify(metadata.data_value, null, 2)}
                        </pre>
                      </div>
                    </div>
                  )}

                  <div>
                    <h4 className="text-sm font-medium">Confianza del Análisis</h4>
                    <div className="w-full bg-secondary rounded-full h-2 mt-2">
                      <div
                        className="bg-primary h-2 rounded-full"
                        style={{ width: `${(aiProjectData.confidence_score || 0) * 100}%` }}
                      />
                    </div>
                    <p className="text-xs text-right mt-1">
                      {Math.round((aiProjectData.confidence_score || 0) * 100)}%
                    </p>
                  </div>
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">No hay datos de análisis de IA disponibles</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="custom-fields" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Campos Personalizados</CardTitle>
              <CardDescription>
                Campos adicionales generados por IA para este proyecto
              </CardDescription>
            </CardHeader>
            <CardContent>
              {customFields.length > 0 ? (
                <div className="space-y-4">
                  {customFields.map((field) => (
                    <div key={field.id}>
                      <h4 className="text-sm font-medium">{field.data_key}</h4>
                      <div className="bg-muted p-3 rounded-md mt-1">
                        <pre className="text-xs whitespace-pre-wrap">
                          {typeof field.data_value === 'object'
                            ? JSON.stringify(field.data_value, null, 2)
                            : field.data_value}
                        </pre>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">No hay campos personalizados para este proyecto</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="related-tables" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Tablas Relacionadas</CardTitle>
              <CardDescription>
                Estructuras de datos adicionales generadas por IA
              </CardDescription>
            </CardHeader>
            <CardContent>
              {relatedTables.length > 0 ? (
                <div className="space-y-6">
                  {relatedTables.map((table) => (
                    <div key={table.id} className="border rounded-md">
                      <div className="bg-muted p-3 border-b">
                        <div className="flex items-center">
                          <Table className="h-4 w-4 mr-2" />
                          <h4 className="text-sm font-medium">{table.data_value.display_name || table.data_key}</h4>
                        </div>
                      </div>
                      <div className="p-3">
                        <div className="overflow-x-auto">
                          <table className="w-full text-sm">
                            <thead>
                              <tr className="border-b">
                                {table.data_value.schema && Object.keys(table.data_value.schema).map((column) => (
                                  <th key={column} className="px-2 py-1 text-left font-medium">{column}</th>
                                ))}
                              </tr>
                            </thead>
                            <tbody>
                              {table.data_value.records && table.data_value.records.map((record: unknown, index: number) => (
                                <tr key={index} className="border-b last:border-0">
                                  {Object.keys(table.data_value.schema).map((column) => (
                                    <td key={`${index}-${column}`} className="px-2 py-1">
                                      {record[column] !== null && record[column] !== undefined
                                        ? typeof record[column] === 'object'
                                          ? JSON.stringify(record[column])
                                          : String(record[column])
                                        : '-'}
                                    </td>
                                  ))}
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">No hay tablas relacionadas para este proyecto</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
