import { NextResponse } from 'next/server'

// Configuración por defecto usando variables de entorno
const defaultConfig = {
  pdf_extractor_url: process.env.NEXT_PUBLIC_PDF_EXTRACTOR_URL || "http://localhost:8001",
  lmstudio_connector_url: process.env.NEXT_PUBLIC_LMSTUDIO_CONNECTOR_URL || "http://localhost:8003",
  document_analyzer_url: process.env.NEXT_PUBLIC_DOCUMENT_ANALYSIS_URL || "http://localhost:8002",
  cache_enabled: true,
  cache_ttl_hours: 24,
  max_file_size_mb: 10,
  allowed_file_types: ".pdf,.doc,.docx,.txt",
  auto_start_service: process.env.NEXT_PUBLIC_DOCUMENT_ANALYSIS_ENABLED === "true",
}

// En una implementación real, esto se guardaría en una base de datos
let currentConfig = { ...defaultConfig }

export async function GET() {
  try {
    return NextResponse.json(currentConfig)
  } catch (error) {
    console.error('Error al obtener la configuración:', error)
    return NextResponse.json(
      { error: 'Error al obtener la configuración' },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    const data = await request.json()

    // Validar los datos recibidos
    if (!data.pdf_extractor_url || !data.lmstudio_connector_url || !data.document_analyzer_url) {
      return NextResponse.json(
        { error: 'Faltan campos obligatorios' },
        { status: 400 }
      )
    }

    // Actualizar la configuración
    currentConfig = {
      ...currentConfig,
      ...data,
      // Asegurarse de que los valores numéricos sean números
      cache_ttl_hours: Number(data.cache_ttl_hours),
      max_file_size_mb: Number(data.max_file_size_mb),
    }

    return NextResponse.json({
      message: 'Configuración actualizada correctamente',
      config: currentConfig
    })
  } catch (error) {
    console.error('Error al actualizar la configuración:', error)
    return NextResponse.json(
      { error: 'Error al actualizar la configuración' },
      { status: 500 }
    )
  }
}
