'use server'

import { supabaseAdmin } from '@/lib/supabase/admin-client';

/**
 * Ejecuta SQL directamente en la base de datos
 * @param sql Script SQL a ejecutar
 */
async function executeDirectSQL(sql: string): Promise<void> {
  // Dividir el script en sentencias individuales
  const statements = sql
    .split(';')
    .map(stmt => stmt.trim())
    .filter(stmt => stmt.length > 0);

  // Ejecutar cada sentencia por separado
  for (const stmt of statements) {
    try {
      // Usar el método rpc con la función pgSQL para ejecutar SQL directamente
      // Esta es la forma recomendada en Supabase v2
      await supabaseAdmin.rpc('pgSQL', { query: `${stmt};` });
    } catch (error) {
      // Si la función pgSQL no existe, intentar con el método alternativo
      try {
        console.log(`Intentando método alternativo para ejecutar SQL: ${stmt}`);

        // Verificar que supabaseAdmin tenga la configuración necesaria
        if (!supabaseAdmin) {
          throw new Error('Cliente de Supabase no inicializado correctamente');
        }

        // Obtener la URL y la clave de servicio del cliente
        const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
        const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

        if (!supabaseUrl || !supabaseServiceKey) {
          throw new Error('Variables de entorno de Supabase no configuradas');
        }

        // Usar REST API para ejecutar SQL directamente
        const response = await fetch(`${supabaseUrl}/rest/v1/`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${supabaseServiceKey}`,
            'apikey': supabaseServiceKey,
            'Prefer': 'params=single-object'
          },
          body: JSON.stringify({
            query: `${stmt};`
          })
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(`Error en respuesta API: ${JSON.stringify(errorData)}`);
        }
      } catch (alternativeError) {
        console.error(`Error al ejecutar sentencia SQL (método alternativo): ${stmt}`, alternativeError);
        throw alternativeError;
      }
    }
  }
}

/**
 * Verifica directamente si una función existe en la base de datos
 * @param functionName Nombre de la función a verificar
 * @returns Promise<boolean> true si la función existe, false en caso contrario
 */
async function checkFunctionExistsDirectly(functionName: string): Promise<boolean> {
  try {
    const { data, error } = await supabaseAdmin.from('pg_proc')
      .select('proname')
      .eq('proname', functionName)
      .limit(1);

    if (error) {
      console.error(`Error al verificar si la función ${functionName} existe:`, error);
      return false;
    }

    return data && data.length > 0;
  } catch (error) {
    console.error(`Error al verificar si la función ${functionName} existe:`, error);
    return false;
  }
}

/**
 * Verifica si las funciones del dashboard están configuradas
 * @returns Promise<boolean> true si están configuradas, false en caso contrario
 */
export async function areDashboardFunctionsConfigured(): Promise<boolean> {
  try {
    try {
      // Verificar si existe la función get_project_distribution
      const { data, error } = await supabaseAdmin.rpc('function_exists', {
        function_name: 'get_project_distribution'
      });

      if (error) {
        // Si el error es porque la función function_exists no existe, verificar directamente
        const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

        if (error.message && error.message.includes('function') && errorMessage.includes('does not exist')) {
          console.log('La función function_exists no existe, verificando directamente...');
          return await checkFunctionExistsDirectly('get_project_distribution');
        } else {
          console.error('Error al verificar si las funciones del dashboard están configuradas:', error);
          return false;
        }
      }

      return !!data;
    } catch (rpcError) {
      // Si es un error de función no encontrada, verificar directamente
      if (rpcError instanceof Error && rpcError.message &&
          (rpcError.message.includes('function') && rpcError.message.includes('does not exist'))) {
        console.log('La función function_exists no existe, verificando directamente...');
        return await checkFunctionExistsDirectly('get_project_distribution');
      } else {
        console.error('Error al verificar si las funciones del dashboard están configuradas:', rpcError);
        return false;
      }
    }
  } catch (error) {
    console.error('Error al verificar si las funciones del dashboard están configuradas:', error);
    return false;
  }
}

/**
 * Verifica si las tablas del dashboard están configuradas
 * @returns Promise<boolean> true si están configuradas, false en caso contrario
 */
export async function areDashboardTablesConfigured(): Promise<boolean> {
  try {
    // Verificar performance_metrics
    const { error: error1 } = await supabaseAdmin.from('performance_metrics')
      .select('id')
      .limit(1);

    // Verificar resource_allocation
    const { error: error2 } = await supabaseAdmin.from('resource_allocation')
      .select('id')
      .limit(1);

    // Si ambas consultas no dan error 404, las tablas existen
    const exists1 = !(error1 && error1.code === '404');
    const exists2 = !(error2 && error2.code === '404');

    return exists1 && exists2;
  } catch (error) {
    console.error('Error al verificar tablas individualmente:', error);
    return false;
  }
}

/**
 * Configura las funciones y tablas del dashboard
 * @returns Promise<boolean> true si se configuraron correctamente, false en caso contrario
 */
export async function setupDashboardFunctions(): Promise<{ success: boolean; message: string }> {
  try {
    console.log('Configurando funciones y tablas del dashboard...');

    // Verificar si las funciones y tablas ya están configuradas
    const functionsConfigured = await areDashboardFunctionsConfigured();
    const tablesConfigured = await areDashboardTablesConfigured();

    if (functionsConfigured && tablesConfigured) {
      console.log('Las funciones y tablas del dashboard ya están configuradas');
      return {
        success: true,
        message: 'Las funciones y tablas del dashboard ya están configuradas'
      };
    }

    // Ejecutar las funciones SQL
    const dashboardSql = `
    -- Función para verificar si existe una función en la base de datos
    CREATE OR REPLACE FUNCTION public.function_exists(function_name TEXT)
    RETURNS BOOLEAN
    LANGUAGE plpgsql
    SECURITY DEFINER
    AS $$
    DECLARE
      func_exists BOOLEAN;
    BEGIN
      -- Verificar si la función existe
      SELECT EXISTS (
        SELECT 1
        FROM pg_proc p
        JOIN pg_namespace n ON p.pronamespace = n.oid
        WHERE n.nspname = 'public'
        AND p.proname = function_name
      ) INTO func_exists;

      RETURN func_exists;
    END;
    $$;

    -- Función para ejecutar SQL dinámico
    CREATE OR REPLACE FUNCTION public.exec_sql(sql TEXT)
    RETURNS VOID
    LANGUAGE plpgsql
    SECURITY DEFINER
    AS $$
    BEGIN
      EXECUTE sql;
    END;
    $$;

    -- Crear tabla de métricas de rendimiento si no existe
    CREATE TABLE IF NOT EXISTS public.performance_metrics (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      category TEXT NOT NULL,
      efficiency NUMERIC NOT NULL DEFAULT 0,
      completion_rate NUMERIC NOT NULL DEFAULT 0,
      quality_score NUMERIC NOT NULL DEFAULT 0,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    -- Crear tabla de asignación de recursos si no existe
    CREATE TABLE IF NOT EXISTS public.resource_allocation (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      category TEXT NOT NULL,
      amount NUMERIC NOT NULL DEFAULT 0,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    -- Crear tabla de log de auditoría si no existe
    CREATE TABLE IF NOT EXISTS public.audit_log (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id UUID REFERENCES auth.users(id),
      action TEXT NOT NULL,
      resource_type TEXT NOT NULL,
      resource_id UUID NOT NULL,
      details JSONB,
      ip_address TEXT,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    -- Crear función para obtener distribución de proyectos
    CREATE OR REPLACE FUNCTION public.get_project_distribution()
    RETURNS TABLE (name TEXT, value NUMERIC)
    LANGUAGE plpgsql
    SECURITY DEFINER
    AS $$
    BEGIN
      -- Verificar si existe la tabla de proyectos
      IF EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_schema = 'public' AND table_name = 'projects'
      ) THEN
        -- Devolver distribución real de proyectos por estado
        RETURN QUERY
        SELECT
          COALESCE(status, 'Sin estado') AS name,
          COUNT(*)::NUMERIC AS value
        FROM public.projects
        GROUP BY COALESCE(status, 'Sin estado')
        ORDER BY value DESC;
      ELSE
        -- Si no existe la tabla, devolver datos de ejemplo
        RETURN QUERY
        SELECT name, value FROM (
          VALUES
            ('Activo', 42),
            ('Completado', 28),
            ('Planificación', 18),
            ('En pausa', 12)
        ) AS t(name, value);
      END IF;
    END;
    $$;

    -- Función para eliminar un proyecto y sus datos relacionados
    CREATE OR REPLACE FUNCTION public.delete_project(project_id UUID)
    RETURNS JSONB
    LANGUAGE plpgsql
    SECURITY DEFINER
    AS $$
    DECLARE
      result JSONB;
      deleted_count INTEGER := 0;
      document_count INTEGER := 0;
      task_count INTEGER := 0;
      order_count INTEGER := 0;
      storage_count INTEGER := 0;
      project_exists BOOLEAN;
    BEGIN
      -- Verificar si el proyecto existe
      SELECT EXISTS (
        SELECT 1 FROM public.projects WHERE id = project_id
      ) INTO project_exists;

      IF NOT project_exists THEN
        RETURN jsonb_build_object(
          'success', false,
          'message', 'El proyecto no existe',
          'project_id', project_id
        );
      END IF;

      -- Eliminar documentos asociados al proyecto
      IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'documents') THEN
        WITH deleted AS (
          DELETE FROM public.documents
          WHERE project_id = project_id
          RETURNING id
        )
        SELECT COUNT(*) INTO document_count FROM deleted;
      END IF;

      -- Eliminar tareas asociadas al proyecto a través de órdenes de trabajo
      IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'work_order_tasks') AND
         EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'work_orders') THEN
        WITH deleted AS (
          DELETE FROM public.work_order_tasks
          WHERE work_order_id IN (
            SELECT id FROM public.work_orders WHERE project_id = project_id
          )
          RETURNING id
        )
        SELECT COUNT(*) INTO task_count FROM deleted;
      END IF;

      -- Eliminar órdenes de trabajo asociadas al proyecto
      IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'work_orders') THEN
        WITH deleted AS (
          DELETE FROM public.work_orders
          WHERE project_id = project_id
          RETURNING id
        )
        SELECT COUNT(*) INTO order_count FROM deleted;
      END IF;

      -- Eliminar archivos de almacenamiento asociados al proyecto
      IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'storage' AND table_name = 'objects') THEN
        WITH deleted AS (
          DELETE FROM storage.objects
          WHERE bucket_id = 'projects' AND path LIKE project_id || '/%'
          RETURNING id
        )
        SELECT COUNT(*) INTO storage_count FROM deleted;
      END IF;

      -- Finalmente, eliminar el proyecto
      DELETE FROM public.projects
      WHERE id = project_id;

      -- Incrementar el contador de eliminados si se eliminó el proyecto
      IF FOUND THEN
        deleted_count := 1;
      END IF;

      -- Construir el resultado
      result := jsonb_build_object(
        'success', true,
        'message', 'Proyecto eliminado correctamente',
        'project_id', project_id,
        'deleted_count', deleted_count,
        'related_data', jsonb_build_object(
          'documents', document_count,
          'tasks', task_count,
          'orders', order_count,
          'storage_files', storage_count
        )
      );

      RETURN result;
    END;
    $$;

    -- Función MCP para eliminar un proyecto con opciones avanzadas
    CREATE OR REPLACE FUNCTION public.mcp_delete_project(project_id UUID, options JSONB DEFAULT '{}'::JSONB)
    RETURNS JSONB
    LANGUAGE plpgsql
    SECURITY DEFINER
    AS $$
    DECLARE
      result JSONB;
      deleted_count INTEGER := 0;
      document_count INTEGER := 0;
      task_count INTEGER := 0;
      order_count INTEGER := 0;
      storage_count INTEGER := 0;
      project_exists BOOLEAN;
      current_user_id UUID;
      is_admin BOOLEAN := FALSE;
      cascade_delete BOOLEAN := COALESCE((options->>'cascade')::BOOLEAN, TRUE);
      soft_delete BOOLEAN := COALESCE((options->>'soft_delete')::BOOLEAN, FALSE);
      project_record RECORD;
    BEGIN
      -- Obtener el ID del usuario actual
      current_user_id := auth.uid();

      -- Verificar si el usuario es administrador
      SELECT EXISTS (
        SELECT 1 FROM auth.users
        WHERE id = current_user_id
        AND raw_app_meta_data->>'is_admin' = 'true'
      ) INTO is_admin;

      -- Verificar si el proyecto existe
      SELECT EXISTS (
        SELECT 1 FROM public.projects WHERE id = project_id
      ) INTO project_exists;

      IF NOT project_exists THEN
        RETURN jsonb_build_object(
          'success', false,
          'message', 'El proyecto no existe',
          'project_id', project_id,
          'code', 'PROJECT_NOT_FOUND'
        );
      END IF;

      -- Obtener información del proyecto para verificar permisos
      SELECT * INTO project_record FROM public.projects WHERE id = project_id;

      -- Verificar permisos (el usuario debe ser propietario o administrador)
      IF NOT is_admin AND project_record.owner_id != current_user_id THEN
        -- Verificar si el usuario tiene rol de administrador en el proyecto
        IF NOT EXISTS (
          SELECT 1 FROM public.project_users
          WHERE project_id = project_id
          AND user_id = current_user_id
          AND role = 'admin'
        ) THEN
          RETURN jsonb_build_object(
            'success', false,
            'message', 'No tienes permisos para eliminar este proyecto',
            'project_id', project_id,
            'code', 'PERMISSION_DENIED'
          );
        END IF;
      END IF;

      -- Si es soft delete, simplemente marcar como eliminado
      IF soft_delete THEN
        UPDATE public.projects
        SET
          status = 'deleted',
          updated_at = NOW(),
          deleted_at = NOW()
        WHERE id = project_id;

        IF FOUND THEN
          deleted_count := 1;
        END IF;

        RETURN jsonb_build_object(
          'success', true,
          'message', 'Proyecto marcado como eliminado',
          'project_id', project_id,
          'deleted_count', deleted_count,
          'soft_delete', true,
          'code', 'SOFT_DELETE_SUCCESS'
        );
      END IF;

      -- Si cascade_delete es true, eliminar datos relacionados
      IF cascade_delete THEN
        -- Eliminar documentos asociados al proyecto
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'documents') THEN
          WITH deleted AS (
            DELETE FROM public.documents
            WHERE project_id = project_id
            RETURNING id
          )
          SELECT COUNT(*) INTO document_count FROM deleted;
        END IF;

        -- Eliminar tareas asociadas al proyecto a través de órdenes de trabajo
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'work_order_tasks') AND
           EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'work_orders') THEN
          WITH deleted AS (
            DELETE FROM public.work_order_tasks
            WHERE work_order_id IN (
              SELECT id FROM public.work_orders WHERE project_id = project_id
            )
            RETURNING id
          )
          SELECT COUNT(*) INTO task_count FROM deleted;
        END IF;

        -- Eliminar órdenes de trabajo asociadas al proyecto
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'work_orders') THEN
          WITH deleted AS (
            DELETE FROM public.work_orders
            WHERE project_id = project_id
            RETURNING id
          )
          SELECT COUNT(*) INTO order_count FROM deleted;
        END IF;

        -- Eliminar usuarios del proyecto
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'project_users') THEN
          DELETE FROM public.project_users
          WHERE project_id = project_id;
        END IF;

        -- Eliminar etapas del proyecto
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'project_stages') THEN
          DELETE FROM public.project_stages
          WHERE project_id = project_id;
        END IF;

        -- Eliminar archivos de almacenamiento asociados al proyecto
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'storage' AND table_name = 'objects') THEN
          WITH deleted AS (
            DELETE FROM storage.objects
            WHERE bucket_id = 'projects' AND path LIKE project_id || '/%'
            RETURNING id
          )
          SELECT COUNT(*) INTO storage_count FROM deleted;
        END IF;
      END IF;

      -- Finalmente, eliminar el proyecto
      DELETE FROM public.projects
      WHERE id = project_id;

      -- Incrementar el contador de eliminados si se eliminó el proyecto
      IF FOUND THEN
        deleted_count := 1;
      END IF;

      -- Construir el resultado
      result := jsonb_build_object(
        'success', true,
        'message', 'Proyecto eliminado correctamente',
        'project_id', project_id,
        'deleted_count', deleted_count,
        'related_data', jsonb_build_object(
          'documents', document_count,
          'tasks', task_count,
          'orders', order_count,
          'storage_files', storage_count
        ),
        'code', 'DELETE_SUCCESS',
        'user_id', current_user_id,
        'is_admin', is_admin,
        'timestamp', extract(epoch from now())
      );

      -- Registrar la operación en el log de auditoría si existe la tabla
      IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'audit_log') THEN
        INSERT INTO public.audit_log (
          user_id,
          action,
          resource_type,
          resource_id,
          details,
          ip_address
        ) VALUES (
          current_user_id,
          'delete',
          'project',
          project_id,
          result,
          COALESCE(nullif(current_setting('request.headers', true)::json->>'x-forwarded-for', ''), 'unknown')
        );
      END IF;

      RETURN result;
    END;
    $$;
    `;

    try {
      // Intentar ejecutar el script SQL usando la función exec_sql
      const { error } = await supabaseAdmin.rpc('exec_sql', { sql: dashboardSql });

      if (error) {
        // Si el error es porque la función no existe, ejecutar directamente
        const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

        if (error.message && error.message.includes('function') && errorMessage.includes('does not exist')) {
          console.log('La función exec_sql no existe, ejecutando SQL directamente...');
          await executeDirectSQL(dashboardSql);
        } else {
          console.error('Error al ejecutar el script SQL:', error);
          return {
            success: false,
            message: `Error al ejecutar el script SQL: ${errorMessage}`
          };
        }
      }
    } catch (rpcError) {
      // Si es un error de función no encontrada, ejecutar directamente
      if (rpcError instanceof Error && rpcError.message &&
          (rpcError.message.includes('function') && rpcError.message.includes('does not exist'))) {
        console.log('La función exec_sql no existe, ejecutando SQL directamente...');
        await executeDirectSQL(dashboardSql);
      } else {
        console.error('Error al ejecutar el script SQL:', rpcError);
        return {
          success: false,
          message: `Error al ejecutar el script SQL: ${rpcError instanceof Error ? rpcError.message : 'Error desconocido'}`
        };
      }
    }

    console.log('Funciones y tablas del dashboard configuradas correctamente');
    return {
      success: true,
      message: 'Funciones y tablas del dashboard configuradas correctamente'
    };
  } catch (error) {
    console.error('Error al configurar funciones y tablas del dashboard:', error);
    return {
      success: false,
      message: `Error al configurar funciones y tablas del dashboard: ${error instanceof Error ? error.message : 'Error desconocido'}`
    };
  }
}
