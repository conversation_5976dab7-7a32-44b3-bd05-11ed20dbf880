/**
 * @ai-file-description: "Page for creating projects from document analysis"
 * @ai-related-files: ["../page.tsx", "../../[id]/page.tsx"]
 * @ai-owner: "File-Based Projects"
 */

"use client";

import { DocumentAnalyzer } from "@/components/features/projects/document-analyzer";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { DashboardShell } from "@/components/shared/layout/dashboard-shell";

/**
 * Page for creating projects from document analysis
 *
 * @ai-responsibility: "Provides UI for document upload and analysis for project creation"
 */
export default function NewProjectFromDocumentPage() {
  return (
    <DashboardShell heading="Crear Proyecto desde Documento">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="icon" asChild>
              <Link href="/dashboard/projects/new">
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <h2 className="text-3xl font-bold tracking-tight">Crear Proyecto desde Documento</h2>
          </div>
        </div>

        <p className="text-muted-foreground">
          Sube un documento y usa IA para extraer automáticamente la información del proyecto.
          Tipos de archivo soportados: PDF, DOCX, TXT.
        </p>

        <DocumentAnalyzer />
      </div>
    </DashboardShell>
  );
}
