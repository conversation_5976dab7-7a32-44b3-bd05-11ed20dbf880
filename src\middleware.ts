/**
 * Middleware principal que combina autenticación y CORS
 */
import { NextResponse, type NextRequest } from 'next/server'
import { authMiddleware } from './middleware/auth-middleware'
import { corsMiddleware } from './middleware/cors-middleware'

/**
 * Middleware principal que combina todos los middlewares
 */
export async function middleware(request: NextRequest) {
  // Crear respuesta inicial
  const response = NextResponse.next()

  // Aplicar middleware CORS
  const corsResponse = corsMiddleware(request, response)

  // Si el middleware CORS devuelve una respuesta diferente (por ejemplo, para OPTIONS),
  // devolverla directamente
  if (corsResponse !== response) {
    return corsResponse
  }

  // Aplicar middleware de autenticación
  return await authMiddleware(request)
}

export const config = {
  matcher: [
    // Excluir archivos estáticos y recursos
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:jpg|jpeg|gif|png|svg|ico)$).*)',
  ],
}