'use client'

import { useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'

/**
 * Hook para mantener la persistencia de sesión
 *
 * Este hook se encarga de refrescar periódicamente la sesión
 * para evitar que expire cuando el usuario está activo.
 */
export function useSessionPersistence() {
  const supabase = createClient()

  useEffect(() => {
    // Verificar si estamos en el navegador
    const isBrowser = typeof window !== 'undefined'
    if (!isBrowser) return

    console.log('[Session Persistence] Inicializando mecanismo de persistencia de sesión')

    // Control de tasa de refresco para evitar demasiadas solicitudes
    let isRefreshing = false
    let lastRefreshAttempt = 0
    const MIN_REFRESH_INTERVAL = 5 * 60 * 1000 // 5 minutos mínimo entre intentos
    const RATE_LIMIT_BACKOFF = 15 * 60 * 1000 // 15 minutos de espera si hay rate limit

    // Función para refrescar la sesión
    const refreshSession = async () => {
      // Evitar múltiples refrescos simultáneos
      if (isRefreshing) {
        console.log('[Session Persistence] Ya hay un refresco en curso, omitiendo')
        return
      }

      // Verificar si ha pasado suficiente tiempo desde el último intento
      const now = Date.now()
      if (now - lastRefreshAttempt < MIN_REFRESH_INTERVAL) {
        console.log(`[Session Persistence] Demasiados intentos de refresco, esperando ${Math.round((MIN_REFRESH_INTERVAL - (now - lastRefreshAttempt)) / 1000 / 60)} minutos más`)
        return
      }

      // Verificar si hay un bloqueo por rate limit
      const rateLimitUntil = localStorage.getItem('auth_rate_limit_until')
      if (rateLimitUntil && parseInt(rateLimitUntil) > now) {
        const waitMinutes = Math.round((parseInt(rateLimitUntil) - now) / 1000 / 60)
        console.log(`[Session Persistence] Esperando ${waitMinutes} minutos más debido a rate limit anterior`)
        return
      }

      // Actualizar estado y timestamp
      isRefreshing = true
      lastRefreshAttempt = now

      try {
        // Verificar si hay una sesión activa
        const { data: sessionData } = await supabase.auth.getSession()

        if (sessionData?.session) {
          // Verificar si la sesión está cerca de expirar (menos de 60 minutos)
          const expiresAt = sessionData.session.expires_at
          const expiresInMs = expiresAt ? (expiresAt * 1000) - Date.now() : 0

          // Solo refrescar si está a menos de 60 minutos de expirar
          if (expiresInMs < 60 * 60 * 1000 && expiresInMs > 0) {
            console.log(`[Session Persistence] Sesión expira en ${Math.round(expiresInMs/1000/60)} minutos, refrescando`)
            const { data, error } = await supabase.auth.refreshSession()

            if (error) {
              const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

              if (error.message.includes('rate limit') || errorMessage.includes('too many requests')) {
                console.warn('[Session Persistence] Límite de tasa alcanzado, esperando antes de reintentar')
                // Aumentar el tiempo de espera para el próximo intento
                lastRefreshAttempt = now + RATE_LIMIT_BACKOFF
                // Guardar timestamp hasta cuando debemos esperar
                const waitUntil = now + RATE_LIMIT_BACKOFF
                localStorage.setItem('auth_rate_limit_until', waitUntil.toString())
                console.log(`[Session Persistence] Esperando hasta ${new Date(waitUntil).toLocaleTimeString()} debido a rate limit`)

                const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

                // Disparar evento personalizado para notificar a la aplicación
                window.dispatchEvent(new CustomEvent('supabase:auth:rate_limited', {
                  detail: { message: errorMessage, waitUntil }
                }))
              } else if (error.message.includes('expired') || errorMessage.includes('invalid')) {
                const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

                console.error('[Session Persistence] Sesión expirada o inválida:', errorMessage)
                // Disparar evento personalizado para notificar a la aplicación
                window.dispatchEvent(new CustomEvent('supabase:auth:session_expired', {
                  detail: { message: errorMessage }
                }))
              } else {
                const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

                console.error('[Session Persistence] Error al refrescar sesión:', errorMessage)
              }
            } else if (data?.session) {
              console.log('[Session Persistence] Sesión refrescada correctamente')
              // Almacenar timestamp del último refresco exitoso
              localStorage.setItem('last_session_refresh', Date.now().toString())
            }
          } else {
            console.log(`[Session Persistence] Sesión válida por ${Math.round(expiresInMs/1000/60)} minutos más, no es necesario refrescar`)
          }
        } else {
          console.log('[Session Persistence] No hay sesión activa para refrescar')
        }
      } catch (err) {
        console.error('[Session Persistence] Error inesperado:', err)
      } finally {
        // Restablecer el estado de refresco
        isRefreshing = false
      }
    }

    // Refrescar la sesión inmediatamente al cargar
    // Ejecutamos inmediatamente para cargar la sesión lo antes posible
    refreshSession()

    // Configurar intervalo para refrescar la sesión (cada 5 minutos)
    // Esto garantiza que la sesión se mantenga actualizada regularmente
    const refreshInterval = setInterval(refreshSession, 5 * 60 * 1000)

    // Configurar eventos para refrescar la sesión cuando el usuario está activo
    // Usamos un debounce para evitar demasiadas llamadas
    let activityTimeout: NodeJS.Timeout | null = null

    const handleUserActivity = () => {
      // Cancelar cualquier timeout pendiente
      if (activityTimeout) {
        clearTimeout(activityTimeout)
      }

      // Configurar un nuevo timeout (debounce)
      activityTimeout = setTimeout(() => {
        // Obtener el último refresco
        const lastRefresh = localStorage.getItem('last_session_refresh')
        const now = Date.now()

        // Si han pasado más de 5 minutos desde el último refresco, refrescar la sesión
        if (!lastRefresh || now - parseInt(lastRefresh) > 5 * 60 * 1000) {
          console.log('[Session Persistence] Refrescando sesión por actividad del usuario')
          refreshSession()
        }
      }, 5000) // Esperar 5 segundos de inactividad antes de refrescar
    }

    // Agregar event listeners para detectar actividad del usuario
    // Usamos eventos menos frecuentes para reducir la sobrecarga
    window.addEventListener('click', handleUserActivity)
    window.addEventListener('keydown', handleUserActivity, { passive: true })

    // Limpiar al desmontar
    return () => {
      if (activityTimeout) clearTimeout(activityTimeout)
      clearInterval(refreshInterval)
      window.removeEventListener('click', handleUserActivity)
      window.removeEventListener('keydown', handleUserActivity)
    }
  }, [supabase])
}
