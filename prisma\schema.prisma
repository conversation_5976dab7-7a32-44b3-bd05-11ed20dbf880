// This is a basic Prisma schema. Update the datasource and models as needed for your application.

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Example model. Replace or extend as needed.
model User {
  id    Int    @id @default(autoincrement())
  email String @unique
  name  String?
  createdAt DateTime @default(now())
}
