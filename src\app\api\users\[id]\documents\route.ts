import { NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function GET(
  request: Request,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const { id } = params;

    // Create client directly in the route handler
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.SUPABASE_SERVICE_ROLE_KEY || ''
    )

    // Get documents uploaded by this user
    const { data: documents, error } = await supabase
      .from('documents')
      .select(`
        id,
        filename,
        file_url,
        file_type,
        file_size,
        description,
        category,
        created_at,
        uploaded_by
      `)
      .eq('uploaded_by', id)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching user documents:', error)
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

      return NextResponse.json({ error: errorMessage }, { status: 500 })
    }

    return NextResponse.json(documents || [])
  } catch (error: unknown) {
    console.error('Unexpected error fetching user documents:', error)
    const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
    return NextResponse.json(
      { error: 'Error del servidor', message: errorMessage },
      { status: 500 }
    )
  }
}

export async function POST(
  request: Request,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const { id } = params;

    // Create client directly in the route handler
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.SUPABASE_SERVICE_ROLE_KEY || ''
    )

    // Parse form data
    const formData = await request.formData()
    const file = formData.get('file') as File
    const description = formData.get('description') as string
    const category = formData.get('category') as string

    if (!file) {
      return NextResponse.json({ error: 'No se proporcionó ningún archivo' }, { status: 400 })
    }

    if (!category) {
      return NextResponse.json({ error: 'La categoría es obligatoria' }, { status: 400 })
    }

    // Validate file type
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'image/jpeg',
      'image/jpg',
      'image/png',
      'text/plain'
    ]

    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({
        error: 'Tipo de archivo no permitido. Solo se permiten PDF, Word, imágenes y texto.'
      }, { status: 400 })
    }

    // Validate file size (10MB max)
    const maxSize = 10 * 1024 * 1024 // 10MB
    if (file.size > maxSize) {
      return NextResponse.json({
        error: 'El archivo es demasiado grande. Tamaño máximo: 10MB'
      }, { status: 400 })
    }

    // Generate unique filename
    const fileExtension = file.name.split('.').pop()
    const fileName = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}.${fileExtension}`
    const filePath = `users/${id}/documents/${fileName}`

    // Upload file to Supabase Storage
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('documents')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false,
      })

    if (uploadError) {
      console.error('Error uploading file to storage:', uploadError)
      return NextResponse.json({ error: 'Error al subir el archivo' }, { status: 500 })
    }

    // Get public URL of the file
    const { data: urlData } = await supabase.storage
      .from('documents')
      .getPublicUrl(filePath)

    // Insert document metadata into database
    const { data: documentData, error: documentError } = await supabase
      .from('documents')
      .insert({
        filename: file.name,
        file_path: filePath,
        file_url: urlData?.publicUrl,
        file_type: file.type,
        file_size: file.size,
        description: description || null,
        category: category,
        uploaded_by: id,
        // For user documents, we don't associate with projects or work orders
        project_id: null,
        work_order_id: null,
        public_url: urlData?.publicUrl,
      })
      .select()
      .single()

    if (documentError) {
      console.error('Error creating document record:', documentError)

      // Try to clean up the uploaded file
      try {
        await supabase.storage
          .from('documents')
          .remove([filePath])
      } catch (cleanupError) {
        console.error('Error cleaning up uploaded file:', cleanupError)
      }

      return NextResponse.json({ error: 'Error al crear el registro del documento' }, { status: 500 })
    }

    return NextResponse.json(documentData)
  } catch (error: unknown) {
    console.error('Unexpected error uploading user document:', error)
    const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
    return NextResponse.json(
      { error: 'Error del servidor', message: errorMessage },
      { status: 500 }
    )
  }
}
