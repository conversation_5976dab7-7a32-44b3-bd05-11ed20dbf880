import { supabaseAdmin } from './admin-client'
import { createClient } from './client'
import { Project, ProjectUser, ProjectStage } from '@/types/projects'

// Interfaces para los tipos de datos relacionados
interface LocalProjectStage {
  id: string
  name: string
  description?: string
  stage_order: number
  completed: boolean
}

// Función para obtener proyectos con todas las relaciones
export async function getProjectsWithRelations() {
  try {
    console.log('Starting getProjectsWithRelations...');

    // Intentar primero con el cliente normal (autenticado)
    const regularClient = createClient();
    console.log('Attempting to fetch projects with regular client first...');

    try {
      const { data: regularProjects, error: regularError } = await regularClient
        .from('projects')
        .select(`
          *,
          users:owner_id(id, first_name, last_name, email),
          project_stages(id, name, description, stage_order, completed),
          project_users:project_users!inner(project_id, user_id, role, users:user_id(id, email, first_name, last_name)),
          work_orders(id, title, status, priority, assigned_to),
          documents(id, filename, upload_date, uploaded_by),
          clients:client_id(id, name, contact_name, contact_email, contact_phone)
        `)
        .order('created_at', { ascending: false });

      if (!regularError && regularProjects && regularProjects.length > 0) {
        console.log('Successfully fetched projects with regular client:', regularProjects.length);
        // Transformar y devolver los proyectos obtenidos con el cliente regular
        return transformProjects(regularProjects);
      }

      if (regularError) {
        console.log('Regular client error:', regularError.message);
        console.log('Falling back to admin client...');
      }
    } catch (regularClientError) {
      console.error('Exception with regular client:', regularClientError);
      console.log('Falling back to admin client...');
    }

    // Si el cliente regular falló, intentar con el cliente admin
    if (!supabaseAdmin) {
      console.error('Admin client not available');
      return [];
    }

    // Verificar la sesión actual con el cliente admin
    const { data: sessionData, error: sessionError } = await supabaseAdmin.auth.getSession();

    if (sessionError) {
      console.error('Error getting session with admin client:', sessionError);
      console.error('Session error details:', JSON.stringify(sessionError));
      return [];
    }

    console.log('Admin session check result:', sessionData ? 'Session exists' : 'No session');
    if (sessionData?.session) {
      console.log('Admin session user:', sessionData.session.user?.email || 'Unknown');
    }

    // Intentar obtener proyectos con el cliente admin
    console.log('Attempting to fetch projects with admin client...');
    try {
      // Primero intentar con la función para administradores que devuelve todos los proyectos
      console.log('Trying with admin_get_all_projects function...');
      try {
        // Intentar con la función para administradores
        const { data: adminProjects, error: adminError } = await supabaseAdmin
          .rpc('admin_get_all_projects')
          .select(`
            *,
            users:owner_id(id, first_name, last_name, email),
            project_stages(id, name, description, stage_order, completed),
            project_users:project_users!inner(project_id, user_id, role, users:user_id(id, email, first_name, last_name)),
            work_orders(id, title, status, priority, assigned_to),
            documents(id, filename, upload_date, uploaded_by),
            clients:client_id(id, name, contact_name, contact_email, contact_phone)
          `);

        if (!adminError && adminProjects && adminProjects.length > 0) {
          console.log('Successfully fetched projects with admin function:', adminProjects.length);
          return transformProjects(adminProjects);
        }

        if (adminError) {
          console.error('Error using admin function:', adminError);
          console.log('Falling back to user-specific function...');
        }

        // Si la función de admin falla, intentar con la función específica de usuario
        console.log('Trying with custom function get_projects_for_user...');
        const { data: sessionData } = await supabaseAdmin.auth.getSession();
        const userId = sessionData?.session?.user?.id;

        if (userId) {
          const { data: functionProjects, error: functionError } = await supabaseAdmin
            .rpc('get_projects_for_user', { user_id: userId })
            .select(`
              *,
              users:owner_id(id, first_name, last_name, email),
              project_stages(id, name, description, stage_order, completed),
              project_users:project_users!inner(project_id, user_id, role, users:user_id(id, email, first_name, last_name)),
              work_orders(id, title, status, priority, assigned_to),
              documents(id, filename, upload_date, uploaded_by),
              clients:client_id(id, name, contact_name, contact_email, contact_phone)
            `);

          if (!functionError && functionProjects && functionProjects.length > 0) {
            console.log('Successfully fetched projects with user function:', functionProjects.length);
            return transformProjects(functionProjects);
          }

          if (functionError) {
            console.error('Error using user function:', functionError);
            console.log('Falling back to direct query...');
          }
        }
      } catch (functionError) {
        console.error('Exception using custom function:', functionError);
        console.log('Falling back to direct query...');
      }

      // Si la función personalizada falla, intentar con la consulta directa
      const { data: projects, error } = await supabaseAdmin
        .from('projects')
        .select(`
          *,
          users:owner_id(id, first_name, last_name, email),
          project_stages(id, name, description, stage_order, completed),
          project_users:project_users!inner(project_id, user_id, role, users:user_id(id, email, first_name, last_name)),
          work_orders(id, title, status, priority, assigned_to),
          documents(id, filename, upload_date, uploaded_by),
          clients:client_id(id, name, contact_name, contact_email, contact_phone)
        `)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching projects with relations:', error);
        console.error('Error details:', JSON.stringify(error));
        return [];
      }

      console.log('Projects fetched successfully:', projects ? projects.length : 0);
      if (projects && projects.length > 0) {
        console.log('First project sample:', JSON.stringify(projects[0].id));
      }

      // Transformar los datos para adaptarlos a nuestro componente
      return transformProjects(projects);
    } catch (error) {
      console.error('Exception fetching projects with relations:', error);
      console.error('Error details:', JSON.stringify(error));
      return [];
    }
  } catch (outerError) {
    console.error('Outer exception fetching projects with relations:', outerError);
    console.error('Outer error details:', JSON.stringify(outerError));
    return [];
  }
}

// Función auxiliar para transformar proyectos
export function transformProjects(projects: unknown[] | null): Project[] {
  if (!projects || projects.length === 0) {
    return [];
  }

  const transformedProjects = projects.map((project: any) => {
    const projectStages = Array.isArray(project.project_stages)
      ? project.project_stages.map((stage: any): ProjectStage => ({
          id: stage.id,
          project_id: stage.project_id || project.id,
          name: stage.name,
          description: stage.description || null,
          stage_order: stage.stage_order,
          completed: stage.completed,
          created_at: stage.created_at,
          updated_at: stage.updated_at
        }))
      : [];

    const projectUsers = Array.isArray(project.project_users)
      ? project.project_users.map((pu: any): ProjectUser => {
          // Handle case where users might be an array or a single object
          const userData = Array.isArray(pu.users) ? pu.users[0] : (pu.users || pu.user);
          if (!userData) {
            throw new Error(`Project user ${pu.user_id} has no associated user data`);
          }
          return {
            project_id: pu.project_id || project.id,
            user_id: pu.user_id,
            role: pu.role,
            user: {
              id: userData.id,
              email: userData.email,
              first_name: userData.first_name || null,
              last_name: userData.last_name || null
            }
          };
        })
      : [];

    const workOrders = Array.isArray(project.work_orders)
      ? project.work_orders.map((wo: any) => ({
          id: wo.id,
          project_id: wo.project_id,
          title: wo.title,
          description: wo.description,
          priority: wo.priority,
          status: wo.status,
          assigned_to: wo.assigned_to,
          due_date: wo.due_date,
          created_at: wo.created_at,
          updated_at: wo.updated_at
        }))
      : [];

    const documents = Array.isArray(project.documents)
      ? project.documents.map((doc: any) => ({
          id: doc.id,
          filename: doc.filename,
          file_url: doc.file_url || '',
          upload_date: doc.upload_date,
          uploaded_by: doc.uploaded_by,
          project_id: doc.project_id,
          work_order_id: doc.work_order_id,
          description: doc.description,
          version_chain: doc.version_chain,
          version_date: doc.version_date,
          current_version_id: doc.current_version_id
        }))
      : [];

    // Calcular el progreso basado en las etapas completadas
    const completedStages = projectStages.filter((stage: ProjectStage) => stage.completed).length;
    const totalStages = projectStages.length;
    const progressPercent = totalStages > 0 ? Math.round((completedStages / totalStages) * 100) : 0;

    // Transformar el proyecto principal
    return {
      id: project.id,
      name: project.name,
      description: project.description,
      status: project.status,
      start_date: project.start_date,
      end_date: project.end_date,
      created_at: project.created_at,
      updated_at: project.updated_at,
      owner_id: project.owner_id,
      created_by: project.created_by,
      contract_id: project.contract_id,
      progress_percent: progressPercent,
      budget_utilization: project.budget_utilization,
      client: project.client,
      client_id: project.client_id,
      budget: project.budget,
      currency: project.currency,
      priority: project.priority,
      estimated_hours: project.estimated_hours,
      actual_hours: project.actual_hours,
      tags: project.tags,
      ai_generated: project.ai_generated,
      source_document_id: project.source_document_id,
      ai_provider: project.ai_provider,
      root_path: project.root_path,
      project_type: project.project_type,
      project_stages: projectStages,
      project_users: projectUsers,
      work_orders: workOrders,
      documents: documents
    };
  });

  return transformedProjects;
}

// Función para obtener proyectos básicos sin relaciones
export async function getBasicProjects() {
  try {
    console.log('Starting getBasicProjects...');

    // Intentar primero con el cliente normal (autenticado)
    const regularClient = createClient();
    console.log('Attempting to fetch basic projects with regular client first...');

    try {
      const { data: regularData, error: regularError } = await regularClient
        .from('projects')
        .select(`
          id,
          name,
          description,
          status,
          created_at,
          updated_at,
          owner_id,
          progress_percent,
          project_users:project_users!inner (
            project_id,
            user_id,
            role,
            user:users (id, email, first_name, last_name)
          )
        `)
        .limit(10)
        .order('created_at', { ascending: false });

      if (!regularError && regularData && regularData.length > 0) {
        console.log('Successfully fetched basic projects with regular client:', regularData.length);
        return regularData;
      }

      if (regularError) {
        console.log('Regular client error for basic projects:', regularError.message);
        console.log('Falling back to admin client for basic projects...');
      }
    } catch (regularClientError) {
      console.error('Exception with regular client for basic projects:', regularClientError);
      console.log('Falling back to admin client for basic projects...');
    }

    // Si el cliente regular falló, intentar con el cliente admin
    if (!supabaseAdmin) {
      console.error('Admin client not available for basic projects');
      return [];
    }

    // Verificar la sesión actual con el cliente admin
    const { data: sessionData, error: sessionError } = await supabaseAdmin.auth.getSession();

    if (sessionError) {
      console.error('Error getting session for basic projects:', sessionError);
      console.error('Session error details:', JSON.stringify(sessionError));
      return [];
    }

    console.log('Session check result for basic projects:', sessionData ? 'Session exists' : 'No session');
    if (sessionData?.session) {
      console.log('Session user for basic projects:', sessionData.session.user?.email || 'Unknown');
    }

    // Intentar obtener proyectos básicos con el cliente admin
    console.log('Attempting to fetch basic projects with admin client...');
    try {
      // Primero intentar con la función para administradores que devuelve todos los proyectos
      console.log('Trying with admin_get_all_projects function for basic projects...');
      try {
        // Intentar con la función para administradores
        const { data: adminProjects, error: adminError } = await supabaseAdmin
          .rpc('admin_get_all_projects')
          .select(`
            id,
            name,
            description,
            status,
            created_at,
            updated_at,
            owner_id,
            progress_percent,
            project_users:project_users!inner (
              project_id,
              user_id,
              role,
              users!inner (id, email, first_name, last_name)
            )
          `)
          .limit(10);

        if (!adminError && adminProjects && adminProjects.length > 0) {
          console.log('Successfully fetched basic projects with admin function:', adminProjects.length);
          return adminProjects;
        }

        if (adminError) {
          console.error('Error using admin function for basic projects:', adminError);
          console.log('Falling back to user-specific function for basic projects...');
        }

        // Si la función de admin falla, intentar con la función específica de usuario
        console.log('Trying with custom function get_projects_for_user for basic projects...');
        const { data: sessionData } = await supabaseAdmin.auth.getSession();
        const userId = sessionData?.session?.user?.id;

        if (userId) {
          const { data: functionProjects, error: functionError } = await supabaseAdmin
            .rpc('get_projects_for_user', { user_id: userId })
            .select(`
              id,
              name,
              description,
              status,
              created_at,
              updated_at,
              owner_id,
              progress_percent,
              project_users:project_users!inner (
                project_id,
                user_id,
                role,
                user:users (id, email, first_name, last_name)
              )
            `)
            .limit(10);

          if (!functionError && functionProjects && functionProjects.length > 0) {
            console.log('Successfully fetched basic projects with user function:', functionProjects.length);
            return functionProjects;
          }

          if (functionError) {
            console.error('Error using user function for basic projects:', functionError);
            console.log('Falling back to direct query for basic projects...');
          }
        }
      } catch (functionError) {
        console.error('Exception using custom function for basic projects:', functionError);
        console.log('Falling back to direct query for basic projects...');
      }

      // Si la función personalizada falla, intentar con la consulta directa
      const { data, error } = await supabaseAdmin
        .from('projects')
        .select(`
          id,
          name,
          description,
          status,
          created_at,
          updated_at,
          owner_id,
          progress_percent,
          project_users:project_users!inner (
            project_id,
            user_id,
            role,
            user:users (id, email, first_name, last_name)
          )
        `)
        .limit(10)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching basic projects:', error);
        console.error('Error details:', JSON.stringify(error));
        return [];
      }

      console.log('Basic projects fetched successfully:', data ? data.length : 0);
      if (data && data.length > 0) {
        console.log('First basic project sample:', JSON.stringify(data[0].id));
      }

      return data || [];
    } catch (innerError) {
      console.error('Inner exception fetching basic projects:', innerError);
      console.error('Inner error details:', JSON.stringify(innerError));
      return [];
    }
  } catch (error) {
    console.error('Outer exception fetching basic projects:', error);
    console.error('Outer error details:', JSON.stringify(error));
    return [];
  }
}
