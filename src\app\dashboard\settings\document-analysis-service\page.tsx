"use client"

import { useState, useEffect, Suspense } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { createClient } from "@/lib/supabase/client"
import { DocumentAnalysisServiceSettings } from "@/components/features/settings/document-analysis-service-settings"
import { Loader2, ArrowLeft } from "lucide-react"
import Link from "next/link"
import { useSearchParams } from "next/navigation"

function DocumentAnalysisServicePageContent() {
  const [isLoading, setIsLoading] = useState(false)
  const [isAdmin, setIsAdmin] = useState(false)
  const [logs, setLogs] = useState<string[]>([])
  const [isLoadingLogs, setIsLoadingLogs] = useState(false)
  const searchParams = useSearchParams()
  const defaultTab = searchParams.get('tab') === 'logs' ? 'logs' : 'settings'

  const supabase = createClient() as any

  useEffect(() => {
    async function checkAdminStatus() {
      try {
        const { data: { user } } = await supabase.auth.getUser();

        if (!user) return;

        // Verificar si el usuario es administrador
        const { data: profile, error: profileError } = await (supabase as any)
          .from('profiles')
          .select('role')
          .eq('id', user.id)
          .single();

        if (!profileError && profile?.role === 'admin') {
          setIsAdmin(true);
        }
      } catch (error) {
        console.error('Error al verificar estado de administrador:', error);
      }
    }

    checkAdminStatus()
    if (defaultTab === 'logs') {
      loadLogs()
    }
  }, [defaultTab])

  const loadLogs = async () => {
    setIsLoadingLogs(true)
    try {
      const response = await fetch('/api/document-analysis/logs')

      if (response.ok) {
        const data = await response.json()
        setLogs(data.logs || [])
      } else {
        setLogs(['Error al cargar los logs. Servicio no disponible.'])
      }
    } catch (error) {
      console.error('Error al cargar los logs:', error)
      setLogs(['Error al cargar los logs. Servicio no disponible.'])
    } finally {
      setIsLoadingLogs(false)
    }
  }

  if (!isAdmin) {
    return (
      <div className="flex items-center justify-center h-[calc(100vh-4rem)]">
        <Card className="w-[400px]">
          <CardHeader>
            <CardTitle>Acceso Restringido</CardTitle>
            <CardDescription>
              Esta sección está disponible solo para administradores.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild className="w-full">
              <Link href="/dashboard/settings">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Volver a Configuración
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Servicio de Análisis de Documentos</h2>
          <p className="text-muted-foreground mt-2">
            Configura y monitorea el servicio de análisis de documentos.
          </p>
        </div>
        <Button asChild variant="outline">
          <Link href="/dashboard/settings">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Volver
          </Link>
        </Button>
      </div>

      <Tabs defaultValue={defaultTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2 mb-4">
          <TabsTrigger value="settings">Configuración</TabsTrigger>
          <TabsTrigger value="logs" onClick={loadLogs}>Logs</TabsTrigger>
        </TabsList>

        <TabsContent value="settings">
          <DocumentAnalysisServiceSettings />
        </TabsContent>

        <TabsContent value="logs">
          <Card>
            <CardHeader>
              <CardTitle>Logs del Servicio</CardTitle>
              <CardDescription>
                Visualiza los logs del servicio de análisis de documentos.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-end mb-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={loadLogs}
                  disabled={isLoadingLogs}
                >
                  {isLoadingLogs ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    "Actualizar"
                  )}
                </Button>
              </div>
              <div className="bg-muted p-4 rounded-md h-[500px] overflow-auto font-mono text-xs">
                {isLoadingLogs ? (
                  <div className="flex items-center justify-center h-full">
                    <Loader2 className="h-6 w-6 animate-spin" />
                  </div>
                ) : logs.length > 0 ? (
                  logs.map((log, index) => (
                    <div key={index} className="py-1">
                      {log}
                    </div>
                  ))
                ) : (
                  <div className="text-center text-muted-foreground py-4">
                    No hay logs disponibles
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default function DocumentAnalysisServicePage() {
  return (
    <Suspense fallback={<div className="p-8 text-center">Cargando página...</div>}>
      <DocumentAnalysisServicePageContent />
    </Suspense>
  )
}
