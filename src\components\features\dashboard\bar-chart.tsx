"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Bar<PERSON><PERSON> as RechartsBarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from "recharts"

interface BarChartProps {
  title: string
  description?: string
  data: unknown[]
  dataKeys: string[]
  colors: string[]
  height?: number
  xAxisDataKey?: string
  showLegend?: boolean
  layout?: "horizontal" | "vertical"
}

export function BarChart({
  title,
  description,
  data,
  dataKeys,
  colors,
  height = 350,
  xAxisDataKey = "name",
  showLegend = true,
  layout = "vertical",
}: BarChartProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <div style={{ height: height }}>
          <ResponsiveContainer width="100%" height="100%">
            <RechartsBarChart
              data={data}
              layout={layout}
              margin={{
                top: 20,
                right: 30,
                left: 20,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              {layout === "horizontal" ? (
                <>
                  <XAxis dataKey={xAxisDataKey} />
                  <YAxis />
                </>
              ) : (
                <>
                  <XAxis type="number" />
                  <YAxis dataKey={xAxisDataKey} type="category" />
                </>
              )}
              <Tooltip />
              {showLegend && <Legend />}
              {dataKeys.map((key, index) => (
                <Bar
                  key={key}
                  dataKey={key}
                  fill={colors[index % colors.length]}
                  radius={[4, 4, 0, 0]}
                />
              ))}
            </RechartsBarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  )
}
