'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/lib/supabase/client';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { FileUpload } from '@/components/shared/file-upload';
import { Loader2, AlertCircle } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { DocumentSelector } from "./document-selector";
import { SessionRecovery } from "@/components/session-recovery";
import { LoadingOverlay } from "@/components/ui/loading-overlay";
import { services, isDevelopment, getServiceUrl, getServiceHeaders } from '@/lib/config/environment';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface DocumentAnalyzerProps {
  onAnalysisComplete?: (result: unknown) => void;
}

export function DocumentAnalyzer({ onAnalysisComplete }: DocumentAnalyzerProps) {
  const [selectedProvider, setSelectedProvider] = useState('gemini');
  const [modelName, setModelName] = useState('gemini-1.5-flash'); // Usar el modelo disponible de Gemini
  const [localModelsAvailable, setLocalModelsAvailable] = useState<boolean>(false);
  const [localModels, setLocalModels] = useState<{id: string, name: string, isActive: boolean}[]>([]);
  const [config, setConfig] = useState<any>({
    modelName: 'gemini-1.5-flash',
    maxTokens: 2000,
    temperature: 0.7,
    useLLMStudio: true // Habilitado por defecto si está disponible
  });
  const [showAdvancedConfig, setShowAdvancedConfig] = useState<boolean>(false);
  const [uploadedDocument, setUploadedDocument] = useState<any>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [sessionLost, setSessionLost] = useState(false);
  const [progress, setProgress] = useState(0);
  const [progressStatus, setProgressStatus] = useState('');
  const [testingConnection, setTestingConnection] = useState(false);
  const [documentType, setDocumentType] = useState<string>('project');
  const [costEstimation, setCostEstimation] = useState<any>(null);
  const [useCache, setUseCache] = useState<boolean>(true);
  const [showConfirmDialog, setShowConfirmDialog] = useState<boolean>(false);
  const router = useRouter();
  const supabase = createClient();

  const handleUploadComplete = async (document: unknown) => {
    setUploadedDocument(document);
    setError(null);

    // Calcular estimación de costo automáticamente
    try {
      await calculateCostEstimation(document);
    } catch (error) {
      console.error('Error al calcular estimación de costo:', error);
    }
  };

  const handleSelectExisting = async (document: unknown) => {
    setUploadedDocument(document);
    setError(null);

    // Calcular estimación de costo automáticamente
    try {
      await calculateCostEstimation(document);
    } catch (error) {
      console.error('Error al calcular estimación de costo:', error);
    }
  };

  // Función para calcular la estimación de costo de forma optimizada
  const calculateCostEstimation = async (doc?: unknown) => {
    const documentToEstimate = doc || uploadedDocument;

    if (!documentToEstimate) {
      return;
    }

    try {
      // Mostrar indicador de carga para la estimación
      setCostEstimation(null);

      // Verificar si tenemos el tamaño del archivo
      let fileSize = documentToEstimate.file_size || 0;

      // Si no tenemos el tamaño del archivo y tenemos la ruta, intentar obtenerlo
      if (fileSize === 0 && documentToEstimate.file_path) {
        console.log('Tamaño de archivo no disponible, intentando obtenerlo del almacenamiento...');
        try {
          const { data: { session } } = await supabase.auth.getSession();
          if (session) {
            // Intentar descargar el archivo para obtener su tamaño
            try {
              const { data: fileData, error: fileError } = await supabase
                .storage
                .from('documents')
                .download(documentToEstimate.file_path);

              if (!fileError && fileData) {
                // Obtener el tamaño del archivo en bytes
                fileSize = fileData.size;
                console.log(`Tamaño de archivo obtenido del almacenamiento: ${fileSize} bytes`);
              } else {
                console.error('Error al obtener el archivo:', fileError);
              }
            } catch (downloadError) {
              console.error('Error al descargar el archivo:', downloadError);
              // Usar un tamaño estimado basado en el nombre del archivo
              fileSize = 100000; // Aproximadamente 100KB como valor predeterminado
              console.log(`Usando tamaño de archivo estimado: ${fileSize} bytes`);
            }
          }
        } catch (error) {
          console.error('Error al obtener el tamaño del archivo:', error);
          // Usar un tamaño estimado
          fileSize = 100000; // Aproximadamente 100KB como valor predeterminado
        }
      }

      // Estimación local rápida para mostrar inmediatamente
      const estimatedTokens = Math.ceil(fileSize / 4); // Aproximación rápida: 4 bytes por token

      // Verificar si es un modelo local (sin costo)
      if (selectedProvider === 'local') {
        setCostEstimation({
          estimatedCost: 0,
          estimatedTokens: estimatedTokens,
          currency: 'USD',
          isQuickEstimate: false,
          isLocal: true
        });
        return; // No necesitamos consultar al servidor para modelos locales
      }

      // Estimación rápida basada en el modelo seleccionado
      let costPerToken = 0.0000002; // Valor por defecto

      if (selectedProvider === 'gemini') {
        if (modelName.includes('2.5-flash')) {
          costPerToken = 0.0000002; // Gemini 2.5 Flash
        } else if (modelName.includes('2.5-pro')) {
          costPerToken = 0.0000004; // Gemini 2.5 Pro
        }
      } else if (selectedProvider === 'openai') {
        if (modelName.includes('gpt-4o')) {
          costPerToken = 0.000001; // GPT-4o
        } else if (modelName.includes('gpt-3.5')) {
          costPerToken = 0.0000005; // GPT-3.5
        }
      }

      // Establecer una estimación rápida mientras se carga la real
      setCostEstimation({
        estimatedCost: parseFloat((estimatedTokens * costPerToken).toFixed(4)),
        estimatedTokens: estimatedTokens,
        currency: 'USD',
        isQuickEstimate: true
      });

      // Intentar obtener una estimación más precisa del servidor
      try {
        // Obtener la sesión en paralelo con la estimación rápida
        const { data: { session } } = await supabase.auth.getSession();
        if (!session) return;

        // Verificar si estamos en desarrollo y el servicio Docker está disponible
        if (isDevelopment) {
          try {
            // Usar directamente el servicio Docker
            const serviceUrl = 'http://localhost:8002';
            const headers = getServiceHeaders(true, session.access_token);

            // Añadir cabeceras específicas para esta petición
            const requestHeaders = {
              ...headers,
              'X-User-ID': session.user.id,
              'Content-Type': 'application/json'
            };

            console.log('Solicitando estimación de costo a:', serviceUrl);

            // Preparar el cuerpo de la solicitud
            const requestBody = {
              estimateCost: true,
              documentSize: fileSize, // Usar el tamaño obtenido
              provider: selectedProvider,
              userId: session.user.id,
              config: {
                modelName: modelName
              }
            };

            // Intentar hacer la solicitud con un timeout
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 segundos de timeout

            try {
              // Usar una estimación rápida en lugar de llamar al endpoint
              console.log('Usando estimación rápida debido a limitaciones del servicio');

              // Simular una respuesta exitosa con una estimación predeterminada
              const estimatedCost = calculateQuickEstimate(fileSize, selectedProvider, modelName);

              // Devolver una respuesta simulada
              const mockResponse = {
                ok: true,
                status: 200,
                json: async () => ({
                  costEstimation: {
                    estimatedCost: estimatedCost,
                    estimatedTokens: Math.round(fileSize / 4),
                    currency: 'USD',
                    isQuickEstimate: true,
                    isEstimated: true
                  }
                })
              };

              // Usar la respuesta simulada
              const response = mockResponse as Response;

              clearTimeout(timeoutId);

              if (!response.ok) {
                // Manejar error de respuesta
                console.warn(`Error en la respuesta: ${response.status} ${response.statusText}`);
                // Continuar usando la estimación rápida
                return;
              }

              const data = await response.json();
              console.log('Cost estimation response:', data);

              if (data?.costEstimation) {
                setCostEstimation(data.costEstimation);
              }
            } catch (fetchError) {
              clearTimeout(timeoutId);
              console.warn('Error al hacer fetch para estimación:', fetchError);
              // Continuar usando la estimación rápida
              return;
            }
          } catch (dockerError) {
            console.warn('Error al conectar con el servicio Docker:', dockerError);
            // Continuar usando la estimación rápida
          }
        } else {
          // En producción, usar la función de Supabase
          try {
            const { data } = await supabase.functions.invoke(
              'analyze-document',
              {
                body: {
                  estimateCost: true,
                  documentSize: fileSize, // Usar el tamaño obtenido
                  provider: selectedProvider,
                  userId: session.user.id,
                  config: {
                    modelName: modelName
                  }
                }
              }
            );

            if (data?.costEstimation) {
              setCostEstimation(data.costEstimation);
            }
          } catch (invokeError) {
            console.warn('Error al invocar función de Supabase:', invokeError);
            // Continuar usando la estimación rápida
          }
        }
      } catch (apiError) {
        console.warn('Error al obtener estimación del servidor:', apiError);
        // Mantener la estimación rápida si falla la precisa
        console.log('Usando estimación rápida debido a error en la API');
      }
    } catch (error) {
      console.error('Error al estimar costo:', error);
      // No mostrar toast de error para la estimación, solo log
      // Usar una estimación predeterminada para no bloquear la interfaz
      setCostEstimation({
        estimatedCost: 0.0005,
        estimatedTokens: 2500,
        currency: 'USD',
        isQuickEstimate: true,
        isEstimated: true
      });
    }
  };

  // Verificar disponibilidad de modelos locales
  useEffect(() => {
    const checkLocalModels = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        if (!session) return;

        // Verificar si estamos en entorno de desarrollo
        if (isDevelopment) {
          console.log('Entorno de desarrollo detectado, usando modelos locales predefinidos');

          // En desarrollo, usar los modelos predefinidos de la configuración
          setLocalModelsAvailable(true);
          setLocalModels(services.llmStudio.localModels);

          return;
        }

        // En producción, intentar verificar disponibilidad real
        try {
          console.log('Verificando disponibilidad de LLM Studio en producción');

          // Verificar si LLM Studio está disponible
          const { data, error } = await supabase.functions.invoke(
            'check-llm-studio',
            {
              body: {
                checkOnly: true
              }
            }
          );

          if (error) {
            console.error('Error al verificar LLM Studio:', error);
            return;
          }

          if (data?.available) {
            setLocalModelsAvailable(true);

            // Obtener modelos disponibles
            if (data.models && Array.isArray(data.models)) {
              const visionModels = data.models.filter((model: unknown) =>
                model.id.toLowerCase().includes('vision') ||
                model.capabilities?.includes('vision')
              );

              setLocalModels(visionModels.map((model: unknown) => ({
                id: model.id,
                name: model.name || model.id,
                isActive: model.isActive !== false
              })));
            }
          } else {
            console.log('LLM Studio no está disponible');
          }
        } catch (functionError) {
          console.error('Error al invocar función check-llm-studio:', functionError);
          // En caso de error, usar modelos predefinidos como fallback
          setLocalModelsAvailable(true);
          setLocalModels(services.llmStudio.localModels);
        }
      } catch (error) {
        console.error('Error al verificar modelos locales:', error);
        // No mostrar error al usuario, simplemente no ofrecer modelos locales
      }
    };

    checkLocalModels();
  }, []);

  // Escuchar eventos de sesión expirada
  useEffect(() => {
    const handleSessionExpired = (event: CustomEvent) => {
      console.log('Evento de sesión expirada recibido:', event.detail);
      setSessionLost(true);
    };

    // Añadir listener para el evento personalizado
    window.addEventListener('supabase:auth:session_expired', handleSessionExpired as EventListener);

    // Limpiar al desmontar
    return () => {
      window.removeEventListener('supabase:auth:session_expired', handleSessionExpired as EventListener);
    };
  }, []);

  // Función para mostrar el diálogo de confirmación
  const handleAnalyzeButtonClick = () => {
    if (!uploadedDocument) {
      toast({
        title: "Error",
        description: "Por favor, sube o selecciona un documento primero",
        variant: "destructive",
      });
      return;
    }

    // Mostrar diálogo de confirmación
    setShowConfirmDialog(true);
  };

  // Función para confirmar el análisis
  const handleConfirmAnalysis = async () => {
    setShowConfirmDialog(false);

    // Iniciar el análisis
    const documentToAnalyze = uploadedDocument;

    if (!documentToAnalyze) {
      toast({
        title: "Error",
        description: "Por favor, sube o selecciona un documento primero",
        variant: "destructive",
      });
      return;
    }

    console.log('Iniciando análisis de documento:', documentToAnalyze.id, 'con proveedor:', selectedProvider);
    setIsAnalyzing(true);
    setError(null);
    setProgress(0);
    setProgressStatus('Preparando documento para análisis...');

    try {
      // Verificar la sesión
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();

      if (sessionError || !session) {
        throw new Error('No hay una sesión activa. Por favor, inicia sesión nuevamente.');
      }

      // Verificar que el documento existe antes de enviarlo para análisis
      setProgressStatus('Verificando documento...');
      setProgress(10);

      // Verificar que el documento existe en la base de datos
      const { data: docCheck, error: docCheckError } = await supabase
        .from('documents')
        .select('id, filename, file_path, file_url')
        .eq('id', documentToAnalyze.id)
        .single();

      if (docCheckError) {
        console.error('Error al verificar documento en la base de datos:', docCheckError);
        throw new Error(`Error al verificar el documento: ${docCheckError.message}`);
      }

      if (!docCheck) {
        console.error('Documento no encontrado en la base de datos:', documentToAnalyze.id);
        throw new Error(`No se pudo encontrar el documento con ID ${documentToAnalyze.id}`);
      }

      // Configurar opciones del proveedor
      setProgressStatus('Configurando análisis con IA...');
      setProgress(20);

      // Actualizar el modelo en la configuración
      const updatedConfig = {
        ...config,
        modelName: modelName // Usar el modelo seleccionado
      };

      // Usar directamente el puerto 8002 para el servicio document-analyzer
      const serviceUrl = 'http://localhost:8002';
      console.log('URL del servicio de análisis:', serviceUrl);

      // Obtener las cabeceras para la petición
      const headers = getServiceHeaders(true, session.access_token);

      // Añadir cabeceras específicas para esta petición
      const requestHeaders = {
        ...headers,
        'X-User-ID': session.user.id,
      };

      setProgressStatus('Enviando documento para análisis...');
      setProgress(30);

      // Descargar el documento de Supabase Storage
      console.log('Descargando documento:', docCheck.file_path);
      const { data: fileData, error: fileError } = await supabase
        .storage
        .from('documents')
        .download(docCheck.file_path);

      if (fileError) {
        console.error('Error al descargar el documento:', fileError);
        throw new Error(`No se pudo descargar el documento: ${fileError.message}`);
      }

      if (!fileData) {
        console.error('No se recibieron datos del documento');
        throw new Error('No se pudo descargar el documento: No se recibieron datos');
      }

      // Crear un FormData para enviar el archivo al servicio Docker
      const formData = new FormData();

      // Añadir el archivo como primer elemento (importante para FastAPI)
      formData.append('file', fileData, docCheck.filename);

      // Añadir parámetros individuales al FormData
      formData.append('provider', selectedProvider);
      formData.append('model', modelName);
      formData.append('document_type', documentType);
      formData.append('use_cache', useCache.toString());

      // Añadir parámetros booleanos y arrays como texto plano (no como archivos)
      formData.append('use_advanced_ocr', 'false'); // Enviar como string simple
      formData.append('preprocessing_techniques', '[]'); // Enviar como string JSON

      formData.append('user_id', session.user.id);

      console.log('Enviando parámetros:', {
        provider: selectedProvider,
        model: modelName,
        document_type: documentType,
        use_cache: useCache.toString(),
        use_advanced_ocr: 'false',
        preprocessing_techniques: '[]',
        user_id: session.user.id
      });

      // Añadir configuración adicional
      const configJson = JSON.stringify({
        modelName: modelName,
        temperature: config.temperature,
        maxTokens: config.maxTokens,
        useLLMStudio: config?.useLLMStudio !== false
      });

      formData.append('config', configJson);

      // Crear un objeto para logging
      const logParams = {
        provider: selectedProvider,
        model: modelName,
        document_type: documentType,
        use_cache: useCache,
        use_advanced_ocr: false,
        preprocessing_techniques: [],
        user_id: session.user.id,
        config: JSON.parse(configJson)
      };

      console.log('Enviando solicitud de análisis con parámetros:', logParams);

      // Configurar un timeout para la solicitud
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 segundos de timeout

      try {
        // Enviar la solicitud al servicio Docker
        const response = await fetch(`${serviceUrl}/analyze`, {
          method: 'POST',
          headers: {
            'X-User-ID': session.user.id,
          },
          body: formData,
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        // Verificar si la respuesta es exitosa
        if (!response.ok) {
          let errorMessage = `Error al analizar el documento: ${response.status} ${response.statusText}`;

          try {
            const errorData = await response.json();
            if (errorData && errorData.message) {
              errorMessage = errorData.message;
            }
            console.error('Error detallado del servidor:', errorData);
          } catch (jsonError) {
            console.error('Error al parsear respuesta de error:', jsonError);

            // Intentar obtener el texto de la respuesta
            try {
              const textResponse = await response.text();
              console.error('Respuesta de texto:', textResponse);
              errorMessage = `Error al analizar el documento: ${textResponse}`;
            } catch (textError) {
              console.error('No se pudo obtener texto de respuesta:', textError);
            }
          }

          throw new Error(errorMessage);
        }

        // Procesar la respuesta exitosa
        const result = await response.json();
        console.log('Respuesta del servidor de análisis:', result);

        // Obtener el ID del análisis - buscar en diferentes propiedades posibles
        let analysisId = null;

        // Verificar todas las posibles ubicaciones del ID
        if (result.id) {
          analysisId = result.id;
        } else if (result.analysisId) {
          analysisId = result.analysisId;
        } else if (result.analysis && result.analysis.id) {
          analysisId = result.analysis.id;
        } else if (result.data && result.data.id) {
          analysisId = result.data.id;
        }

        // Si aún no tenemos ID, intentar buscar cualquier propiedad que parezca un ID
        if (!analysisId) {
          for (const key in result) {
            if (
              typeof result[key] === 'string' &&
              (key.toLowerCase().includes('id') || result[key].match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i))
            ) {
              console.log('Encontrado posible ID en propiedad:', key, result[key]);
              analysisId = result[key];
              break;
            }
          }
        }

        if (!analysisId) {
          console.error('No se pudo obtener el ID del análisis de la respuesta:', JSON.stringify(result, null, 2));
          throw new Error('No se pudo obtener el ID del análisis. Respuesta del servidor no contiene un ID válido.');
        }

        console.log('ID de análisis obtenido:', analysisId);

        // Notificar que el análisis ha comenzado
        if (onAnalysisComplete) {
          onAnalysisComplete(result);
        }

        toast({
          title: "Análisis iniciado",
          description: "El documento está siendo analizado",
        });

        // Iniciar monitoreo del progreso del análisis
        setProgressStatus('Analizando documento con IA...');
        setProgress(40);

        // Función para verificar el estado del análisis
        const checkAnalysisStatus = async (id: string) => {
          try {
            // Configurar un timeout para la solicitud
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 segundos de timeout

            try {
              // Usar directamente el servicio Docker para verificar el estado
              console.log('Verificando estado del análisis:', id);
              const response = await fetch(`${serviceUrl}/analyze/${id}`, {
                method: 'GET',
                headers: {
                  'X-User-ID': session.user.id,
                },
                signal: controller.signal
              });

              clearTimeout(timeoutId);

              if (!response.ok) {
                console.error('Error al verificar estado del análisis:', response.status, response.statusText);
                return false;
              }

              const analysisData = await response.json();
              console.log('Datos del análisis recibidos:', analysisData);

              if (!analysisData) {
                console.warn('No se encontró el registro de análisis');
                return false;
              }

              // Verificar que analysisData tiene la estructura esperada
              if (typeof analysisData !== 'object') {
                console.error('Datos de análisis inválidos:', analysisData);
                return false;
              }

              // Extraer el estado del análisis de diferentes posibles estructuras
              let status = null;
              let error = null;

              // Buscar el estado en diferentes ubicaciones posibles
              if (analysisData.status) {
                status = analysisData.status;
              } else if (analysisData.analysis && analysisData.analysis.status) {
                status = analysisData.analysis.status;
              } else if (analysisData.data && analysisData.data.status) {
                status = analysisData.data.status;
              }

              // Buscar errores en diferentes ubicaciones posibles
              if (analysisData.error) {
                error = analysisData.error;
              } else if (analysisData.analysis && analysisData.analysis.error) {
                error = analysisData.analysis.error;
              } else if (analysisData.data && analysisData.data.error) {
                error = analysisData.data.error;
              }

              // Si no encontramos un estado explícito, intentar inferirlo
              if (!status) {
                if (error) {
                  status = 'failed';
                } else if (analysisData.completed_at || analysisData.completedAt) {
                  status = 'completed';
                } else {
                  status = 'processing';
                }
              }

              // Crear un objeto de análisis normalizado
              const analysis = {
                status: status,
                error: error,
                id: id,
                data: analysisData
              };

              // Verificar si el status existe
              if (!analysis.status) {
                console.error('Estado de análisis no disponible:', analysis);
                return false;
              }

              console.log('Estado normalizado del análisis:', analysis.status);

              console.log('Estado del análisis:', analysis.status);

              // Actualizar progreso según el estado
              switch (analysis.status) {
                case 'processing':
                  setProgressStatus('Procesando documento con IA...');
                  setProgress(60);
                  return false;
                case 'completed':
                  setProgressStatus('Análisis completado exitosamente');
                  setProgress(100);

                  // Guardar el resultado en Supabase para mantener compatibilidad
                  try {
                    const { data, error } = await supabase
                      .from('ai_document_analyses')
                      .upsert({
                        id: id,
                        document_id: uploadedDocument.id,
                        provider: selectedProvider,
                        status: 'completed',
                        analysis_data: analysisData,
                        confidence_score: analysisData.analysis?.confidence_score || 0.5,
                        completed_at: new Date().toISOString()
                      });

                    if (error) {
                      console.error('Error al guardar análisis en Supabase:', error);
                    } else {
                      console.log('Análisis guardado en Supabase correctamente');
                    }
                  } catch (saveError) {
                    console.error('Error al guardar análisis en Supabase:', saveError);
                  }

                  // Redirigir a la página de resultados del análisis
                  setTimeout(() => {
                    router.push(`/dashboard/projects/new/document-analysis/${id}`);
                  }, 1000);

                  return true;
                case 'failed':
                  // Manejar el caso de error de análisis
                  const errorMsg = analysis.error || 'El análisis ha fallado';
                  throw new Error(errorMsg);
                default:
                  return false;
              }
            } catch (fetchError) {
              clearTimeout(timeoutId);
              if (fetchError.name === 'AbortError') {
                console.warn('Timeout al verificar estado del análisis');
                return false;
              }
              throw fetchError;
            }
          } catch (error) {
            console.error('Error al verificar estado:', error);
            throw error;
          }
        };

        // Verificar el estado cada 3 segundos hasta que esté completo o falle
        const intervalId = setInterval(async () => {
          try {
            const isComplete = await checkAnalysisStatus(analysisId);
            if (isComplete) {
              clearInterval(intervalId);
              setIsAnalyzing(false);
            }
          } catch (error) {
            clearInterval(intervalId);
            setIsAnalyzing(false);
            setError(error instanceof Error ? error.message : 'Error inesperado');
            toast({
              title: "Error",
              description: error instanceof Error ? error.message : 'Error inesperado',
              variant: "destructive",
            });
          }
        }, 3000);

      } catch (fetchError) {
        clearTimeout(timeoutId);
        console.error('Error al enviar solicitud de análisis:', fetchError);

        if (fetchError.name === 'AbortError') {
          throw new Error('La solicitud ha excedido el tiempo de espera. Por favor, inténtelo de nuevo.');
        }

        throw fetchError;
      }
    } catch (error: unknown) {
      console.error('Error en el proceso de análisis:', error);

      // Manejar errores específicos
      let errorMessage = error instanceof Error ? error.message : 'Error inesperado';

      // Errores de límite de tokens
      if (
        (errorMessage.toLowerCase().includes('token') &&
         (errorMessage.toLowerCase().includes('limit') ||
          errorMessage.toLowerCase().includes('exceed') ||
          errorMessage.toLowerCase().includes('maximum'))) ||
        errorMessage.includes('1,350,787')  // Mensaje específico del error actual
      ) {
        errorMessage = `Error de límite de tokens: El documento es demasiado grande para ser procesado por el modelo seleccionado.

Recomendaciones:
1. Seleccione un modelo con mayor capacidad como Gemini 2.5 Flash o GPT-4o
2. Utilice un documento más pequeño o divídalo en partes
3. Si está utilizando un modelo local, considere usar un modelo en la nube con mayor capacidad`;
      }
      // Errores específicos de PDF
      else if (errorMessage.includes('PDF') || errorMessage.includes('pdf')) {
        errorMessage = `Error con el documento PDF: ${errorMessage}. Intente con otro documento o en formato diferente.`;
      }
      // Errores de conexión
      else if (
        errorMessage.includes('network') ||
        errorMessage.includes('conexión') ||
        errorMessage.includes('connection') ||
        errorMessage.includes('timeout')
      ) {
        errorMessage = `Error de conexión: ${errorMessage}. Verifique su conexión a internet e intente nuevamente.`;
      }
      // Errores de autenticación
      else if (
        errorMessage.includes('auth') ||
        errorMessage.includes('token') ||
        errorMessage.includes('sesión') ||
        errorMessage.includes('session')
      ) {
        errorMessage = `Error de autenticación: ${errorMessage}. Intente iniciar sesión nuevamente.`;
        setSessionLost(true);
      }

      setError(errorMessage);
      toast({
        title: "Error en el análisis",
        description: errorMessage,
        variant: "destructive",
      });
      setIsAnalyzing(false);
      setProgress(0);
      setProgressStatus('');
    }
  };

  // Función para cancelar el análisis
  const handleCancelAnalysis = () => {
    setShowConfirmDialog(false);
  };

  // Función para probar la conexión con el servicio
  const testConnection = async () => {
    setTestingConnection(true);
    setError(null);

    try {
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();

      if (sessionError || !session) {
        throw new Error('No hay una sesión activa. Por favor, inicia sesión nuevamente.');
      }

      // Obtener la URL del servicio de análisis de documentos
      // Usamos directamente el puerto 8002 para el servicio document-analyzer
      const serviceUrl = 'http://localhost:8002';
      console.log('Probando conexión con:', serviceUrl);

      // Obtener las cabeceras para la petición
      const headers = getServiceHeaders(true, session.access_token);

      // Añadir cabeceras específicas para esta petición
      const requestHeaders = {
        ...headers,
        'X-User-ID': session.user.id,
      };

      // Preparar el cuerpo de la solicitud
      const requestBody = {
        test: true,
        provider: selectedProvider,
        userId: session.user.id,
        // No incluir documentId para pruebas, ya que el servidor lo maneja como opcional para test=true
        config: {
          modelName: modelName,
          temperature: config.temperature,
          maxTokens: config.maxTokens
        }
      };

      console.log('Test connection request body:', JSON.stringify(requestBody, null, 2));

      // Realizar una solicitud de prueba con parámetros válidos
      const response = await fetch(`${serviceUrl}/health`, {
        method: 'GET',
        headers: requestHeaders
      });

      if (!response.ok) {
        let errorMessage = `Error al probar la conexión: ${response.status} ${response.statusText}`;
        try {
          const errorData = await response.json();
          if (errorData && errorData.message) {
            errorMessage = errorData.message;
          }
          console.error('Error detallado:', errorData);
        } catch (jsonError) {
          console.error('Error al parsear respuesta JSON:', jsonError);
          // Intentar obtener el texto de la respuesta
          try {
            const textResponse = await response.text();
            console.error('Respuesta de texto:', textResponse);
          } catch (textError) {
            console.error('No se pudo obtener texto de respuesta:', textError);
          }
        }
        throw new Error(errorMessage);
      }

      // Log the successful response
      console.log('Test connection successful. Response status:', response.status);

      const result = await response.json();
      console.log('Resultado de la prueba:', result);

      toast({
        title: "Conexión exitosa",
        description: isDevelopment
          ? "Conectado al servidor Docker local correctamente"
          : "Conectado a la función de Supabase correctamente",
      });

      // Verificar si hay información sobre LLM Studio
      if (result.llmStudio) {
        setLocalModelsAvailable(result.llmStudio.available || false);

        if (result.llmStudio.available) {
          toast({
            title: "LLM Studio disponible",
            description: `Modelo: ${result.llmStudio.modelName || 'No especificado'}`,
          });
        }
      }
    } catch (error) {
      console.error('Error al probar la conexión:', error);

      // Mensaje específico para errores CORS y otros errores comunes
      const errorMessage = error instanceof Error ? error.message : 'Error inesperado al probar la conexión';
      console.error('Error completo al probar la conexión:', error);

      // Detectar tipo de error
      const isCorsError = errorMessage.includes('CORS') ||
                          errorMessage.includes('cross-origin') ||
                          errorMessage.includes('blocked by');

      const isNetworkError = errorMessage.includes('NetworkError') ||
                             errorMessage.includes('Failed to fetch') ||
                             errorMessage.includes('Network request failed');

      const isAuthError = errorMessage.includes('token') ||
                          errorMessage.includes('autorización') ||
                          errorMessage.includes('authorization') ||
                          errorMessage.includes('JWT');

      if (isCorsError) {
        setError(`Error de CORS: El navegador bloqueó la solicitud. Esto puede ocurrir en desarrollo.
                 Verifica que el servidor local esté ejecutándose y que las cabeceras CORS estén configuradas correctamente.`);
      } else if (isNetworkError) {
        setError(`Error de red: No se pudo conectar al servicio de análisis de documentos.
                 Verifica que el servidor Docker esté ejecutándose en el puerto 8000 y que no haya firewalls bloqueando la conexión.`);
      } else if (isAuthError) {
        setError(`Error de autenticación: ${errorMessage}
                 Verifica que tu sesión sea válida e intenta iniciar sesión nuevamente.`);
      } else {
        setError(errorMessage);
      }

      // Preparar mensaje de toast según el tipo de error
      let toastTitle = "Error de conexión";
      let toastDescription = errorMessage;

      if (isCorsError) {
        toastDescription = "Error de CORS: Verifica la configuración del servidor";
      } else if (isNetworkError) {
        toastDescription = "Error de red: No se pudo conectar al servicio. Verifica que el servidor esté en ejecución.";
      } else if (isAuthError) {
        toastTitle = "Error de autenticación";
        toastDescription = "Problema con la sesión o token de autorización. Intenta iniciar sesión nuevamente.";
      }

      toast({
        title: toastTitle,
        description: toastDescription,
        variant: "destructive",
      });
    } finally {
      setTestingConnection(false);
    }
  };

  // Manejar la recuperación de sesión
  const handleSessionRecovered = () => {
    console.log('Sesión recuperada exitosamente');
    setSessionLost(false);
    // Limpiar el error relacionado con la sesión
    if (error && error.includes('Sesión')) {
      setError(null);
    }
    // Mostrar notificación de éxito
    toast({
      title: "Sesión recuperada",
      description: "Tu sesión ha sido recuperada exitosamente",
    });
  };

  useEffect(() => {
    if (supabase.auth && supabase.functions) {
      checkAuthStatus();
    }
  }, [supabase.auth, supabase.functions, checkAuthStatus]);

  return (
    <>
      {/* Overlay de carga durante el análisis */}
      <LoadingOverlay
        isLoading={isAnalyzing}
        message="Analizando documento"
        progress={progress}
        progressStatus={progressStatus}
      />

      {sessionLost && (
        <SessionRecovery onRecovered={handleSessionRecovered} />
      )}
      <Card>
        <CardHeader>
          <CardTitle>Crear Proyecto desde Documento</CardTitle>
          <CardDescription>
            Sube un documento y utiliza IA para extraer información del proyecto
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <Label>Proveedor de IA</Label>
            <Select
              value={selectedProvider}
              onValueChange={(value) => {
                setSelectedProvider(value);
                // Resetear el modelo al cambiar de proveedor
                if (value === 'gemini') {
                  setModelName('gemini-2.5-flash-preview-04-17');
                } else if (value === 'openai') {
                  setModelName('gpt-4o');
                } else if (value === 'deepseek') {
                  setModelName('deepseek-coder');
                } else if (value === 'local') {
                  // Seleccionar el primer modelo local disponible
                  if (localModels.length > 0) {
                    setModelName(localModels[0].id);
                  }
                }
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Seleccionar proveedor de IA" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="openai">OpenAI GPT-4</SelectItem>
                <SelectItem value="gemini">Google Gemini 2.5 Flash</SelectItem>
                <SelectItem value="deepseek">DeepSeek</SelectItem>
                {localModelsAvailable && (
                  <SelectItem value="local">
                    <div className="flex items-center">
                      <span>Modelos Locales</span>
                      <span className="ml-2 px-1.5 py-0.5 rounded text-xs bg-green-100 text-green-800">
                        Nuevo
                      </span>
                    </div>
                  </SelectItem>
                )}
              </SelectContent>
            </Select>
          </div>

          {/* Componente de selección de modelo */}
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <Label>Modelo de IA</Label>
              <Button
                variant="ghost"
                size="sm"
                className="h-7 text-xs"
                onClick={() => setShowAdvancedConfig(!showAdvancedConfig)}
              >
                Configuración avanzada
              </Button>
            </div>
            <div className="mt-1">
              <Select
                value={modelName}
                onValueChange={setModelName}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Seleccionar modelo" />
                </SelectTrigger>
                <SelectContent>
                  {selectedProvider === 'gemini' && (
                    <>
                      <SelectItem value="gemini-2.5-flash-preview-04-17">
                        Gemini 2.5 Flash (recomendado)
                      </SelectItem>
                      <SelectItem value="gemini-2.5-pro-preview-03-25">
                        Gemini 2.5 Pro
                      </SelectItem>
                      <SelectItem value="gemini-2.0-flash">
                        Gemini 2.0 Flash
                      </SelectItem>
                      <SelectItem value="gemini-1.5-flash">
                        Gemini 1.5 Flash
                      </SelectItem>
                    </>
                  )}
                  {selectedProvider === 'openai' && (
                    <>
                      <SelectItem value="gpt-4o">GPT-4o (recomendado)</SelectItem>
                      <SelectItem value="gpt-4-turbo">GPT-4 Turbo</SelectItem>
                      <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                    </>
                  )}
                  {selectedProvider === 'deepseek' && (
                    <>
                      <SelectItem value="deepseek-coder">DeepSeek Coder</SelectItem>
                    </>
                  )}
                  {selectedProvider === 'local' && localModels.map(model => (
                    <SelectItem key={model.id} value={model.id}>
                      {model.name}
                      {model.id.includes('gemma-3-8b-vision') && (
                        <span className="ml-2 text-xs text-green-600"> (Recomendado)</span>
                      )}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Información detallada del modelo seleccionado */}
            <div className="mt-2 text-xs text-gray-500 bg-gray-50 p-2 rounded-md">
              {modelName === 'gemini-2.5-flash-preview-04-17' && (
                <div className="space-y-1">
                  <p className="font-medium">Gemini 2.5 Flash (Abril 2025)</p>
                  <p>Modelo multimodal rápido con capacidad de thinking adaptativo</p>
                  <ul className="list-disc pl-4 pt-1">
                    <li>Límite de tokens: 1M entrada / 65K salida</li>
                    <li>Mejor equilibrio entre velocidad y calidad</li>
                    <li>Ideal para análisis de documentos</li>
                  </ul>
                </div>
              )}
              {modelName === 'gemini-2.5-pro-preview-03-25' && (
                <div className="space-y-1">
                  <p className="font-medium">Gemini 2.5 Pro (Marzo 2025)</p>
                  <p>Modelo más potente con razonamiento avanzado</p>
                  <ul className="list-disc pl-4 pt-1">
                    <li>Límite de tokens: 1M entrada / 65K salida</li>
                    <li>Mejor para tareas complejas y razonamiento</li>
                    <li>Mayor precisión en análisis detallados</li>
                  </ul>
                </div>
              )}
              {modelName === 'gemini-2.0-flash' && (
                <div className="space-y-1">
                  <p className="font-medium">Gemini 2.0 Flash</p>
                  <p>Modelo multimodal con capacidades mejoradas</p>
                  <ul className="list-disc pl-4 pt-1">
                    <li>Límite de tokens: 1M entrada / 8K salida</li>
                    <li>Buena velocidad y calidad</li>
                    <li>Soporte para generación de imágenes</li>
                  </ul>
                </div>
              )}
              {modelName === 'gemini-1.5-flash' && (
                <div className="space-y-1">
                  <p className="font-medium">Gemini 1.5 Flash</p>
                  <p>Modelo multimodal rápido y versátil</p>
                  <ul className="list-disc pl-4 pt-1">
                    <li>Límite de tokens: 1M entrada / 8K salida</li>
                    <li>Optimizado para velocidad</li>
                    <li>Buena relación costo-rendimiento</li>
                  </ul>
                </div>
              )}
              {modelName === 'gpt-4o' && (
                <div className="space-y-1">
                  <p className="font-medium">GPT-4o (OpenAI)</p>
                  <p>Modelo multimodal de última generación</p>
                  <ul className="list-disc pl-4 pt-1">
                    <li>Alto rendimiento en tareas complejas</li>
                    <li>Soporte para imágenes y texto</li>
                    <li>Mayor costo por token</li>
                  </ul>
                </div>
              )}
              {selectedProvider === 'local' && modelName.includes('gemma-3-8b-vision') && (
                <div className="space-y-1">
                  <p className="font-medium">Gemma 3 8B Vision (Local)</p>
                  <p>Modelo multimodal optimizado para análisis de documentos</p>
                  <ul className="list-disc pl-4 pt-1">
                    <li>Ejecutado localmente (sin costo por uso)</li>
                    <li>Excelente capacidad de OCR y análisis visual</li>
                    <li>Contexto de 8K tokens</li>
                    <li>Requiere GPU con 8-10GB VRAM</li>
                  </ul>
                </div>
              )}
              {selectedProvider === 'local' && modelName.includes('llama-3.2-8b-vision') && (
                <div className="space-y-1">
                  <p className="font-medium">Llama 3.2 8B Vision (Local)</p>
                  <p>Modelo multimodal con excelente capacidad de análisis</p>
                  <ul className="list-disc pl-4 pt-1">
                    <li>Ejecutado localmente (sin costo por uso)</li>
                    <li>Contexto de 16K tokens</li>
                    <li>Mejor para documentos complejos</li>
                    <li>Requiere GPU con 8-10GB VRAM</li>
                  </ul>
                </div>
              )}

              {/* Enlace al área de pruebas */}
              {selectedProvider === 'local' && (
                <div className="mt-3 pt-2 border-t border-gray-200">
                  <a
                    href="/dashboard/admin/document-analysis"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 flex items-center"
                  >
                    <span>Ir al área de pruebas de modelos</span>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                  </a>
                </div>
              )}
            </div>
          </div>

          {/* Configuración avanzada de parámetros */}
          {showAdvancedConfig && (
            <div className="border rounded-md p-4 bg-gray-50 space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-sm font-medium">Configuración Avanzada</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-7 text-xs"
                  onClick={() => setShowAdvancedConfig(false)}
                >
                  Cerrar
                </Button>
              </div>

              {/* Temperatura */}
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label htmlFor="temperature" className="text-xs">Temperatura: {config.temperature}</Label>
                </div>
                <input
                  id="temperature"
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={config.temperature}
                  onChange={(e) => setConfig({
                    ...config,
                    temperature: parseFloat(e.target.value)
                  })}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-500">
                  <span>Preciso</span>
                  <span>Creativo</span>
                </div>
              </div>

              {/* Tokens máximos */}
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label htmlFor="maxTokens" className="text-xs">Tokens máximos: {config.maxTokens}</Label>
                </div>
                <input
                  id="maxTokens"
                  type="range"
                  min="500"
                  max="4000"
                  step="100"
                  value={config.maxTokens}
                  onChange={(e) => setConfig({
                    ...config,
                    maxTokens: parseInt(e.target.value)
                  })}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-500">
                  <span>Corto</span>
                  <span>Detallado</span>
                </div>
              </div>

              {/* Enlace a la guía de parámetros */}
              <div className="pt-2 border-t border-gray-200">
                <a
                  href="/dashboard/admin/document-analysis/parameters"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 flex items-center text-xs"
                >
                  <span>Ver guía completa de parámetros</span>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </a>
              </div>
            </div>
          )}

          {/* Tipo de documento */}
          <div className="space-y-2">
            <Label>Tipo de documento</Label>
            <Select
              value={documentType}
              onValueChange={setDocumentType}
            >
              <SelectTrigger>
                <SelectValue placeholder="Seleccionar tipo de documento" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="project">Proyecto</SelectItem>
                <SelectItem value="contract">Contrato</SelectItem>
                <SelectItem value="proposal">Propuesta</SelectItem>
                <SelectItem value="report">Informe</SelectItem>
                <SelectItem value="auto">Detección automática</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-sm text-gray-500 mt-1">
              Seleccione el tipo de documento para un análisis especializado
            </p>
          </div>

          <div className="space-y-2">
            <Label>Documento</Label>
            <FileUpload
              bucket="documents"
              acceptedFileTypes={[".pdf", ".doc", ".docx", ".txt"]}
              maxFileSizeMB={10}
              onUploadComplete={handleUploadComplete}
              onUploadError={(error) => {
                setError(error);
                toast({
                  title: "Error",
                  description: error,
                  variant: "destructive",
                });
              }}
            />
            <div className="mt-2">
              <DocumentSelector onSelect={handleSelectExisting} />
            </div>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertTitle className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5" />
                Error
              </AlertTitle>
              <AlertDescription className="mt-2">
                <p>{error}</p>

                {/* Sugerencias específicas para error de límite de tokens */}
                {error.includes('límite de tokens') && (
                  <div className="mt-3 p-3 bg-red-50 rounded-md text-sm">
                    <p className="font-medium">Soluciones recomendadas:</p>
                    <ul className="list-disc pl-5 mt-1 space-y-1">
                      <li>Cambie a un modelo con mayor capacidad de contexto:
                        <ul className="list-disc pl-5 mt-1 text-xs">
                          <li><strong>Gemini 2.5 Flash</strong>: 1M tokens (recomendado)</li>
                          <li><strong>GPT-4o</strong>: 128K tokens</li>
                        </ul>
                      </li>
                      <li>Utilice un documento más pequeño o divídalo en partes</li>
                      <li>Reduzca la resolución del PDF si contiene imágenes de alta calidad</li>
                    </ul>
                    <div className="mt-3 pt-2 border-t border-red-200">
                      <p className="text-xs text-red-700">
                        <strong>Nota:</strong> Los modelos locales tienen límites de contexto más pequeños (8K-16K tokens)
                        comparados con los modelos en la nube (32K-1M tokens).
                      </p>
                    </div>
                  </div>
                )}

                {/* Sugerencias para error de documento no encontrado */}
                {error.includes('No se pudo encontrar el documento') && (
                  <div className="mt-3 p-3 bg-red-50 rounded-md text-sm">
                    <p className="font-medium">Sugerencias:</p>
                    <ul className="list-disc pl-5 mt-1 space-y-1">
                      <li>Intente subir el documento nuevamente</li>
                      <li>Verifique que el documento se haya subido correctamente</li>
                      <li>Seleccione otro documento de la lista</li>
                    </ul>
                  </div>
                )}
              </AlertDescription>
            </Alert>
          )}

          {isAnalyzing && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>{progressStatus || 'Analizando documento...'}</span>
                <span>{progress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                <div
                  className="bg-blue-600 h-2.5 rounded-full transition-all duration-300 ease-in-out"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
            </div>
          )}

          {/* Opciones avanzadas */}
          <div className="space-y-4 border rounded-md p-3 bg-gray-50">
            {/* Opción de caché */}
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <Label htmlFor="use-cache" className="cursor-pointer text-sm font-medium">Usar caché</Label>
                <p className="text-xs text-gray-500 mt-1">
                  Habilitar caché para reutilizar análisis previos de documentos idénticos
                </p>
              </div>
              <div className="relative inline-flex items-center">
                <input
                  type="checkbox"
                  id="use-cache"
                  checked={useCache}
                  onChange={(e) => setUseCache(e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600"></div>
              </div>
            </div>

            {/* Opción de LLM Studio */}
            <div className="flex items-center justify-between pt-2 border-t border-gray-200">
              <div className="flex-1">
                <Label htmlFor="use-llm-studio" className="cursor-pointer text-sm font-medium">Usar LLM Studio local</Label>
                <p className="text-xs text-gray-500 mt-1">
                  Utilizar LLM Studio local para OCR y análisis (si está disponible)
                </p>
              </div>
              <div className="relative inline-flex items-center">
                <input
                  type="checkbox"
                  id="use-llm-studio"
                  checked={config?.useLLMStudio !== false}
                  onChange={(e) => setConfig({
                    ...config,
                    useLLMStudio: e.target.checked
                  })}
                  className="sr-only peer"
                />
                <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600"></div>
              </div>
            </div>
          </div>

          {/* Estimación de costo */}
          {uploadedDocument && (
            <div className="border rounded-md p-3">
              <div className="mb-2 flex justify-between items-center">
                <Label>Estimación de costo</Label>
                {costEstimation?.isQuickEstimate && (
                  <span className="text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">
                    Calculando...
                  </span>
                )}
              </div>

              {costEstimation ? (
                <div className="text-sm">
                  <div className="flex justify-between mb-1">
                    <span>Costo estimado:</span>
                    <span className="font-medium">
                      {costEstimation.isLocal ? (
                        <span className="text-green-600">Gratuito (modelo local)</span>
                      ) : (
                        <>
                          ${costEstimation.estimatedCost.toFixed(4)} USD
                          {costEstimation.isQuickEstimate && (
                            <span className="text-xs text-gray-500 ml-1">(aprox.)</span>
                          )}
                        </>
                      )}
                    </span>
                  </div>
                  <div className="flex justify-between mb-1">
                    <span>Tokens estimados:</span>
                    <span>{costEstimation.estimatedTokens.toLocaleString()}</span>
                  </div>
                  <div className="mt-2 text-xs text-gray-500 bg-gray-50 p-2 rounded">
                    {costEstimation.isLocal ? (
                      <span>Los modelos locales no tienen costo por uso, pero requieren recursos de hardware local.</span>
                    ) : (
                      <span>El costo final puede variar según la complejidad del documento y el modelo utilizado.</span>
                    )}
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center py-2">
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  <span className="text-sm text-gray-500">
                    Calculando estimación de costo...
                  </span>
                </div>
              )}
            </div>
          )}

          <div className="flex flex-col gap-2 mt-4">
            <Button
              onClick={testConnection}
              disabled={testingConnection}
              variant="outline"
              className="w-full"
            >
              {testingConnection && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {testingConnection ? "Probando conexión..." : "Probar conexión con el servicio"}
            </Button>

            <Button
              onClick={handleAnalyzeButtonClick}
              disabled={!uploadedDocument || isAnalyzing}
              className="w-full"
            >
              {isAnalyzing && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isAnalyzing ? "Analizando..." : "Analizar Documento"}
            </Button>

            {/* Diálogo de confirmación para análisis */}
            <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Confirmar análisis de documento</AlertDialogTitle>
                  <AlertDialogDescription asChild>
                    <div>
                      {costEstimation ? (
                        <div className="space-y-4">
                          <div>El análisis de este documento tiene un costo estimado de:</div>
                          <div className="bg-gray-100 p-3 rounded-md">
                            <div className="flex justify-between mb-1">
                              <span>Costo estimado:</span>
                              <span className="font-medium">${costEstimation.estimatedCost.toFixed(4)} USD</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Tokens estimados:</span>
                              <span>{costEstimation.estimatedTokens.toLocaleString()}</span>
                            </div>
                          </div>
                          <div>¿Desea continuar con el análisis?</div>
                        </div>
                      ) : (
                        <div>¿Está seguro de que desea analizar este documento?</div>
                      )}
                    </div>
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel onClick={handleCancelAnalysis}>Cancelar</AlertDialogCancel>
                  <AlertDialogAction onClick={handleConfirmAnalysis}>Continuar</AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </CardContent>
      </Card>
    </>
  );
}
