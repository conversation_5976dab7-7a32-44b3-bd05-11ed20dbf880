'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { FcGoogle } from "react-icons/fc"
import { Loader2 } from "lucide-react"

interface GoogleButtonProps {
  onClick: () => void
  isLoading?: boolean
  text?: string
  disabled?: boolean
}

export function GoogleButton({
  onClick,
  isLoading = false,
  text = "Continuar con Google",
  disabled = false
}: GoogleButtonProps) {
  return (
    <Button
      variant="outline"
      type="button"
      disabled={isLoading || disabled}
      className="w-full flex items-center justify-center gap-2 relative"
      onClick={onClick}
    >
      {!isLoading && <FcGoogle className="h-5 w-5" />}
      {isLoading && <Loader2 className="h-5 w-5 animate-spin" />}
      <span>{isLoading ? "Conectando con Google..." : text}</span>
    </Button>
  )
}
