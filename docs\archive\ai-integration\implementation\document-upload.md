# Document Upload Component Implementation

## Overview

This guide details how to implement and enhance the document upload component to support AI document processing for project creation.

## Requirements

- Extend the existing document upload component
- Add AI processing option
- Support multiple file types
- Provide feedback during processing
- Handle errors gracefully

## Component Structure

```
DocumentUploadWithAI/
├── index.tsx                 # Main component
├── AIProcessingOptions.tsx   # AI provider selection and options
├── ProcessingStatus.tsx      # Status and progress indicators
├── FileValidation.ts         # File type and size validation
└── __tests__/                # Test files
    └── DocumentUploadWithAI.test.tsx
```

## Implementation Steps

### 1. Extend Existing Component

Start by extending the existing `DocumentUpload` component:

```typescript
// web/src/components/features/documents/document-upload-with-ai.tsx
"use client"

import { useState, useRef } from "react"
import { Upload, File, X, Loader2, Brain } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { toast } from "@/hooks/use-toast"
import { createClient } from "@/lib/supabase/client"
import { AIProcessingOptions } from "./AIProcessingOptions"
import { ProcessingStatus } from "./ProcessingStatus"

interface DocumentUploadWithAIProps {
  projectId?: string
  workOrderId?: string
  onUploadComplete?: (fileData: any) => void
  onAIProcessingComplete?: (analysisData: any) => void
  allowedFileTypes?: string[]
  maxFileSize?: number // en MB
}

export function DocumentUploadWithAI({
  projectId,
  workOrderId,
  onUploadComplete,
  onAIProcessingComplete,
  allowedFileTypes = [".pdf", ".doc", ".docx", ".txt"],
  maxFileSize = 10, // 10MB por defecto
}: DocumentUploadWithAIProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [description, setDescription] = useState("")
  const [enableAI, setEnableAI] = useState(false)
  const [selectedProvider, setSelectedProvider] = useState<string>("gemini")
  const [processingStatus, setProcessingStatus] = useState<string>("")
  const fileInputRef = useRef<HTMLInputElement>(null)
  const supabase = createClient()

  // ... rest of the component implementation
}
```

### 2. Add AI Processing Options

Create a component for AI provider selection:

```typescript
// web/src/components/features/documents/AIProcessingOptions.tsx
"use client"

import { useState, useEffect } from "react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Card, CardContent } from "@/components/ui/card"

interface AIProcessingOptionsProps {
  selectedProvider: string
  onProviderChange: (provider: string) => void
  disabled?: boolean
}

export function AIProcessingOptions({
  selectedProvider,
  onProviderChange,
  disabled = false
}: AIProcessingOptionsProps) {
  const [availableProviders, setAvailableProviders] = useState<{id: string, name: string}[]>([
    { id: "gemini", name: "Google Gemini" },
    { id: "openai", name: "OpenAI" },
    { id: "deepseek", name: "DeepSeek" }
  ])

  // Optionally fetch available providers from API
  useEffect(() => {
    async function fetchProviders() {
      try {
        const response = await fetch('/api/ai/providers')
        if (response.ok) {
          const data = await response.json()
          setAvailableProviders(data)
        }
      } catch (error) {
        console.error('Error fetching AI providers:', error)
      }
    }
    
    // Uncomment when API is implemented
    // fetchProviders()
  }, [])

  return (
    <Card className="mt-4">
      <CardContent className="pt-4">
        <div className="space-y-2">
          <Label htmlFor="ai-provider">AI Provider</Label>
          <Select
            value={selectedProvider}
            onValueChange={onProviderChange}
            disabled={disabled}
          >
            <SelectTrigger id="ai-provider">
              <SelectValue placeholder="Select AI provider" />
            </SelectTrigger>
            <SelectContent>
              {availableProviders.map(provider => (
                <SelectItem key={provider.id} value={provider.id}>
                  {provider.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <p className="text-sm text-muted-foreground mt-1">
            Select the AI provider to use for document analysis
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
```

### 3. Add Processing Status Component

Create a component to show processing status:

```typescript
// web/src/components/features/documents/ProcessingStatus.tsx
"use client"

import { Loader2, CheckCircle, XCircle } from "lucide-react"
import { Progress } from "@/components/ui/progress"

interface ProcessingStatusProps {
  status: string // 'idle', 'uploading', 'processing', 'success', 'error'
  progress?: number
  message?: string
}

export function ProcessingStatus({
  status,
  progress = 0,
  message = ""
}: ProcessingStatusProps) {
  if (status === 'idle') return null

  return (
    <div className="mt-4 space-y-2">
      {status === 'uploading' && (
        <>
          <div className="flex items-center">
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            <span>Uploading document...</span>
          </div>
          <Progress value={progress} className="h-2" />
        </>
      )}
      
      {status === 'processing' && (
        <>
          <div className="flex items-center">
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            <span>Processing document with AI...</span>
          </div>
          <Progress value={progress} className="h-2" />
        </>
      )}
      
      {status === 'success' && (
        <div className="flex items-center text-green-600">
          <CheckCircle className="h-4 w-4 mr-2" />
          <span>{message || "Processing complete"}</span>
        </div>
      )}
      
      {status === 'error' && (
        <div className="flex items-center text-red-600">
          <XCircle className="h-4 w-4 mr-2" />
          <span>{message || "Error processing document"}</span>
        </div>
      )}
    </div>
  )
}
```

### 4. Implement File Upload with AI Processing

Enhance the upload handler to include AI processing:

```typescript
// Inside DocumentUploadWithAI component

const handleUpload = async () => {
  if (!selectedFile) {
    toast({
      title: "Error",
      description: "No file selected",
      variant: "destructive",
    })
    return
  }

  setIsUploading(true)
  setUploadProgress(0)
  setProcessingStatus("uploading")

  try {
    // Generate a unique name for the file
    const fileExtension = selectedFile.name.split('.').pop()
    const fileName = `${Date.now()}_${Math.random().toString(36).substring(2, 15)}.${fileExtension}`
    const filePath = `documents/${fileName}`

    // Upload file to Supabase Storage
    const { data: storageData, error: storageError } = await supabase.storage
      .from('documents')
      .upload(filePath, selectedFile, {
        cacheControl: '3600',
        upsert: false,
      })

    if (storageError) throw storageError

    // Get public URL of the file
    const { data: urlData } = await supabase.storage
      .from('documents')
      .getPublicUrl(filePath)

    // Create record in the documents table
    const { data: documentData, error: documentError } = await supabase
      .from('documents')
      .insert({
        filename: selectedFile.name,
        file_path: filePath,
        file_size: selectedFile.size,
        file_type: selectedFile.type,
        description: description,
        project_id: projectId || null,
        work_order_id: workOrderId || null,
        public_url: urlData?.publicUrl,
        uploaded_by: (await supabase.auth.getUser()).data.user?.id,
      })
      .select()

    if (documentError) throw documentError

    // If AI processing is enabled, send for analysis
    if (enableAI && documentData && documentData[0]) {
      setProcessingStatus("processing")
      setUploadProgress(0)
      
      // Start AI processing
      const response = await fetch('/api/ai/analyze-document', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          documentId: documentData[0].id,
          provider: selectedProvider,
        }),
      })
      
      if (!response.ok) {
        throw new Error(`AI processing failed: ${response.statusText}`)
      }
      
      const analysisData = await response.json()
      
      setProcessingStatus("success")
      setUploadProgress(100)
      
      toast({
        title: "Document processed",
        description: "The document has been analyzed successfully",
      })
      
      // Call the callback with analysis data
      if (onAIProcessingComplete) {
        onAIProcessingComplete(analysisData)
      }
    } else {
      setProcessingStatus("success")
      setUploadProgress(100)
      
      toast({
        title: "Document uploaded",
        description: "The document has been uploaded successfully",
      })
    }

    // Clean up form
    setSelectedFile(null)
    setDescription("")
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
    
    // Call the callback with document data
    if (onUploadComplete && documentData) {
      onUploadComplete(documentData[0])
    }
  } catch (error) {
    console.error('Error uploading document:', error)
    setProcessingStatus("error")
    
    toast({
      title: "Upload failed",
      description: error.message || "An error occurred during upload",
      variant: "destructive",
    })
  } finally {
    setIsUploading(false)
  }
}
```

### 5. Update the UI to Include AI Options

Add AI processing toggle and options to the UI:

```tsx
// Inside the return statement of DocumentUploadWithAI

return (
  <div className="space-y-4">
    {/* Existing file drop zone */}
    <div
      className={`border-2 border-dashed rounded-lg p-6 text-center ${
        selectedFile ? "border-primary" : "border-muted-foreground/25"
      }`}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      {/* ... existing file drop UI ... */}
    </div>
    
    {/* File details and description */}
    {selectedFile && (
      <div className="space-y-4">
        {/* ... existing file details UI ... */}
        
        {/* AI Processing toggle */}
        <div className="flex items-center space-x-2">
          <Switch
            id="enable-ai"
            checked={enableAI}
            onCheckedChange={setEnableAI}
            disabled={isUploading}
          />
          <Label htmlFor="enable-ai">Enable AI document processing</Label>
        </div>
        
        {/* AI Provider options (only shown when AI is enabled) */}
        {enableAI && (
          <AIProcessingOptions
            selectedProvider={selectedProvider}
            onProviderChange={setSelectedProvider}
            disabled={isUploading}
          />
        )}
        
        {/* Processing status */}
        <ProcessingStatus
          status={processingStatus}
          progress={uploadProgress}
        />
        
        {/* Upload button */}
        <Button
          onClick={handleUpload}
          disabled={!selectedFile || isUploading}
          className="w-full"
        >
          {isUploading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {enableAI ? "Uploading & Processing..." : "Uploading..."}
            </>
          ) : (
            <>
              {enableAI ? (
                <>
                  <Brain className="mr-2 h-4 w-4" />
                  Upload & Process with AI
                </>
              ) : (
                <>
                  <Upload className="mr-2 h-4 w-4" />
                  Upload Document
                </>
              )}
            </>
          )}
        </Button>
      </div>
    )}
  </div>
)
```

## API Integration

The document upload component will interact with the following API endpoints:

1. `/api/ai/providers` - Get available AI providers
2. `/api/ai/analyze-document` - Submit a document for analysis
3. `/api/ai/create-project-from-analysis` - Create a project from analysis results

## Testing

Create tests for the component:

```typescript
// web/src/components/features/documents/__tests__/DocumentUploadWithAI.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { DocumentUploadWithAI } from '../document-upload-with-ai'
import { createClient } from '@/lib/supabase/client'

// Mock Supabase client
jest.mock('@/lib/supabase/client', () => ({
  createClient: jest.fn()
}))

// Mock fetch for API calls
global.fetch = jest.fn()

describe('DocumentUploadWithAI', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    
    // Mock Supabase methods
    const mockSupabase = {
      storage: {
        from: jest.fn().mockReturnValue({
          upload: jest.fn().mockResolvedValue({ data: { path: 'test-path' }, error: null }),
          getPublicUrl: jest.fn().mockReturnValue({ data: { publicUrl: 'https://test-url.com' } })
        })
      },
      from: jest.fn().mockReturnValue({
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockResolvedValue({ data: [{ id: 'test-id' }], error: null })
        })
      }),
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: { id: 'user-id' } } })
      }
    }
    
    ;(createClient as jest.Mock).mockReturnValue(mockSupabase)
  })
  
  test('renders upload component', () => {
    render(<DocumentUploadWithAI />)
    expect(screen.getByText(/arrastra y suelta un archivo aquí/i)).toBeInTheDocument()
  })
  
  test('shows AI options when toggle is enabled', async () => {
    render(<DocumentUploadWithAI />)
    
    // Create a mock file and trigger selection
    const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' })
    const input = screen.getByLabelText(/seleccionar archivo/i)
    
    Object.defineProperty(input, 'files', {
      value: [file]
    })
    
    fireEvent.change(input)
    
    // Toggle AI processing
    const aiToggle = screen.getByLabelText(/enable ai document processing/i)
    fireEvent.click(aiToggle)
    
    // Check if provider selection appears
    await waitFor(() => {
      expect(screen.getByText(/ai provider/i)).toBeInTheDocument()
    })
  })
  
  // Add more tests for file upload, AI processing, error handling, etc.
})
```

## Usage Example

Example of how to use the component in a project creation page:

```tsx
// web/src/app/dashboard/projects/new/page.tsx
"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { DocumentUploadWithAI } from "@/components/features/documents/document-upload-with-ai"
import { ProjectForm } from "@/components/features/projects/project-form"

export default function NewProjectPage() {
  const [analysisData, setAnalysisData] = useState(null)
  const router = useRouter()
  
  const handleAIProcessingComplete = (data) => {
    // Pre-populate form with AI analysis results
    setAnalysisData(data.projectData)
  }
  
  return (
    <div className="space-y-8">
      <h1 className="text-2xl font-bold">Create New Project</h1>
      
      <div className="grid gap-8 md:grid-cols-2">
        <div>
          <h2 className="text-xl font-semibold mb-4">Upload Project Document</h2>
          <DocumentUploadWithAI 
            onAIProcessingComplete={handleAIProcessingComplete}
            allowedFileTypes={[".pdf", ".doc", ".docx", ".txt"]}
          />
        </div>
        
        <div>
          <h2 className="text-xl font-semibold mb-4">Project Details</h2>
          <ProjectForm initialData={analysisData} />
        </div>
      </div>
    </div>
  )
}
```

## Considerations

1. **File Size Limits**
   - Consider implementing chunking for large files
   - Add clear warnings about file size limits

2. **Supported File Types**
   - Initially focus on text-based formats (PDF, DOCX, TXT)
   - Add support for image-based documents later

3. **Error Handling**
   - Provide clear error messages for each stage of the process
   - Implement retry mechanisms for failed uploads or processing

4. **Accessibility**
   - Ensure all UI elements are properly labeled
   - Provide keyboard navigation support
   - Include appropriate ARIA attributes

5. **Performance**
   - Consider implementing upload progress tracking
   - Add cancellation support for long-running operations
