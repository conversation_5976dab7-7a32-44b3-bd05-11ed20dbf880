/**
 * @ai-file-description: "API endpoint for retrieving document analysis results"
 * @ai-related-files: ["../../analyze-document/route.ts", "../../create-project-from-analysis/route.ts"]
 * @ai-owner: "File-Based Projects"
 */

import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server-client';
import { cookies } from 'next/headers';

interface AnalysisData {
  id: string;
  document_id: string;
  document_filename: string;
  document_file_path: string;
  provider: string;
  status: string;
  created_at: string;
  updated_at: string;
  started_at?: string | null;
  completed_at?: string | null;
  analysis_data?: Record<string, unknown>;
  error_message?: string | null;
  confidence_score?: number | null;
}

/**
 * GET handler for retrieving analysis results
 *
 * @ai-responsibility: "Retrieves document analysis results by ID"
 */
export async function GET(
  _request: Request,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const { id } = params;
    const analysisId = id;

    if (!analysisId) {
      return NextResponse.json(
        { error: 'Analysis ID is required' },
        { status: 400 }
      );
    }

    const cookieStore = await cookies();
    const supabase = createClient(cookieStore);

    // Get user data for authorization
    // No pasar parámetros a getUser() para compatibilidad con todas las versiones de Supabase
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError || !userData.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get analysis details using the secure function
    const { data: analysis, error } = await supabase
      .rpc('get_analysis_by_id', {
        p_analysis_id: analysisId,
        p_user_id: userData.user.id
      }) as { data: AnalysisData[] | null; error: Error | null };

    if (error) {
      console.error('Error fetching analysis:', error);
      return NextResponse.json(
        { error: 'Analysis not found or access denied' },
        { status: 404 }
      );
    }

    if (!analysis || analysis.length === 0) {
      return NextResponse.json(
        { error: 'Analysis not found or access denied' },
        { status: 404 }
      );
    }

    const analysisData = analysis[0] as AnalysisData;

    // Verificar que los campos requeridos existen
    if (!analysisData || typeof analysisData !== 'object') {
      throw new Error('Invalid analysis data format');
    }

    return NextResponse.json({
      id: analysisData.id,
      documentId: analysisData.document_id,
      documentName: analysisData.document_filename,
      documentPath: analysisData.document_file_path,
      provider: analysisData.provider,
      status: analysisData.status,
      createdAt: analysisData.created_at,
      updatedAt: analysisData.updated_at,
      startedAt: analysisData.started_at,
      completedAt: analysisData.completed_at,
      projectData: analysisData.analysis_data,
      errorMessage: analysisData.error_message,
      confidenceScore: analysisData.confidence_score
    });
  } catch (error: unknown) {
    console.error('Unexpected error fetching analysis:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
