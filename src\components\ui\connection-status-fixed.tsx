"use client"

import { useState, useEffect } from "react"
import { CheckCircle, XCircle, AlertCircle, Wifi, WifiOff } from "lucide-react"
import { connectionService, ConnectionEvent, ConnectionStatus as ConnStatus } from "@/lib/services/connection-service"

export function ConnectionStatus() {
  const [status, setStatus] = useState<ConnStatus>('checking')
  const [isOnline, setIsOnline] = useState<boolean>(typeof navigator !== 'undefined' ? navigator.onLine : true)
  const [errorMessage, setErrorMessage] = useState<string | null>(null)

  // Usar el servicio de conexión para monitorear el estado
  useEffect(() => {
    // Configurar listeners para eventos de conexión
    const handleStatusChange = (stats: unknown) => {
      setStatus(stats.status)
      setIsOnline(stats.isOnline)
      setErrorMessage(stats.lastErrorMessage)
    }

    // Suscribirse a eventos
    connectionService.on(ConnectionEvent.STATUS_CHANGE, handleStatusChange)

    // Iniciar monitoreo con intervalo de 60 segundos
    connectionService.startMonitoring(60000)

    // Obtener estado actual
    const currentStats = connectionService.getStats()
    setStatus(currentStats.status)
    setIsOnline(currentStats.isOnline)
    setErrorMessage(currentStats.lastErrorMessage)

    // Limpiar al desmontar
    return () => {
      connectionService.removeListener(ConnectionEvent.STATUS_CHANGE, handleStatusChange)
    }
  }, [])

  // Renderizar el indicador de estado
  return (
    <div className="flex items-center">
      <div
        className={`
          flex items-center px-2 py-1 rounded-full text-xs border cursor-help
          ${status === 'checking' ? 'animate-pulse border-gray-300 bg-gray-100' :
            status === 'connected' ? 'border-green-200 bg-green-50 text-green-700' :
            'border-red-200 bg-red-50 text-red-700'}
        `}
        title={status === 'checking' ? 'Verificando conexión...' :
              status === 'connected' ? 'Conectado a Supabase' :
              `Error: ${errorMessage || 'Error desconocido'}`}
      >
        {status === 'checking' ? (
          <>
            <AlertCircle className="h-3 w-3 mr-1" />
            <span>Verificando...</span>
          </>
        ) : status === 'connected' ? (
          <>
            <CheckCircle className="h-3 w-3 mr-1" />
            <span>Conectado</span>
          </>
        ) : (
          <>
            {isOnline ? <XCircle className="h-3 w-3 mr-1" /> : <WifiOff className="h-3 w-3 mr-1" />}
            <span>Error</span>
          </>
        )}
      </div>
    </div>
  )
}
