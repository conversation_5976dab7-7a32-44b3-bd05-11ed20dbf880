# GitHub Integration for AdminCore

This document provides instructions on how to set up and use the GitHub integration feature in AdminCore.

## Overview

The GitHub integration allows users to:

1. Authenticate with GitHub using OAuth
2. Sync GitHub repositories as projects
3. Keep projects synchronized with GitHub repositories

## Setup Instructions

### 1. Create a GitHub OAuth App

1. Go to your GitHub account settings
2. Navigate to "Developer settings" > "OAuth Apps" > "New OAuth App"
3. Fill in the following details:
   - **Application name**: AdminCore
   - **Homepage URL**: Your application URL (e.g., http://localhost:3000)
   - **Authorization callback URL**: Your callback URL (e.g., http://localhost:3000/auth/callback)
4. Click "Register application"
5. Generate a new client secret
6. Copy the Client ID and Client Secret

### 2. Configure Environment Variables

Add the following variables to your `.env` file:

```
NEXT_PUBLIC_GITHUB_CLIENT_ID=your_github_client_id
NEXT_PUBLIC_GITHUB_CLIENT_SECRET=your_github_client_secret
```

### 3. Configure Supabase Auth

1. Go to your Supabase dashboard
2. Navigate to "Authentication" > "Providers"
3. Enable "GitHub" provider
4. Enter your GitHub Client ID and Client Secret
5. Save changes

## Usage

### Authentication with GitHub

Users can authenticate with GitHub in two ways:

1. From the login/register page by clicking the "Sign in with GitHub" button
2. From the GitHub integration page in the dashboard

### Syncing Repositories

Once authenticated with GitHub, users can:

1. Go to the GitHub integration page in the dashboard
2. View their connected GitHub account
3. Sync repositories by clicking the "Sync repositories" button
4. Create projects from repositories

### Project Synchronization

Projects synced with GitHub repositories can be configured to:

1. Automatically sync at regular intervals
2. Sync with a specific branch
3. Sync from a specific path in the repository

## Database Schema

The GitHub integration uses the following database tables:

### github_connections

Stores GitHub OAuth connections for users.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| user_id | UUID | Reference to auth.users |
| access_token | TEXT | GitHub OAuth access token |
| refresh_token | TEXT | GitHub OAuth refresh token |
| token_expires_at | TIMESTAMP | Token expiration time |
| github_user_id | TEXT | GitHub user ID |
| github_username | TEXT | GitHub username |
| github_email | TEXT | GitHub email |
| github_avatar_url | TEXT | GitHub avatar URL |
| is_active | BOOLEAN | Whether the connection is active |
| created_at | TIMESTAMP | Creation timestamp |
| updated_at | TIMESTAMP | Update timestamp |
| last_synced_at | TIMESTAMP | Last sync timestamp |

### github_repositories

Stores GitHub repositories for users.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| connection_id | UUID | Reference to github_connections |
| repository_id | TEXT | GitHub repository ID |
| repository_name | TEXT | GitHub repository name |
| repository_full_name | TEXT | GitHub repository full name |
| repository_url | TEXT | GitHub repository URL |
| repository_description | TEXT | GitHub repository description |
| is_private | BOOLEAN | Whether the repository is private |
| default_branch | TEXT | Default branch name |
| created_at | TIMESTAMP | Creation timestamp |
| updated_at | TIMESTAMP | Update timestamp |
| last_synced_at | TIMESTAMP | Last sync timestamp |

### github_project_sync

Stores synchronization settings for projects.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| project_id | UUID | Reference to projects |
| repository_id | UUID | Reference to github_repositories |
| sync_enabled | BOOLEAN | Whether sync is enabled |
| auto_sync | BOOLEAN | Whether auto-sync is enabled |
| sync_interval_minutes | INTEGER | Auto-sync interval in minutes |
| sync_branch | TEXT | Branch to sync from |
| sync_path | TEXT | Path in repository to sync from |
| created_at | TIMESTAMP | Creation timestamp |
| updated_at | TIMESTAMP | Update timestamp |
| last_synced_at | TIMESTAMP | Last sync timestamp |

## API Endpoints

The GitHub integration uses the following API endpoints:

- `GET /api/github/repositories`: Get user's GitHub repositories
- `POST /api/github/sync`: Sync user's GitHub repositories
- `POST /api/github/create-project`: Create a project from a GitHub repository
- `GET /api/github/connection`: Get user's GitHub connection

## Troubleshooting

### Common Issues

1. **Authentication fails**: Ensure your GitHub OAuth app is configured correctly and the callback URL matches your application.
2. **Cannot see repositories**: Try syncing repositories manually by clicking the "Sync repositories" button.
3. **Sync fails**: Check that your GitHub token has the necessary permissions (repo scope).

### Logs

Check the browser console and server logs for detailed error messages.

## Security Considerations

- GitHub access tokens are stored securely in the database
- Row-level security policies ensure users can only access their own connections and repositories
- Scopes are limited to what's necessary for the integration to function
