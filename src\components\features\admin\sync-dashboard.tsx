'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { Switch } from '@/components/ui/switch'
import { 
  AlertCircle, 
  AlertTriangle, 
  ArrowDownIcon, 
  ArrowUpIcon, 
  CheckCircle2, 
  Database, 
  RefreshCw, 
  Save, 
  Trash2, 
  Upload,
  Download,
  Clock,
  Wifi,
  WifiOff
} from 'lucide-react'
import { useLocalDbStats, useSyncManager } from '@/hooks/use-local-database'
import { SyncConfig } from '@/lib/db/local-database'

// Componente para mostrar estadísticas de sincronización
const SyncStatsCard = ({ stats, lastSyncTime }: { stats: unknown, lastSyncTime: number | null }) => {
  if (!stats) return null;

  // Formatear tiempo desde última sincronización
  const getLastSyncText = () => {
    if (!lastSyncTime) return 'Nunca';
    
    const now = Date.now();
    const diff = now - lastSyncTime;
    
    if (diff < 60000) {
      return 'Hace menos de un minuto';
    } else if (diff < 3600000) {
      return `Hace ${Math.floor(diff / 60000)} minutos`;
    } else if (diff < 86400000) {
      return `Hace ${Math.floor(diff / 3600000)} horas`;
    } else {
      return `Hace ${Math.floor(diff / 86400000)} días`;
    }
  };

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg">Estado de Sincronización</CardTitle>
          <Badge variant={stats.errorLogsLast24h > 0 ? 'destructive' : 'outline'}>
            {stats.errorLogsLast24h > 0 ? `${stats.errorLogsLast24h} errores` : 'Saludable'}
          </Badge>
        </div>
        <CardDescription>
          Estadísticas de sincronización con Supabase
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <p className="text-sm font-medium leading-none">
              Última sincronización
            </p>
            <p className="text-sm text-muted-foreground">
              {getLastSyncText()}
            </p>
          </div>
          <Clock className="h-4 w-4 text-muted-foreground" />
        </div>
        
        <Separator />
        
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <p className="text-sm font-medium leading-none">
              Operaciones pendientes
            </p>
            <p className="text-sm text-muted-foreground">
              Cambios esperando sincronización
            </p>
          </div>
          <Badge variant={stats.totalPendingOperations > 10 ? 'secondary' : 'outline'}>
            {stats.totalPendingOperations}
          </Badge>
        </div>
        
        <Separator />
        
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <p className="text-sm font-medium leading-none">
              Sincronizaciones (24h)
            </p>
            <p className="text-sm text-muted-foreground">
              Total de sincronizaciones en las últimas 24 horas
            </p>
          </div>
          <Badge variant="outline">
            {stats.syncLogsLast24h}
          </Badge>
        </div>
      </CardContent>
    </Card>
  )
}

// Componente para configurar la sincronización
const SyncConfigCard = ({ 
  config, 
  onSave,
  isOnline
}: { 
  config: SyncConfig | null, 
  onSave: (config: Partial<SyncConfig>) => void,
  isOnline: boolean
}) => {
  const [localConfig, setLocalConfig] = useState<Partial<SyncConfig>>({
    enabled: true,
    interval_minutes: 5,
    sync_on_startup: true,
    sync_on_network_reconnect: true
  });
  const [hasChanges, setHasChanges] = useState(false);

  // Actualizar configuración local cuando cambia la configuración externa
  useEffect(() => {
    if (config) {
      setLocalConfig({
        enabled: config.enabled,
        interval_minutes: config.interval_minutes,
        sync_on_startup: config.sync_on_startup,
        sync_on_network_reconnect: config.sync_on_network_reconnect
      });
      setHasChanges(false);
    }
  }, [config]);

  // Manejar cambios en los campos
  const handleChange = (field: keyof SyncConfig, value: unknown) => {
    setLocalConfig(prev => {
      const updated = { ...prev, [field]: value };
      setHasChanges(true);
      return updated;
    });
  };

  // Guardar cambios
  const handleSave = () => {
    onSave(localConfig);
    setHasChanges(false);
  };

  // Restablecer cambios
  const handleReset = () => {
    if (config) {
      setLocalConfig({
        enabled: config.enabled,
        interval_minutes: config.interval_minutes,
        sync_on_startup: config.sync_on_startup,
        sync_on_network_reconnect: config.sync_on_network_reconnect
      });
      setHasChanges(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg">Configuración de Sincronización</CardTitle>
          {isOnline ? (
            <Badge variant="outline" className="bg-green-50">
              <Wifi className="h-3 w-3 mr-1" /> En línea
            </Badge>
          ) : (
            <Badge variant="outline" className="bg-red-50">
              <WifiOff className="h-3 w-3 mr-1" /> Sin conexión
            </Badge>
          )}
        </div>
        <CardDescription>
          Configura cómo se sincroniza la base de datos local con Supabase
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center space-x-2">
          <Switch
            id="sync-enabled"
            checked={localConfig.enabled}
            onCheckedChange={(checked) => handleChange('enabled', checked)}
          />
          <Label htmlFor="sync-enabled">Habilitar sincronización automática</Label>
        </div>
        
        <div className="grid gap-2">
          <Label htmlFor="interval">Intervalo de sincronización (minutos)</Label>
          <Input
            id="interval"
            type="number"
            min="1"
            max="60"
            value={localConfig.interval_minutes}
            onChange={(e) => handleChange('interval_minutes', parseInt(e.target.value))}
            disabled={!localConfig.enabled}
          />
        </div>
        
        <div className="flex items-center space-x-2">
          <Switch
            id="sync-startup"
            checked={localConfig.sync_on_startup}
            onCheckedChange={(checked) => handleChange('sync_on_startup', checked)}
            disabled={!localConfig.enabled}
          />
          <Label htmlFor="sync-startup">Sincronizar al iniciar la aplicación</Label>
        </div>
        
        <div className="flex items-center space-x-2">
          <Switch
            id="sync-reconnect"
            checked={localConfig.sync_on_network_reconnect}
            onCheckedChange={(checked) => handleChange('sync_on_network_reconnect', checked)}
            disabled={!localConfig.enabled}
          />
          <Label htmlFor="sync-reconnect">Sincronizar al reconectar a Internet</Label>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={handleReset} disabled={!hasChanges}>
          Restablecer
        </Button>
        <Button onClick={handleSave} disabled={!hasChanges}>
          <Save className="mr-2 h-4 w-4" />
          Guardar cambios
        </Button>
      </CardFooter>
    </Card>
  )
}

// Componente para mostrar estadísticas de la base de datos local
const LocalDbStatsCard = ({ stats, onRefresh }: { stats: unknown, onRefresh: () => void }) => {
  if (!stats) return null;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Base de Datos Local</CardTitle>
        <CardDescription>
          Estadísticas de la base de datos IndexedDB
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-1">
            <p className="text-sm font-medium leading-none">Proyectos</p>
            <p className="text-2xl font-bold">{stats.projectsCount}</p>
          </div>
          
          <div className="space-y-1">
            <p className="text-sm font-medium leading-none">Documentos</p>
            <p className="text-2xl font-bold">{stats.documentsCount}</p>
          </div>
          
          <div className="space-y-1">
            <p className="text-sm font-medium leading-none">Usuarios</p>
            <p className="text-2xl font-bold">{stats.usersCount}</p>
          </div>
          
          <div className="space-y-1">
            <p className="text-sm font-medium leading-none">Operaciones pendientes</p>
            <p className="text-2xl font-bold">{stats.pendingOperationsCount}</p>
          </div>
        </div>
        
        <Separator />
        
        <div className="flex justify-between items-center">
          <div className="space-y-1">
            <p className="text-sm font-medium leading-none">Tamaño estimado</p>
            <p className="text-sm">{stats.dbSize}</p>
          </div>
          
          <div className="space-y-1">
            <p className="text-sm font-medium leading-none">Registros de sincronización</p>
            <p className="text-sm">{stats.syncLogsCount}</p>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button variant="outline" className="w-full" onClick={onRefresh}>
          <RefreshCw className="mr-2 h-4 w-4" />
          Actualizar estadísticas
        </Button>
      </CardFooter>
    </Card>
  )
}

// Componente principal del dashboard de sincronización
export function SyncDashboard() {
  const { 
    isSyncing, 
    lastSyncTime, 
    syncError, 
    syncStats, 
    config, 
    syncNow, 
    updateSyncConfig, 
    cleanupLogs 
  } = useSyncManager();
  
  const { stats, refreshStats } = useLocalDbStats();
  const [activeTab, setActiveTab] = useState<string>('overview');
  const [isOnline, setIsOnline] = useState<boolean>(true);

  // Detectar estado de conexión
  useEffect(() => {
    const updateOnlineStatus = () => {
      setIsOnline(navigator.onLine);
    };
    
    // Configurar listeners
    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);
    
    // Estado inicial
    updateOnlineStatus();
    
    // Limpiar al desmontar
    return () => {
      window.removeEventListener('online', updateOnlineStatus);
      window.removeEventListener('offline', updateOnlineStatus);
    };
  }, []);

  // Si no hay estadísticas, mostrar carga
  if (!stats || !syncStats) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex flex-col items-center space-y-4">
          <RefreshCw className="h-8 w-8 animate-spin text-primary" />
          <p className="text-sm text-muted-foreground">Cargando estadísticas de sincronización...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Sincronización Local</h2>
          <p className="text-muted-foreground">
            Gestiona la sincronización entre la base de datos local y Supabase
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button 
            onClick={syncNow} 
            disabled={isSyncing || !isOnline}
            className="flex items-center"
          >
            {isSyncing ? (
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="mr-2 h-4 w-4" />
            )}
            {isSyncing ? 'Sincronizando...' : 'Sincronizar ahora'}
          </Button>
        </div>
      </div>

      {!isOnline && (
        <Alert variant="warning" className="mb-6">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Sin conexión a Internet</AlertTitle>
          <AlertDescription>
            Estás trabajando en modo sin conexión. Los cambios se sincronizarán cuando vuelvas a estar en línea.
          </AlertDescription>
        </Alert>
      )}

      {syncError && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error de sincronización</AlertTitle>
          <AlertDescription>
            {syncError}
          </AlertDescription>
        </Alert>
      )}

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <SyncStatsCard stats={syncStats} lastSyncTime={lastSyncTime} />
        <SyncConfigCard config={config} onSave={updateSyncConfig} isOnline={isOnline} />
        <LocalDbStatsCard stats={stats} onRefresh={refreshStats} />
      </div>
      
      <div className="flex justify-between mt-8">
        <Button variant="outline" onClick={cleanupLogs}>
          <Trash2 className="mr-2 h-4 w-4" />
          Limpiar registros antiguos
        </Button>
        
        <div className="space-x-2">
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Exportar datos locales
          </Button>
          <Button variant="outline">
            <Upload className="mr-2 h-4 w-4" />
            Importar datos
          </Button>
        </div>
      </div>
    </div>
  );
}
