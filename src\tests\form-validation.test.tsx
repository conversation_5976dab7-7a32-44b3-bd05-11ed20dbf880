/**
 * @file Tests for form validation
 * @description Unit tests for form validation components and utilities
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import { vi } from 'vitest';
import { FormValidationAlert } from '@/components/ui/form-validation-alert';
import { dataValidator } from '@/lib/services/data-validator-service';
import { errorHandler, ErrorType } from '@/lib/services/error-handler-service';

// Mock the toast function
vi.mock('@/hooks/use-toast', () => ({
  toast: vi.fn(),
}));

import React from 'react';

/**
 * @file Form Validation Components tests
 * @description Unit tests for form validation components and utilities
 */

describe('Form Validation Components', () => {
  describe('FormValidationAlert', () => {
    it('should not render when there are no errors', () => {
      const { container } = render(<FormValidationAlert errors={[]} />);
      expect(container.firstChild).toBeNull();
    });

    it('should render errors when provided', () => {
      const errors = ['Error 1', 'Error 2'];
      render(<FormValidationAlert errors={errors} />);
      
      expect(screen.getByText('Error de validación')).toBeInTheDocument();
      expect(screen.getByText('Error 1')).toBeInTheDocument();
      expect(screen.getByText('Error 2')).toBeInTheDocument();
    });

    it('should use custom title when provided', () => {
      const errors = ['Error 1'];
      render(<FormValidationAlert errors={errors} title="Custom Title" />);
      
      expect(screen.getByText('Custom Title')).toBeInTheDocument();
    });
  });

  describe('DataValidatorService', () => {
    describe('validateRequiredFields', () => {
      it('should validate required fields correctly', () => {
        const data = {
          name: 'Test',
          email: '',
          description: null,
        };

        const result = dataValidator.validateRequiredFields(data, ['name', 'email']);
        
        expect(result.isValid).toBe(false);
        expect(result.errors).toHaveProperty('email');
        expect(result.errors).not.toHaveProperty('name');
      });

      it('should handle missing fields', () => {
        const data = {
          name: 'Test',
        };

        const result = dataValidator.validateRequiredFields(data, ['name', 'email']);
        
        expect(result.isValid).toBe(false);
        expect(result.errors).toHaveProperty('email');
        expect(result.errors).not.toHaveProperty('name');
      });

      it('should return isValid=true when all required fields are present', () => {
        const data = {
          name: 'Test',
          email: '<EMAIL>',
        };

        const result = dataValidator.validateRequiredFields(data, ['name', 'email']);
        
        expect(result.isValid).toBe(true);
        expect(Object.keys(result.errors).length).toBe(0);
      });
    });

    describe('isEmpty', () => {
      it('should correctly identify empty values', () => {
        expect(dataValidator.isEmpty(null)).toBe(true);
        expect(dataValidator.isEmpty(undefined)).toBe(true);
        expect(dataValidator.isEmpty('')).toBe(true);
        expect(dataValidator.isEmpty('   ')).toBe(true);
        expect(dataValidator.isEmpty([])).toBe(true);
        expect(dataValidator.isEmpty({})).toBe(true);
      });

      it('should correctly identify non-empty values', () => {
        expect(dataValidator.isEmpty('text')).toBe(false);
        expect(dataValidator.isEmpty(0)).toBe(false);
        expect(dataValidator.isEmpty(false)).toBe(false);
        expect(dataValidator.isEmpty([1, 2])).toBe(false);
        expect(dataValidator.isEmpty({ key: 'value' })).toBe(false);
      });
    });

    describe('sanitizeTextFields', () => {
      it('should convert empty strings to null', () => {
        const data = {
          name: 'Test',
          description: '',
          notes: '   ',
          other: 'value',
        };

        const result = dataValidator.sanitizeTextFields(data, ['description', 'notes']);
        
        expect(result.description).toBeNull();
        expect(result.notes).toBeNull();
        expect(result.name).toBe('Test');
        expect(result.other).toBe('value');
      });

      it('should trim non-empty strings', () => {
        const data = {
          name: '  Test  ',
          description: 'Description',
        };

        const result = dataValidator.sanitizeTextFields(data, ['name', 'description']);
        
        expect(result.name).toBe('Test');
        expect(result.description).toBe('Description');
      });
    });
  });

  describe('ErrorHandlerService', () => {
    it('should handle required field errors', () => {
      const error = new Error('required_field');
      const result = errorHandler.handleError(error);
      
      expect(result.type).toBe(ErrorType.REQUIRED_FIELD);
      expect(result.message).toContain('obligatorio');
    });

    it('should handle empty field errors', () => {
      const error = new Error('empty_field');
      const result = errorHandler.handleError(error);
      
      expect(result.type).toBe(ErrorType.EMPTY_FIELD);
      expect(result.message).toContain('vacío');
    });

    it('should include field name in error message when provided', () => {
      const error = new Error('required_field');
      const result = errorHandler.handleError(error, { field: 'email' });
      
      expect(result.message).toContain('email');
    });
  });
});
