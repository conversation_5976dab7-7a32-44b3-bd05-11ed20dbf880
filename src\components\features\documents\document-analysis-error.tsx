import React from 'react';
import {
  <PERSON><PERSON>,
  AlertTitle,
  AlertDescription,
} from "@/components/ui/alert";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AlertCircle, RefreshCw, FileText, HelpCircle } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";

// Tipos de errores que pueden ocurrir en el análisis de documentos
export enum DocumentAnalysisErrorType {
  PDF_EXTRACTION = 'pdf_extraction',
  TOKEN_LIMIT = 'token_limit',
  MODEL_NOT_FOUND = 'model_not_found',
  API_ERROR = 'api_error',
  UNKNOWN = 'unknown'
}

// Interfaz para los errores estructurados
export interface DocumentAnalysisErrorDetails {
  type: DocumentAnalysisErrorType;
  message: string;
  details?: string;
  suggestions?: string[];
}

// Propiedades del componente
interface DocumentAnalysisErrorProps {
  error: string | DocumentAnalysisErrorDetails;
  onRetry?: () => void;
  onSelectDifferentDocument?: () => void;
  onSelectDifferentModel?: () => void;
}

/**
 * Componente para mostrar errores de análisis de documentos con sugerencias
 */
export function DocumentAnalysisError({
  error,
  onRetry,
  onSelectDifferentDocument,
  onSelectDifferentModel
}: DocumentAnalysisErrorProps) {
  // Convertir el error a un objeto estructurado si es una cadena
  const errorDetails: DocumentAnalysisErrorDetails = typeof error === 'string'
    ? {
        type: DocumentAnalysisErrorType.UNKNOWN,
        message: error,
        suggestions: ['Intente nuevamente', 'Seleccione otro documento']
      }
    : error;

  // Determinar el título y el icono según el tipo de error
  let title = 'Error en el análisis de documentos';
  let icon = <AlertCircle className="h-5 w-5" />;
  let badgeVariant: 'default' | 'destructive' | 'outline' = 'destructive';

  switch (errorDetails.type) {
    case DocumentAnalysisErrorType.PDF_EXTRACTION:
      title = 'Error al extraer texto del PDF';
      icon = <FileText className="h-5 w-5" />;
      break;
    case DocumentAnalysisErrorType.TOKEN_LIMIT:
      title = 'Documento demasiado grande';
      icon = <AlertCircle className="h-5 w-5" />;
      badgeVariant = 'default';
      break;
    case DocumentAnalysisErrorType.MODEL_NOT_FOUND:
      title = 'Modelo no disponible';
      icon = <HelpCircle className="h-5 w-5" />;
      badgeVariant = 'outline';
      break;
    case DocumentAnalysisErrorType.API_ERROR:
      title = 'Error de comunicación con el servicio';
      icon = <AlertCircle className="h-5 w-5" />;
      break;
  }

  return (
    <Card className="border-destructive/50">
      <CardHeader>
        <div className="flex items-center space-x-2">
          {icon}
          <CardTitle>{title}</CardTitle>
        </div>
        <CardDescription>
          Se ha producido un error durante el análisis del documento
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{errorDetails.message}</AlertDescription>
        </Alert>

        {errorDetails.details && (
          <Accordion type="single" collapsible className="w-full">
            <AccordionItem value="details">
              <AccordionTrigger>Detalles técnicos</AccordionTrigger>
              <AccordionContent>
                <div className="bg-muted p-3 rounded-md text-sm font-mono whitespace-pre-wrap">
                  {errorDetails.details}
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        )}

        {errorDetails.suggestions && errorDetails.suggestions.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Sugerencias:</h4>
            <ul className="list-disc pl-5 space-y-1">
              {errorDetails.suggestions.map((suggestion, index) => (
                <li key={index} className="text-sm">{suggestion}</li>
              ))}
            </ul>
          </div>
        )}

        <div className="flex items-center space-x-2">
          <Badge variant={badgeVariant}>
            {errorDetails.type.replace('_', ' ')}
          </Badge>
        </div>
      </CardContent>
      <Separator />
      <CardFooter className="flex justify-between pt-4">
        {onRetry && (
          <Button variant="outline" onClick={onRetry}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Intentar nuevamente
          </Button>
        )}
        <div className="space-x-2">
          {onSelectDifferentDocument && (
            <Button variant="secondary" onClick={onSelectDifferentDocument}>
              <FileText className="mr-2 h-4 w-4" />
              Seleccionar otro documento
            </Button>
          )}
          {onSelectDifferentModel && (
            <Button variant="default" onClick={onSelectDifferentModel}>
              <HelpCircle className="mr-2 h-4 w-4" />
              Cambiar modelo
            </Button>
          )}
        </div>
      </CardFooter>
    </Card>
  );
}
