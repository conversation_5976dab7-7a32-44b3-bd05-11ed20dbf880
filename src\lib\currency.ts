/**
 * Tipos de divisas soportadas
 */
export type Currency = 'USD' | 'CLP';

/**
 * Información de las divisas
 */
export const CURRENCIES = {
  USD: {
    code: 'USD',
    symbol: '$',
    name: 'Dólar estadounidense',
    locale: 'en-US',
    exchangeRate: 1, // Base rate
  },
  CLP: {
    code: 'CLP',
    symbol: '$',
    name: 'Peso chileno',
    locale: 'es-CL',
    exchangeRate: 900, // Aproximado: 1 USD = 900 CLP (ajustar según sea necesario)
  },
};

/**
 * Detecta la divisa basada en la ubicación del usuario
 * @returns La divisa detectada (USD o CLP)
 */
export async function detectUserCurrency(): Promise<Currency> {
  try {
    // Intentar obtener la ubicación del usuario mediante la API de geolocalización
    const response = await fetch('https://ipapi.co/json/');
    const data = await response.json();
    
    // Si el país es Chile, devolver CLP, de lo contrario USD
    return data.country_code === 'CL' ? 'CLP' : 'USD';
  } catch (error) {
    console.error('Error al detectar la ubicación:', error);
    // Por defecto, usar CLP si hay un error
    return 'CLP';
  }
}

/**
 * Formatea un valor numérico según la divisa especificada
 * @param value El valor a formatear
 * @param currency La divisa (USD o CLP)
 * @returns El valor formateado con el símbolo de la divisa
 */
export function formatCurrency(value: number | null | undefined, currency: Currency = 'CLP'): string {
  if (value === null || value === undefined) return '-';
  
  const currencyInfo = CURRENCIES[currency];
  
  return new Intl.NumberFormat(currencyInfo.locale, {
    style: 'currency',
    currency: currency,
    maximumFractionDigits: currency === 'CLP' ? 0 : 2,
  }).format(value);
}

/**
 * Convierte un valor de una divisa a otra
 * @param value El valor a convertir
 * @param fromCurrency La divisa de origen
 * @param toCurrency La divisa de destino
 * @returns El valor convertido
 */
export function convertCurrency(
  value: number,
  fromCurrency: Currency,
  toCurrency: Currency
): number {
  if (fromCurrency === toCurrency) return value;
  
  const fromRate = CURRENCIES[fromCurrency].exchangeRate;
  const toRate = CURRENCIES[toCurrency].exchangeRate;
  
  // Convertir a USD primero (divisa base) y luego a la divisa destino
  return (value / fromRate) * toRate;
}
