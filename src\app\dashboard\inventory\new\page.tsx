"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { createClient } from "@/lib/supabase/client"
import { InventoryItemForm } from "@/components/features/inventory/inventory-item-form"
import { toast } from "@/components/ui/use-toast"

export default function NewInventoryItemPage() {
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()
  const supabase = createClient()

  const handleSubmit = async (data: unknown) => {
    setIsLoading(true)
    try {
      // Insertar el nuevo ítem en la base de datos
      const { error } = await supabase.from("inventory_items").insert({
        name: data.name,
        description: data.description,
        quantity_available: data.quantity_available,
        unit: data.unit,
        unit_cost: data.unit_cost,
        location: data.location,
        barcode: data.barcode,
      })

      if (error) throw error

      toast({
        title: "Ítem creado",
        description: "El ítem de inventario se ha creado correctamente.",
      })

      // Redirigir a la lista de inventario
      router.push("/dashboard/inventory")
      router.refresh()
    } catch (error: unknown) {
      console.error("Error al crear el ítem de inventario:", error)
      const errorMessage = error instanceof Error ? error.message : "Ha ocurrido un error al crear el ítem de inventario."
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Nuevo Ítem de Inventario</h2>
        <p className="text-muted-foreground mt-2">
          Complete el formulario para agregar un nuevo ítem al inventario.
        </p>
      </div>

      <div className="rounded-md border p-6">
        <InventoryItemForm
          onSubmit={handleSubmit}
          isLoading={isLoading}
        />
      </div>
    </div>
  )
}
